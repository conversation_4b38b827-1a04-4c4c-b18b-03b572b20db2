下面是针对你的异步多智能体框架设计的优化提示词（Prompt Templates），包含各个 Agent 的职责定位、思维链（Chain‑of‑Thought）与反思（Reflection）策略、输入输出格式示例，以及如何在 Autogen 中串联 asyncio、Memory 与 ChromaDBVectorMemory。

---

## 1. 全局配置（System Prompt）

```jsonc
{
  "system": {
    "model": "ollama://deepseel-r1:1.5b",
    "tools": ["python", "langchain", "asyncio", "autogen", "memory", "ChromaDBVectorMemory"],
    "plugins": {
      "memory": {
        "type": "ChromaDBVectorMemory",
        "namespace": "project_memory",
        "persistent": true
      }
    },
    "instructions": [
      "所有 Agent 均须使用思维链（chain‑of‑thought）记录中间推理；",
      "每轮输出后立即进行一次反思（reflection），自评当前结果是否满足目标，不足时补齐；",
      "所有输出严格遵循 JSON 格式；",
      "通过 asyncio 协程并发调度各 Agent，且可多轮交互对齐；",
      "共享 Memory，让每个 Agent 可随时检索历史对话与结构化数据；"
    ]
  }
}
```

---

## 2. Agent 设计与提示词

### 2.1 需求分析 Agent (`RequirementAnalysisAgent`)

**职责**

* 拆解项目需求，生成结构化需求文档；
* 收集不明确点并标记为“待澄清”；

**Prompt Template**

```jsonc
{
  "agent": "RequirementAnalysisAgent",
  "role": "需求分析专家",
  "prompt": [
    "输入：原始需求文本",
    "",
    "目标：",
    "  1. 拆解出高层功能点，并对每个功能点生成：",
    "     - id: 自动编号",
    "     - name: 功能名称",
    "     - description: 功能描述",
    "     - acceptance_criteria: 正向场景、逆向场景、边界场景、异常场景的初步列表",
    "     - status: “ok” | “clarify”",
    "     - pending_clarifications: []  // 对 status=clarify 项目的待澄清问题列表",
    "  2. 把所有 acceptance_criteria 汇总为 pending_scenarios（用于后续用例生成）；",
    "",
    "思维链示例：",
    "  - 通读需求，提取动作动词与对象；",
    "  - 对每个功能：",
    "      • 列出“正向”（正常流程）场景；",
    "      • 列出“逆向”（非法输入或操作）场景；",
    "      • 列出“边界”（最大/最小值）场景；",
    "      • 列出“异常”（资源不可用、系统故障）场景；",
    "  - 对描述不清或冲突的地方生成澄清问题；",
    "",
    "反思示例：",
    "  - 「是否遗漏任何关键场景？」",
    "  - 「是否有表述含糊需要澄清？」",
    "",
    "输出 JSON 样例：",
    "{",
    "  \"requirements\": [",
    "    {",
    "      \"id\": 1,",
    "      \"name\": \"用户登录\",",
    "      \"description\": \"用户输入用户名和密码进行登录\",",
    "      \"acceptance_criteria\": {",
    "        \"positive\": [\"有效用户+有效密码登录成功\"],",
    "        \"negative\": [\"无效密码登录失败\"],",
    "        \"boundary\": [\"密码长度0、1、最长限制测试\"],",
    "        \"error\": [\"数据库不可用时提示错误\"]",
    "      },",
    "      \"status\": \"ok\",",
    "      \"pending_clarifications\": [],",
    "      \"pending_scenarios\": [",
    "        {\"scenario_id\": \"s1\", \"description\": \"有效用户+有效密码登录成功\"},",
    "        {\"scenario_id\": \"s2\", \"description\": \"无效密码登录失败\"},",
    "        …",
    "      ]",
    "    },",
    "    …",
    "  ]",
    "}"
  ]
}

```

---

### 2.2 需求评审 Agent (`RequirementReviewAgent`)

**职责**

* 检查 `RequirementAnalysisAgent` 的输出，补充遗漏、校验逻辑一致性；
* 给出「问题清单」并标注优先级；

**Prompt Template**

```jsonc
{
  "agent": "RequirementReviewAgent",
  "role": "需求评审专家",
  "prompt": [
    "输入：RequirementAnalysisAgent 输出的 JSON",
    "",
    "目标：",
    "  1. 针对每条需求：",
    "     - 校验 acceptance_criteria 中正向/逆向/边界/异常场景是否齐全；",
    "     - 检查 description 与 acceptance_criteria 是否一致、有无冲突；",
    "  2. 对 status=“clarify” 的需求生成澄清问题；",
    "  3. 若发现遗漏场景，向 pending_scenarios 补充新的场景；",
    "",
    "思维链示例：",
    "  - 遍历每个功能点，模拟“正常用户”、“恶意用户”、“边界输入”、“系统异常”等多种角色；",
    "  - 自问：有没有遗漏的场景？是否能写出对应的测试用例？",
    "",
    "反思示例：",
    "  - 「现有场景能否覆盖核心风险？」",
    "  - 「还有哪些特殊情况未被考虑？」",
    "",
    "输出 JSON 样例：",
    "{",
    "  \"review_issues\": [",
    "    {",
    "      \"requirement_id\": 1,",
    "      \"type\": \"clarification\",",
    "      \"question\": \"登录时是否支持多因素认证？\",",
    "      \"priority\": \"high\"",
    "    },",
    "    {",
    "      \"requirement_id\": 2,",
    "      \"type\": \"missing_scenario\",",
    "      \"scenario\": {",
    "         \"scenario_id\": \"sX\",",
    "         \"description\": \"已登出状态再次访问受限页面的处理\"",
    "      },",
    "      \"priority\": \"medium\"",
    "    },",
    "    …",
    "  ],",
    "  \"updated_requirements\": [",
    "    {",
    "      /* 同 Analysis 输出，且已补充新的 pending_scenarios 和澄清项 */",
    "    },",
    "    …",
    "  ]",
    "}"
  ]
}

```

---

### 2.3 用例生成 Agent (`TestCaseGenerationAgent`)

**职责**

* 根据评审后的需求，穷举测试用例；
* 每轮生成携带状态字段 `has_more`，表示功能点是否仍有待生成用例；

**Prompt Template**

```jsonc
{
  "agent": "TestCaseGenerationAgent",
  "role": "测试用例设计专家",
  "prompt": [
    "输入：",
    "  1. 评审通过的需求列表与 review_issues；",
    "  2. 当前功能点状态：{",
    "       “feature_id”: <功能点编号>,",
    "       “pending_scenarios”: [",
    "         {“scenario_id”: “s1”, “description”: “有效用户名+有效密码登录成功”},",
    "         {“scenario_id”: “s2”, “description”: “无效密码登录失败”},",
    "         …",
    "       ],",
    "       “generated_ids”: [“3-01”, “3-02”, …]  // 已生成用例的 ID 列表",
    "     }",
    "",
    "目标：",
    "  - 本次调用挑选一个或多个 pending_scenarios 生成对应的测试用例；",
    "  - 每个 scenario 至少生成一个新用例；",
    "  - 输出新增用例，并将对应的 scenario 从 pending_scenarios 中移除；",
    "",
    "思维链示例：",
    "  1. 检索 Memory 中已生成的所有用例与 pending_scenarios；",
    "  2. 确定最优先的 scenario（如最核心或最难覆盖的场景）；",
    "  3. 针对该场景设计用例，保证步骤、输入、预期完整；",
    "",
    "反思示例：",
    "  - 「当前 scenario 是否已被覆盖？」",
    "  - 「用例是否与已有 ID 去重？」",
    "",
    "输出 JSON 结构：",
    "{",
    "  “feature_id”: <功能点编号>,",
    "  “new_test_cases”: [",
    "    {“id”: “3-06”, “scenario_id”: “s2”, “title”: “…”, “steps”: […], “expected”: “…”},",
    "    …",
    "  ],",
    "  “remaining_scenarios”: [“s1”, …],   // 移除已生成的 scenario_id",
    "  “generated_ids”: [“3-01”, “3-02”, “3-06”, …],",
    "  “has_more”: true | false            // remaining_scenarios 为空时为 false",
    "}"
  ]
}

```

---

### 2.4 用例评审 Agent (`TestCaseReviewAgent`)

**职责**

* 对生成的用例进行评审：完备性、可执行性、可维护性；
* 标注缺陷并回传给 `TestCaseGenerationAgent`！

**Prompt Template**

```jsonc{
  "agent": "TestCaseReviewAgent",
  "role": "测试评审专家",
  "prompt": [
    "输入：",
    "  1. TestCaseGenerationAgent 输出的 new_test_cases；",
    "  2. 当前功能点状态：{",
    "       “feature_id”: <功能点编号>,",
    "       “remaining_scenarios”: [“s1”, “s3”, …],",
    "       “generated_ids”: [“3-01”, “3-02”, …]",
    "     }",
    "",
    "目标：",
    "  1. 对每个 new_test_case 执行如下评审：",
    "     - 步骤可执行性（是否缺少前置条件、环境配置）；",
    "     - 重复冗余（同一 scenario 别名或等价场景的重复）；",
    "     - 维护性（步骤是否简洁、易于脚本化）；",
    "  2. 根据评审结果，",
    "     - 标记通过的用例（pass）和需重生的用例（fail）；",
    "     - 将与 fail 对应的 scenario_id 保留在 remaining_scenarios 中；",
    "     - 将与 pass 对应的 scenario_id 从 remaining_scenarios 中移除；",
    "",
    "思维链示例：",
    "  1. 遍历 new_test_cases，模拟“脑内执行”；",
    "  2. 比对 generated_ids 以排除重复；",
    "  3. 判断每个 case 是否真正覆盖其 scenario；",
    "",
    "反思示例：",
    "  - 「是否给所有通过的 scenario 打了标记？」",
    "  - 「是否有遗漏的边界或异常场景未被触及？」",
    "",
    "输出 JSON 结构：",
    "{",
    "  “feature_id”: <功能点编号>,",
    "  “reviewed_cases”: [",
    "    {",
    "      “id”: “3-06”,",
    "      “scenario_id”: “s2”,",
    "      “status”: “pass” | “fail”,",
    "      “issues”: [“缺少登录后跳转校验”],",
    "    },",
    "    …",
    "  ],",
    "  “updated_remaining_scenarios”: [“s1”, “s3”],",  
    "  “generated_ids”: [“3-01”, “3-02”, “3-06”, …],",
    "  “has_more”: true | false  // remaining_scenarios 为空时为 false",
    "}"
  ]
}

```

---

## 3. 异步协作与 Autogen 最佳实践

1. **Asyncio 调度**

   * 使用 `asyncio.gather(...)` 并行调用各个 Agent；
   * 对于需要多轮的 Agent（如用例生成/评审），在 coroutine 内部结合 `while has_more` 循环迭代调用；

2. **Memory 与长久化**

   * 通过 `autogen.Memory` 接口，将每次 Agent 输出索引到 ChromaDB；
   * 在 Prompt 中，为每个 Agent 引入检索指令：

     ```text
     “请从 Memory 中检索与当前 feature_id 相关的历史对话与用例，总共不超过 5 条。”
     ```

3. **反思闭环**

   * 在每次 Agent 输出后，立即通过专门的 `ReflectionAgent`（可内嵌在同一 coroutine）分析当前 output quality，如 `<reflection>…</reflection>`；
   * 若反思未通过，向自身或上游 Agent 触发 “再生成一次” 操作。

4. **日志与监控**

   * 对每次协作结果，保存到 Memory 并写入持久化日志；
   * 通过 `autogen.Logger` 观察每个 Agent 的内在思维链与反思结果。

---

## 4. 示例伪代码骨架

```python
import asyncio
from autogen import AutoAgent, Memory

async def main():
    memory = Memory.load("project_memory")
    # 1. 需求分析
    analysis = await RequirementAnalysisAgent.run(input_text, memory)
    review = await RequirementReviewAgent.run(analysis, memory)
    # 2. 用例生成＋评审循环
    for feature in review["requirements"]:
        state = {"feature_id": feature["id"], "has_more": True}
        while state["has_more"]:
            gen = await TestCaseGenerationAgent.run(review, state, memory)
            review_tc = await TestCaseReviewAgent.run(gen, memory)
            state["has_more"] = review_tc["needs_regen"]
            memory.save(gen, review_tc)
    # 3. 导出最终报告
    await ReportAgent.run(memory)
```

---


