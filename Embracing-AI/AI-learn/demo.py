# 百度千帆的demo
# import qianfan
# import os
# os.environ["QIANFAN_ACCESS_KEY"] = "f9867253fe5a4cf89116412f1678ffd3"
# os.environ["QIANFAN_SECRET_KEY"] = "97aeab1581e34cacac302766ae70e01c"

# chat_comp = qianfan.ChatCompletion()

# # 指定特定模型
# resp = chat_comp.do(model="ERNIE-4.0-8K", messages=[{
#     "role": "user",
#     "content": "生成测试用例推荐用哪个模型，只需要模型名称即可"
# }])

# print(resp["body"])


# 调本地模型的demo
# import requests

# url = "http://localhost:11434/api/chat"
# headers = {"Content-Type": "application/json"}
# data = {
#     "model": "deepseek-r1:7b",
#     "messages": [{"role": "user", "content": "999+999=？ 使用中文回答， 只要告诉我答案就可以了不要其他内容"}],
#     "stream": False
# }

# response = requests.post(url, json=data, headers=headers)
# print(response.json())
# {'model': 'deepseek-r1:7b', 'created_at': '2025-04-24T10:14:52.429857Z', 'message': {'role': 'assistant', 'content': '<think>\n好，我要计算999加999等于多少。首先，我可以把这两个数都看作是接近1000的数，这样可能会比较容易计算。\n\n我知道1000加1000等于2000，那如果我把两个数都减去1的话，那就是999=1000-1。所以，999+999就可以写成(1000-1)+(1000-1)。\n\n接下来，我需要把这两个括号展开来计算：1000 - 1 + 1000 - 1。然后，我可以先把所有的1000加起来，也就是1000+1000=2000。\n\n现在，剩下的就是减去两个1了，也就是-1 -1=-2。那么，总的结果就是2000-2=1998。\n\n所以，经过这样的计算，我可以得出结论：999加999等于1998。\n</think>\n\n999 + 999 = 1998'}, 'done_reason': 'stop', 'done': True, 'total_duration': 55161077553, 'load_duration': 28187911, 'prompt_eval_count': 25, 'prompt_eval_duration': 1506394254, 'eval_count': 239, 'eval_duration': 53625667479}
# print(response.json()["message"]["content"])


# -----------------
# 使用openai风格调本地模型llama3.2:1b
# from openai import OpenAI
# llm = OpenAI(base_url="http://localhost:11434/v1/",api_key="ollama")

# response = llm.chat.completions.create(
#     messages=[{"role":"user","content":"你好，你叫什么名字？你是由谁创造的？"}],model="qwen3:0.6b"
# )

# print(response.choices[0].message.content)


# # 示例1：使用类型注解和__annotations__
# var: dict[str, any] = None
# print(__annotations__["var"])  # 输出：dict[str, Any]
# print(type(var))               # 输出：<class 'NoneType'>
# print(isinstance(var, dict))   # 输出：False

# # 示例2：赋值后
# var = {"name": "Alice"}
# print(__annotations__["var"])  # 输出：dict[str, Any]（注解类型不变）
# print(type(var))               # 输出：<class 'dict'>（实际类型改变）
# print(isinstance(var, dict))   # 输出：True



# 1. Agent核心机制示例
# class TestAgent:
#     def __init__(self):
#         self.planner = TaskPlanner()
#         self.memory = VectorMemory()
#         self.tools = ToolRegistry()
    
#     def execute(self, task):
#         # ReAct推理流程
#         plan = self.planner.plan(task)
#         for step in plan:
#             observation = self.tools.execute(step)
#             reflection = self.reflect(observation)
#             plan = self.planner.update(plan, reflection)
#         return result

# # 2. 多Agent协作示例
# class MultiAgentSystem:
#     def __init__(self):
#         self.coordinator = CoordinatorAgent()
#         self.agents = [PlannerAgent(), ExecutorAgent(), AnalyzerAgent()]
    
#     def collaborate(self, complex_task):
#         # Agent编排和协作
#         subtasks = self.coordinator.decompose(complex_task)
#         results = self.coordinator.orchestrate(subtasks, self.agents)
#         return self.coordinator.synthesize(results)


# 智谱AI开放平台
# import asyncio
# from autogen_agentchat.agents import AssistantAgent
# from autogen_agentchat.ui import Console
# from autogen_ext.models.openai import OpenAIChatCompletionClient
# model_client1 = OpenAIChatCompletionClient(
#     model="glm-4-air",
#     base_url="https://open.bigmodel.cn/api/paas/v4",
#     api_key="2da0d2cf04e440248ed48b88c93eb0d9.K081R2rZcUaOk5pR",
#     model_info={
#         "vision": False,
#         "function_calling": True,
#         "json_output": True,
#         "family": "unknown",
#     },
# )
# async def main():
#     model_client = model_client1
#     primary_agent = AssistantAgent(
#         "primary",
#         model_client=model_client,
#         system_message="你是一个有用的 AI 助手."
#     )
#     stream = primary_agent.run_stream(task="python")
#     await Console(stream)
#     message = await primary_agent._model_context.get_messages()
#     for item in message:
#         print(item)
#     print("-" * 50)
# if __name__ == "__main__":
#     asyncio.run(main())






model="glm-4-air",
base_url="https://open.bigmodel.cn/api/paas/v4",
api_key="2da0d2cf04e440248ed48b88c93eb0d9.K081R2rZcUaOk5pR",



model="Qwen/Qwen3-8B",
base_url="https://api.siliconflow.cn/v1",
api_key="sk-qnhrgxkfjdhaiwbdblefelnfratgyjqnustbusxkrkehfllu",