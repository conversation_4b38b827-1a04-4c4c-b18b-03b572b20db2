{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# LangChain  --⭐️重头到尾敲、熟悉api、理解\n", "\n", "💡 这节课会带给你\n", "\n", "1. 如何使用 LangChain：一套在大模型能力上封装的工具框架\n", "2. 如何用几行代码实现一个复杂的 AI 应用\n", "3. 面向大模型的流程开发的过程抽象"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 写在前面\n", "\n", "官网：https://www.langchain.com/\n", "\n", "- LangChain 也是一套面向大模型的开发框架（SDK）\n", "- LangChain 是 AGI 时代软件工程的一个探索和原型\n", "- 学习 LangChain 要关注接口变更"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON><PERSON><PERSON><PERSON> 的核心组件\n", "\n", "1. 模型 I/O 封装\n", "   - LLMs：大语言模型\n", "   - Chat Models：一般基于 LLMs，但按对话结构重新封装\n", "   - PromptTemple：提示词模板\n", "   - OutputParser：解析输出\n", "2. 数据连接封装\n", "   - Document Loaders：各种格式文件的加载器\n", "   - Document Transformers：对文档的常用操作，如：split, filter, translate, extract metadata, etc\n", "   - Text Embedding Models：文本向量化表示，用于检索等操作（啥意思？别急，后面详细讲）\n", "   - Verctorstores: （面向检索的）向量的存储\n", "   - Retrievers: 向量的检索\n", "3. 对话历史管理\n", "   - 对话历史的存储、加载与剪裁\n", "4. 架构封装\n", "   - Chain：实现一个功能或者一系列顺序功能组合\n", "   - Agent：根据用户输入，自动规划执行步骤，自动选择每步需要的工具，最终完成用户指定的功能\n", "     - Tools：调用外部功能的函数，例如：调 google 搜索、文件 I/O、Linux Shell 等等\n", "     - Toolkits：操作某软件的一组工具集，例如：操作 DB、操作 Gmail 等等\n", "5. Callbacks\n", "\n", "<img src=\"./assets/langchain.png\" style=\"margin-left: 0px\" width=600px>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 文档（以 Python 版为例）\n", "\n", "- 功能模块：https://python.langchain.com/docs/get_started/introduction\n", "- API 文档：https://api.python.langchain.com/en/latest/langchain_api_reference.html\n", "- 三方组件集成：https://python.langchain.com/docs/integrations/platforms/\n", "- 官方应用案例：https://python.langchain.com/docs/use_cases\n", "- 调试部署等指导：https://python.langchain.com/docs/guides/debugging"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<div class=\"alert alert-success\">\n", "<b>划重点：</b> 创建一个新的 conda 环境，<b>langchain-learn</b>，再开始下面的学习！\n", "</div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["```powershell\n", "conda create -n langchain-learn python=3.10\n", "\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 一、模型 I/O 封装\n", "\n", "把不同的模型，统一封装成一个接口，方便更换模型而不用重构代码。\n", "\n", "### 1.1 模型 API：LLM vs. ChatModel"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# !pip install --upgrade langchain\n", "# !pip install --upgrade langchain-openai\n", "# !pip install --upgrade langchain-community"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 1.1.1 OpenAI 模型封装"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "\n", "# 保证操作系统的环境变量里面配置好了OPENAI_API_KEY, OPENAI_BASE_URL\n", "llm = ChatOpenAI(model=\"gpt-4o-mini\")  # 默认是gpt-3.5-turbo\n", "response = llm.invoke(\"你是谁\")\n", "print(response.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 1.1.2 多轮对话 Session 封装"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain.schema import (\n", "    AIMessage,  # 等价于OpenAI接口中的assistant role\n", "    HumanMessage,  # 等价于OpenAI接口中的user role\n", "    SystemMessage  # 等价于OpenAI接口中的system role\n", ")\n", "\n", "messages = [\n", "    SystemMessage(content=\"你是聚客AI研究院的课程助理。\"),\n", "    HumanMessage(content=\"我是学员，我叫大拿。\"),\n", "    AIMessage(content=\"欢迎！\"),\n", "    HumanMessage(content=\"我是谁\")\n", "]\n", "\n", "ret = llm.invoke(messages)\n", "\n", "print(ret.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<div class=\"alert alert-success\">\n", "<b>划重点：</b>通过模型封装，实现不同模型的统一接口调用\n", "</div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.2 模型的输入与输出\n", "\n", "<img src=\"./assets/model_io.jpg\" style=\"margin-left: 0px\" width=800px>\n", "\n", "#### 1.2.1 Prompt 模板封装\n", "\n", "1. PromptTemplate 可以在模板中自定义变量"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain.prompts import PromptTemplate\n", "\n", "template = PromptTemplate.from_template(\"给我讲个关于{subject}的笑话\")\n", "print(\"===Template===\")\n", "print(template)\n", "print(\"===Prompt===\")\n", "print(template.format(subject='小明'))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "\n", "# 定义 LLM\n", "llm = ChatOpenAI(model=\"gpt-4o-mini\")\n", "# 通过 Prompt 调用 LLM\n", "ret = llm.invoke(template.format(subject='小明'))\n", "# 打印输出\n", "print(ret.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["2. ChatPromptTemplate 用模板表示的对话上下文"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain.prompts import (\n", "    ChatPromptTemplate,\n", "    HumanMessagePromptTemplate,\n", "    SystemMessagePromptTemplate,\n", ")\n", "from langchain_openai import ChatOpenAI\n", "\n", "template = ChatPromptTemplate.from_messages(\n", "    [\n", "        SystemMessagePromptTemplate.from_template(\"你是{product}的客服助手。你的名字叫{name}\"),\n", "        HumanMessagePromptTemplate.from_template(\"{query}\"),\n", "    ]\n", ")\n", "\n", "llm = ChatOpenAI(model=\"gpt-4o-mini\")\n", "prompt = template.format_messages(\n", "    product=\"聚客AI研究院\",\n", "    name=\"大吉\",\n", "    query=\"你是谁\"\n", ")\n", "\n", "print(prompt)\n", "\n", "ret = llm.invoke(prompt)\n", "\n", "print(ret.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["3. MessagesPlaceholder 把多轮对话变成模板"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["from langchain.prompts import (\n", "    ChatPromptTemplate,\n", "    HumanMessagePromptTemplate,\n", "    MessagesPlaceholder,\n", ")\n", "\n", "human_prompt = \"Translate your answer to {language}.\"\n", "human_message_template = HumanMessagePromptTemplate.from_template(human_prompt)\n", "\n", "chat_prompt = ChatPromptTemplate.from_messages(\n", "    # variable_name 是 message placeholder 在模板中的变量名\n", "    # 用于在赋值时使用\n", "    [MessagesPlaceholder(\"history\"), human_message_template]\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_core.messages import AIMessage, HumanMessage\n", "\n", "human_message = HumanMessage(content=\"Who is <PERSON><PERSON>?\")\n", "ai_message = AIMessage(\n", "    content=\"<PERSON><PERSON> is a billionaire entrepreneur, inventor, and industrial designer\"\n", ")\n", "\n", "messages = chat_prompt.format_prompt(\n", "    # 对 \"history\" 和 \"language\" 赋值\n", "    history=[human_message, ai_message], language=\"中文\"\n", ")\n", "\n", "print(messages.to_messages())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result = llm.invoke(messages)\n", "print(result.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<div class=\"alert alert-success\">\n", "<b>划重点：</b>把Prompt模板看作带有参数的函数\n", "</div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 1.2.2 从文件加载 Prompt 模板"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain.prompts import PromptTemplate\n", "\n", "template = PromptTemplate.from_file(\"example_prompt_template.txt\")\n", "print(\"===Template===\")\n", "print(template)\n", "print(\"===Prompt===\")\n", "print(template.format(topic='黑色幽默'))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.3 结构化输出\n", "\n", "#### 1.3.1 直接输出 Pydantic 对象"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from pydantic import BaseModel, Field\n", "\n", "# 定义你的输出对象\n", "class Date(BaseModel):\n", "    year: int = Field(description=\"Year\")\n", "    month: int = Field(description=\"Month\")\n", "    day: int = Field(description=\"Day\")\n", "    era: str = Field(description=\"BC or AD\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain.prompts import PromptTemplate, ChatPromptTemplate, HumanMessagePromptTemplate\n", "from langchain_openai import ChatOpenAI\n", "\n", "from langchain_core.output_parsers import PydanticOutputParser\n", "\n", "\n", "model_name = 'gpt-4o-mini'\n", "temperature = 0  # llm的创新能力\n", "llm = ChatOpenAI(model_name=model_name, temperature=temperature)\n", "\n", "# 定义结构化输出的模型\n", "structured_llm = llm.with_structured_output(Date)\n", "\n", "template = \"\"\"提取用户输入中的日期。\n", "用户输入:\n", "{query}\"\"\"\n", "\n", "prompt = PromptTemplate(\n", "    template=template,\n", ")\n", "\n", "query = \"2024年十二月23日天气晴...\"\n", "input_prompt = prompt.format_prompt(query=query)\n", "\n", "structured_llm.invoke(input_prompt)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 1.3.2 输出指定格式的 JSON"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["json_schema = {\n", "    \"title\": \"Date\",\n", "    \"description\": \"Formated date expression\",\n", "    \"type\": \"object\",\n", "    \"properties\": {\n", "        \"year\": {\n", "            \"type\": \"integer\",\n", "            \"description\": \"year, YYYY\",\n", "        },\n", "        \"month\": {\n", "            \"type\": \"integer\",\n", "            \"description\": \"month, MM\",\n", "        },\n", "        \"day\": {\n", "            \"type\": \"integer\",\n", "            \"description\": \"day, DD\",\n", "        },\n", "        \"era\": {\n", "            \"type\": \"string\",\n", "            \"description\": \"BC or AD\",\n", "        },\n", "    },\n", "}\n", "structured_llm = llm.with_structured_output(json_schema)\n", "\n", "structured_llm.invoke(input_prompt)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 1.3.3 使用 OutputParser\n", "\n", "[`OutputParser`](https://python.langchain.com/v0.2/docs/concepts/#output-parsers) 可以按指定格式解析模型的输出"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_core.output_parsers import JsonOutputParser\n", "\n", "parser = JsonOutputParser(pydantic_object=Date)\n", "\n", "prompt = PromptTemplate(\n", "    template=\"提取用户输入中的日期。\\n用户输入:{query}\\n{format_instructions}\",\n", "    input_variables=[\"query\"],\n", "    partial_variables={\"format_instructions\": parser.get_format_instructions()},\n", ")\n", "\n", "input_prompt = prompt.format_prompt(query=query)\n", "output = llm.invoke(input_prompt)\n", "print(\"原始输出:\\n\"+output.content)\n", "\n", "print(\"\\n解析后:\")\n", "parser.invoke(output)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["也可以用 `PydanticOutputParser`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_core.output_parsers import PydanticOutputParser\n", "\n", "parser = PydanticOutputParser(pydantic_object=Date)\n", "\n", "input_prompt = prompt.format_prompt(query=query)\n", "output = llm.invoke(input_prompt)\n", "print(\"原始输出:\\n\"+output.content)\n", "\n", "print(\"\\n解析后:\")\n", "parser.invoke(output)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["`OutputFixingParser` 利用大模型做格式自动纠错"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain.output_parsers import OutputFixingParser\n", "\n", "new_parser = OutputFixingParser.from_llm(parser=parser, llm=ChatOpenAI(model=\"gpt-4o\"))\n", "\n", "bad_output = output.content.replace(\"4\",\"四\")\n", "print(\"PydanticOutputParser:\")\n", "try:\n", "    parser.invoke(bad_output)\n", "except Exception as e:\n", "    print(e)\n", "\n", "print(\"OutputFixingParser:\")\n", "new_parser.invoke(bad_output)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.4 Function Calling"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["from langchain_core.tools import tool\n", "\n", "@tool\n", "def add(a: int, b: int) -> int:\n", "    \"\"\"Add two integers.\n", "\n", "    Args:\n", "        a: First integer\n", "        b: Second integer\n", "    \"\"\"\n", "    return a + b\n", "\n", "@tool\n", "def multiply(a: int, b: int) -> int:\n", "    \"\"\"Multiply two integers.\n", "\n", "    Args:\n", "        a: First integer\n", "        b: Second integer\n", "    \"\"\"\n", "    return a * b"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "llm_with_tools = llm.bind_tools([add, multiply])\n", "\n", "query = \"3的4倍是多少?\"\n", "messages = [HumanMessage(query)]\n", "\n", "output = llm_with_tools.invoke(messages)\n", "\n", "print(json.dumps(output.tool_calls, indent=4))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["回传 Funtion Call 的结果"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["messages.append(output)\n", "\n", "available_tools = {\"add\": add, \"multiply\": multiply}\n", "\n", "for tool_call in output.tool_calls:\n", "    selected_tool = available_tools[tool_call[\"name\"].lower()]\n", "    tool_msg = selected_tool.invoke(tool_call)\n", "    messages.append(tool_msg)\n", "\n", "new_output = llm_with_tools.invoke(messages)\n", "for message in messages:\n", "    print(json.dumps(message.dict(), indent=4, ensure_ascii=False))\n", "print(new_output.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.5、小结\n", "\n", "1. <PERSON><PERSON><PERSON><PERSON> 统一封装了各种模型的调用接口，包括补全型和对话型两种\n", "2. <PERSON><PERSON><PERSON><PERSON> 提供了 PromptTemplate 类，可以自定义带变量的模板\n", "3. <PERSON><PERSON><PERSON><PERSON> 提供了一些列输出解析器，用于将大模型的输出解析成结构化对象\n", "4. <PERSON><PERSON><PERSON><PERSON> 提供了 Function Calling 的封装\n", "5. 上述模型属于 LangChain 中较为实用的部分"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 二、数据连接封装\n", "\n", "<img src=\"./assets/data_connection.jpg\" style=\"margin-left: 0px\" width=800px>\n", "\n", "### 2.1 文档加载器：Document Loaders"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#!pip install pymupdf"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_community.document_loaders import PyMuPDFLoader\n", "\n", "loader = PyMuPDFLoader(\"llama2.pdf\")\n", "pages = loader.load_and_split()\n", "\n", "print(pages[0].page_content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.2 文档处理器\n", "\n", "#### 2.2.1 TextSplitter"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#!pip install --upgrade langchain-text-splitters"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_text_splitters import RecursiveCharacterTextSplitter\n", "\n", "# 简单的文本内容切割\n", "text_splitter = RecursiveCharacterTextSplitter(\n", "    chunk_size=200,\n", "    chunk_overlap=100, \n", "    length_function=len,\n", "    add_start_index=True,\n", ")\n", "\n", "paragraphs = text_splitter.create_documents([pages[0].page_content])\n", "for para in paragraphs:\n", "    print(para.page_content)\n", "    print('-------')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<div class=\"alert alert-info\">\n", "类似 LlamaIndex，LangChain 也提供了丰富的 <code><a href=\"https://python.langchain.com/v0.2/docs/how_to/#document-loaders\">Document Loaders</a></code> 和 <code><a href=\"https://python.langchain.com/v0.2/docs/how_to/#text-splitters\">Text Splitters</a></code>。\n", "</div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.3、向量数据库与向量检索"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# conda install -c pytorch faiss-cpu"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_openai import OpenAIEmbeddings\n", "from langchain_text_splitters import RecursiveCharacterTextSplitter\n", "from langchain_community.vectorstores import FAISS\n", "from langchain_openai import ChatOpenAI\n", "from langchain_community.document_loaders import PyMuPDFLoader\n", "\n", "# 加载文档\n", "loader = PyMuPDFLoader(\"llama2.pdf\")\n", "pages = loader.load_and_split()\n", "\n", "# 文档切分\n", "text_splitter = RecursiveCharacterTextSplitter(\n", "    chunk_size=300,\n", "    chunk_overlap=100,\n", "    length_function=len,\n", "    add_start_index=True,\n", ")\n", "\n", "texts = text_splitter.create_documents(\n", "    [page.page_content for page in pages[:4]]\n", ")\n", "\n", "# 灌库\n", "embeddings = OpenAIEmbeddings(model=\"text-embedding-ada-002\")\n", "db = FAISS.from_documents(texts, embeddings)\n", "\n", "# 检索 top-3 结果\n", "retriever = db.as_retriever(search_kwargs={\"k\": 3})\n", "\n", "docs = retriever.invoke(\"llama2有多少参数\")\n", "\n", "for doc in docs:\n", "    print(doc.page_content)\n", "    print(\"----\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["更多的三方检索组件链接，参考：https://python.langchain.com/v0.3/docs/integrations/vectorstores/"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.4、小结\n", "\n", "1. 文档处理部分，建议在实际应用中详细测试后使用\n", "2. 与向量数据库的链接部分本质是接口封装，向量数据库需要自己选型"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 三、对话历史管理\n", "\n", "### 3.1、历史记录的剪裁"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_core.messages import (\n", "    AIMessage,\n", "    HumanMessage,\n", "    SystemMessage,\n", "    trim_messages,\n", ")\n", "from langchain_openai import ChatOpenAI\n", "\n", "messages = [\n", "    SystemMessage(\"you're a good assistant, you always respond with a joke.\"),\n", "    HumanMessage(\"i wonder why it's called langchain\"),\n", "    AIMessage(\n", "        'Well, I guess they thought \"WordRope\" and \"SentenceString\" just didn\\'t have the same ring to it!'\n", "    ),\n", "    HumanMessage(\"and who is harrison chasing anyways\"),\n", "    AIMessage(\n", "        \"Hmmm let me think.\\n\\nWhy, he's probably chasing after the last cup of coffee in the office!\"\n", "    ),\n", "    HumanMessage(\"what do you call a speechless parrot\"),\n", "]\n", "\n", "trim_messages(\n", "    messages,\n", "    max_tokens=45,\n", "    strategy=\"last\",\n", "    token_counter=ChatOpenAI(model=\"gpt-4o-mini\"),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 保留 system prompt\n", "trim_messages(\n", "    messages,\n", "    max_tokens=45,\n", "    strategy=\"last\",\n", "    token_counter=ChatOpenAI(model=\"gpt-4o-mini\"),\n", "    include_system=True,\n", "    allow_partial=True,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.2、过滤带标识的历史记录"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_core.messages import (\n", "    AIMessage,\n", "    HumanMessage,\n", "    SystemMessage,\n", "    filter_messages,\n", ")\n", "\n", "messages = [\n", "    SystemMessage(\"you are a good assistant\", id=\"1\"),\n", "    HumanMessage(\"example input\", id=\"2\", name=\"example_user\"),\n", "    AIMessage(\"example output\", id=\"3\", name=\"example_assistant\"),\n", "    HumanMessage(\"real input\", id=\"4\", name=\"bob\"),\n", "    AIMessage(\"real output\", id=\"5\", name=\"alice\"),\n", "]\n", "\n", "filter_messages(messages, include_types=\"human\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["filter_messages(messages, exclude_names=[\"example_user\", \"example_assistant\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["filter_messages(messages, include_types=[HumanMessage, AIMessage], exclude_ids=[\"3\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 四、Chain 和 LangChain Expression Language (LCEL)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["LangChain Expression Language（LCEL）是一种声明式语言，可轻松组合不同的调用顺序构成 Chain。LCEL 自创立之初就被设计为能够支持将原型投入生产环境，**无需代码更改**，从最简单的“提示+LLM”链到最复杂的链（已有用户成功在生产环境中运行包含数百个步骤的 LCEL Chain）。\n", "\n", "LCEL 的一些亮点包括：\n", "\n", "1. **流支持**：使用 LCEL 构建 Chain 时，你可以获得最佳的首个令牌时间（即从输出开始到首批输出生成的时间）。对于某些 Chain，这意味着可以直接从 LLM 流式传输令牌到流输出解析器，从而以与 LLM 提供商输出原始令牌相同的速率获得解析后的、增量的输出。\n", "\n", "2. **异步支持**：任何使用 LCEL 构建的链条都可以通过同步 API（例如，在 Jupyter 笔记本中进行原型设计时）和异步 API（例如，在 LangServe 服务器中）调用。这使得相同的代码可用于原型设计和生产环境，具有出色的性能，并能够在同一服务器中处理多个并发请求。\n", "\n", "3. **优化的并行执行**：当你的 LCEL 链条有可以并行执行的步骤时（例如，从多个检索器中获取文档），我们会自动执行，无论是在同步还是异步接口中，以实现最小的延迟。\n", "\n", "4. **重试和回退**：为 LCEL 链的任何部分配置重试和回退。这是使链在规模上更可靠的绝佳方式。目前我们正在添加重试/回退的流媒体支持，因此你可以在不增加任何延迟成本的情况下获得增加的可靠性。\n", "\n", "5. **访问中间结果**：对于更复杂的链条，访问在最终输出产生之前的中间步骤的结果通常非常有用。这可以用于让最终用户知道正在发生一些事情，甚至仅用于调试链条。你可以流式传输中间结果，并且在每个 LangServe 服务器上都可用。\n", "\n", "6. **输入和输出模式**：输入和输出模式为每个 LCEL 链提供了从链的结构推断出的 Pydantic 和 JSONSchema 模式。这可以用于输入和输出的验证，是 LangServe 的一个组成部分。\n", "\n", "7. **无缝 LangSmith 跟踪集成**：随着链条变得越来越复杂，理解每一步发生了什么变得越来越重要。通过 LCEL，所有步骤都自动记录到 LangSmith，以实现最大的可观察性和可调试性。\n", "\n", "8. **无缝 LangServe 部署集成**：任何使用 LCEL 创建的链都可以轻松地使用 LangServe 进行部署。\n", "\n", "原文：https://python.langchain.com/docs/expression_language/"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.1 Pipeline 式调用 PromptTemplate, LLM 和 OutputParser"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "from langchain.prompts import ChatPromptTemplate\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.runnables import RunnablePassthrough\n", "from pydantic import BaseModel, Field, validator\n", "from typing import List, Dict, Optional\n", "from enum import Enum\n", "import json"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 输出结构\n", "class SortEnum(str, Enum):\n", "    data = 'data'\n", "    price = 'price'\n", "\n", "\n", "class OrderingEnum(str, Enum):\n", "    ascend = 'ascend'\n", "    descend = 'descend'\n", "\n", "\n", "class Semantics(BaseModel):\n", "    name: Optional[str] = Field(description=\"流量包名称\", default=None)\n", "    price_lower: Optional[int] = Field(description=\"价格下限\", default=None)\n", "    price_upper: Optional[int] = Field(description=\"价格上限\", default=None)\n", "    data_lower: Optional[int] = Field(description=\"流量下限\", default=None)\n", "    data_upper: Optional[int] = Field(description=\"流量上限\", default=None)\n", "    sort_by: Optional[SortEnum] = Field(description=\"按价格或流量排序\", default=None)\n", "    ordering: Optional[OrderingEnum] = Field(description=\"升序或降序排列\", default=None)\n", "\n", "\n", "# Prompt 模板\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", \"你是一个语义解析器。你的任务是将用户的输入解析成JSON表示。不要回答用户的问题。\"),\n", "        (\"human\", \"{text}\"),\n", "    ]\n", ")\n", "\n", "# 模型\n", "llm = ChatOpenAI(model=\"gpt-4o\", temperature=0)\n", "\n", "structured_llm = llm.with_structured_output(Semantics)\n", "\n", "# LCEL 表达式\n", "runnable = (\n", "    {\"text\": RunnablePassthrough()} | prompt | structured_llm\n", ")\n", "\n", "# 直接运行\n", "ret = runnable.invoke(\"不超过100元的流量大的套餐有哪些\")\n", "print(\n", "    json.dumps(\n", "        ret.dict(),\n", "        indent = 4,\n", "        ensure_ascii=False\n", "    )\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 流式输出"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prompt = PromptTemplate.from_template(\"讲个关于{topic}的笑话\")\n", "\n", "runnable = (\n", "    {\"topic\": RunnablePassthrough()} | prompt | llm | StrOutputParser()\n", ")\n", "\n", "# 流式输出\n", "for s in runnable.stream(\"小明\"):\n", "    print(s, end=\"\", flush=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<div class=\"alert alert-warning\">\n", "<b>注意:</b> 在当前的文档中 LCEL 产生的对象，被叫做 runnable 或 chain，经常两种叫法混用。本质就是一个自定义调用流程。\n", "</div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<div class=\"alert alert-success\">\n", "<b>使用 LCEL 的价值，也就是 LangChain 的核心价值。</b> <br />\n", "官方从不同角度给出了举例说明：<a href=\"https://python.langchain.com/v0.1/docs/expression_language/why/\">https://python.langchain.com/v0.1/docs/expression_language/why/</a>\n", "</div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.2 用 LCEL 实现 RAG"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["from langchain_openai import OpenAIEmbeddings\n", "from langchain_text_splitters import RecursiveCharacterTextSplitter\n", "from langchain_community.vectorstores import FAISS\n", "from langchain_openai import ChatOpenAI\n", "from langchain.chains import RetrievalQA\n", "from langchain_community.document_loaders import PyMuPDFLoader\n", "\n", "# 加载文档\n", "loader = PyMuPDFLoader(\"llama2.pdf\")\n", "pages = loader.load_and_split()\n", "\n", "# 文档切分\n", "text_splitter = RecursiveCharacterTextSplitter(\n", "    chunk_size=300,\n", "    chunk_overlap=100,\n", "    length_function=len,\n", "    add_start_index=True,\n", ")\n", "\n", "texts = text_splitter.create_documents(\n", "    [page.page_content for page in pages[:4]]\n", ")\n", "\n", "# 灌库\n", "embeddings = OpenAIEmbeddings(model=\"text-embedding-ada-002\")\n", "db = FAISS.from_documents(texts, embeddings)\n", "\n", "# 检索 top-2 结果\n", "retriever = db.as_retriever(search_kwargs={\"k\": 2})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain.schema.output_parser import StrOutputParser\n", "from langchain.schema.runnable import RunnablePassthrough\n", "\n", "# Prompt模板\n", "template = \"\"\"Answer the question based only on the following context:\n", "{context}\n", "\n", "Question: {question}\n", "\"\"\"\n", "prompt = ChatPromptTemplate.from_template(template)\n", "\n", "# Chain\n", "rag_chain = (\n", "    {\"question\": RunnablePassthrough(), \"context\": retriever}\n", "    | prompt\n", "    | llm\n", "    | StrOutputParser()\n", ")\n", "\n", "rag_chain.invoke(\"Llama 2有多少参数\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.3 用 LCEL 实现工厂模式（选）"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_core.runnables.utils import ConfigurableField\n", "from langchain_openai import ChatOpenAI\n", "from langchain_community.chat_models import QianfanChatEndpoint\n", "from langchain.prompts import (\n", "    ChatPromptTemplate,\n", "    HumanMessagePromptTemplate,\n", ")\n", "from langchain.schema import HumanMessage\n", "import os\n", "\n", "# 模型1\n", "ernie_model = QianfanChatEndpoint(\n", "    qianfan_ak=os.getenv('ERNIE_CLIENT_ID'),\n", "    qianfan_sk=os.getenv('ERNIE_CLIENT_SECRET')\n", ")\n", "\n", "# 模型2\n", "gpt_model = ChatOpenAI(model=\"gpt-4o-mini\", temperature=0)\n", "\n", "\n", "# 通过 configurable_alternatives 按指定字段选择模型\n", "model = gpt_model.configurable_alternatives(\n", "    ConfigurableField(id=\"llm\"), \n", "    default_key=\"gpt\", \n", "    ernie=ernie_model,\n", "    # claude=claude_model,\n", ")\n", "\n", "# Prompt 模板\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        HumanMessagePromptTemplate.from_template(\"{query}\"),\n", "    ]\n", ")\n", "\n", "# LCEL\n", "chain = (\n", "    {\"query\": RunnablePassthrough()} \n", "    | prompt\n", "    | model \n", "    | StrOutputParser()\n", ")\n", "\n", "# 运行时指定模型 \"gpt\" or \"ernie\"\n", "ret = chain.with_config(configurable={\"llm\": \"gpt\"}).invoke(\"请自我介绍\")\n", "\n", "print(ret)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["扩展阅读：什么是[**工厂模式**](https://www.runoob.com/design-pattern/factory-pattern.html)；[**设计模式**](https://www.runoob.com/design-pattern/design-pattern-intro.html)概览。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<div class=\"alert alert-warning\">\n", "<b>思考：</b>从模块间解依赖角度，LCEL的意义是什么？\n", "</div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.4 存储与管理对话历史"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["from langchain_community.chat_message_histories import SQLChatMessageHistory\n", "\n", "def get_session_history(session_id):\n", "    # 通过 session_id 区分对话历史，并存储在 sqlite 数据库中， 高并发的时候用redis\n", "    return SQLChatMessageHistory(session_id, \"sqlite:///memory.db\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_core.messages import HumanMessage\n", "from langchain_core.runnables.history import RunnableWithMessageHistory\n", "from langchain_openai import ChatOpenAI\n", "from langchain.schema.output_parser import StrOutputParser\n", "\n", "model = ChatOpenAI(model=\"gpt-4o-mini\", temperature=0)\n", "\n", "runnable = model | StrOutputParser()\n", "\n", "runnable_with_history = RunnableWithMessageHistory(\n", "    runnable, # 指定 runnable\n", "    get_session_history, # 指定自定义的历史管理方法\n", ")\n", "\n", "runnable_with_history.invoke(\n", "    [HumanMessage(content=\"你好，我叫大拿\")],\n", "    config={\"configurable\": {\"session_id\": \"dana\"}},\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["runnable_with_history.invoke(\n", "    [HumanMessage(content=\"你知道我叫什么名字\")],\n", "    config={\"configurable\": {\"session_id\": \"dana\"}},\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["runnable_with_history.invoke(\n", "    [HumanMessage(content=\"你知道我叫什么名字\")],\n", "    config={\"configurable\": {\"session_id\": \"test\"}},\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 通过 LCEL，还可以实现\n", "\n", "1. 配置运行时变量：https://python.langchain.com/v0.3/docs/how_to/configure/\n", "2. 故障回退：https://python.langchain.com/v0.3/docs/how_to/fallbacks\n", "3. 并行调用：https://python.langchain.com/v0.3/docs/how_to/parallel/\n", "4. 逻辑分支：https://python.langchain.com/v0.3/docs/how_to/routing/\n", "5. 动态创建 Chain: https://python.langchain.com/v0.3/docs/how_to/dynamic_chain/\n", "\n", "更多例子：https://python.langchain.com/v0.3/docs/how_to/lcel_cheatsheet/"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 五、LangServe\n", "\n", "LangServe 用于将 Chain 或者 Runnable 部署成一个 REST API 服务。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 安装 LangServe\n", "# !pip install --upgrade \"langserve[all]\"\n", "\n", "# 也可以只安装一端\n", "# !pip install \"langserve[client]\"\n", "# !pip install \"langserve[server]\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.1、Server 端"]}, {"cell_type": "markdown", "metadata": {}, "source": ["```python\n", "#!/usr/bin/env python\n", "from fastapi import FastAPI\n", "from langchain.prompts import ChatPromptTemplate\n", "from langchain_openai import ChatOpenAI\n", "from langserve import add_routes\n", "import uvicorn\n", "\n", "app = FastAPI(\n", "  title=\"LangChain Server\",\n", "  version=\"1.0\",\n", "  description=\"A simple api server using Langchain's Runnable interfaces\",\n", ")\n", "\n", "model = ChatOpenAI(model=\"gpt-4o-mini\")\n", "prompt = ChatPromptTemplate.from_template(\"讲一个关于{topic}的笑话\")\n", "add_routes(\n", "    app,\n", "    prompt | model,\n", "    path=\"/joke\",\n", ")\n", "\n", "if __name__ == \"__main__\":\n", "    uvicorn.run(app, host=\"localhost\", port=9999)\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.2、<PERSON><PERSON> 端"]}, {"cell_type": "markdown", "metadata": {}, "source": ["```python\n", "import requests\n", "\n", "response = requests.post(\n", "    \"http://localhost:9999/joke/invoke\",\n", "    json={'input': {'topic': '小明'}}\n", ")\n", "print(response.json())\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## LangChain 与 LlamaIndex 的错位竞争\n", "\n", "- LangChain 侧重与 LLM 本身交互的封装\n", "  - Prompt、LLM、Message、OutputParser 等工具丰富\n", "  - 在数据处理和 RAG 方面提供的工具相对粗糙\n", "  - 主打 LCEL 流程封装\n", "  - 配套 Agent、LangGraph 等智能体与工作流工具\n", "  - 另有 LangServe 部署工具和 LangSmith 监控调试工具\n", "- LlamaIndex 侧重与数据交互的封装\n", "  - 数据加载、切割、索引、检索、排序等相关工具丰富\n", "  - Prompt、LLM 等底层封装相对单薄\n", "  - 配套实现 RAG 相关工具\n", "  - 有 Agent 相关工具，不突出\n", "- LlamaIndex 为 LangChain 提供了集成\n", "  - 在 LlamaIndex 中调用 LangChain 封装的 LLM 接口：https://docs.llamaindex.ai/en/stable/api_reference/llms/langchain/\n", "  - 将 LlamaIndex 的 Query Engine 作为 LangChain Agent 的工具：https://docs.llamaindex.ai/en/v0.10.17/community/integrations/using_with_langchain.html\n", "  - <PERSON><PERSON><PERSON><PERSON> 也 _曾经_ 集成过 LlamaIndex，目前相关接口仍在：https://api.python.langchain.com/en/latest/retrievers/langchain_community.retrievers.llama_index.LlamaIndexRetriever.html"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 总结\n", "\n", "1. Lang<PERSON>hain 随着版本迭代可用性有明显提升\n", "2. 使用 LangChain 要注意维护自己的 Prompt，尽量 Prompt 与代码逻辑解依赖\n", "3. 它的内置基础工具，建议充分测试效果后再决定是否使用"]}], "metadata": {"kernelspec": {"display_name": "langchain-learn", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 2}