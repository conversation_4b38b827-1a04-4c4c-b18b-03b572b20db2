# 导入langchain_core.tools模块以使用工具装饰器
from langchain_core.tools import tool
# 导入json模块以处理JSON数据
import json
# 导入langchain_openai模块以使用ChatOpenAI类
from langchain_openai import ChatOpenAI
# 导入langchain_core.messages模块以使用HumanMessage类
from langchain_core.messages import HumanMessage

# 定义一个工具函数，用于将两个整数相加
@tool
# 定义add函数，接受两个整数参数并返回它们的和
def add(a:int,b:int) -> int:
    """ Add two integers"""
    return a + b

# 定义一个工具函数，用于将两个整数相乘
@tool
# 定义multiply函数，接受两个整数参数并返回它们的乘积
def multiply(a:int,b:int) -> int:
    """Mutiply two integers"""
    return a * b


# 初始化一个ChatOpenAI对象，指定模型、温度、API的基本URL和API密钥
llm = ChatOpenAI(
    model="Qwen/Qwen2.5-7B-Instruct", 
    temperature=0,
    base_url="https://api.siliconflow.cn",
    api_key="sk-qnhrgxkfjdhaiwbdblefelnfratgyjqnustbusxkrkehfllu"
)

# 将定义的工具函数绑定到语言模型中，以便在对话中调用
llm_with_tools = llm.bind_tools([add, multiply])

# 构建一个包含用户查询的消息列表
query = input("请输入您的问题：")
# 创建一个HumanMessage对象，包含用户查询
messages = [HumanMessage(query)]
# 打印消息列表内容
# print(f"messages: {messages}")

# 调用模型以获取初始输出，并解析工具调用
output = llm_with_tools.invoke(messages)
# 打印工具调用的详细信息
# print(json.dumps(output.tool_calls, indent=4))

# 将工具调用的结果添加到消息列表中
messages.append(output)
# 打印更新后的消息列表内容
# print(f"messages: {messages}")
# 定义一个字典，包含可用的工具函数
available_tools = {"add":add, "multiply": multiply}

# 遍历工具调用，执行相应的工具函数并将结果添加到消息列表中
for tool_call in output.tool_calls:
    # 根据工具调用名称选择相应的工具函数
    selected_tool = available_tools[tool_call["name"].lower()]
    # 执行工具函数并获取结果消息
    tool_msg = selected_tool.invoke(tool_call)
    # 将结果消息添加到消息列表中
    messages.append(tool_msg)

# 再次调用模型以获取最终输出，并打印所有消息内容
new_output = llm_with_tools.invoke(messages)
# 打印每个消息的详细内容
# for message in messages:
#     print(json.dumps(message.model_dump(),indent=4,ensure_ascii=False))
# 打印最终输出内容
print("答案：",new_output.content)




"""
# Function Calling 简易解释

函数调用（Function Calling）是大语言模型的一个强大功能，它允许模型识别何时需要调用特定函数并提供正确的参数。我来用简单的语言解释一下这个例子中的函数调用过程：

## 基本概念

函数调用就像是给AI一个工具箱，当用户提出特定问题时，AI能够：
1. 识别需要使用哪个工具（函数）
2. 准备好工具需要的参数
3. 调用工具获得结果
4. 将结果融入到回答中

## 代码示例解析

在这个例子中：

1. **定义工具函数**：首先定义了两个简单的数学工具函数（`add`和`multiply`），并用`@tool`装饰器标记它们

2. **绑定工具到模型**：通过`llm.bind_tools([add, multiply])`将这些工具绑定到语言模型，让模型知道有这些工具可用

3. **用户提问**：用户输入一个问题，比如"计算5乘以3是多少？"

4. **模型分析**：模型分析问题，发现需要使用乘法工具

5. **工具调用**：模型生成一个工具调用请求，指定使用`multiply`函数，参数为`a=5, b=3`

6. **执行工具**：代码执行工具函数并获得结果（15）

7. **结果整合**：将工具执行结果添加到对话中，模型生成最终回答

## 实际应用场景

函数调用使AI能够：
- 查询数据库
- 调用API获取实时信息
- 执行计算
- 控制其他系统

这使得AI不仅能理解问题，还能采取实际行动解决问题，大大扩展了AI助手的能力范围。

简单来说，函数调用就是让AI知道"什么时候该用什么工具，以及如何正确使用这些工具"的能力。
"""