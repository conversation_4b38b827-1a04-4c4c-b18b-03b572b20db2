"""

from langchain_community.document_loaders import PyMuPDFLoader
# 1.加载文档内容
loader = PyMuPDFLoader("llama2.pdf")
pages = loader.load_and_split()
# print(loader)  # <langchain_community.document_loaders.pdf.PyMuPDFLoader object at 0x10617bb90>
# print('--------------')
print(f"第一页的内容: {pages[0].page_content}")
print("==============="*10)

# 2.对第一页的内容进行切割（简单的文本切割，无法处理表格、图片等）
from langchain_text_splitters import RecursiveCharacterTextSplitter  

text_splitter = RecursiveCharacterTextSplitter(
    chunk_size = 200,  # 指定拆分后每个文本块（chunk）最多包含的字符数
    chunk_overlap = 100,  # 相邻两个文本块之间的重叠字符数
    length_function = len,  # 用于测量文本长度的函数
    add_start_index = True,  # 是否在返回的每个文本块里，附带一个表示该块在原文中起始位置（start index）的元数据字段
)

paragraphs = text_splitter.create_documents([pages[0].page_content])
for para in paragraphs:
    print(para.page_content)
    print('------------------')

"""

# 3.向量化（向量数据库和向量检索）
from langchain_openai import OpenAIEmbeddings
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_community.vectorstores import FAISS
from langchain_openai import ChatOpenAI
from langchain_community.document_loaders import PyMuPDFLoader

# 加载文档
loader = PyMuPDFLoader("llama2.pdf")
pages = loader.load_and_split()

# 文档切分
text_splitter = RecursiveCharacterTextSplitter(
    chunk_size = 300,  # 指定拆分后每个文本块（chunk）最多包含的字符数
    chunk_overlap = 100,  # 相邻两个文本块之间的重叠字符数
    length_function = len,  # 用于测量文本长度的函数
    add_start_index = True,  # 是否在返回的每个文本块里，附带一个表示该块在原文中起始位置（start index）的元数据字段
)

texts = text_splitter.create_documents(
    [page.page_content for page in pages[:4]]
)

# 灌库
embeddings = OpenAIEmbeddings(model="BAAI/bge-m3",api_key="sk-qnhrgxkfjdhaiwbdblefelnfratgyjqnustbusxkrkehfllu",base_url="https://api.siliconflow.cn/v1")
db = FAISS.from_documents(texts, embeddings)

# 检索top-3结果
retriever = db.as_retriever(search_kwargs={"k":3})
docs = retriever.invoke("llama2有多少个参数")

# for doc in docs:
#     print(doc.page_content)
#     print("------------")
# -----------------------------------------------------

# 4.向量存储



# 5.检索


# 6.检索的文档灌入到Prompt


# 7.LLM推理：生成最终的答案


# 8.响应



