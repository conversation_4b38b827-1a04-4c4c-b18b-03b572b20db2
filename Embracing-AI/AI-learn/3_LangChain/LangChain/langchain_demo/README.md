# LangChain Function Calling 演示

这个项目提供了使用LangChain技术栈实现Function Calling（函数调用）的最小化示例。Function Calling是一项强大功能，使LLM能够根据用户查询智能调用预定义的函数。

## 项目结构

本项目包含四个示例文件，展示了不同方式的函数调用实现：

1. `function_calling_demo.py`: 基础示例，展示了最简单的函数调用配置
2. `advanced_function_calling.py`: 增强版示例，实现了完整的函数调用和结果处理流程
3. `lcel_function_calling.py`: 使用现代化的LCEL（LangChain Expression Language）方式实现函数调用
4. `pydantic_function_calling.py`: 使用Pydantic模型定义函数参数的示例，代码更简洁、更规范

## 安装依赖

```bash
pip install -r requirements.txt
```

## 环境变量

在运行示例前，请创建一个`.env`文件并设置您的OpenAI API密钥：

```
OPENAI_API_KEY=your_api_key_here
```

## API设置

本示例默认使用轨迹流动（SiliconFlow）API：

```python
base_url="https://api.siliconflow.cn"
```

此设置已在所有示例文件的`ChatOpenAI`初始化部分配置。如需修改，请在各示例文件中查找并更新相应部分。

## 运行示例

基础示例：
```bash
python function_calling_demo.py
```

增强版示例：
```bash
python advanced_function_calling.py
```

LCEL示例：
```bash
python lcel_function_calling.py
```

Pydantic版示例：
```bash
python pydantic_function_calling.py
```

## 功能说明

所有示例都实现了相同的功能：

1. 加法计算 (`add` 函数)
2. 乘法计算 (`multiply` 函数)

当用户询问相关问题时（如"计算 5 加 3 等于多少"或"7 乘以 8 是多少"），LLM会自动识别用户意图并调用相应函数以获取计算结果。

## 示例用法

1. 运行任一示例程序
2. 输入问题，例如：
   - "计算 5 加 3 等于多少"
   - "7 乘以 8 是多少"
   - "23.5 加上 17.8 的结果是多少"
3. 观察程序如何识别用户意图并调用适当的函数

## Pydantic与传统方法的比较

Pydantic版本（`pydantic_function_calling.py`）相比传统方法有以下优势：

1. **类型安全**：使用Pydantic模型可以自动验证输入和输出参数的类型
2. **代码简洁**：通过`model_json_schema()`自动生成函数参数的JSON Schema描述
3. **更易维护**：参数定义集中在Pydantic模型中，便于管理和扩展
4. **更好的IDE支持**：类型提示和自动补全更完善
5. **符合最佳实践**：这是LangChain推荐的函数定义方式 