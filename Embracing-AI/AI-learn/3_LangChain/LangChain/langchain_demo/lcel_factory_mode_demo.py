from langchain_core.runnables.utils import Configurable<PERSON>ield
from langchain_openai import ChatOpenAI
from langchain_community.chat_models import QianfanChatEndpoint
from langchain_core.runnables import RunnablePassthrough
from langchain_core.output_parsers import StrOutputParser
from langchain.prompts import (
    ChatPromptTemplate,
    HumanMessagePromptTemplate,
)
from langchain.schema import HumanMessage
import os

# 模型1
ernie_model = QianfanChatEndpoint(
    qianfan_ak=os.getenv('QIANFAN_ACCESS_KEY'),
    qianfan_sk=os.getenv('QIANFAN_SECRET_KEY')
)

# 模型2
gpt_model = ChatOpenAI(model="ERNIE-4.0-8K", temperature=0)

# 通过 configurable_alternatives 按指定字段选择模型
model = gpt_model.configurable_alternatives(
    ConfigurableField(id="llm"),
    default_key="DeepSeek-V3",
    ernie=ernie_model,
    # claude=claude_model,
)

# Prompt模板
prompt = ChatPromptTemplate.from_messages(
    [
        HumanMessagePromptTemplate.from_template("{query}"),
    ]
)

# LCEL
chain = (
    {"query": RunnablePassthrough()}
    |prompt
    |model
    |StrOutputParser()
)

# 运行时指定模型
ret = chain.with_config(configurable={"llm":"DeepSeek-V3"}).invoke("请自我介绍")