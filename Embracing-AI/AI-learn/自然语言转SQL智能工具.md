# 自然语言转SQL智能工具

## Core Features

- 自然语言转SQL查询

- 多数据库类型支持

- 数据库schema管理

- SQL语法验证和优化

- 查询解释和建议

- 复杂查询场景支持

## Tech Stack

{
  "Web": {
    "arch": "react",
    "component": "shadcn"
  },
  "后端": "FastAPI + Python + Qwen模型",
  "数据库": "MySQL、PostgreSQL、SQLite适配器",
  "AI模型": "Qwen大语言模型"
}

## Design

现代简约Material Design风格，深蓝色主色调配白色背景，采用卡片式布局。主工作台包含数据库配置面板、自然语言输入区域、SQL输出编辑器和查询解释面板。支持历史记录管理和查询收藏功能。

## Plan

Note: 

- [ ] is holding
- [/] is doing
- [X] is done

---

[X] 增强现有FastAPI后端SQL生成接口，添加多数据库支持和schema解析功能

[X] 实现SQL语法验证和优化建议模块

[X] 开发React前端主工作台界面，包含自然语言输入和SQL输出区域

[X] 实现数据库配置管理和schema信息展示功能

[X] 集成查询历史记录和收藏管理功能

[X] 添加SQL查询解释和性能分析面板

[X] 完善错误处理和用户反馈机制

[X] 修复Schema管理文件上传功能

[/] 进行功能测试和性能优化
