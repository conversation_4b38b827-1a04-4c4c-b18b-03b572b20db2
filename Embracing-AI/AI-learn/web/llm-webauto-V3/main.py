#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试代理运行脚本

此脚本用于运行测试代理，执行自然语言描述的测试任务。
"""

import os
import sys
import asyncio
import argparse
import logging
import json
from datetime import datetime
from pathlib import Path

from core.agent.service import TestAgent
from config.config import BROWSER_CONFIG, TEST_CONFIG,LLM_CONFIG, LOCATOR_CONFIG

# 确保日志目录存在 - 先创建目录，再配置日志
logs_dir = os.path.join(os.getcwd(), "logs")
os.makedirs(logs_dir, exist_ok=True)

# 日志配置函数
def setup_logging(log_to_file=True):
    """设置日志配置
    
    Args:
        log_to_file: 是否将日志写入文件
    """
    # 创建根日志器
    root_logger = logging.getLogger()
    root_logger.handlers = []  # 清除现有处理器
    
    # 配置日志格式
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    formatter = logging.Formatter(log_format)
    
    # 添加控制台处理器（添加到根日志器）
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # 配置日志文件
    log_file = None
    if log_to_file:
        try:
            log_file = os.path.join(logs_dir, f"{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
            file_handler = logging.FileHandler(log_file, 'w', 'utf-8')
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
        except Exception as e:
            print(f"创建日志文件处理器时出错: {str(e)}")
    
    # 设置根日志级别
    root_logger.setLevel(logging.INFO)
    
    # 输出配置信息
    logger = logging.getLogger(__name__)
    if log_to_file and log_file:
        logger.info(f"日志已配置，文件保存在: {log_file}")
    else:
        logger.debug("日志仅输出到控制台")

async def run_test(task, config, max_steps, save_screenshots, api_key, api_base, record_video=False, force_complete=False, highlight_elements=True):
    """
    运行测试任务
    
    Args:
        task: 测试任务描述
        config: 配置信息
        max_steps: 最大步骤数
        save_screenshots: 是否保存截图
        api_key: OpenAI API密钥
        api_base: OpenAI API基础URL
        record_video: 是否录制视频
        force_complete: 强制标记测试为完成状态，即使执行中断
        highlight_elements: 是否高亮页面元素
        
    Returns:
        测试结果
    """
    logger = logging.getLogger(__name__)
    logger.info(f"开始执行测试任务: {task}")
    
    try:
        # 处理视频录制配置
        if record_video:
            # 确保视频目录存在
            videos_dir = os.path.join(os.getcwd(), "videos")
            os.makedirs(videos_dir, exist_ok=True)
            
            # 更新浏览器配置
            if "browser" not in config:
                config["browser"] = {}
            config["browser"]["record_video"] = True
            logger.info(f"已启用视频录制功能，视频将保存到: {videos_dir}")
        
        # 处理元素高亮设置
        if not highlight_elements:
            if "dom" not in config:
                config["dom"] = {}
            config["dom"]["highlight_elements"] = False
            logger.info("已禁用页面元素高亮功能")
        
        # 初始化测试代理
        agent = TestAgent(
            task=task,
            config=config,
            max_steps=max_steps,
            save_screenshots=save_screenshots,
            api_key=api_key,
            api_base=api_base
        )
        
        # 运行测试
        result = await agent.run()
        
        # 检查是否返回了完整的结果结构
        if "current_state" not in result:
            logger.warning("测试结果中缺少current_state信息，可能未完全执行完毕")
            result["current_state"] = {
                "evaluation_previous_step": "执行状态未知",
                "memory": "执行记录不完整",
                "next_goal": "无后续步骤信息"
            }
            
        # 检查是否所有步骤都成功执行
        all_steps_passed = True
        failed_steps = []
        
        for i, step_result in enumerate(result.get('results', [])):
            if not step_result.get('success', False):
                all_steps_passed = False
                step_num = i + 1
                step_desc = step_result.get('step', f'步骤 {step_num}')
                error_msg = step_result.get('error', '未知错误')
                failed_steps.append(f"步骤 {step_num}: {step_desc} - {error_msg}")
                logger.error(f"步骤执行失败: {step_desc}")
                logger.error(f"错误信息: {error_msg}")
        
        # 更新整体测试结果
        result['success'] = all_steps_passed
        
        # 添加额外的诊断信息
        result['failed_steps'] = failed_steps
        result['execution_complete'] = True
        
        # 如果强制完成，确保current_state反映完成状态
        if force_complete:
            logger.info("强制标记测试为完成状态")
            result['current_state']['evaluation_previous_step'] = "任务已完成 (强制标记)"
            result['current_state']['next_goal'] = "生成测试报告"
            result['execution_complete'] = True
        
        # 如果启用了视频录制，查找最新的视频文件
        if record_video:
            try:
                # 获取最新录制的视频文件
                video_path = await agent.keyword_driver.get_last_recorded_video()
                if video_path:
                    # 将视频路径添加到结果中
                    result['video_path'] = video_path
                    logger.info(f"视频录制完成，保存到: {video_path}")
                else:
                    logger.warning("未找到录制的视频文件")
            except Exception as ve:
                logger.error(f"获取视频文件时出错: {str(ve)}")
        
        # 输出结果摘要
        if all_steps_passed:
            logger.info(f"测试成功完成！共执行 {result.get('steps')} 个步骤")
            logger.info(f"最终状态: {result['current_state']['evaluation_previous_step']}")
        else:
            logger.error(f"测试执行失败: 有 {len(failed_steps)} 个步骤执行失败")
            logger.error(f"失败步骤: {', '.join(failed_steps)}")
            
        # 保存结果报告
        save_report(result)
        
        return result
    except Exception as e:
        logger.exception(f"测试执行过程中发生错误: {str(e)}")
        error_result = {
            "success": False, 
            "error": str(e),
            "execution_complete": False,
            "current_state": {
                "evaluation_previous_step": "执行过程中出错",
                "memory": f"错误: {str(e)}",
                "next_goal": "重新执行测试"
            }
        }
        
        # 如果强制完成，更新状态
        if force_complete:
            error_result["execution_complete"] = True
            error_result["current_state"]["evaluation_previous_step"] = "任务中断但已标记为完成 (强制标记)"
            error_result["current_state"]["next_goal"] = "生成测试报告"
        
        # 即使出错也尝试保存报告
        save_report(error_result)
        
        return error_result

def save_report(result):
    """
    保存测试报告
    
    Args:
        result: 测试结果
    """
    logger = logging.getLogger(__name__)
    try:
        # 创建报告目录
        report_dir = os.path.join(os.getcwd(), "reports")
        os.makedirs(report_dir, exist_ok=True)
        
        # 生成报告文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_path = os.path.join(report_dir, f"report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        
        # 保存JSON报告
        with open(report_path, "w", encoding="utf-8") as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        logger.info(f"测试报告已保存: {report_path}")
        
        # 生成Markdown报告
        md_path = os.path.join(report_dir, f"report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md")
        with open(md_path, "w", encoding="utf-8") as f:
            f.write(f"# 测试报告\n\n")
            
            # 任务和结果概要
            f.write(f"## 任务描述\n\n{result.get('task', '无任务描述')}\n\n")
            f.write(f"## 执行结果\n\n")
            f.write(f"- **成功**: {'是' if result.get('success', False) else '否'}\n")
            f.write(f"- **步骤数**: {result.get('steps', 0)}\n")
            f.write(f"- **执行时间**: {result.get('timestamp', datetime.now().isoformat())}\n")
            
            # 添加视频链接（如果有）
            if "video_path" in result:
                video_path = result["video_path"]
                f.write(f"- **录制视频**: [{os.path.basename(video_path)}]({video_path})\n")
            
            # 失败步骤
            if not result.get('success', False) and result.get('failed_steps'):
                f.write(f"\n## 失败步骤\n\n")
                for step in result.get('failed_steps', []):
                    f.write(f"- {step}\n")
            
            # 当前状态
            if "current_state" in result:
                f.write(f"\n## 执行状态\n\n")
                f.write(f"- **上一步评估**: {result['current_state'].get('evaluation_previous_step', '无')}\n")
                f.write(f"- **下一个目标**: {result['current_state'].get('next_goal', '无')}\n")
            
            # 详细结果
            f.write(f"\n## 详细步骤\n\n")
            for i, step_result in enumerate(result.get('results', [])):
                step_num = i + 1
                step_desc = step_result.get('step', f'步骤 {step_num}')
                success = step_result.get('success', False)
                f.write(f"### 步骤 {step_num}: {step_desc}\n\n")
                f.write(f"- **成功**: {'是' if success else '否'}\n")
                
                if 'message' in step_result:
                    f.write(f"- **详情**: {step_result['message']}\n")
                    
                if not success and 'error' in step_result:
                    f.write(f"- **错误**: {step_result['error']}\n")
                    
                # 包含截图引用
                if 'screenshot' in step_result:
                    f.write(f"- **截图**: [{step_result['screenshot']}]({step_result['screenshot']})\n")
                    
                f.write("\n")
            
            # 如果有错误信息
            if "error" in result and result["error"]:
                f.write(f"\n## 错误信息\n\n```\n{result['error']}\n```\n")
        
        logger.info(f"Markdown报告已保存: {md_path}")
        
        return {
            "json_path": report_path,
            "md_path": md_path
        }
        
    except Exception as e:
        logger.error(f"保存测试报告时出错: {str(e)}")
        return None

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='LLM驱动的网页自动化测试工具')
    parser.add_argument('-t', '--task', help='测试任务描述', required=True)
    parser.add_argument('--max-steps', type=int, default=50, help='最大步骤数')
    parser.add_argument('--screenshots', action='store_true', help='保存截图')
    parser.add_argument('--headless', action='store_true', help='无头模式运行')
    parser.add_argument('--disable-realtime', action='store_true', help='禁用实时元素定位')
    parser.add_argument('--force-complete', action='store_true', help='强制完成')
    parser.add_argument('--log-file', action='store_true', help='将日志写入文件（已弃用，现在始终写入日志文件）')
    parser.add_argument('--api-key', help='API密钥')
    parser.add_argument('--api-base', help='API基础URL')
    parser.add_argument('--record-video', action='store_true', help='录制测试过程视频')
    parser.add_argument('--highlight', action='store_true', help='启用页面元素高亮（默认不高亮）')
    
    args = parser.parse_args()
    
    # 设置日志 - 无论是否指定--log-file参数，都写入日志文件
    setup_logging(log_to_file=True)
    logger = logging.getLogger(__name__)
    
    # 显示启动信息
    logger.info(f"启动LLM Web自动化测试工具")
    logger.info(f"参数: {vars(args)}")
    
    # 设置配置
    config = {}
    
    # 如果启用无头模式
    if args.headless:
        logger.info("启用无头模式")
        if "browser" not in config:
            config["browser"] = {}
        config["browser"]["headless"] = True
    
    # 如果禁用实时元素定位
    if args.disable_realtime:
        logger.info("禁用实时元素定位")
        if "realtime" not in config:
            config["realtime"] = {}
        config["realtime"]["enable_realtime_locating"] = False
    
    # 运行测试循环
    loop = asyncio.get_event_loop()
    try:
        result = loop.run_until_complete(
            run_test(
                task=args.task,
                config=config,
                max_steps=args.max_steps,
                save_screenshots=args.screenshots,
                api_key=args.api_key,
                api_base=args.api_base,
                record_video=args.record_video,
                force_complete=args.force_complete,
                highlight_elements=args.highlight
            )
        )
        
        # 根据测试结果设置退出码
        if result.get('success', False):
            logger.info("✅ 测试用例成功完成！")
            sys.exit(0)
        else:
            logger.error("❌ 测试用例执行失败！")
            sys.exit(1)
    except Exception as e:
        logger.exception(f"运行期间发生错误: {str(e)}")
        sys.exit(2)
    finally:
        if loop.is_running():
            loop.close()

if __name__ == "__main__":
    main()