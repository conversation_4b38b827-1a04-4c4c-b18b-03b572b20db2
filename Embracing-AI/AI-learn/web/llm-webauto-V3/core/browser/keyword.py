# -*- coding: utf-8 -*-
"""
关键字驱动引擎模块

此模块是框架的核心，负责解析和执行关键字命令，调用相应的功能模块。
结合元素管理器和大模型技术，提供稳定可靠的自动化测试能力。
"""

import os
import sys
import time
import json
import base64
import logging
import asyncio
from typing import Dict, List, Any, Optional, Union, Callable, Tuple
from copy import deepcopy
from datetime import datetime
import re
from pathlib import Path
from urllib.parse import urlparse

from config.config import BROWSER_CONFIG, TEST_CONFIG, LOCATOR_CONFIG
from core.browser.element_manager import ElementManager
from core.browser.element_manager_realtime import RealtimeElementManager
from core.dom.vision import ImageRecognition
from core.agent.llm import LLMIntegration

# 自定义异常类
class KeywordNotFoundError(Exception):
    """当关键字未找到时抛出的异常"""
    pass

try:
    from playwright.sync_api import sync_playwright, <PERSON>, Browser, <PERSON><PERSON><PERSON><PERSON><PERSON>, Locator
    from playwright.async_api import async_playwright, <PERSON> as AsyncPage, <PERSON>rowser as As<PERSON><PERSON>rowser, <PERSON><PERSON><PERSON> as AsyncLocator
except ImportError:
    logging.error("请安装 playwright: pip install playwright")
    raise

# 获取特定的自定义日志记录器，不使用root logger
logger = logging.getLogger(__name__)

class KeywordDriver:
    """
    关键字驱动引擎类
    
    负责解析和执行关键字命令，是框架的核心组件。
    支持多种浏览器操作关键字，并结合元素管理器提供稳定的元素定位。
    """
    
    def __init__(self, config: Dict = None):
        """
        初始化关键字驱动引擎
        
        Args:
            config: 配置信息
        """
        self.browser = None
        self.context = None
        self.page = None
        self.async_playwright = None
        self.async_browser = None
        self.async_context = None
        self.async_page = None
        self.loop = None  # 初始化事件循环属性
        
        # 尝试获取当前事件循环
        try:
            self.loop = asyncio.get_event_loop()
        except RuntimeError:
            # 如果没有事件循环，创建一个新的
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)
        
        # 合并配置
        self.config = config or {}
        self.browser_config = deepcopy(BROWSER_CONFIG)
        self.browser_config.update(self.config.get('browser', {}))
        
        # 确保超时设置合理
        if self.browser_config.get('timeout', 30) < 30:
            self.browser_config['timeout'] = 30
        
        # 设置合理的操作超时
        self.action_timeout = self.browser_config.get('action_timeout', 10000)  # 默认操作超时10秒
        self.navigation_timeout = self.browser_config.get('navigation_timeout', 30000)  # 默认导航超时30秒
        
        # 获取实时元素配置
        realtime_config = deepcopy(LOCATOR_CONFIG)
        if 'realtime' in self.config:
            realtime_config.update(self.config.get('realtime', {}))
            
        # 处理DOM配置 - 优先使用dom.highlight_elements配置
        dom_config = self.config.get('dom', {})
        if 'highlight_elements' in dom_config:
            realtime_config['highlight_elements'] = dom_config['highlight_elements']
            
        # 使用元素管理器（根据配置决定是否使用实时元素管理器）
        if realtime_config.get('enable_realtime_locating', False):
            self.element_manager = RealtimeElementManager(keyword_driver=self)
            logger.info("已启用实时元素定位功能")
        else:
            self.element_manager = ElementManager(keyword_driver=self)
            logger.info("使用离线元素库进行元素定位")
        
        # 如果创建了实时元素管理器，设置其配置
        if hasattr(self.element_manager, 'realtime_config'):
            self.element_manager.realtime_config = realtime_config
        
        self.current_domain = None
        self.current_page_path = ""  # 添加当前页面路径属性
        self.keywords = self._register_keywords()
        
        logger.info("关键字驱动引擎初始化完成")
    
    def _register_keywords(self) -> Dict[str, Callable]:
        """
        注册关键字映射
        
        将自然语言关键字映射到类方法
        
        Returns:
            Dict[str, Callable]: 关键字到方法名的映射
        """
        keyword_map = {
            # 浏览器操作
            "打开浏览器": "open_browser",
            "关闭浏览器": "close_browser",
            "最大化浏览器": "maximize_browser",
            "导航到": "navigate_to",
            "后退": "go_back",
            "前进": "go_forward",
            "获取标题": "get_title",
            "获取当前URL": "get_url",
            
            # 等待操作
            "等待": "wait",
            "等待页面加载完成": "wait_for_page_load",
            "等待元素可见": "wait_for_element_visible",
            
            # 元素操作
            "点击": "click_element",
            "点位并点击": "click_element",
            "输入文本": "input_text",
            "定位并输入文本": "locate_and_input_text",
            "清除文本": "clear_text",
            "获取文本": "get_text",
            "获取属性": "get_attribute",
            "选择选项": "select_option",
            "上传文件": "upload_file_async",
            
            # 键盘操作
            "按下回车键": "press_enter",
            "点击回车": "press_enter",
            "按回车键": "press_enter",
            "回车": "press_enter",
            "按下回车": "press_enter",
            "按Enter键": "press_enter",
            
            
            # 新增React树形下拉框操作
            "展开树形组件": "expand_tree_dropdown",
            "选择树形节点": "select_tree_node",
            "选择部门": "select_department_from_tree",
            "选择组织": "select_department_from_tree",
            "选择部门和组织": "select_department_from_tree",
            "从部门树中选择": "select_department_from_tree",
            "导航组织树": "navigate_organization_tree",
            "组织树导航": "navigate_organization_tree",
            "展开组织树节点": "navigate_organization_tree",
            "在组织树中导航并选择节点": "navigate_organization_tree",
            
            # 登录相关操作（使用更可靠的登录专用方法）
            "点击登录": "login_click",
            "点击登录按钮": "login_click",
            "登录": "login_click",
            "提交登录": "login_click",
            "登录点击": "login_click",
            
            # 验证操作
            "验证当前页面包含文本": "verify_text",
            "验证属性": "verify_attribute",
            "验证URL": "verify_url",
            "验证标题": "verify_title",
            
            # 框架和窗口操作
            "切换到框架": "switch_to_frame",
            "切换到默认内容": "switch_to_default_content",
            "切换到窗口": "switch_to_window",
            "接受警告": "accept_alert",
            "取消警告": "dismiss_alert",
            
            # JavaScript执行
            "执行JavaScript": "execute_javascript",
            
            # 屏幕截图
            "截图": "take_screenshot",
            "保存截图": "save_screenshot",
            
            # 状态检查
            "检查元素存在": "check_element_exists",
            
            # 异步操作
            "获取页面元素": "get_page_elements_async",
            "获取当前页面URL": "get_current_url_async",
            "异步截图": "screenshot_async",
            
            # Playwright特有的定位器方法
            "使用定位器查找元素": "locate_element_with",
            "使用定位器点击": "click_with_locator",
            "使用定位器输入文本": "input_text_with_locator",
            "使用定位器验证文本": "verify_text_with_locator",
            
            # 视频录制相关操作
            "设置视频录制": "set_video_recording",
            "启用视频录制": lambda: self.set_video_recording(True),
            "禁用视频录制": lambda: self.set_video_recording(False),
            "开启视频录制": lambda: self.set_video_recording(True),
            "关闭视频录制": lambda: self.set_video_recording(False),
            
            # 简写的定位器方法别名
            "点击文本": lambda text: self.click_with_locator("text", text, f"包含文本'{text}'的元素"),
            "点击按钮": lambda text: self.click_with_locator("role", f"button", f"按钮'{text}'"),
            "点击链接": lambda text: self.click_with_locator("role", f"link", f"链接'{text}'"),
            "在输入框中输入": lambda placeholder, text: self.input_text_with_locator("placeholder", placeholder, text, f"占位符为'{placeholder}'的输入框"),
            
        }
        
        return keyword_map
    
    async def run_keyword_async(self, keyword: str, *args, **kwargs) -> Any:
        """
        异步运行关键字命令
        
        Args:
            keyword (str): 执行的关键字
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            Any: 命令执行结果
            
        Raises:
            KeywordNotFoundError: 当关键字不存在时抛出
        """
        try:
            # 如果关键字是等待特定秒数的格式
            if keyword.startswith("等待") and "秒" in keyword:
                try:
                    # 提取数字
                    seconds = int(''.join(filter(str.isdigit, keyword)))
                    logger.info(f"执行等待 {seconds} 秒")
                    # 使用异步等待而不是同步的time.sleep
                    await asyncio.sleep(seconds)
                    return True
                except (ValueError, TypeError) as e:
                    logger.error(f"无法解析等待时间: {str(e)}")
                    raise
            
            # 获取方法名
            method_name = await self._keyword_to_method_async(keyword)
            
            # 查找方法
            method = getattr(self, method_name, None)
            if not method:
                raise KeywordNotFoundError(f"关键字 '{keyword}' 没有对应的方法")
            
            # 检查方法是否为异步方法
            if asyncio.iscoroutinefunction(method):
                # 直接调用异步方法
                logger.info(f"执行异步关键字: {keyword}")
                return await method(*args, **kwargs)
            else:
                # 对于同步方法，在线程池中执行
                logger.info(f"在线程池中执行同步关键字: {keyword}")
                return await asyncio.to_thread(method, *args, **kwargs)
                
        except Exception as e:
            logger.error(f"执行关键字 '{keyword}' 时出错: {str(e)}")
            raise
    
    # 将wait方法转换为异步
    async def wait_async(self, seconds) -> None:
        """
        异步等待指定的秒数
        
        Args:
            seconds: 等待的秒数
        """
        try:
            if isinstance(seconds, str):
                # 尝试将字符串转换为整数
                seconds = int(seconds)
            logger.info(f"等待 {seconds} 秒")
            await asyncio.sleep(seconds)
            logger.info(f"等待 {seconds} 秒完成")
        except ValueError as e:
            logger.error(f"等待失败: 无法将输入 '{seconds}' 转换为有效的秒数")
            raise ValueError(f"等待失败: 无法将输入 '{seconds}' 转换为有效的秒数") from e
        except Exception as e:
            logger.error(f"等待失败: {str(e)}")
            raise

    # 保留同步版本但是调用异步版本
    async def wait(self, seconds) -> None:
        """
        异步等待指定的秒数
        
        Args:
            seconds: 等待的秒数
        """
        await asyncio.sleep(seconds)

    async def _keyword_to_method_async(self, keyword: str) -> str:
        """
        将关键字转换为异步方法名
        
        Args:
            keyword (str): 执行的关键字
            
        Returns:
            str: 对应的方法名
            
        Raises:
            KeywordNotFoundError: 当关键字找不到对应的方法时
        """
        # 处理特殊格式的关键字 "等待X秒"
        if keyword.startswith("等待") and "秒" in keyword:
            # 尝试提取数字
            try:
                seconds = int(''.join(filter(str.isdigit, keyword)))
                logger.info(f"检测到等待X秒格式: {keyword} -> 等待 {seconds}秒")
                return "wait"
            except (ValueError, TypeError):
                pass  # 如果无法提取数字，继续正常流程
                
        # 常规关键字映射查找
        method_name = self.keywords.get(keyword)
        
        if not method_name:
            # 对于中文关键字，尝试查找对应的中文方法
            if hasattr(self, keyword):
                return keyword  # 如果类中直接存在同名方法（如"等待"），直接返回
                
            # 查找相似关键字
            similar_keywords = self._find_similar_keywords(keyword)
            if similar_keywords:
                similar_str = ", ".join([f"'{k}'" for k in similar_keywords])
                logger.warning(f"关键字 '{keyword}' 不存在。您是否想要使用以下关键字: {similar_str}?")
                
            raise KeywordNotFoundError(f"找不到关键字 '{keyword}'")
            
        return method_name
    
    def _find_similar_keywords(self, keyword: str, threshold: float = 0.7) -> List[str]:
        """
        查找相似的关键字
        
        Args:
            keyword (str): 要查找的关键字
            threshold (float): 相似度阈值
            
        Returns:
            List[str]: 相似的关键字列表
        """
        import difflib
        
        return [k for k in self.keywords.keys() 
                if difflib.SequenceMatcher(None, keyword, k).ratio() > threshold]
    
    async def get_current_url_async(self) -> str:
        """
        异步获取当前URL
        
        Returns:
            str: 当前页面URL
        """
        if not self.page:
            return ""
        
        try:
            # 如果是同步页面对象，则在事件循环中执行
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, lambda: self.page.url)
        except Exception as e:
            logger.error(f"获取当前URL失败: {str(e)}")
            return ""
    
    async def get_page_elements_async(self) -> List[Dict[str, Any]]:
        """
        异步获取页面元素
        
        Returns:
            List[Dict[str, Any]]: 页面元素列表
        """
        if not self.page:
            return []
        
        try:
            # 注入JavaScript脚本收集页面元素
            elements_js = """
            () => {
                const interactiveElements = [];
                const allElements = document.querySelectorAll('button, a, input, select, textarea, [role="button"], [role="link"], [role="checkbox"], [role="radio"], [role="menuitem"], [role="tab"], [onclick]');
                
                allElements.forEach((element, index) => {
                    // 判断元素是否可见
                    const rect = element.getBoundingClientRect();
                    const isVisible = rect.width > 0 && rect.height > 0 &&
                        window.getComputedStyle(element).visibility !== 'hidden' &&
                        window.getComputedStyle(element).display !== 'none';
                    
                    if (!isVisible) return;
                    
                    // 获取元素文本内容
                    let textContent = element.textContent?.trim() || '';
                    
                    // 获取元素类型
                    let type = element.tagName.toLowerCase();
                    
                    // 获取属性
                    const attributes = {};
                    Array.from(element.attributes).forEach(attr => {
                        // 排除一些不必要的属性
                        if (!['style', 'class'].includes(attr.name)) {
                            attributes[attr.name] = attr.value;
                        }
                    });
                    
                    // 特殊处理输入元素
                    if (type === 'input') {
                        attributes['value'] = element.value || '';
                        if (element.placeholder) {
                            attributes['placeholder'] = element.placeholder;
                        }
                    }
                    
                    // 获取元素在视口中的位置
                    const position = {
                        x: rect.left,
                        y: rect.top,
                        width: rect.width,
                        height: rect.height
                    };
                    
                    interactiveElements.push({
                        index,
                        type,
                        text: textContent,
                        attributes,
                        position
                    });
                });
                
                return interactiveElements;
            }
            """
            
            # 执行JavaScript脚本
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, lambda: self.page.evaluate(elements_js))
        except Exception as e:
            logger.error(f"获取页面元素失败: {str(e)}")
            return []
    
    async def screenshot_async(self, return_base64: bool = False, path: str = None) -> Optional[str]:
        """
        异步截图
        
        Args:
            return_base64: 是否返回base64编码的截图
            path: 截图保存路径
            
        Returns:
            Optional[str]: base64编码的截图或保存路径
        """
        try:
            if not self.page:
                logger.error("浏览器未打开，无法截图")
                return None
                
            if not path:
                # 生成默认的截图文件名
                path = os.path.join(
                    self.browser_config['screenshot_dir'], 
                    f"screenshot_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
                )
                
            # 确保路径存在
            os.makedirs(os.path.dirname(path), exist_ok=True)
            
            # 使用增加的超时
            if return_base64:
                try:
                    screenshot = await asyncio.shield(self.page.screenshot(type='jpeg', quality=80, path=None, timeout=5000))
                    import base64
                    return base64.b64encode(screenshot).decode('utf-8')
                except asyncio.CancelledError:
                    logger.warning("截图操作被取消")
                    return None
            else:
                try:
                    await asyncio.shield(self.page.screenshot(path=path, timeout=5000))
                    logger.info(f"截图已保存到: {path}")
                    return path
                except asyncio.CancelledError:
                    logger.warning("截图操作被取消")
                    return None
        except Exception as e:
            logger.error(f"截图失败: {str(e)}")
            return None

    
    # ===== 浏览器操作关键字 =====
    
    async def open_browser(self, browser_type: str = None) -> None:
        """
        异步打开浏览器
        
        Args:
            browser_type: 浏览器类型 (chromium, firefox, webkit)
        """
        try:
            browser_type = browser_type or self.browser_config.get('browser_type', 'chromium')
            logger.info(f"正在异步打开 {browser_type} 浏览器")
            
            # 释放可能已存在的资源
            if self.async_page:
                await self.async_page.close()
                self.async_page = None
            
            if self.async_context:
                await self.async_context.close()
                self.async_context = None
                
            if self.async_browser:
                await self.async_browser.close()
                self.async_browser = None
                
            if self.async_playwright:
                await self.async_playwright.stop()
                self.async_playwright = None
                
            # 创建新的async playwright实例
            self.async_playwright = await async_playwright().start()
            browser_instance = getattr(self.async_playwright, browser_type.lower())
            
            # 配置浏览器
            launch_options = {
                "headless": self.browser_config.get('headless', False),
                "slow_mo": self.browser_config.get('slow_mo', 50),
            }
            
            # 如果需要最大化或全屏，添加额外的启动参数
            if self.browser_config.get('maximize', False) or self.browser_config.get('fullscreen', False):
                # 添加启动参数以支持最大化
                if 'args' not in launch_options:
                    launch_options['args'] = []
                
                # 检测操作系统类型
                import platform
                is_mac = platform.system() == 'Darwin'
                is_windows = platform.system() == 'Windows'
                
                # 根据操作系统添加不同的参数
                if browser_type.lower() == 'chromium':
                    if is_mac:
                        # macOS系统使用特殊参数
                        if self.browser_config.get('fullscreen', False):
                            # 优先使用全屏模式
                            launch_options['args'].append('--start-fullscreen')
                        else:
                            # 尝试其他最大化方式
                            launch_options['args'].extend([
                                '--kiosk',  # kiosk模式(另一种全屏方式)
                                '--window-size=9999,9999',  # 设置非常大的窗口尺寸
                            ])
                        logger.info("macOS系统：已添加全屏/最大化启动参数")
                    elif is_windows:
                        # Windows系统使用标准最大化参数
                        if self.browser_config.get('fullscreen', False):
                            launch_options['args'].append('--start-fullscreen')
                        else:
                            launch_options['args'].append('--start-maximized')
                        logger.info("Windows系统：已添加全屏/最大化启动参数")
                    else:
                        # Linux等其他系统
                        if self.browser_config.get('fullscreen', False):
                            launch_options['args'].append('--start-fullscreen')
                        else:
                            launch_options['args'].extend([
                                '--start-maximized',
                                '--window-size=9999,9999'
                            ])
                        logger.info("Linux系统：已添加全屏/最大化启动参数")
                
                logger.info(f"已添加窗口控制参数，操作系统: {platform.system()}")
            
            # 启动浏览器
            self.async_browser = await browser_instance.launch(**launch_options)
            
            # 检查视频录制目录，如果不存在则创建
            video_dir = self.browser_config.get('video_dir')
            if video_dir:
                # 确保视频目录存在
                try:
                    os.makedirs(video_dir, exist_ok=True)
                    logger.info(f"视频录制目录已准备: {video_dir}")
                except Exception as e:
                    logger.warning(f"创建视频录制目录时出错: {str(e)}，视频录制功能可能无法正常工作")
            
            # 创建上下文
            context_options = {
                "viewport": {
                    "width": self.browser_config.get('width', 1440),
                    "height": self.browser_config.get('height', 900)
                },
                "ignore_https_errors": True,
            }
            
            # 添加视频录制功能（如果启用）
            if video_dir:
                context_options.update({
                    "record_video_dir": video_dir,
                    "record_video_size": {
                        "width": self.browser_config.get('width', 1440),
                        "height": self.browser_config.get('height', 900)
                    }
                })
            
            # 使用持久会话或创建新的上下文
            self.async_context = await self.async_browser.new_context(**context_options)
            
            # 创建新页面
            self.async_page = await self.async_context.new_page()
            
            # 设置超时
            self.async_page.set_default_timeout(self.action_timeout)
            self.async_page.set_default_navigation_timeout(self.navigation_timeout)
            
            # 设置页面事件监听器
            await self._setup_page_event_listeners()
            
            # 设置同步浏览器对象用于兼容现有代码
            self.browser = self.async_browser
            self.context = self.async_context
            self.page = self.async_page
            
            # 如果配置了最大化，则最大化浏览器窗口
            if self.browser_config.get('maximize', False):
                await self._maximize_browser_async()
            
            # 输出日志信息
            message = f"浏览器 {browser_type} 已成功异步打开"
            if self.browser_config.get('maximize', False):
                message += "并已最大化"
            if video_dir:
                message += f"，视频录制已启用，保存路径: {video_dir}"
            logger.info(message)
            
        except Exception as e:
            logger.error(f"打开浏览器失败: {str(e)}")
            raise
    
    async def _setup_page_event_listeners(self):
        """配置页面事件监听器"""
        # 监听页面错误
        self.async_page.on("pageerror", lambda error: logger.error(f"页面错误: {error}"))
        # 监听页面控制台消息
        self.async_page.on("console", lambda msg: logger.debug(f"控制台 {msg.type}: {msg.text}"))

    def check_browser_opened(self) -> None:
        """
        检查浏览器是否已打开，如果未打开则抛出异常
        
        Raises:
            RuntimeError: 当浏览器未打开时抛出
        """
        if not self.page or not self.browser:
            raise RuntimeError("浏览器未打开，请先调用'打开浏览器'关键字")
        logger.info("浏览器状态验证成功")

    async def close_browser_async(self) -> None:
        """异步关闭浏览器"""
        try:
            logger.info("开始异步关闭浏览器...")
            
            # 先关闭页面
            if self.page:
                try:
                    await asyncio.wait_for(self.page.close(), timeout=5.0)
                    logger.info("页面已关闭")
                except asyncio.TimeoutError:
                    logger.warning("页面关闭操作超时")
                except Exception as page_err:
                    logger.warning(f"关闭页面时出错: {str(page_err)}")
                self.page = None
                
            if self.async_page:
                try:
                    await asyncio.wait_for(self.async_page.close(), timeout=5.0)
                    logger.info("异步页面已关闭") 
                except asyncio.TimeoutError:
                    logger.warning("异步页面关闭操作超时")
                except Exception as page_err:
                    logger.warning(f"关闭异步页面时出错: {str(page_err)}")
                self.async_page = None
            
            # 在关闭上下文前等待一小段时间，确保视频录制完成
            if self.async_context:
                logger.info("等待视频录制完成...")
                await asyncio.sleep(1.0)  # 给视频录制一点时间完成
                
                try:
                    await asyncio.wait_for(self.async_context.close(), timeout=5.0)
                    logger.info("浏览器上下文已关闭，视频录制已完成")
                except asyncio.TimeoutError:
                    logger.warning("上下文关闭操作超时")
                except Exception as context_err:
                    logger.warning(f"关闭上下文时出错: {str(context_err)}")
                self.async_context = None
                self.context = None
                
            # 再关闭浏览器
            if self.browser:
                try:
                    await asyncio.wait_for(self.browser.close(), timeout=5.0)
                    logger.info("浏览器已异步关闭")
                except asyncio.TimeoutError:
                    logger.warning("浏览器关闭操作超时")
                except Exception as browser_err:
                    logger.warning(f"关闭浏览器时出错: {str(browser_err)}")
                self.browser = None
                
            if self.async_browser:
                try:
                    await asyncio.wait_for(self.async_browser.close(), timeout=5.0)
                    logger.info("异步浏览器实例已关闭")
                except asyncio.TimeoutError:
                    logger.warning("异步浏览器关闭操作超时")
                except Exception as browser_err:
                    logger.warning(f"关闭异步浏览器时出错: {str(browser_err)}")
                self.async_browser = None
            
            # 重置浏览器相关属性
            self.current_domain = None
            self.current_page_path = None
            logger.info("浏览器和相关资源已全部清理完毕")
            
            # 如果视频录制目录存在，输出提示信息
            video_dir = self.browser_config.get('video_dir')
            if video_dir and os.path.exists(video_dir):
                logger.info(f"视频已保存至目录: {video_dir}")
                
            return True
        except Exception as e:
            logger.error(f"异步关闭浏览器过程中发生错误: {str(e)}")
            # 尝试强制清理所有浏览器相关属性
            self.browser = None
            self.async_browser = None
            self.page = None
            self.async_page = None
            self.current_domain = None
            self.current_page_path = None
            return False

    def close_browser(self) -> None:
        """同步关闭浏览器 - 通过调用异步方法实现"""
        try:
            logger.info("开始同步关闭浏览器(通过异步方法)...")
            # 使用run_sync包装异步关闭方法
            self.run_sync(self.close_browser_async())
            logger.info("浏览器同步关闭完成")
        except Exception as e:
            logger.error(f"同步关闭浏览器时出错: {str(e)}")
            # 尝试直接清理资源
            self._cleanup_playwright()

    def __del__(self):
        """析构函数，确保浏览器被关闭"""
        try:
            if self.browser or self.async_browser:
                logger.info("在对象销毁时关闭浏览器")
                # 在析构函数中使用同步方式关闭
                self.close_browser()
        except Exception as e:
            # 析构函数中的异常应该被捕获而不传播
            if hasattr(self, 'logger'):
                self.logger.error(f"在对象销毁时关闭浏览器出错: {str(e)}")
            else:
                print(f"错误: 在对象销毁时关闭浏览器出错: {str(e)}")
                
    # 确保run_sync函数存在
    def run_sync(self, coroutine):
        """运行异步协程的同步方法"""
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 如果事件循环正在运行，使用run_until_complete
                return asyncio.run_coroutine_threadsafe(coroutine, loop).result()
            else:
                # 如果事件循环未运行，创建一个新的事件循环
                return asyncio.run(coroutine)
        except RuntimeError:
            # 如果无法获取现有事件循环，创建一个新的
            new_loop = asyncio.new_event_loop()
            asyncio.set_event_loop(new_loop)
            try:
                return new_loop.run_until_complete(coroutine)
            finally:
                new_loop.close()

    def maximize_browser(self) -> None:
        """
        最大化浏览器窗口
        
        Raises:
            RuntimeError: 如果浏览器未打开
        """
        if not self.page:
            logger.error("尝试最大化未打开的浏览器窗口")
            raise RuntimeError("浏览器未打开")
        
        try:
            # 使用JavaScript获取屏幕尺寸并设置浏览器窗口大小
            screen_size = self.page.evaluate("""
                () => {
                    return {
                        width: window.screen.availWidth,
                        height: window.screen.availHeight
                    }
                }
            """)
            
            # 设置浏览器视窗大小为屏幕可用大小
            self.page.set_viewport_size(screen_size)
            
            # 使用evaluate运行最大化命令
            self.page.evaluate("() => { window.moveTo(0, 0); window.resizeTo(window.screen.availWidth, window.screen.availHeight); }")
            
            logger.info(f"浏览器窗口已最大化为屏幕尺寸: {screen_size['width']}x{screen_size['height']}")
        except Exception as e:
            logger.error(f"最大化浏览器窗口失败: {str(e)}")
            raise

    async def _maximize_browser_async(self) -> None:
        """
        异步最大化浏览器窗口
        """
        if not self.async_page:
            logger.error("尝试最大化未打开的浏览器窗口")
            return
        
        try:
            # 检测操作系统类型
            import platform
            is_mac = platform.system() == 'Darwin'
            
            # 优先检查是否启用了全屏模式
            if self.browser_config.get('fullscreen', False):
                logger.info("全屏模式已启用，尝试进入全屏模式")
                
                # 方法1：使用Playwright的全屏API
                try:
                    await self.async_page.evaluate("document.documentElement.requestFullscreen()")
                    logger.info("已尝试使用标准全屏API")
                    await asyncio.sleep(1)  # 等待全屏API生效
                except Exception as e1:
                    logger.warning(f"使用标准全屏API失败: {str(e1)}")
                    
                    # 尝试备用方法
                    try:
                        # 使用针对不同浏览器的全屏API
                        await self.async_page.evaluate("""
                            () => {
                                const elem = document.documentElement;
                                if (elem.requestFullscreen) {
                                    elem.requestFullscreen();
                                } else if (elem.webkitRequestFullscreen) {
                                    elem.webkitRequestFullscreen();
                                } else if (elem.mozRequestFullScreen) {
                                    elem.mozRequestFullScreen();
                                } else if (elem.msRequestFullscreen) {
                                    elem.msRequestFullscreen();
                                }
                            }
                        """)
                        logger.info("已尝试使用备用全屏API")
                        await asyncio.sleep(1)
                    except Exception as e2:
                        logger.warning(f"使用备用全屏API也失败: {str(e2)}")
                        
                        # macOS系统特有的快捷键
                        if is_mac:
                            try:
                                # 使用macOS的全屏快捷键
                                await self.async_page.keyboard.press("Meta+Control+F")
                                logger.info("已尝试使用macOS的全屏快捷键")
                                await asyncio.sleep(1)
                            except Exception as e3:
                                logger.warning(f"使用macOS全屏快捷键失败: {str(e3)}")
            
            # 如果全屏不可用或失败，尝试常规最大化
            if is_mac:
                # macOS系统：尝试多种最大化方法
                logger.info("检测到macOS系统，使用特殊的最大化方法")
                
                # 如果上述全屏方法失败，尝试使用最大可能的视窗尺寸
                try:
                    # 获取屏幕尺寸
                    screen_size = await self.async_page.evaluate("""
                        () => {
                            return {
                                width: window.screen.width,
                                height: window.screen.height
                            }
                        }
                    """)
                    
                    # 设置窗口尺寸
                    await self.async_page.set_viewport_size(screen_size)
                    logger.info(f"已将视窗大小设置为: {screen_size['width']}x{screen_size['height']}")
                    
                    # 尝试使用Chromium的特殊命令(macOS)
                    try:
                        await self.async_page.evaluate("""
                            () => {
                                // Chromium在macOS上的全屏/最大化命令
                                if (window.chrome && window.chrome.app && window.chrome.app.window) {
                                    window.chrome.app.window.current().maximize();
                                }
                            }
                        """)
                        logger.info("已尝试使用Chromium特有API最大化窗口")
                    except Exception as e_chrome:
                        # 这个错误很常见，因为并非所有情况下都有chrome.app接口
                        logger.debug(f"使用Chromium特有API失败: {str(e_chrome)}")
                except Exception as e3:
                    logger.warning(f"设置视窗大小失败: {str(e3)}")
            else:
                # 非macOS系统：使用标准方法
                # 使用JavaScript获取屏幕尺寸并设置浏览器窗口大小
                screen_size = await self.async_page.evaluate("""
                    () => {
                        return {
                            width: window.screen.availWidth,
                            height: window.screen.availHeight
                        }
                    }
                """)
                
                # 设置浏览器视窗大小为屏幕可用大小
                await self.async_page.set_viewport_size(screen_size)
                
                # 使用evaluate运行最大化命令
                await self.async_page.evaluate("() => { window.moveTo(0, 0); window.resizeTo(window.screen.availWidth, window.screen.availHeight); }")
                
                logger.info(f"浏览器窗口已异步最大化为屏幕尺寸: {screen_size['width']}x{screen_size['height']}")
            
        except Exception as e:
            logger.warning(f"异步最大化浏览器窗口失败: {str(e)}")
            
            # 尝试退回到使用固定的较大尺寸
            try:
                large_size = {"width": 1920, "height": 1080}
                await self.async_page.set_viewport_size(large_size)
                logger.info(f"使用固定大尺寸作为备选: {large_size['width']}x{large_size['height']}")
            except Exception as fallback_err:
                logger.error(f"设置备选尺寸也失败: {str(fallback_err)}")

    def _cleanup_playwright(self) -> None:
        """
        清理Playwright资源
        """
        if self.page:
            try:
                self.page.close()
            except:
                pass
            self.page = None
            
        if self.context:
            try:
                self.context.close()
            except:
                pass
            self.context = None
            
        if self.browser:
            try:
                self.browser.close()
            except:
                pass
            self.browser = None
            
        if self.async_browser:
            try:
                self.async_browser.close()
            except:
                pass
            self.async_browser = None
    
    async def navigate_to(self, url: str) -> None:
        """
        导航到指定URL
        
        Args:
            url: 目标URL
        """
        try:
            self._verify_browser_state()
            logger.info(f"正在导航到: {url}")
            
            # 保存当前URL用于检测重定向
            old_url = self.get_url()
            
            # 使用await等待异步操作完成
            await self.async_page.goto(url, timeout=self.navigation_timeout)
            await self.async_page.wait_for_load_state("networkidle", timeout=self.navigation_timeout)
            
            # 获取新URL
            new_url = await self.get_current_url_async()
            
            # 检测重定向
            if old_url != new_url and old_url is not None:
                logger.info(f"URL重定向发生: {url} -> {new_url}")
            
            # 更新当前域名和路径
            parsed_url = urlparse(new_url)
            self.current_domain = parsed_url.netloc
            self.current_page_path = parsed_url.path
            
            logger.info(f"关键字引擎已更新当前域名为: {self.current_domain}")
            logger.info(f"关键字引擎已更新当前页面路径为: {self.current_page_path.strip('/') or 'blank'}")
            
            # 同步元素管理器的域名和路径
            self._synchronize_domain_with_element_manager()
            
            # 等待DOM更新
            await self._update_dom_after_action_async()
            
        except Exception as e:
            logger.error(f"导航到URL失败: {url} - {str(e)}")
            raise

    def go_back(self) -> None:
        """
        浏览器后退
        """
        if not self.page:
            raise RuntimeError("浏览器未打开，请先调用'打开浏览器'关键字")
        
        if description:
            log_desc = f"'{description}' ({locator_type}={locator_value})"
        else:
            log_desc = f"{locator_type}={locator_value}"
            
        logger.info(f"使用 {locator_type} 定位器查找元素: {log_desc}")
        
        try:
            # 使用增强的定位方式
            element = self._create_element_locator(locator_type, locator_value)
            
            # 等待元素可见
            try:
                element.wait_for(state='visible', timeout=self.action_timeout)
            except Exception as e:
                logger.error(f"等待元素可见失败: {log_desc} - {str(e)}")
                raise ValueError(f"元素未变为可见状态: {log_desc}")
            
            # 检查元素是否存在
            count = element.count()
            if count == 0:
                logger.error(f"未找到元素: {log_desc}")
                raise ValueError(f"未找到元素: {log_desc}")
            elif count > 1:
                logger.warning(f"找到多个匹配元素({count}个): {log_desc}，将使用第一个")
            
            logger.info(f"成功找到元素: {log_desc}")
            return element.first()
        
        except Exception as e:
            logger.error(f"定位元素失败: {log_desc} - {str(e)}")
            
            # 保存错误截图
            screenshot_path = os.path.join(
                self.browser_config['screenshot_dir'],
                f"locate_error_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            )
            try:
                self.page.screenshot(path=screenshot_path, timeout=5000)
                logger.info(f"错误截图已保存至: {screenshot_path}")
            except Exception as ss_err:
                logger.error(f"保存截图失败: {str(ss_err)}")
            
            raise ValueError(f"定位元素失败: {log_desc} - {str(e)}")

    def click_with_locator(self, locator_type: str, locator_value: str, description: str = None) -> None:
        """
        使用指定定位器类型直接点击元素
        
        Args:
            locator_type: 定位器类型
            locator_value: 定位器值
            description: 元素描述(可选)，用于日志记录
            
        Raises:
            ValueError: 当元素未找到时抛出
            RuntimeError: 当点击失败时抛出
        """
        if description:
            log_desc = f"'{description}' ({locator_type}={locator_value})"
        else:
            log_desc = f"{locator_type}={locator_value}"
            
        logger.info(f"尝试点击元素: {log_desc}")
        
        try:
            element = self.locate_element_with(locator_type, locator_value, description)
            element.click(timeout=self.action_timeout)
            logger.info(f"成功点击元素: {log_desc}")
        except Exception as e:
            logger.error(f"点击元素失败: {log_desc} - {str(e)}")
            raise RuntimeError(f"点击元素失败: {log_desc} - {str(e)}")

    def input_text_with_locator(self, locator_type: str, locator_value: str, text: str, description: str = None) -> None:
        """
        使用指定定位器类型直接在元素中输入文本
        
        Args:
            locator_type: 定位器类型
            locator_value: 定位器值
            text: 要输入的文本
            description: 元素描述(可选)，用于日志记录
            
        Raises:
            ValueError: 当元素未找到时抛出
            RuntimeError: 当输入失败时抛出
        """
        if description:
            log_desc = f"'{description}' ({locator_type}={locator_value})"
        else:
            log_desc = f"{locator_type}={locator_value}"
            
        logger.info(f"尝试在元素中输入文本: {log_desc}, 文本: '{text}'")
        
        try:
            element = self.locate_element_with(locator_type, locator_value, description)
            
            # 尝试清除现有文本
            try:
                element.clear(timeout=self.action_timeout)
            except Exception as e:
                logger.warning(f"清除文本失败，将直接填充: {str(e)}")
            
            # 尝试填充文本
            element.fill(text, timeout=self.action_timeout)
            logger.info(f"成功在元素中输入文本: {log_desc}")
        except Exception as e:
            logger.error(f"输入文本失败: {log_desc} - {str(e)}")
            raise RuntimeError(f"输入文本失败: {log_desc} - {str(e)}")

    def verify_text_with_locator(self, locator_type: str, locator_value: str, expected_text: str, description: str = None) -> None:
        """
        使用指定定位器类型验证元素文本
        
        Args:
            locator_type: 定位器类型
            locator_value: 定位器值
            expected_text: 预期文本
            description: 元素描述(可选)，用于日志记录
            
        Raises:
            ValueError: 当元素未找到时抛出
            AssertionError: 当文本验证失败时抛出
        """
        if description:
            log_desc = f"'{description}' ({locator_type}={locator_value})"
        else:
            log_desc = f"{locator_type}={locator_value}"
            
        logger.info(f"尝试验证元素文本: {log_desc}, 预期文本: '{expected_text}'")
        
        try:
            element = self.locate_element_with(locator_type, locator_value, description)
            actual_text = element.text_content().strip()
            
            if actual_text == expected_text:
                logger.info(f"文本验证成功: {log_desc} - '{expected_text}'")
                return
            elif expected_text in actual_text:
                logger.info(f"文本模糊匹配成功: {log_desc} - 预期'{expected_text}'包含在实际文本'{actual_text}'中")
                return
            else:
                error_msg = f"文本验证失败: {log_desc} - 预期 '{expected_text}' 实际 '{actual_text}'"
                logger.error(error_msg)
                raise AssertionError(error_msg)
        except Exception as e:
            if not isinstance(e, AssertionError):
                logger.error(f"验证文本过程异常: {log_desc} - {str(e)}")
                raise ValueError(f"验证文本失败: {log_desc} - {str(e)}")
            raise

    def login_click(self, element_key: str = "登录按钮", page_key: str = None) -> None:
        """
        专门用于处理登录按钮的点击操作，使用多种策略确保登录成功
        
        Args:
            element_key: 元素标识，默认为"登录按钮"
            page_key: 页面标识
            
        Raises:
            RuntimeError: 登录点击失败时抛出
        """
        self.check_browser_opened()
        logger.info(f"尝试特殊登录点击: {element_key}")
        
        # 记录点击前的状态，用于验证点击是否有效
        pre_click_url = self.page.url
        try:
            pre_click_dom_count = self.page.evaluate("() => document.querySelectorAll('*').length")
            pre_click_html = self.page.content()
        except Exception as e:
            logger.warning(f"记录点击前状态失败: {str(e)}")
            pre_click_dom_count = 0
            pre_click_html = ""
        
        # 等待页面完全加载
        try:
            logger.info("确保页面完全加载")
            self.page.wait_for_load_state("domcontentloaded", timeout=5000)
            self.page.wait_for_load_state("networkidle", timeout=5000)
        except Exception as e:
            logger.warning(f"等待页面加载失败，但将继续尝试: {str(e)}")
        
        # 尝试方法1：使用标准点击方法
        try:
            logger.info("首先尝试使用标准点击方法")
            self.click_element(element_key, page_key)
            logger.info("[尝试] 标准点击方法执行完成，检查是否有效")
        except Exception as e:
            logger.warning(f"标准点击方法失败: {str(e)}，尝试其他方法")
        
        # 尝试方法2：通用登录按钮选择器
        click_succeeded = False
        login_selectors = [
            # 通用登录按钮选择器
            "button:has-text('登录')",
            "button:has-text('登 录')",
            "button[type='submit']",
            ".login-btn",
            "#login-btn",
            "#login",
            "button.login",
            "button.submit",
            ".submit-btn",
            "//button[contains(text(),'登录') or contains(text(),'登 录')]",
            "//input[@type='submit']",
            "//button[@type='submit']"
        ]
        
        for selector in login_selectors:
            if click_succeeded:
                break
            
            logger.info(f"尝试使用通用登录选择器: {selector}")
            try:
                # 确定选择器类型
                if selector.startswith("//"):
                    # XPath选择器
                    element = self.page.locator(f"xpath={selector}")
                else:
                    # CSS选择器
                    element = self.page.locator(selector)
                
                # 检查元素是否存在并可见
                if element.count() > 0 and element.first.is_visible():
                    element.first.click(timeout=5000, force=True)
                    logger.info(f"[尝试] 使用选择器 {selector} 执行点击操作")
                    click_succeeded = True
            except Exception as e:
                logger.warning(f"选择器 {selector} 点击失败: {str(e)}")
        
        # 尝试方法3：使用JavaScript点击
        if not click_succeeded:
            js_script = """
                function clickLoginButton() {
                    // 所有可能的登录按钮文本
                    const loginTexts = ['登录', '登 录', 'login', 'Login', 'LOGIN', '确定', '提交', 'Submit'];
                    
                    // 查找所有按钮
                    const buttons = document.querySelectorAll('button, input[type="submit"], [role="button"]');
                    for (const btn of buttons) {
                        const text = btn.textContent && btn.textContent.trim();
                        if (text && loginTexts.some(t => text.includes(t))) {
                            console.log('找到登录按钮:', btn);
                            btn.click();
                            return true;
                        }
                        
                        // 检查class和id
                        const className = btn.className && btn.className.toLowerCase();
                        if (className && (className.includes('login') || className.includes('submit'))) {
                            console.log('找到登录按钮(class):', btn);
                            btn.click();
                            return true;
                        }
                        
                        const id = btn.id && btn.id.toLowerCase();
                        if (id && (id.includes('login') || id.includes('submit'))) {
                            console.log('找到登录按钮(id):', btn);
                            btn.click();
                            return true;
                        }
                    }
                    
                    // 尝试提交表单
                    const forms = document.forms;
                    for (const form of forms) {
                        try {
                            form.submit();
                            console.log('提交表单成功');
                            return true;
                        } catch (e) {}
                    }
                    
                    return false;
                }
                
                return clickLoginButton();
            """
            
            try:
                logger.info("使用JavaScript尝试点击登录按钮")
                result = self.page.evaluate(js_script)
                if result:
                    logger.info("[尝试] JavaScript执行点击操作")
                    click_succeeded = True
                else:
                    logger.warning("JavaScript未能找到或点击登录按钮")
            except Exception as e:
                logger.warning(f"JavaScript点击登录按钮失败: {str(e)}")
        
        # 尝试方法4：按回车键提交
        if not click_succeeded:
            try:
                logger.info("尝试按下回车键提交登录表单")
                self.page.keyboard.press('Enter')
                logger.info("[尝试] 已发送回车键，可能已提交表单")
                click_succeeded = True
            except Exception as e:
                logger.warning(f"按下回车键失败: {str(e)}")
        
        # 等待可能的页面变化
        try:
            logger.info("等待页面可能的变化...")
            time.sleep(2)  # 给页面一些时间来响应点击
            self.page.wait_for_load_state("networkidle", timeout=5000)
        except Exception as e:
            logger.warning(f"等待页面变化超时: {str(e)}")
        
        # 验证登录是否成功
        post_click_url = self.page.url
        login_success = False
        
        # 检查URL是否包含登录成功的标志
        if "dashboard" in post_click_url:
            logger.info("[✓成功] 登录成功：URL包含dashboard路径")
            login_success = True
        elif post_click_url != pre_click_url:
            logger.info(f"[变化] URL已改变：{pre_click_url} -> {post_click_url}")
        else:
            logger.warning("[⚠️警告] URL未变化，检查DOM变化...")
        
        # 检查DOM结构是否有变化
        try:
            post_click_dom_count = self.page.evaluate("() => document.querySelectorAll('*').length")
            dom_change_percent = ((post_click_dom_count - pre_click_dom_count) / pre_click_dom_count * 100) if pre_click_dom_count > 0 else 0
            
            if dom_change_percent > 10:  # DOM元素数量变化超过10%
                logger.info(f"[变化] DOM结构显著变化：{pre_click_dom_count} -> {post_click_dom_count} (变化{dom_change_percent:.1f}%)")
                login_success = True
            elif post_click_dom_count != pre_click_dom_count:
                logger.info(f"[变化] DOM结构有微小变化：{pre_click_dom_count} -> {post_click_dom_count}")
            else:
                logger.warning("[⚠️警告] DOM结构无变化，登录可能失败")
            
            # 检查页面内容是否有明显变化
            post_click_html = self.page.content()
            if pre_click_html and post_click_html and pre_click_html != post_click_html:
                logger.info("[变化] 页面HTML内容已变化")
                login_success = True
        except Exception as e:
            logger.warning(f"验证DOM变化失败: {str(e)}")
        
        # 检查是否存在登录失败提示
        try:
            error_messages = [
                "密码错误", "账号不存在", "用户名或密码错误", "登录失败", 
                "验证码错误", "账号已锁定", "错误的用户名或密码"
            ]
            
            page_text = self.page.text_content()
            for error in error_messages:
                if error in page_text:
                    logger.error(f"[✗失败] 检测到登录失败提示: '{error}'")
                    login_success = False
                    break
        except Exception as e:
            logger.warning(f"检查登录错误提示失败: {str(e)}")
        
        # 保存截图记录结果
        screenshot_path = os.path.join(
            self.browser_config['screenshot_dir'],
            f"login_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        )
        try:
            self.page.screenshot(path=screenshot_path, timeout=10000)
            logger.info(f"登录操作截图已保存至: {screenshot_path}")
        except Exception as se:
            logger.error(f"保存截图失败: {str(se)}")
        
        if login_success:
            logger.info("[✓成功] 登录操作有效，检测到页面变化")
        else:
            # 如果没有确认登录成功，记录警告但不要中断测试流程
            logger.warning("[⚠️警告] 无法确认登录是否成功，未检测到明显页面变化")
        
        # 更新DOM状态，以便后续操作
        self._update_dom_after_action()

    def _update_dom_after_action(self) -> None:
        """
        在操作后更新DOM状态
        """
        if not hasattr(self.element_manager, 'update_dom_state'):
            return
            
        try:
            # 首先等待一小段时间，确保DOM更新
            time.sleep(0.5)
            
            # 等待页面加载状态
            try:
                self.page.wait_for_load_state("domcontentloaded", timeout=5000)
            except Exception as e:
                logger.warning(f"等待dom加载状态超时，但将继续: {str(e)}")
            
            # 尝试等待网络活动完成
            try:
                self.page.wait_for_load_state("networkidle", timeout=5000)
            except Exception as e:
                logger.warning(f"等待网络活动完成超时，但将继续: {str(e)}")
                
            # 更新DOM状态
            logger.info("正在更新DOM状态...")
            self.element_manager.update_dom_state()
            logger.info("DOM状态更新完成")
        except Exception as e:
            logger.warning(f"更新DOM状态失败, 但将继续执行: {str(e)}")

    async def click_element(self, element_key: str) -> None:
        """
        点击元素
        
        Args:
            element_key: 元素标识
            
        Raises:
            RuntimeError: 点击元素失败时抛出
        """
        try:
            self.check_browser_opened()
            logger.info(f"尝试点击元素: {element_key}")
            
            # 等待元素可见
            if not await self.wait_for_element_visible_async(element_key):
                raise RuntimeError(f"元素 {element_key} 不可见，无法点击")
            
            # 等待页面加载完成
            try:
                await self.async_page.wait_for_load_state("networkidle", timeout=5000)
            except Exception as e:
                logger.warning(f"等待页面加载完成超时，但将继续尝试点击: {str(e)}")
            
            # 通过元素管理器查找元素
            element = await self.find_element(element_key)
            
            # 增加点击超时时间并使用force选项确保点击成功
            try:
                await element.click(timeout=10000, force=True)
                logger.info(f"成功点击元素: {element_key}")
            except Exception as click_error:
                logger.warning(f"常规点击失败: {str(click_error)}，尝试JavaScript点击")
                
                # 如果常规点击失败，尝试使用JavaScript点击
                try:
                    await self.async_page.evaluate("""(element) => { 
                        element.click(); 
                    }""", element)
                    logger.info(f"使用JavaScript成功点击元素: {element_key}")
                except Exception as js_error:
                    raise RuntimeError(f"JavaScript点击也失败: {str(js_error)}")
            
            # 点击后更新DOM状态
            await self._update_dom_after_action_async()
        except Exception as e:
            logger.error(f"点击元素失败: {element_key} - {str(e)}")
            
            # 保存错误截图
            screenshot_path = os.path.join(
                self.browser_config['screenshot_dir'],
                f"click_error_{element_key}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            )
            try:
                await self.async_page.screenshot(path=screenshot_path)
                logger.info(f"错误截图已保存至: {screenshot_path}")
            except Exception as se:
                logger.error(f"保存截图失败: {str(se)}")
            
            # 尝试使用元素管理器的失败处理
            try:
                await self.element_manager.update_element_failure_async(
                    str(e), screenshot_path
                )
            except Exception as ue:
                logger.warning(f"更新元素失败信息失败: {str(ue)}")
                
            raise RuntimeError(f"点击元素失败: {element_key} - {str(e)}")

    async def upload_file_async(self, element_key: str, file_path: str) -> None:
        """
        上传文件的关键字异步版本，使用filechooser处理隐藏的文件上传输入框

        Args:
            element_key: 上传按钮元素的名称或标识符
            file_path: 待上传文件的路径
        
        操作说明：
            1. 查找并定位上传按钮元素
            2. 创建文件选择器的Promise
            3. 点击上传按钮触发文件选择器
            4. 等待文件选择器出现
            5. 使用文件选择器设置待上传文件
        """
        self._verify_browser_state()
        logger.info(f"开始上传文件: {element_key}, 文件路径: {file_path}")
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        # 查找元素
        element = await self._find_element(element_key)
        if not element:
            raise KeywordNotFoundError(f"未找到上传按钮元素: {element_key}")
        
        try:
            # 开始等待文件选择器事件
            async with self.async_page.expect_file_chooser() as file_chooser_info:
                # 点击上传按钮
                await element.click()
                logger.info(f"已点击上传按钮: {element_key}")
            
            # 获取文件选择器
            file_chooser = await file_chooser_info.value
            # 设置文件
            await file_chooser.set_files(file_path)
            logger.info(f"已选择文件: {file_path}")
            asyncio.sleep(1)
            # 上传后更新DOM
            await self._update_dom_after_action_async()

        except Exception as e:
            logger.error(f"上传文件失败: {element_key} - {str(e)}")
            # 保存错误截图
            screenshot_path = os.path.join(
                self.browser_config['screenshot_dir'],
                f"upload_error_{element_key}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            )
            try:
                await self.async_page.screenshot(path=screenshot_path)
                logger.info(f"错误截图已保存至: {screenshot_path}")
            except Exception as se:
                logger.error(f"保存截图失败: {str(se)}")
            
            raise RuntimeError(f"上传文件失败: {element_key} - {str(e)}")

    def upload_file(self, element_key: str, file_path: str) -> None:
        """
        上传文件的关键字，使用filechooser处理隐藏的文件上传输入框

        Args:
            element_key: 上传按钮元素的名称或标识符
            file_path: 待上传文件的路径
        
        操作说明：
            1. 查找并定位上传按钮元素
            2. 创建文件选择器的Promise
            3. 点击上传按钮触发文件选择器
            4. 等待文件选择器出现
            5. 使用文件选择器设置待上传文件
        """
        return self.run_sync(self.upload_file_async(element_key, file_path))

    async def _find_element(self, element_key: str) -> Optional[Locator]:
        """
        查找元素
        
        Args:
            element_key: 元素标识
            
        Returns:
            找到的元素定位器，未找到则返回None
        """
        try:
            # 先从本地元素库查找
            element = None
            locators = self.element_manager.get_element_locators(element_key)
            
            if locators:
                # 尝试使用ElementManager进行定位
                if hasattr(self.element_manager, '_locate_with_locators'):
                    element = await self.element_manager._locate_with_locators(element_key, locators)
                else:
                    # 如果是RealtimeElementManager，使用它的实时定位方法
                    if hasattr(self.element_manager, 'find_element_in_realtime_async'):
                        element = await self.element_manager.find_element_in_realtime_async(element_key)
            
            if element:
                count = await element.count()
                if count > 0:
                    logger.info(f"在本地元素库中找到元素: {element_key}")
                    return element
            
            # 本地元素库未找到，尝试实时定位
            logger.info(f"本地定位策略未找到元素 {element_key}，尝试实时定位")
            if hasattr(self.element_manager, 'find_element_in_realtime_async'):
                element = await self.element_manager.find_element_in_realtime_async(element_key)
                if element:
                    logger.info(f"实时定位成功找到元素: {element_key}")
                    return element
                logger.warning(f"实时定位未找到元素: {element_key}")
            else:
                logger.warning(f"元素管理器不支持实时定位: {element_key}")
            
            return None
        except Exception as e:
            logger.error(f"查找元素失败: {str(e)}")
            return None

    async def wait_for_page_load(self, timeout: int = None) -> None:
        """
        等待页面加载完成
        
        Args:
            timeout: 超时时间(毫秒)
        """
        try:
            self._verify_browser_state()
            timeout = timeout or self.navigation_timeout
            logger.info(f"等待页面加载完成，超时时间: {timeout}ms")
            
            # 记录当前URL和路径
            current_url = self.async_page.url
            current_parts = urlparse(current_url)
            logger.info(f"当前URL: {current_url}")
            logger.info(f"当前路径: {current_parts.path}#{current_parts.fragment}")
            
            # 等待DOM内容加载
            try:
                await self.async_page.wait_for_load_state("domcontentloaded", timeout=timeout)
                logger.info("DOM内容加载完成")
            except Exception as e:
                logger.warning(f"等待DOM内容加载超时: {str(e)}")
            
            # 等待网络活动完成
            try:
                await self.async_page.wait_for_load_state("networkidle", timeout=timeout)
                logger.info("网络活动完成")
            except Exception as e:
                logger.warning(f"等待网络活动完成超时: {str(e)}")
            
            # 检查URL是否变化
            new_url = self.async_page.url
            new_parts = urlparse(new_url)
            
            if new_url != current_url:
                logger.info(f"页面URL已变化: {current_url} -> {new_url}")
                logger.info(f"页面路径已变化: {current_parts.path}#{current_parts.fragment} -> {new_parts.path}#{new_parts.fragment}")
                
                # 清除旧的高亮
                await self.clear_all_highlights_async()
                logger.info("已清除所有高亮元素，准备重新构建DOM")
            
            # 更新DOM状态
            await self._update_dom_after_action_async()
            
            logger.info("页面加载完成")
        except Exception as e:
            logger.error(f"等待页面加载完成失败: {str(e)}")
            raise

    async def locate_and_input_text(self, element_key: str, *text_args) -> None:
        """
        定位并在元素中输入文本
        
        Args:
            element_key: 元素标识
            *text_args: 要输入的文本，可以是多个参数，会被合并成一个完整的文本
        """
        self._verify_browser_state()
        # 将所有文本参数合并成一个字符串，保持原始空格
        text = " ".join(text_args)
        logger.info(f"在元素 {element_key} 中输入文本: {text}")
        
        try:
            # 先等待元素可见
            await self.wait_for_element_visible_async(element_key)
            
            # 找到元素并输入文本
            element = await self.find_element(element_key)
            await element.fill(text, timeout=10000)  # 使用10秒的超时时间
            
            # 更新DOM状态
            await self._update_dom_after_action_async()
            
            logger.info(f"成功在元素 {element_key} 中输入文本: {text}")
        except Exception as e:
            logger.error(f"输入文本失败: {str(e)}")
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            screenshot_path = os.path.join(
                os.getcwd(), 
                "screenshots", 
                f"error_{element_key}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            )
            os.makedirs(os.path.dirname(screenshot_path), exist_ok=True)
            await self.async_page.screenshot(path=screenshot_path)
            logger.info(f"失败截图已保存至: {screenshot_path}")
            raise
            
    async def verify_text(self, expected_text: str, actual_text: str = None) -> bool:
        """
        验证文本是否包含预期内容
        
        Args:
            expected_text: 预期文本
            actual_text: 实际文本，如果为None，则获取页面内容
            
        Returns:
            bool: 验证成功返回True，否则返回False
        """
        try:
            self._verify_browser_state()
            logger.info(f"验证文本 - 期望: '{expected_text}'，实际: {'页面内容' if actual_text is None else actual_text}")
            
            # 确定实际文本
            if actual_text is None:
                # 获取页面内容
                actual_text = await self.async_page.content()
            
            # 执行验证
            if expected_text in actual_text:
                logger.info(f"[✓成功] 文本验证成功: 找到 '{expected_text}'")
                return True
            else:
                logger.error(f"[✗失败] 文本验证失败: 未找到 '{expected_text}'")
                # 保存验证失败的截图
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                screenshot_path = os.path.join(
                    os.getcwd(), 
                    "screenshots", 
                    f"text_verification_error_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
                )
                os.makedirs(os.path.dirname(screenshot_path), exist_ok=True)
                await self.async_page.screenshot(path=screenshot_path)
                logger.info(f"验证失败截图已保存: {screenshot_path}")
                return False
        except Exception as e:
            logger.error(f"验证文本失败: {str(e)}")
            return False

    def _verify_browser_state(self) -> None:
        """
        验证浏览器是否处于可用状态
        
        Raises:
            ValueError: 浏览器未打开或状态异常时抛出
        """
        if not self.browser or not self.page or not self.context:
            raise ValueError("浏览器未打开")
        logger.info("浏览器状态验证成功")

    def wait_for_element_visible(self, element_key: str, timeout: int = None) -> bool:
        """
        等待元素可见 (同步版本，内部使用异步实现)
        
        Args:
            element_key: 元素标识
            timeout: 超时时间（秒），默认为30秒
            
        Returns:
            bool: 元素是否可见
        """
        try:
            # 使用同步包装运行异步方法
            return self._safe_run_sync(self.wait_for_element_visible_async(element_key, timeout))
        except Exception as e:
            logger.error(f"同步等待元素可见失败: {str(e)}")
            return False
            
    async def wait_for_element_visible_async(self, element_key: str, timeout: int = None) -> bool:
        """
        异步等待元素可见
        
        Args:
            element_key: 元素标识
            timeout: 超时时间（秒），默认为30秒
            
        Returns:
            bool: 元素是否可见
        """
        try:
            self._verify_browser_state()
            timeout = timeout or self.browser_config.get('timeout', 30)
            
            logger.info(f"等待元素可见: {element_key}, 超时: {timeout}秒")
            start_time = time.time()
            
            # 先同步当前域名
            self._synchronize_domain_with_element_manager()
            
            # 循环尝试定位元素
            while time.time() - start_time < timeout:
                try:
                    # 查找元素
                    element = await self.find_element(element_key)
                    
                    # 严格检查element是否为None
                    if element is None:
                        logger.info(f"未找到元素 {element_key}，重试中...")
                        await asyncio.sleep(0.5)
                        continue
                    
                    # 检查元素可见性
                    try:
                        # 使用异步API直接调用
                        is_visible = await element.is_visible(timeout=1000)
                        if is_visible:
                            logger.info(f"元素 {element_key} 已可见，耗时: {time.time() - start_time:.2f}秒")
                            return True
                    except Exception as visibility_err:
                        logger.info(f"检查元素可见性时出错: {str(visibility_err)}")
                    
                    # 等待后重试
                    await asyncio.sleep(0.5)
                except Exception as e:
                    logger.info(f"等待过程中出错: {str(e)}")
                    await asyncio.sleep(0.5)
            
            # 超时
            logger.warning(f"等待元素可见超时: {element_key}，耗时: {time.time() - start_time:.2f}秒")
            return False
        except Exception as e:
            logger.error(f"等待元素可见失败: {element_key} - {str(e)}")
            return False

    def _get_current_domain(self) -> str:
        if not self.page:
            return ""
        try:
            return urlparse(self.page.url).netloc
        except Exception as e:
            logger.error(f"获取当前域名失败: {str(e)}")
            return ""

    def save_screenshot(self, filename: str) -> None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        screenshot_path = os.path.join(
            self.browser_config['screenshot_dir'],
            f"{filename}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        )
        try:
            self.page.screenshot(path=screenshot_path)
            logger.info(f"截图已保存至: {screenshot_path}")
        except Exception as e:
            logger.error(f"保存截图失败: {str(e)}")

    def _synchronize_domain_with_element_manager(self) -> None:
        """同步当前域名，用于元素定位"""
        try:
            if not self.page:
                logger.warning("浏览器页面未初始化，无法同步域名")
                return
            
            # 获取当前URL
            current_url = self.page.url
            
            if not current_url:
                logger.warning("当前URL为空，无法提取域名")
                return
            
            # 解析URL获取域名
            from urllib.parse import urlparse
            parsed_url = urlparse(current_url)
            new_domain = parsed_url.netloc
            
            # 记录之前的域名以便比较
            old_domain = getattr(self, 'current_domain', None)
            
            # 更新当前域名
            self.current_domain = new_domain
            
            # 记录域名变化
            if old_domain != new_domain:
                logger.info(f"域名已更新: {old_domain} -> {new_domain}")
            
            # 验证域名在元素库中是否存在(不再调用get_available_domains)
            try:
                if hasattr(self, 'element_manager') and self.element_manager:
                    # 检查域名是否在元素库中 - 直接访问locators字典
                    if hasattr(self.element_manager, 'locators') and self.element_manager.locators:
                        if self.current_domain in self.element_manager.locators:
                            logger.info(f"当前域名 {self.current_domain} 在元素库中")
                        else:
                            logger.info(f"当前域名 {self.current_domain} 不在元素库中，将使用实时定位")
            except Exception as domain_check_err:
                logger.info(f"检查域名是否在元素库中时出错: {str(domain_check_err)}")
        
        except Exception as e:
            logger.error(f"同步域名失败: {str(e)}")

    async def _update_dom_after_action_async(self) -> None:
        """
        异步在操作后更新DOM状态
        """
        if not hasattr(self.element_manager, 'update_dom_state_async'):
            return
            
        try:
            # 首先等待一小段时间，确保DOM更新
            await asyncio.sleep(0.5)
            
            # 检查URL是否发生变化（包括路径部分）
            current_url = self.async_page.url
            previous_url = getattr(self, 'previous_url', None)
            
            # 解析URL以比较路径变化
            current_parts = urlparse(current_url)
            previous_parts = urlparse(previous_url) if previous_url else None
            
            # 检测URL变化，特别注意路径和hash的变化
            url_changed = False
            path_changed = False
            
            if previous_url and current_url != previous_url:
                url_changed = True
                logger.info(f"URL已变化: {previous_url} -> {current_url}")
                
                # 检查路径是否变化（对SPA应用很重要）
                if previous_parts and current_parts:
                    if (previous_parts.path != current_parts.path or 
                        previous_parts.fragment != current_parts.fragment):
                        path_changed = True
                        logger.info(f"URL路径已变化: {previous_parts.path}#{previous_parts.fragment} -> {current_parts.path}#{current_parts.fragment}")
            
            # 无论URL是否变化，确保清除所有旧的高亮
            try:
                # 强制清除所有高亮元素
                await self.clear_all_highlights_async()
                logger.info("已清除所有高亮元素以确保页面干净")
            except Exception as clear_err:
                logger.warning(f"清除高亮元素失败: {str(clear_err)}")
            
            # 如果URL或路径变化了，进行额外等待以确保页面完全加载
            if url_changed or path_changed:
                # 增加一个额外等待，确保页面完全加载
                try:
                    await self.async_page.wait_for_load_state("domcontentloaded", timeout=3000)
                    await self.async_page.wait_for_load_state("networkidle", timeout=3000)
                    logger.info("页面加载完成，准备更新DOM状态")
                except Exception as load_err:
                    logger.warning(f"等待页面加载完成超时，但将继续: {str(load_err)}")
            
            # 保存当前URL以便下次比较
            self.previous_url = current_url
            
            # 等待页面加载状态
            try:
                await self.async_page.wait_for_load_state("domcontentloaded", timeout=5000)
            except Exception as e:
                logger.warning(f"等待dom加载状态超时，但将继续: {str(e)}")
            
            # 尝试等待网络活动完成
            try:
                await self.async_page.wait_for_load_state("networkidle", timeout=5000)
            except Exception as e:
                logger.warning(f"等待网络活动完成超时，但将继续: {str(e)}")
            
            # 如果URL或路径变化了，强制更新域名同步
            if url_changed or path_changed:
                self._synchronize_domain_with_element_manager()
                
            # 执行额外强制清除高亮的JavaScript操作
            try:
                await self.async_page.evaluate("""() => {
                    // 移除所有高亮元素
                    const highlights = document.querySelectorAll('.browser-use-highlight');
                    highlights.forEach(el => el.remove());
                    
                    // 移除所有高亮相关的CSS效果
                    const highlighted = document.querySelectorAll('[data-highlighted="true"]');
                    highlighted.forEach(el => {
                        el.removeAttribute('data-highlighted');
                        el.style.removeProperty('border');
                        el.style.removeProperty('box-shadow');
                        el.style.removeProperty('outline');
                    });
                }""")
                logger.info("执行额外的JavaScript清除所有高亮元素")
            except Exception as js_err:
                logger.warning(f"执行JavaScript清除高亮失败: {str(js_err)}")
                
            # 更新DOM状态
            logger.info("正在异步更新DOM状态...")
            await self.element_manager.update_dom_state_async()
            logger.info("DOM状态异步更新完成")
        except Exception as e:
            logger.warning(f"异步更新DOM状态失败, 但将继续执行: {str(e)}")

    def get_url(self) -> str:
        """
        获取当前页面URL
        
        Returns:
            str: 当前页面URL
        """
        if not self.page:
            return None
        
        try:
            return self.page.url
        except Exception as e:
            logger.error(f"获取当前URL失败: {str(e)}")
            return None

    # 添加安全的异步转同步方法
    def _safe_run_sync(self, coroutine):
        """
        安全地同步运行协程函数
        
        Args:
            coroutine: 协程对象
            
        Returns:
            协程的结果
        """
        if asyncio.iscoroutine(coroutine):
            try:
                if self.loop and self.loop.is_running():
                    # 使用已有事件循环
                    return asyncio.run_coroutine_threadsafe(coroutine, self.loop).result()
                else:
                    # 创建新的事件循环
                    return asyncio.run(coroutine)
            except Exception as e:
                logger.error(f"运行协程出错: {str(e)}")
                raise
        else:
            # 不是协程，直接返回
            return coroutine
    
    def _get_version(self) -> str:
        """
        获取框架版本
        
        Returns:
            str: 版本号
        """
        return "1.0.0"

    def sync_to_async(self, func):
        """
        将同步函数转换为异步函数的装饰器
        
        用法:
            @sync_to_async
            def some_sync_function():
                pass
        
        Args:
            func: 要转换的同步函数
            
        Returns:
            转换后的异步函数
        """
        import functools
        
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # 在线程池中执行同步函数
            return await asyncio.to_thread(func, *args, **kwargs)
        
        return wrapper
        
    def _get_version(self) -> str:
        """
        获取框架版本
        
        Returns:
            str: 版本号
        """
        return "1.0.0"

    async def _find_element_in_library_async(self, element_key: str) -> Optional[AsyncLocator]:
        """
        异步从元素库中查找元素
        
        Args:
            element_key: 元素标识
            
        Returns:
            Optional[AsyncLocator]: 找到的元素定位器，未找到则返回None
        """
        try:
            # 确保元素管理器已初始化
            if not self.element_manager:
                logger.error("元素管理器未初始化")
                return None
                
            # 如果元素管理器支持异步查找，使用异步方法
            if hasattr(self.element_manager, 'find_element_async'):
                return await self.element_manager.find_element_async(element_key)
            
            # 否则使用同步方法包装为异步
            return await asyncio.to_thread(self.element_manager.find_element, element_key)
        except Exception as e:
            logger.error(f"从元素库中异步查找元素失败: {str(e)}")
            return None

    async def find_element(self, element_key: str) -> Optional[Locator]:
        """
        查找元素(公共接口)
        
        Args:
            element_key: 元素标识
            
        Returns:
            找到的元素定位器，未找到则返回None
        """
        return await self._find_element(element_key)

    async def clear_all_highlights_async(self):
        """
        清除页面上所有的高亮元素（异步版本）
        """
        try:
            if not self.page:
                logger.warning("页面未初始化，无法清除高亮元素")
                return False
                
            await self.page.evaluate("""() => {
                // 移除所有现有的高亮元素
                const highlights = document.querySelectorAll('.browser-use-highlight');
                highlights.forEach(el => el.remove());
                
                // 移除可能的边框高亮样式
                const elements = document.querySelectorAll('[data-highlighted="true"]');
                elements.forEach(el => {
                    el.removeAttribute('data-highlighted');
                    el.style.removeProperty('border');
                    el.style.removeProperty('box-shadow');
                });
            }""")
            
            logger.info("已清除所有高亮元素")
            return True
        except Exception as e:
            logger.error(f"清除高亮元素失败: {str(e)}")
            return False

    async def set_video_recording(self, enable: bool = True) -> None:
        """
        设置是否启用视频录制
        
        Args:
            enable: 是否启用视频录制，默认为True
        
        注意: 此方法需要在打开浏览器前调用才能生效。如果浏览器已经打开，需要关闭后重新打开。
        """
        try:
            if enable:
                logger.info("启用视频录制功能")
                # 确保video_dir存在
                if not self.browser_config.get('video_dir'):
                    logger.warning("video_dir未配置，使用默认路径")
                    self.browser_config['video_dir'] = os.path.join(os.getcwd(), "videos")
                
                # 确保目录存在
                os.makedirs(self.browser_config['video_dir'], exist_ok=True)
            else:
                logger.info("禁用视频录制功能")
                # 设置为None以禁用视频录制
                self.browser_config['video_dir'] = None
            
            # 如果浏览器已经打开，提示用户
            if self.browser or self.async_browser:
                logger.warning("浏览器已经打开，视频录制设置将在下次打开浏览器时生效")
                
            return True
        except Exception as e:
            logger.error(f"设置视频录制功能时出错: {str(e)}")
            raise
    
    async def get_last_recorded_video(self) -> str:
        """
        获取最近录制的视频文件路径
        
        返回最近录制的视频文件的完整路径。如果没有找到视频文件，则返回None。
        
        Returns:
            str: 视频文件路径，如果未找到则返回None
        """
        try:
            video_dir = self.browser_config.get('video_dir')
            if not video_dir or not os.path.exists(video_dir):
                logger.warning("视频目录不存在或未配置")
                return None
                
            # 查找所有.webm文件（Playwright默认的视频格式）
            video_files = [os.path.join(video_dir, f) for f in os.listdir(video_dir) 
                        if f.endswith('.webm')]
            
            if not video_files:
                logger.warning("未找到任何录制的视频文件")
                return None
                
            # 按文件修改时间排序，获取最新的
            latest_video = sorted(video_files, key=os.path.getmtime, reverse=True)[0]
            video_size = os.path.getsize(latest_video) / (1024 * 1024)  # 转换为MB
            
            logger.info(f"找到最近录制的视频: {latest_video} (大小: {video_size:.2f} MB)")
            return latest_video
            
        except Exception as e:
            logger.error(f"获取最近视频文件时出错: {str(e)}")
            return None
    
    async def clean_video_files(self, keep_latest: int = 5) -> None:
        """
        清理旧的视频文件，只保留最新的若干个视频
        
        Args:
            keep_latest: 保留的最新视频数量，默认为5
        
        Returns:
            None
        """
        try:
            video_dir = self.browser_config.get('video_dir')
            if not video_dir or not os.path.exists(video_dir):
                logger.warning("视频目录不存在或未配置，无需清理")
                return
                
            # 查找所有.webm文件
            video_files = [os.path.join(video_dir, f) for f in os.listdir(video_dir) 
                        if f.endswith('.webm')]
            
            if not video_files:
                logger.info("未找到任何视频文件，无需清理")
                return
                
            # 如果视频文件数量不超过保留数量，则无需清理
            if len(video_files) <= keep_latest:
                logger.info(f"视频文件数量({len(video_files)})不超过保留数量({keep_latest})，无需清理")
                return
            
            # 按修改时间排序
            video_files.sort(key=os.path.getmtime, reverse=True)
            
            # 保留最新的keep_latest个文件，删除其余文件
            files_to_delete = video_files[keep_latest:]
            total_size = 0
            
            for file_path in files_to_delete:
                try:
                    file_size = os.path.getsize(file_path) / (1024 * 1024)  # 转换为MB
                    total_size += file_size
                    os.remove(file_path)
                    logger.debug(f"已删除旧视频文件: {file_path} (大小: {file_size:.2f} MB)")
                except Exception as e:
                    logger.warning(f"删除视频文件时出错: {file_path}, {str(e)}")
            
            logger.info(f"已清理 {len(files_to_delete)} 个旧视频文件，释放空间: {total_size:.2f} MB")
            
        except Exception as e:
            logger.error(f"清理视频文件时出错: {str(e)}")
            
    async def get_video_files_info(self) -> Dict[str, Any]:
        """
        获取视频文件的相关信息
        
        Returns:
            Dict: 包含视频文件信息的字典
        """
        try:
            video_dir = self.browser_config.get('video_dir')
            if not video_dir or not os.path.exists(video_dir):
                return {"status": "error", "message": "视频目录不存在或未配置"}
                
            # 查找所有.webm文件
            video_files = [os.path.join(video_dir, f) for f in os.listdir(video_dir) 
                        if f.endswith('.webm')]
            
            if not video_files:
                return {"status": "info", "message": "未找到任何视频文件", "count": 0}
            
            # 计算总空间占用
            total_size = sum(os.path.getsize(f) for f in video_files) / (1024 * 1024)  # 转换为MB
            
            # 按修改时间排序
            video_files.sort(key=os.path.getmtime, reverse=True)
            
            # 获取每个文件的详细信息
            file_details = []
            for file_path in video_files:
                file_name = os.path.basename(file_path)
                file_size = os.path.getsize(file_path) / (1024 * 1024)  # 转换为MB
                modify_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                
                file_details.append({
                    "name": file_name,
                    "path": file_path,
                    "size_mb": round(file_size, 2),
                    "modified": modify_time.strftime("%Y-%m-%d %H:%M:%S")
                })
            
            return {
                "status": "success",
                "directory": video_dir,
                "count": len(video_files),
                "total_size_mb": round(total_size, 2),
                "files": file_details
            }
            
        except Exception as e:
            logger.error(f"获取视频文件信息时出错: {str(e)}")
    
    async def expand_tree_dropdown(self, element_key: str, wait_time: int = 1) -> bool:
        """
        展开树形组件
        
        通过点击输入框元素触发React组件的显示。适用于点击后动态创建树形结构的下拉框组件。
        
        Args:
            element_key: 元素标识符（输入框元素）
            wait_time: 展开后等待时间(秒)，确保DOM树完全加载
            
        Returns:
            bool: 操作是否成功
        """
        logger.info(f"开始展开树形下拉框: {element_key}")
        
        try:
            # 首先尝试定位输入框元素
            element = await self._find_element(element_key)
            if not element:
                logger.error(f"无法找到下拉框输入元素: {element_key}")
                return False
                
            # 点击输入框触发下拉框展开
            await element.click()
            logger.info(f"已点击输入框，触发树形组件展开")
            
            # 等待树形组件加载
            await asyncio.sleep(wait_time)
            
            # 清除之前的DOM分析状态并重新分析DOM树
            await self._update_dom_after_action_async()
            
            # 注入JavaScript检查是否存在树形结构
            has_tree = await self.page.evaluate("""
                () => {
                    // 查找可能的树形结构标记
                    const treeElements = document.querySelectorAll('ul[role="tree"], div[role="tree"], .ant-tree, .tree-wrapper, .tree-component');
                    if (treeElements && treeElements.length > 0) {
                        return true;
                    }
                    
                    // 如果没有明确的树形角色，查找常见树形结构的特征
                    const possibleTreeLists = document.querySelectorAll('ul li ul, div[class*="tree"], [class*="dropdown"][class*="menu"], [class*="select"][class*="dropdown"]');
                    return possibleTreeLists && possibleTreeLists.length > 0;
                }
            """)
            
            if has_tree:
                logger.info(f"树形下拉框组件已成功展开")
                return True
            else:
                # 尝试再次点击，某些组件可能需要第二次点击才能完全展开
                await element.click()
                await asyncio.sleep(wait_time)
                await self._update_dom_after_action_async()
                
                has_tree_retry = await self.page.evaluate("""() => {
                    const treeElements = document.querySelectorAll('ul[role="tree"], div[role="tree"], .ant-tree, .tree-wrapper, .tree-component, ul li ul, div[class*="tree"], [class*="dropdown"][class*="menu"], [class*="select"][class*="dropdown"]');
                    return treeElements && treeElements.length > 0;
                }""")
                
                if has_tree_retry:
                    logger.info(f"树形下拉框组件在第二次尝试后成功展开")
                    return True
                else:
                    logger.warning(f"未检测到树形结构，组件可能未成功展开")
                    return False
                
        except Exception as e:
            logger.error(f"展开树形下拉框时出错: {str(e)}")
            return False
            
    async def select_tree_node(self, node_text: str, expand_parent: bool = True, partial_match: bool = True) -> bool:
        """
        选择树形结构中的节点
        
        查找并点击树形组件中包含指定文本的节点。支持部分文本匹配和自动展开父节点。
        
        Args:
            node_text: 节点文本
            expand_parent: 是否自动展开父节点以找到子节点
            partial_match: 是否允许部分文本匹配
            
        Returns:
            bool: 操作是否成功
        """
        logger.info(f"开始选择树形节点: {node_text}")
        
        try:
            # 为文本查询构建 CSS 选择器
            escape_text = node_text.replace("'", "\\'")
            query_contains = f":contains('{escape_text}')" if partial_match else f":text('{escape_text}')"
            
            # 检查当前DOM中是否有树形结构
            tree_exists = await self.page.evaluate("""
                () => {
                    // 各种可能的树形组件标记
                    const selectors = [
                        'ul[role="tree"]', 
                        'div[role="tree"]', 
                        '.ant-tree',
                        '.tree-wrapper',
                        '.tree-component',
                        '[class*="tree"]',
                        '[class*="dropdown"][class*="menu"]',
                        '[class*="select"][class*="dropdown"]',
                        'ul li ul'
                    ];
                    
                    for (const selector of selectors) {
                        if (document.querySelector(selector)) {
                            return true;
                        }
                    }
                    return false;
                }
            """)
            
            if not tree_exists:
                logger.error("当前页面中没有检测到树形结构")
                return False
            
            # 如果需要展开父节点，先执行展开操作
            if expand_parent:
                # 注入jQuery辅助函数，帮助查找和展开节点
                await self.page.evaluate("""
                    () => {
                        if (!window.jQuery) {
                            // 添加简单的文本选择器实现
                            const findElementsWithText = (text, partial) => {
                                const result = [];
                                const walk = (node) => {
                                    if (node.nodeType === Node.TEXT_NODE) {
                                        const content = node.textContent.trim();
                                        if ((partial && content.includes(text)) || content === text) {
                                            result.push(node.parentNode);
                                        }
                                    } else {
                                        for (const child of node.childNodes) {
                                            walk(child);
                                        }
                                    }
                                };
                                walk(document.body);
                                return result;
                            };
                            
                            // 将函数添加到window对象
                            window.findElementsWithText = findElementsWithText;
                        }
                    }
                """)
                
                # 尝试展开可能的父节点
                await self.page.evaluate(f"""
                    (nodeText, partialMatch) => {{
                        // 使用我们的自定义函数查找节点
                        const nodeElements = window.findElementsWithText(nodeText, partialMatch);
                        
                        // 对于每个找到的文本节点，查找父节点
                        for (let element of nodeElements) {{
                            // 向上查找可能的节点控件
                            let current = element;
                            let found = false;
                            
                            // 向上最多查找5层父节点
                            for (let i = 0; i < 5 && current; i++) {{
                                // 寻找展开/折叠控件的特征
                                const expandIcon = current.querySelector('[class*="expand"], [class*="toggle"], [class*="arrow"], [class*="icon"]');
                                if (expandIcon) {{
                                    expandIcon.click();
                                    found = true;
                                    console.log('展开了父节点');
                                    break;
                                }}
                                current = current.parentNode;
                            }}
                            
                            if (found) {{
                                // 如果找到并点击了展开控件，等待一段时间让子节点渲染
                                return true;
                            }}
                        }}
                        return false;
                    }}
                """, node_text, partial_match)
                
                # 等待展开动画完成
                await asyncio.sleep(1)
            
            # 更新DOM状态以获取新的节点
            await self._update_dom_after_action_async()
            
            # 使用各种定位策略查找节点
            selectors_to_try = [
                # 1. 使用text定位器
                f":text('{escape_text}')",
                f":text-is('{escape_text}')",
                
                # 2. 使用包含文本的定位器
                f":text-matches('{escape_text}')",
                
                # 3. 基于DOM属性的定位
                f"[aria-label='{escape_text}']",
                f"[title='{escape_text}']",
                
                # 4. 组合定位
                f".ant-tree-node-content-wrapper:has(:text('{escape_text}'))",
                f".tree-node:has(:text('{escape_text}'))",
                f"li:has(:text('{escape_text}'))",
                
                # 5. 使用data属性
                f"[data-label='{escape_text}']",
                f"[data-value='{escape_text}']",
                
                # 6. CSS选择器（处理节点内容）
                f"span:text('{escape_text}')"
            ]
            
            # 依次尝试各种定位策略
            for selector in selectors_to_try:
                try:
                    # 检测此选择器是否有匹配元素
                    count = await self.page.locator(selector).count()
                    if count > 0:
                        # 找到匹配的元素，点击它
                        await self.page.locator(selector).first.click()
                        logger.info(f"已点击树形节点: {node_text}")
                        
                        # 等待可能的组件状态变化
                        await asyncio.sleep(0.5)
                        await self._update_dom_after_action_async()
                        return True
                except Exception as selector_err:
                    logger.debug(f"选择器 {selector} 失败: {str(selector_err)}")
                    continue
            
            # 如果前面的尝试都失败，使用自定义JavaScript查找
            found = await self.page.evaluate(f"""
                (nodeText, partialMatch) => {{
                    try {{
                        // 使用创建的辅助函数
                        const elements = window.findElementsWithText(nodeText, partialMatch);
                        
                        if (elements && elements.length > 0) {{
                            // 找到第一个可见元素
                            for (const el of elements) {{
                                // 检查元素是否可见
                                const rect = el.getBoundingClientRect();
                                if (rect.width > 0 && rect.height > 0) {{
                                    // 点击元素
                                    el.click();
                                    return true;
                                }}
                            }}
                        }}
                        return false;
                    }} catch (e) {{
                        console.error('查找节点时出错:', e);
                        return false;
                    }}
                }}
            """, node_text, partial_match)
            
            if found:
                logger.info(f"通过JavaScript成功点击树形节点: {node_text}")
                await asyncio.sleep(0.5)
                await self._update_dom_after_action_async()
                return True
                
            logger.error(f"未能找到并点击树形节点: {node_text}")
            return False
            
        except Exception as e:
            logger.error(f"选择树形节点时出错: {str(e)}")
            return False
    
    async def select_department_from_tree(self, department_name: str) -> bool:
        """
        从部门/组织树形结构中选择特定部门
        
        专门针对React创建的部门/组织树形选择框，例如用户截图中的部门选择界面。
        此方法会点击输入框，等待树形结构加载，然后选择指定部门。
        
        Args:
            department_name: 部门/组织名称
            
        Returns:
            bool: 操作是否成功
        """
        logger.info(f"开始从部门树中选择部门: {department_name}")
        
        try:
            # 1. 先尝试定位部门输入框
            department_input_locators = [
                "部门/组织",
                "部门",
                "组织",
                "所属部门",
                "[placeholder='请选择部门/组织']",
                "[placeholder='请选择']",
                "input[placeholder*='部门']",
                "input[placeholder*='组织']"
            ]
            
            input_element = None
            for locator in department_input_locators:
                try:
                    # 尝试定位输入框
                    if locator.startswith("[") or locator.startswith("input"):
                        # CSS选择器定位
                        count = await self.page.locator(locator).count()
                        if count > 0:
                            input_element = self.page.locator(locator).first
                            logger.info(f"通过CSS选择器 '{locator}' 找到部门输入框")
                            break
                    else:
                        # 文本定位
                        count = await self.page.get_by_text(locator, exact=False).count()
                        if count > 0:
                            # 找到标签后，尝试找相邻的输入框
                            label = self.page.get_by_text(locator, exact=False).first
                            
                            # 注入JavaScript查找相关输入框
                            input_selector = await self.page.evaluate("""
                                (labelText) => {
                                    // 使用文本内容查找标签元素
                                    const textNodes = [];
                                    const walk = document.createTreeWalker(
                                        document.body, 
                                        NodeFilter.SHOW_TEXT,
                                        null,
                                        false
                                    );
                                    
                                    let node;
                                    while(node = walk.nextNode()) {
                                        if (node.textContent.includes(labelText)) {
                                            textNodes.push(node.parentNode);
                                        }
                                    }
                                    
                                    // 对于每个找到的标签元素
                                    for (const label of textNodes) {
                                        // 1. 检查标签是否有for属性
                                        if (label.hasAttribute && label.hasAttribute('for')) {
                                            const forId = label.getAttribute('for');
                                            return `#${forId}`;
                                        }
                                        
                                        // 2. 查找父元素中的输入框
                                        let parent = label.parentNode;
                                        for (let i = 0; i < 3 && parent; i++) {
                                            const input = parent.querySelector('input, .ant-select-selector, [class*="select"], [class*="dropdown"]');
                                            if (input) {
                                                // 返回该元素的唯一选择器
                                                let classes = Array.from(input.classList).join('.');
                                                if (classes) classes = '.' + classes;
                                                return input.id ? `#${input.id}` : (classes || null);
                                            }
                                            parent = parent.parentNode;
                                        }
                                        
                                        // 3. 查找兄弟元素中的输入框
                                        const siblings = label.parentNode.children;
                                        for (const sibling of siblings) {
                                            if (sibling === label) continue;
                                            if (sibling.tagName === 'INPUT' || 
                                                sibling.classList.contains('ant-select-selector') || 
                                                sibling.querySelector('input')) {
                                                return sibling.id ? `#${sibling.id}` : null;
                                            }
                                        }
                                    }
                                    return null;
                                }
                            """, locator)
                            
                            if input_selector:
                                input_element = self.page.locator(input_selector).first
                                logger.info(f"通过文本标签 '{locator}' 找到部门输入框")
                                break
                except Exception as e:
                    logger.debug(f"尝试定位器 '{locator}' 失败: {str(e)}")
            
            # 如果没有找到输入框，尝试识别特定部门字段
            if not input_element:
                # 注入JavaScript专门查找部门选择框
                input_selector = await self.page.evaluate("""
                    () => {
                        // 专门查找部门选择框的特征
                        // 1. 查找包含"部门"或"组织"的label附近的输入元素
                        const labels = Array.from(document.querySelectorAll('label, span, div'))
                            .filter(el => el.textContent.includes('部门') || el.textContent.includes('组织'));
                        
                        for (const label of labels) {
                            const input = label.parentElement.querySelector('input, .ant-select-selector');
                            if (input) {
                                return input.id ? `#${input.id}` : '.ant-select-selector';
                            }
                        }
                        
                        // 2. 直接查找部门选择器常见类名
                        const selectors = [
                            '.ant-select-selector',
                            '.department-select',
                            '.org-select',
                            '[class*="department"]',
                            '[class*="org-select"]'
                        ];
                        
                        for (const selector of selectors) {
                            const el = document.querySelector(selector);
                            if (el) return selector;
                        }
                        
                        return null;
                    }
                """)
                
                if input_selector:
                    input_element = self.page.locator(input_selector).first
                    logger.info(f"通过特殊选择器找到部门输入框: {input_selector}")
            
            # 如果仍未找到输入框，尝试使用截图中显示的"部门/组织"文本定位旁边的输入框
            if not input_element:
                # 截图中的案例：点击显示"请选择部门/组织"的输入框
                dept_field = await self.page.get_by_text("部门/组织", exact=True).nth(0)
                print(f"---------部门/组织: {dept_field}")
                
                # 先获取位置信息
                box = await dept_field.bounding_box()
                x_pos = box['x'] + box['width']
                y_pos = box['y'] + box['height'] / 2

                # 然后传递给evaluate函数
                await self.page.evaluate("""
                    async (x, y) => {
                        // 创建一个简单的查找附近元素的函数
                        const findClickableNearby = (x, y, radius = 200) => {
                            const elements = [];
                            // 收集所有在指定半径内的可点击元素
                            document.querySelectorAll('input, .ant-select-selector, [class*="select"], [role="combobox"], [class*="input"]')
                                .forEach(el => {
                                    const rect = el.getBoundingClientRect();
                                    const centerX = rect.left + rect.width / 2;
                                    const centerY = rect.top + rect.height / 2;
                                    const distance = Math.sqrt(Math.pow(centerX - x, 2) + Math.pow(centerY - y, 2));
                                    
                                    if (distance < radius) {
                                        elements.push({element: el, distance});
                                    }
                                });
                            
                            // 按距离排序
                            elements.sort((a, b) => a.distance - b.distance);
                            return elements.map(item => item.element);
                        };
                        
                        // 查找附近元素
                        const nearbyElements = findClickableNearby(x, y);
                        
                        // 点击最近的元素
                        if (nearbyElements.length > 0) {
                            // 添加一个临时标记，以便后续定位
                            nearbyElements[0].setAttribute('data-dept-input', 'true');
                        }
                    }
                """, x_pos, y_pos)
                
                # 尝试定位标记的元素
                marked_input = self.page.locator('[data-dept-input="true"]')
                if await marked_input.count() > 0:
                    input_element = marked_input.first
                    logger.info("通过临近定位策略找到部门输入框")
            
            # 如果找到了输入框，点击展开树形结构
            if input_element:
                await input_element.click()
                logger.info("已点击部门输入框，等待树形结构加载")
                
                # 等待树形结构加载
                await asyncio.sleep(1)
                await self._update_dom_after_action_async()
                
                # 查找并选择指定部门
                return await self.select_tree_node(department_name, expand_parent=True, partial_match=True)
            else:
                logger.error("未能找到部门输入框")
                return False
            
        except Exception as e:
            logger.error(f"选择部门时出错: {str(e)}")
            return False
    
    async def navigate_organization_tree(self, path: str, input_element_key: str = None, wait_time: float = 0.5) -> bool:
        """
        在组织树中导航并选择节点
        
        依次展开和选择组织树中的节点，支持多级路径。
        【重要】展开节点操作只能点击节点左侧的三角形图标（svg标签），而不是点击节点文本。
        点击节点文本是选择操作，通常只对最后一个节点执行。
        
        Args:
            path: 组织路径，格式如 "辽阳市兴宇纸业有限公司 > 科技中心 > 质量中心 > 效能组"，
                 支持 ">" 或 "/" 作为分隔符
            input_element_key: 可选，触发组织树展开的输入框元素标识，如 "部门/组织输入框"
            wait_time: 每次操作后的等待时间(秒)，默认0.5秒
            
        Returns:
            bool: 操作是否成功
        """
        # 记录操作开始，并输出路径信息
        logger.info(f"开始导航组织树: {path}")
        
        try:
            # 解析路径字符串，支持多种分隔符格式
            if ">" in path:
                # 如果路径使用 > 分隔，按 > 拆分并去除每部分的空白
                nodes = [node.strip() for node in path.split(">")]
            elif "/" in path:
                # 如果路径使用 / 分隔，按 / 拆分并去除每部分的空白
                nodes = [node.strip() for node in path.split("/")]
            else:
                # 如果没有分隔符，视为单一节点
                nodes = [path.strip()]
            
            # 如果提供了输入框元素标识，先点击该元素触发树的展开
            if input_element_key:
                # 使用expand_tree_dropdown方法点击输入框，触发树形组件的显示
                success = await self.expand_tree_dropdown(input_element_key, wait_time=wait_time)
                if not success:
                    # 如果展开失败，记录错误并返回
                    logger.error(f"无法展开树形组件: {input_element_key}")
                    return False
                # 等待树形组件完全展开
                await asyncio.sleep(wait_time)
            
            # 更新DOM状态，确保能够找到新加载的节点元素
            await self._update_dom_after_action_async()
            
            # 初始化当前层级，用于记录导航深度
            current_level = 0
            
            # 依次处理路径中的每个节点
            for i, node_text in enumerate(nodes):
                # 判断当前节点是否是路径中的最后一个节点
                is_last_node = (i == len(nodes) - 1)
                # 记录当前处理的节点信息
                logger.info(f"处理第 {i+1}/{len(nodes)} 级节点: {node_text}")
                
                # 使用JavaScript在页面中查找和操作节点
                # 这样可以提高在复杂DOM结构中定位元素的准确性
                found = await self.page.evaluate("""
                    (nodeText, isLastNode, currentLevel) => {
                        // 定义查找包含指定文本的节点的函数
                        const findNodeElement = (text) => {
                            // 定义多种可能的节点选择器，按优先级排序
                            const selectors = [
                                // 常见的树节点类名选择器，优先使用标准树组件的节点类
                                '.ant-tree-node-content-wrapper, .ant-select-tree-node-content-wrapper',
                                // 带有文本的span元素，常用于显示节点文本
                                'span',
                                // 带有title属性的元素，可能包含节点文本
                                '[title]',
                                // 最后尝试一般的div或li元素
                                'div, li'
                            ];
                            
                            // 遍历所有选择器，尝试找到匹配的节点
                            for (const selector of selectors) {
                                const elements = Array.from(document.querySelectorAll(selector));
                                for (const el of elements) {
                                    // 检查元素文本或title属性是否与目标文本完全匹配
                                    if (el.textContent && el.textContent.trim() === text || 
                                        (el.getAttribute('title') && el.getAttribute('title').trim() === text)) {
                                        return el;
                                    }
                                }
                            }
                            // 如果没找到匹配的节点，返回null
                            return null;
                        };
                        
                        // 使用上面定义的函数查找当前节点
                        const nodeElement = findNodeElement(nodeText);
                        if (!nodeElement) {
                            // 如果未找到节点，记录错误并返回失败状态
                            console.error(`未找到节点: ${nodeText}`);
                            return { success: false, message: '未找到节点' };
                        }
                        
                        try {
                            // 根据节点在路径中的位置执行不同操作
                            if (!isLastNode) {
                                // 如果不是最后一个节点，需要展开它（点击三角形图标）
                                // 重要：展开操作必须点击展开图标，而不是节点文本
                                
                                // 初始化展开图标变量
                                let expandIcon = null;
                                
                                // 1. 首先查找节点相关元素中的展开图标（通常是左侧的三角形）
                                let parent = nodeElement.parentNode;
                                if (parent) {
                                    // 查找常见的展开图标类名
                                    expandIcon = parent.querySelector('.ant-tree-switcher, .tree-expand-icon, .expand-icon, [class*="expand"], [class*="switcher"]');
                                    
                                    // 如果在直接父节点中未找到，向上再找一层
                                    if (!expandIcon && parent.parentNode) {
                                        expandIcon = parent.parentNode.querySelector('.ant-tree-switcher, .tree-expand-icon, .expand-icon, [class*="expand"], [class*="switcher"]');
                                    }
                                }
                                
                                // 2. 如果在相关元素中未找到展开图标，尝试精确定位节点左侧的三角形元素
                                if (!expandIcon) {
                                    // 获取节点元素的位置信息
                                    const rect = nodeElement.getBoundingClientRect();
                                    // 使用elementsFromPoint获取节点左侧位置上的所有元素
                                    // 偏移15像素，确保能够找到左侧的三角形图标
                                    const elementsAtLeft = document.elementsFromPoint(rect.left - 15, rect.top + rect.height/2);
                                    
                                    // 遍历左侧元素，查找符合展开图标特征的元素
                                    for (const el of elementsAtLeft) {
                                        if (el.classList && (
                                            el.classList.contains('ant-tree-switcher') || 
                                            el.classList.contains('tree-expand-icon') || 
                                            el.classList.contains('expand-icon') ||
                                            el.className.includes('expand') || 
                                            el.className.includes('switcher')
                                        )) {
                                            // 找到符合条件的元素，将其作为展开图标
                                            expandIcon = el;
                                            break;
                                        }
                                    }
                                }
                                
                                // 如果成功找到展开图标
                                if (expandIcon) {
                                    // 检查节点当前是否是折叠状态
                                    // 通常通过类名判断：包含'close'或不包含'open'的图标是折叠状态
                                    const isCollapsed = expandIcon.className.includes('close') || 
                                                        !expandIcon.className.includes('open');
                                    
                                    if (isCollapsed) {
                                        // 如果图标是折叠状态，点击它展开节点
                                        // 注意：这里点击的是三角形图标，不是节点文本
                                        expandIcon.click();
                                        console.log(`展开了节点: ${nodeText}`);
                                        return { success: true, action: 'expanded' };
                                    } else {
                                        // 如果图标已是展开状态，不需要再次点击
                                        console.log(`节点已展开: ${nodeText}`);
                                        return { success: true, action: 'already-expanded' };
                                    }
                                } else {
                                    // 如果未找到展开图标，可能节点已被自动展开或没有子节点
                                    console.log(`未找到展开图标，可能节点已自动展开或无子节点: ${nodeText}`);
                                    return { success: true, action: 'no-expand-icon' };
                                }
                            } else {
                                // 这是路径中的最后一个节点，执行选择操作（点击节点文本）
                                // 重要：选择操作是点击节点文本，而不是三角形图标
                                nodeElement.click();
                                console.log(`选择了节点: ${nodeText}`);
                                return { success: true, action: 'selected' };
                            }
                        } catch (error) {
                            // 捕获并记录操作过程中的任何错误
                            console.error(`操作节点错误: ${error.message}`);
                            return { success: false, message: error.message };
                        }
                    }
                """, node_text, is_last_node, current_level)
                
                # 处理JavaScript返回的操作结果
                if found and found.get('success', False):
                    if found.get('action') in ['expanded', 'no-expand-icon', 'already-expanded']:
                        # 如果节点已被展开或尝试展开（通过点击三角形图标）
                        logger.info(f"节点 '{node_text}' {found.get('action')}")
                        # 等待子节点加载完成
                        await asyncio.sleep(wait_time)
                        # 更新DOM状态，确保能够找到新加载的子节点
                        await self._update_dom_after_action_async()
                        # 层级加1，继续处理下一级节点
                        current_level += 1
                    elif found.get('action') == 'selected':
                        # 如果执行了节点选择操作（点击了节点文本）
                        logger.info(f"成功选择节点: {node_text}")
                        # 等待选择操作后的UI更新
                        await asyncio.sleep(wait_time)
                        # 更新DOM状态
                        await self._update_dom_after_action_async()
                        # 选择成功，返回true
                        return True
                else:
                    # 如果操作失败，获取错误信息并记录
                    error_msg = found.get('message', '未知错误') if found else '未知错误'
                    logger.error(f"处理节点 '{node_text}' 失败: {error_msg}")
                    # 返回失败状态
                    return False
            
            # 如果执行到这里，表示遍历完所有节点但未执行最后的选择操作
            # 这是一种异常情况，因为最后一个节点应该触发选择操作
            logger.warning("导航组织树完成，但未执行最终选择操作")
            # 尽管有警告，但整体导航过程已完成
            return True
            
        except Exception as e:
            # 捕获并记录方法执行过程中的任何异常
            logger.error(f"导航组织树时出错: {str(e)}")
            # 返回失败状态
            return False
    
    async def press_enter(self) -> bool:
        """
        模拟键盘回车键点击
        
        如果指定了元素，会先定位到该元素并聚焦，然后按下回车键。
        如果未指定元素，则直接在当前活动元素上按下回车键。
        
        Args:
            element_key: 可选，要按回车的元素标识
            
        Returns:
            bool: 操作是否成功
        """
        try:
            # 验证浏览器状态
            self._verify_browser_state()
            time.sleep(1)
            # 使用Playwright的keyboard.press方法模拟按下回车键
            await self.page.keyboard.press('Enter')
            
            # 等待页面响应
            await asyncio.sleep(1)
            
            # 更新DOM状态
            await self._update_dom_after_action_async()
            
            logger.info("回车键按下成功")
            return True
            
        except Exception as e:
            logger.error(f"键盘操作执行失败: {str(e)}")
            return False
    