# -*- coding: utf-8 -*-
"""
元素定位工具模块

此模块提供扩展的Playwright元素定位方法，包括多种定位策略、
定位器优先级计算、定位器生成与验证等功能。
"""

import re
import logging
from typing import List, Dict, Any, Optional, Tuple
from playwright.sync_api import Locator, Page

# 获取此模块的日志记录器，不要重新初始化日志配置
logger = logging.getLogger(__name__)

class ElementLocatingUtils:
    """
    元素定位工具类
    
    提供扩展的Playwright元素定位方法，支持多种定位策略、
    智能定位器生成、优先级计算和验证等功能。
    """
    
    # 定位器类型优先级映射 - 按照优先级从高到低排序（数字越小优先级越高）
    LOCATOR_TYPE_PRIORITY = {
        'id': 1,          # ID选择器
        'text': 2,        # 文本内容
        'label': 3,       # 标签文本
        'placeholder': 4, # 占位符文本
        'alt_text': 5,    # 图片alt文本
        'test_id': 6,     # 测试ID
        'title': 7,       # 标题属性
        'role': 8,        # ARIA角色选择器最稳定       
        'name': 9,        # name属性
        'css': 10,        # CSS选择器
        'xpath': 11       # XPath (较不稳定)
    }
    
    # 元素类型关键词映射
    ELEMENT_TYPE_KEYWORDS = {
        'button': ['按钮', 'button', 'btn', '提交', 'submit', '确认', '确定', '取消'],
        'input': ['输入框', '输入', 'input', 'field', '文本框', 'textbox', '账号', '密码', 'password'],
        'link': ['链接', 'link', '超链接', 'hyperlink', 'a标签'],
        'select': ['下拉框', '下拉', 'select', 'dropdown', '选择框'],
        'checkbox': ['复选框', 'checkbox', '勾选框'],
        'radio': ['单选框', 'radio', '单选按钮'],
        'image': ['图片', 'image', 'img', '图像'],
        'text': ['文本', 'text', '段落', 'paragraph']
    }
    
    @staticmethod
    def generate_playwright_locators(element_key: str) -> List[Dict]:
        """
        根据元素关键字生成多种Playwright定位器
        
        为给定的元素关键字生成多种可能的定位器，包括CSS、XPath、
        文本、角色等多种定位方法，并基于元素类型进行针对性优化。
        
        Args:
            element_key: 元素标识符或描述
            
        Returns:
            List[Dict]: 包含多种定位器的列表，每个定位器包含类型和值
        """
        result = []
        # 将元素关键字转为小写并去除空格
        normalized_key = element_key.lower().strip()
        
        # 1. 确定元素类型
        element_type = "default"
        for type_name, keywords in ElementLocatingUtils.ELEMENT_TYPE_KEYWORDS.items():
            for keyword in keywords:
                if keyword.lower() in normalized_key:
                    element_type = type_name
                    break
            if element_type != "default":
                break
        
        logger.info(f"元素 '{element_key}' 推断类型: {element_type}")
        
        # 2. 根据元素类型生成定位器
        
        # 2.1 基于文本内容的定位器
        result.append({
            'type': 'text',
            'value': element_key,
            'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('text', 2)
        })
        
        # 提取可能的元素名称
        element_name = re.sub(r'按钮|输入框|链接|下拉框|框|选择框', '', element_key).strip()
        if element_name and element_name != element_key:
            result.append({
                'type': 'text',
                'value': element_name,
                'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('text', 2) + 1
            })
        
        # 2.2 针对特定元素类型的定位器
        if element_type == 'button':
            # 按钮定位
            result.append({
                'type': 'role',
                'value': {'role': 'button', 'name': element_name if element_name else element_key, 'exact': False},
                'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('role', 1)
            })
            
            # 登录按钮特殊处理
            if '登录' in element_key or 'login' in normalized_key:
                result.append({
                    'type': 'css',
                    'value': 'button.login-btn, button.btn-login, button[type="submit"]',
                    'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('css', 10) + 1
                })
                result.append({
                    'type': 'xpath',
                    'value': '//button[contains(@class, "login") or contains(@class, "btn")]',
                    'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('xpath', 11)
                })
                
                # 处理可能的嵌套文本
                result.append({
                    'type': 'css',
                    'value': 'button:has(span:text("登录")), button:has(span:text("确认"))',
                    'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('css', 2) + 2
                })
                
                # 更多的登录按钮选择器
                result.append({
                    'type': 'xpath',
                    'value': '//button[contains(., "登录") or contains(., "登 录")]',
                    'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('xpath', 11) + 1
                })
                result.append({
                    'type': 'xpath',
                    'value': '//span[contains(text(), "登录") or contains(text(), "登 录")]/parent::button',
                    'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('xpath', 11) + 2
                })
            
            # 百度一下按钮特殊处理
            if '百度一下' in element_key:
                result.append({
                    'type': 'xpath',
                    'value': '//input[@value="百度一下"]',
                    'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('xpath', 11) + 1
                })
                result.append({
                    'type': 'css',
                    'value': 'input[value="百度一下"]',
                    'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('css', 10) + 1
                })
            
        elif element_type == 'input':
            # 输入框定位
            
            # 密码输入框特殊处理
            if '密码' in element_key or 'password' in normalized_key:
                # 密码输入框的常见选择器
                result.append({
                    'type': 'css',
                    'value': 'input[type="password"]',
                    'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('css', 10) + 3
                })
                result.append({
                    'type': 'css',
                    'value': 'input[name="password"]',
                    'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('css', 10) + 2
                })
                result.append({
                    'type': 'xpath',
                    'value': '//input[@type="password"]',
                    'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('xpath', 11) + 3
                })
                result.append({
                    'type': 'css',
                    'value': 'input.el-input__inner[type="password"]',
                    'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('css', 2) + 1
                })
                
                # 具体网站的密码字段选择器
                result.append({
                    'type': 'css',
                    'value': '[placeholder="请输入密码"]',
                    'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('css', 2) + 1
                })
                result.append({
                    'type': 'xpath',
                    'value': '//form//input[@type="password"]',
                    'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('xpath', 11) + 2
                })
                result.append({
                    'type': 'xpath', 
                    'value': '//div[contains(@class,"password")]//input',
                    'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('xpath', 11) + 1
                })
                
                # 更通用但优先级较低的选择器
                result.append({
                    'type': 'xpath',
                    'value': '//input[contains(@placeholder, "密码")]',
                    'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('xpath', 11)
                })
            
            # 搜索框特殊处理
            elif '搜索' in element_key:
                # 常见的搜索框选择器
                result.append({
                    'type': 'css',
                    'value': 'input[type="search"], input[name="q"], input[name="query"], input[name="search"]',
                    'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('css', 10) + 2
                })
                result.append({
                    'type': 'xpath',
                    'value': '//input[@placeholder="搜索" or contains(@placeholder, "search") or contains(@class, "search")]',
                    'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('xpath', 11) + 2
                })
                result.append({
                    'type': 'role',
                    'value': {'role': 'searchbox', 'exact': False},
                    'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('role', 1) + 1
                })
            
            # 手机号/用户名输入框特殊处理
            elif '手机号' in element_key or '账号' in element_key or '用户名' in element_key:
                # ID选择器
                result.append({
                    'type': 'css',
                    'value': '#mobile, #username, #account',
                    'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('id', 8)
                })
                result.append({
                    'type': 'css',
                    'value': 'input#mobile, input#username, input#account',
                    'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('id', 8) - 1
                })
                
                # 名称和类型选择器
                result.append({
                    'type': 'css',
                    'value': 'input[name="mobile"], input[name="username"], input[name="account"]',
                    'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('name', 9)
                })
                result.append({
                    'type': 'css',
                    'value': 'input[type="tel"], input[type="text"]',
                    'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('css', 2)
                })
                
                # 占位符选择器
                result.append({
                    'type': 'css',
                    'value': 'input[placeholder*="手机号"], input[placeholder*="账号"], input[placeholder*="用户名"]',
                    'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('css', 2) + 1
                })
                result.append({
                    'type': 'css',
                    'value': '[placeholder="请输入手机号"], [placeholder="请输入账号"], [placeholder="请输入用户名"]',
                    'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('css', 2) + 2
                })
                
                # XPath选择器
                result.append({
                    'type': 'xpath',
                    'value': '//input[@id="mobile" or @name="mobile"]',
                    'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('xpath', 11) + 2
                })
                result.append({
                    'type': 'xpath',
                    'value': '//form//input[@type="text" or @type="tel"][1]',
                    'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('xpath', 11)
                })
                result.append({
                    'type': 'xpath',
                    'value': '//div[contains(@class,"form")]//input[1]',
                    'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('xpath', 11) - 1
                })
            
            # 尝试根据placeholder定位
            placeholder_texts = []
            if '账号' in element_key or '用户名' in element_key:
                placeholder_texts = ['请输入账号', '请输入用户名', '账号', '用户名']
            elif '密码' in element_key:
                placeholder_texts = ['请输入密码', '密码']
            elif '手机号' in element_key:
                placeholder_texts = ['请输入手机号', '手机号']
            elif '搜索' in element_key:
                placeholder_texts = ['搜索', '请输入搜索内容', '请输入关键词']
            
            for placeholder in placeholder_texts:
                result.append({
                    'type': 'placeholder',
                    'value': placeholder,
                    'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('placeholder', 4)
                })
                
                # CSS定位
                result.append({
                    'type': 'css',
                    'value': f'input[placeholder="{placeholder}"]',
                    'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('css', 2)
                })
            
            # 基于标签文本定位
            if element_name:
                result.append({
                    'type': 'label',
                    'value': element_name,
                    'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('label', 3)
                })
            
            # 基于类型和名称的CSS定位
            if '密码' in element_key:
                result.append({
                    'type': 'css',
                    'value': 'input[type="password"]',
                    'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('css', 2) - 1
                })
            elif '账号' in element_key or '用户名' in element_key:
                result.append({
                    'type': 'css',
                    'value': 'input[name="username"], input[name="account"], input[name="mobile"]',
                    'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('css', 2)
                })
        
        elif element_type == 'link':
            # 链接定位
            result.append({
                'type': 'role',
                'value': {'role': 'link', 'name': element_name if element_name else element_key, 'exact': False},
                'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('role', 1)
            })
        
        elif element_type == 'select':
            # 下拉框定位
            result.append({
                'type': 'role',
                'value': {'role': 'combobox', 'name': element_name if element_name else element_key, 'exact': False},
                'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('role', 1)
            })
            
            # CSS定位
            if element_name:
                result.append({
                    'type': 'css',
                    'value': f'select[name="{element_name.lower()}"], select[aria-label="{element_name}"]',
                    'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('css', 2)
                })
        
        # 通用元素定位策略
        # 2.3 通用CSS定位器
        if element_name:
            # ID选择器
            result.append({
                'type': 'css',
                'value': f'#{element_name.lower().replace(" ", "-")}',
                'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('id', 8)
            })
            
            # 名称选择器
            result.append({
                'type': 'css',
                'value': f'[name="{element_name.lower().replace(" ", "_")}"]',
                'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('name', 9)
            })
            
            # 测试ID选择器
            result.append({
                'type': 'test_id',
                'value': element_name.lower().replace(" ", "-"),
                'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('test_id', 6)
            })
        
        # 2.4 XPath定位器
        result.append({
            'type': 'xpath',
            'value': f'//*[contains(text(), "{element_key}")]',
            'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('xpath', 11)
        })
        
        # 2.5 特定场景的通用选择器（更低优先级）
        # 表单中的第一个输入框
        if '第一' in element_key and ('输入框' in element_key or 'input' in normalized_key):
            result.append({
                'type': 'xpath',
                'value': '//form//input[1]',
                'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('xpath', 11) - 2
            })
        
        # 表单中的提交按钮
        if ('提交' in element_key or 'submit' in normalized_key) and '按钮' in element_key:
            result.append({
                'type': 'css',
                'value': 'form button[type="submit"], form input[type="submit"]',
                'priority': ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get('css', 2) - 2
            })
        
        # 根据优先级排序
        result.sort(key=lambda x: x.get('priority', 0))
        
        return result
    
    @staticmethod
    def calculate_locator_priority(locator_type: str, count: int, is_visible: bool, is_enabled: bool) -> int:
        """
        计算定位器优先级
        
        基于定位器类型、匹配元素数量、可见性和可用性计算优先级分数
        
        Args:
            locator_type: 定位器类型
            count: 匹配元素数量
            is_visible: 元素是否可见
            is_enabled: 元素是否可用
            
        Returns:
            int: 优先级分数，值越小优先级越高
        """
        # 基础分值 (类型优先级)
        base_score = ElementLocatingUtils.LOCATOR_TYPE_PRIORITY.get(locator_type, 2)
        
        # 唯一性加分 (匹配元素数量为1给予最高分)
        uniqueness_score = 5 if count == 1 else max(0, 5 - count)
        
        # 可见性和可用性加分
        visibility_score = 3 if is_visible else 0
        enabled_score = 2 if is_enabled else 0
        
        # 总分
        total_score = base_score + uniqueness_score + visibility_score + enabled_score
        
        # 转换为优先级 (1-10，1为最高优先级)
        # 将20分制转换为10分制并反转
        priority = max(1, min(10, 11 - (total_score // 2)))
        
        return priority
    
    @staticmethod
    async def select_most_relevant_element(locator: Locator, element_key: str) -> Locator:
        """
        从多个匹配的元素中选择最相关的一个
        
        基于多种相关性因素（可见性、文本匹配度、位置等）
        从多个匹配的元素中选择一个最可能是目标的元素。
        
        Args:
            locator: 匹配多个元素的Playwright定位器
            element_key: 元素标识符
            
        Returns:
            Locator: 筛选后的定位器，只指向一个最相关的元素
        """
        try:
            # 获取元素总数
            count = await locator.count()
            if count <= 1:
                return locator
            
            best_element = None
            best_score = -1
            
            # 基于元素特性为每个元素计算得分
            for i in range(count):
                current = locator.nth(i)
                score = 0
                
                # 1. 可见性得分（可见元素优先）
                try:
                    is_visible = await current.is_visible(timeout=500)
                    if is_visible:
                        score += 10
                except:
                    pass
                
                # 2. 是否可交互（可交互元素优先）
                try:
                    is_enabled = await current.is_enabled()
                    if is_enabled:
                        score += 5
                except:
                    pass
                
                # 3. 文本匹配度（文本与目标更匹配的元素优先）
                try:
                    text = await current.text_content(timeout=500)
                    if text:
                        text = text.strip()
                        if element_key in text:
                            score += 8
                        elif text in element_key:
                            score += 6
                        
                        # 编辑距离相似度
                        similarity = ElementLocatingUtils._calculate_string_similarity(element_key.lower(), text.lower())
                        score += similarity * 10  # 相似度0-1，权重0-10
                except:
                    pass
                
                # 4. 位置得分（视口内元素优先）
                try:
                    bbox = await current.bounding_box()
                    if bbox:
                        # 元素在视口内加分
                        if bbox['x'] >= 0 and bbox['y'] >= 0:
                            score += 3
                            
                        # 元素在页面上方加分（通常更重要）
                        if bbox['y'] < 500:  # 假设前500px是页面上方
                            score += 2
                except:
                    pass
                
                # 更新最佳元素
                if score > best_score:
                    best_score = score
                    best_element = i
            
            # 返回得分最高的元素
            if best_element is not None:
                return locator.nth(best_element)
            
            # 如果无法确定最佳元素，返回第一个
            return locator.first
            
        except Exception as e:
            logger.error(f"选择最相关元素时发生错误: {str(e)}")
            # 出错时返回原始定位器
            return locator
    
    @staticmethod
    async def is_unique_and_visible(locator: Locator) -> Tuple[bool, bool, int]:
        """
        检查定位器是否唯一且可见
        
        Args:
            locator: Playwright定位器
            
        Returns:
            Tuple[bool, bool, int]: 是否唯一、是否可见、匹配元素数量
        """
        try:
            # 直接使用异步方式获取元素计数
            count = await locator.count()
            is_unique = count == 1
            
            # 检查可见性
            is_visible = False
            if count > 0:
                try:
                    is_visible = await locator.first.is_visible(timeout=1000)
                except Exception as e:
                    # 可见性检查失败但不中断流程
                    logger.warning(f"检查元素可见性失败: {str(e)}")
                    pass
            
            return is_unique, is_visible, count
        except Exception as e:
            logger.error(f"检查定位器唯一性和可见性失败: {str(e)}")
            return False, False, 0
    
    @staticmethod
    def extract_dominant_features(element_key: str) -> Dict[str, Any]:
        """
        提取元素关键字的主要特征
        
        从元素标识符中提取主要特征，如元素类型、名称等
        
        Args:
            element_key: 元素标识符
            
        Returns:
            Dict[str, Any]: 元素特征字典
        """
        features = {
            'type': 'default',
            'name': element_key,
            'action': None,
            'state': None
        }
        
        normalized_key = element_key.lower()
        
        # 确定元素类型
        for type_name, keywords in ElementLocatingUtils.ELEMENT_TYPE_KEYWORDS.items():
            for keyword in keywords:
                if keyword.lower() in normalized_key:
                    features['type'] = type_name
                    # 提取名称 (移除类型关键词)
                    features['name'] = re.sub(f'{keyword}|{type_name}', '', element_key, flags=re.IGNORECASE).strip()
                    break
            if features['type'] != 'default':
                break
        
        # 如果没有提取到名称，使用原始关键字
        if not features['name']:
            features['name'] = element_key
        
        # 识别可能的动作
        action_keywords = {
            'click': ['点击', '单击', 'click'],
            'input': ['输入', '填写', 'input', 'enter'],
            'select': ['选择', 'select', 'choose'],
            'check': ['勾选', '选中', 'check', 'tick']
        }
        
        for action, keywords in action_keywords.items():
            for keyword in keywords:
                if keyword in normalized_key:
                    features['action'] = action
                    break
            if features['action']:
                break
        
        # 识别可能的状态
        state_keywords = {
            'enabled': ['可用', 'enabled', 'active'],
            'disabled': ['禁用', '不可用', 'disabled', 'inactive'],
            'selected': ['已选', '已选中', 'selected'],
            'checked': ['已勾选', 'checked']
        }
        
        for state, keywords in state_keywords.items():
            for keyword in keywords:
                if keyword in normalized_key:
                    features['state'] = state
                    break
            if features['state']:
                break
        
        return features
    
    @staticmethod
    def _calculate_string_similarity(str1: str, str2: str) -> float:
        """
        计算两个字符串的相似度（基于Levenshtein距离）
        
        Args:
            str1: 第一个字符串
            str2: 第二个字符串
            
        Returns:
            float: 相似度，范围0-1.0
        """
        if not str1 and not str2:
            return 1.0
        if not str1 or not str2:
            return 0.0
            
        # 计算编辑距离
        def levenshtein_distance(a, b):
            if len(a) < len(b):
                return levenshtein_distance(b, a)
            if not b:
                return len(a)
                
            prev_row = range(len(b) + 1)
            for i, a_char in enumerate(a):
                curr_row = [i + 1]
                for j, b_char in enumerate(b):
                    insertions = prev_row[j + 1] + 1
                    deletions = curr_row[j] + 1
                    substitutions = prev_row[j] + (a_char != b_char)
                    curr_row.append(min(insertions, deletions, substitutions))
                prev_row = curr_row
                
            return prev_row[-1]
            
        # 计算归一化的相似度
        max_len = max(len(str1), len(str2))
        if max_len == 0:
            return 1.0
            
        distance = levenshtein_distance(str1, str2)
        similarity = 1.0 - distance / max_len
        return similarity 