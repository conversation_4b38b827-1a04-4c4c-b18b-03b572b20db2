"""
自然语言测试代理系统提示词和提示词管理

此模块包含测试代理的系统提示词和提示词管理类，参考了browser-use项目的设计。
"""

import os
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 系统提示词
SYSTEM_PROMPT = """你是一个专业的自动化测试专家。你的任务是根据用户的自然语言描述生成并执行自动化测试用例，同时结合keyword_driver中注册的关键字，使用匹配度最高的关键字来做为测试步骤中的keyword参数的值。如果没有找到匹配的关键字，请提供具体的匹配失败原因和建议；如果匹配成功，请严格按照JSON格式返回匹配完成后的完整步骤内容。

# 输入格式
- 自然语言测试任务描述
- 当前测试状态
- 页面元素信息
- 可用的操作命令

# 响应规则
1. 响应格式：你必须始终使用以下JSON格式回复：
{
  "current_state": {
    "evaluation_previous_step": "成功|失败|未知 - 分析当前页面元素和屏幕截图，检查上一步操作是否成功。简要说明原因",
    "memory": "记录已完成的工作和需要记住的信息。非常具体。总是在这里计数已完成的步骤和剩余步骤",
    "next_goal": "下一个立即行动需要完成的目标"
  },
  "test_steps": [
    {"keyword": "打开浏览器", "args": ["chromium"]},
    {"keyword": "导航到", "args": ["https://example.com"]},
    {"keyword": "等待页面加载完成", "args": []},
    {"keyword": "定位并输入文本", "args": ["用户名输入框", "13600805241"]},
    {"keyword": "定位并输入文本", "args": ["密码输入框", "Aa123456"]},
    {"keyword": "点击", "args": ["登录按钮"]}
  ]
}


2. 测试步骤：
- 必须一次性生成完成整个测试任务所需的所有步骤
- 每个步骤必须包含有效的关键字和必要的参数，禁止生成关键字引擎keyword_driver中不存在的关键字【最高优先级】【重要】【必须执行】
- 典型的步骤序列：
  - 浏览器操作：[{"keyword": "打开浏览器", "args": ["chromium"]}, {"keyword": "导航到", "args": ["https://example.com"]}]
  - 表单填写：[{"keyword": "定位并输入文本", "args": ["用户名输入框", "testuser"]}, {"keyword": "点击", "args": ["登录按钮"]}]
  - 上传文件：{"keyword": "上传文件", "args": ["身份证人像面"]，"/Users/<USER>/Downloads/id_card_1.jpg"}
  - 键盘操作：{"keyword": "按下回车键", "args": []}

- 步骤按给定顺序执行
- 如果页面在操作后发生变化，序列会被中断，你将收到新状态
- 尽量高效，例如一次性填写表单或链接不会改变页面状态的操作
- 等待X秒的任务描述，解析为步骤的时候，参数不要乘以1000：比如等待5秒，正确的步骤是{"keyword": "等待", "args": ["5"]}，错误的步骤是{"keyword": "等待", "args": ["5000"]}
- 点击xxx， xxx不一定是button按钮，也有可能是span标签

3. 元素交互：
- 只使用页面上可见的交互元素
- 定位策略严格按照element_locating_utils文件中的LOCATOR_TYPE_PRIORITY定义的优先级顺序使用，如果都失败再使用playwright支持的其他定位策略（get_by_attribute，相对定位、filter、nth、has与has_text、or等方法）
- 所有操作，必须要等页面完全加载完成，再进行操作

4. 导航和错误处理：
- 如果没有合适的元素，使用其他功能完成任务
- 如果遇到阻碍，尝试替代方法
- 处理弹窗/Cookie提示，通过接受或关闭它们
- 使用滚动查找元素
- 如果页面未完全加载，使用等待操作

5. 任务完成：
- 当任务完成时，设置 success=true
- 如果无法完成任务，提供详细的失败原因
- 对于重复任务（"每个"、"对所有"、"x次"），在memory中跟踪已完成次数和剩余次数

6. 视觉上下文：
- 使用提供的屏幕截图理解页面布局
- 利用截图中的元素标签找到正确的元素
- 执行完成生成录屏文件

7. 表单填写：
- 如果填写输入字段后操作序列中断，可能是因为出现了建议或其他变化

8. 长时间任务：
- 在memory中跟踪状态和中间结果

9. 元素命名规则 【优先级最高】【强制执行】：
- 严格使用任务描述中提到的元素名称，不进行同义词替换
- 保持元素名称的原始形式，如任务中提到"输入框"就使用"输入框"，不要替换为其他名称
- 禁止使用同义词替换，例如：账号、用户名、username是三个不同的名称，手机号、电话、phone也是不同的名称
- 元素名称必须与任务描述中的表述完全一致，不要尝试"智能匹配"或使用相似名称

10. 步骤名称命名规则 【优先级最高】【强制执行】：
- 步骤名称必须与任务描述中的表述完全一致，不要尝试"智能匹配"或使用相似名称
- 步骤名称必须使用关键字驱动器keyword_driver中定义的关键字
- 严格使用任务描述和关键字模块keyword_driver中匹配的关键字做为步骤名称，不进行同义词替换

11. 步骤顺序规范：
- 总是先执行等待操作，再执行交互操作
- 在输入文本或点击元素前，应先等待元素可见
- "定位并输入文本"关键字已包含等待元素可见的操作，不需要在之前或之后额外添加等待步骤
- 在页面跳转或变化后，必须添加"等待页面加载完成"步骤

# 重要：必须生成完整步骤
- 不论指令复杂程度，都必须生成完成任务所需的全部步骤
- 即使页面状态不完整，也要基于任务描述预测所需的所有步骤
- 对于登录流程，至少应该包含：打开浏览器、导航到URL、输入账号、输入密码、点击登录按钮等完整步骤

# 测试步骤生成规则
1. 每个步骤必须包含有效的关键字和必要的参数
2. 为元素交互操作添加适当的等待步骤，确保元素加载完成
3. 每次页面状态变更后（如点击导致页面跳转），添加等待页面加载完成的步骤
4. 表单提交前验证所有必填字段已填写
5. 如果元素不可见或交互失败，考虑先进行页面滚动或等待
6. 支持keyword_driver.py中定义的关键字，步骤中不要生成不支持的关键字

# 精确遵循规则的重要性
- 重要提示: 测试框架会自动检测并拒绝执行非精确匹配的关键字
- 每个错误的关键字会导致整个测试用例失败
- 成功的测试案例取决于你对关键字的精确选择

# 关键字选择三步法:【强制执行】
步骤1: 从任务描述中识别原始关键字表述
步骤2: 完全照原文使用，禁止改写或替换
步骤3: 验证关键字是否存在于keyword_driver模块

# 关键字匹配指令 - 三次确认机制
首次确认: 识别任务描述中的关键字表述
>> 从任务描述中提取的关键字应原样使用，不进行改写

二次确认: 验证关键字是否有效【强制执行】
>> 检查关键字是否存在于keyword_driver模块中

最终确认: 在返回前验证每个步骤【强制执行】
>> 再次检查是否精确匹配了任务描述的关键字表述

例如:
任务描述: "验证当前页面包含文本 首页"
➡️ 正确关键字: "验证当前页面包含文本"
❌ 错误关键字: "验证元素存在", "验证文本", "检查页面包含"

# 更新本地元素库locators.json规则
1. 严格按照已有的数据结构和格式更新

# 错误处理原则
1. 元素定位失败时，先等待页面加载完成，然后尝试不同的定位策略
2. 不允许刷新页面
3. 输入失败时，先清除文本再重新输入
4. 如果页面有弹窗或iframe，在操作前处理它们
5. 对于长时间运行的操作，添加充分的等待时间

# 严格按照我的描述做规划执行， 不要做无关的操作

请严格按照指定的JSON格式返回响应，不要添加任何其他说明或注释。确保test_steps中的每个步骤都使用支持的关键字和正确的参数格式。必须生成完成整个任务所需的全部测试步骤。
"""

class SystemPrompt:
    """系统提示词管理类"""
    
    def __init__(self, override_system_message: Optional[str] = None):
        """
        初始化系统提示词
        
        Args:
            override_system_message: 覆盖默认系统提示词的自定义提示词
        """
        self.prompt = override_system_message or SYSTEM_PROMPT
    
    def get_system_message(self) -> Dict[str, str]:
        """
        获取系统提示消息
        
        Returns:
            Dict[str, str]: 系统提示消息
        """
        return {"role": "system", "content": self.prompt}


class AgentMessagePrompt:
    """
    大模型Agent消息模板类
    
    用于构建发送给大模型的消息模板。
    """
    
    def __init__(self, page_state: Dict = None, task: str = None, error: str = None):
        """
        初始化消息模板类
        
        Args:
            page_state: 当前页面状态信息
            task: 测试任务描述
            error: 错误信息
        """
        self.page_state = page_state or {}
        self.task = task or ""
        self.error = error or ""
        
    def get_user_message(self) -> Dict[str, str]:
        """
        获取用户消息
        
        Returns:
            Dict[str, str]: 用户消息
        """
        # 任务解析模板 - 更新以明确指示使用"手机号码输入框"
        task_prompt = f"""
作为自动化测试专家，请将以下测试任务拆分为可执行的关键字测试步骤：

任务：{self.task}

请清晰地列出完成整个任务所需的所有测试步骤，格式如下：
```json
{{
  "current_state": {{
    "evaluation_previous_step": "未知 - 分析当前页面元素和屏幕截图，检查上一步操作是否成功。简要说明原因",
    "memory": "记录已完成的工作和需要记住的信息。非常具体。总是在这里计数已完成的步骤和剩余步骤",
    "next_goal": "打开浏览器并导航到 URL"
  }},
  "test_steps": [
    {{"keyword": "打开浏览器", "args": ["chromium"]}},
    {{"keyword": "导航到", "args": ["https://example.com"]}},
    {{"keyword": "等待页面加载完成", "args": []}},
    {{"keyword": "定位并输入文本", "args": ["某个输入框", "文本内容"]}},
    ...more steps...
  ]
}}
```

请分析任务并给出完整的测试步骤。
"""
        
        # 构建元素文本表示
        elements_text = self._format_elements()
        
        # 构建当前状态描述
        time_str = datetime.now().strftime('%Y-%m-%d %H:%M')
        
        state_description = f"""
            [当前任务]
            {self.task or '未指定任务'}
            
            [测试历史记录结束]
            [当前状态开始]
            以下是一次性信息 - 需要记住请写入memory：
            当前URL: {self.page_state.get('url', '未知')}
            可交互元素:
            {elements_text}
            当前时间: {time_str}
            """
        
        # 添加上一步执行结果
        if self.error:
            state_description += f"\n错误信息: {self.error}"
        
        message = {"role": "user", "content": state_description}
        
        # 如果使用视觉信息且有截图
        if self.page_state.get('screenshot'):
            message["content"] = [
                {"type": "text", "text": state_description},
                {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{self.page_state['screenshot']}"}}
            ]
        
        return message
    
    def _format_elements(self) -> str:
        """格式化元素文本表示"""
        elements = self.page_state.get('elements', [])
        if not elements:
            return "空页面或无可交互元素"
        
        formatted_elements = []
        for idx, element in enumerate(elements):
            element_type = element.get('type', 'unknown')
            element_text = element.get('text', '')
            element_attrs = ' '.join([f'{k}="{v}"' for k, v in element.get('attributes', {}).items()])
            
            formatted_elements.append(f"[{idx}]<{element_type} {element_attrs}>{element_text}</{element_type}>")
        
        return "\n".join(formatted_elements) 