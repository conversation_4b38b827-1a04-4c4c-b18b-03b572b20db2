"""
大模型集成模块

此模块负责与大模型API交互，用于元素定位优化和自然语言测试用例生成。
"""

import os
import re
import json
import logging
import asyncio
from typing import Dict, List, Any, Optional, Union

try:
    from openai import OpenAI, AsyncOpenAI
except ImportError:
    logging.error("请安装 openai>=1.0.0: pip install openai>=1.0.0")
    raise

from config.config import LLM_CONFIG

logger = logging.getLogger(__name__)

class LLMIntegration:
    """
    大模型集成类
    
    负责与大模型API交互，提供元素定位分析、测试用例生成等功能。
    """
    
    def __init__(self, config: Dict = None):
        """
        初始化大模型集成
        
        Args:
            config: 大模型配置，默认使用全局配置
        """
        self.config = config or LLM_CONFIG
        
        # 初始化同步和异步客户端
        self.client = None
        self.async_client = None
        
        # 设置客户端
        self._setup_clients()
        logger.info("大模型集成初始化完成")
    
    def _setup_clients(self) -> None:
        """
        设置大模型客户端，同时初始化同步和异步客户端
        """
        try:
            # 获取API密钥和基础URL
            api_key = self.config.get('api_key') or os.getenv('OPENAI_API_KEY')
            api_base = self.config.get('api_base') or os.getenv('OPENAI_API_BASE')
            
            if not api_key:
                logger.warning("未设置API密钥，请确保在配置或环境变量中设置OPENAI_API_KEY")
            
            # 初始化同步客户端
            self.client = OpenAI(
                api_key=api_key,
                base_url=api_base
            )
            
            # 初始化异步客户端
            self.async_client = AsyncOpenAI(
                api_key=api_key,
                base_url=api_base
            )
            
            logger.info(f"大模型客户端设置完成，使用模型: {self.config.get('model', 'gpt-3.5-turbo')}")
        except Exception as e:
            logger.error(f"设置大模型客户端失败: {str(e)}")
    
    def call_llm_api(self, messages: List[Dict[str, Any]]) -> Optional[str]:
        """
        同步调用大模型API
        
        Args:
            messages: 消息列表，包含角色和内容
            
        Returns:
            Optional[str]: 大模型返回的文本，如果失败则返回None
        """
        try:
            # 确保client已初始化
            if not self.client:
                self._setup_clients()
                
            response = self.client.chat.completions.create(
                model=self.config.get('model', 'Qwen/Qwen2.5-14B-Instruct'),
                messages=messages,
                temperature=self.config.get('temperature', 0.7),
                max_tokens=self.config.get('max_tokens', 4096),
                top_p=self.config.get('top_p', 0.95)
            )
            
            logger.info(f"大模型返回：{response.choices}")
            return response.choices[0].message.content if response.choices else None
        except Exception as e:
            logger.error(f"调用大模型API失败: {str(e)}")
            return None
    
    async def call_llm_api_async(self, messages: List[Dict[str, Any]]) -> Optional[str]:
        """
        异步调用大模型API
        
        Args:
            messages: 消息列表，包含角色和内容
            
        Returns:
            Optional[str]: 大模型返回的文本，如果失败则返回None
        """
        try:
            # 确保async_client已初始化
            if not self.async_client:
                self._setup_clients()
                
            # 直接使用异步客户端进行API调用
            response = await self.async_client.chat.completions.create(
                model=self.config.get('model', 'Qwen/Qwen2.5-14B-Instruct'),
                messages=messages,
                temperature=self.config.get('temperature', 0.7),
                max_tokens=self.config.get('max_tokens', 4096),
                top_p=self.config.get('top_p', 0.95)
            )
            
            logger.info(f"大模型异步返回：{response.choices}")
            return response.choices[0].message.content if response.choices else None
        except Exception as e:
            logger.error(f"异步调用大模型API失败: {str(e)}")
            return None
    
    def validate_test_steps(self, steps: List[Dict]) -> List[Dict]:
        """
        使用大模型验证测试步骤有效性
        
        Args:
            steps: 原始测试步骤列表
        
        Returns:
            List[Dict]: 验证后的测试步骤
        """
        try:
            messages = [
                {"role": "system", "content": """你是一个专业的自动化测试专家，你的任务是负责验证测试用例步骤的有效性，校验关键字必须是keyword_driver中存在且完全匹配的，如果校验不通过要给出具体原因并给出建议匹配的关键字,必须严格按照JSON格式返回匹配完成后的完整步骤内容。
                # 精确遵循规则的重要性
                - 重要提示: 测试框架会自动检测并拒绝执行非精确匹配的关键字
                - 每个错误的关键字会导致整个测试用例失败
                - 成功的测试案例取决于你对关键字的精确选择

                # 关键字选择三步法:
                步骤1: 从任务描述中识别原始关键字表述
                步骤2: 完全照原文使用，禁止改写或替换
                步骤3: 验证关键字是否存在于keyword_driver模块

                # 关键字匹配指令 - 三次确认机制
                首次确认: 识别任务描述中的关键字表述
                >> 从任务描述中提取的关键字应原样使用，不进行改写

                二次确认: 验证关键字是否有效
                >> 检查关键字是否存在于keyword_driver模块中

                最终确认: 在返回前验证每个步骤
                >> 再次检查是否精确匹配了任务描述的关键字表述

                例如:
                任务描述: "验证当前页面包含文本 首页"
                ➡️ 正确关键字: "验证当前页面包含文本"
                ❌ 错误关键字: "验证元素存在", "验证文本", "检查页面包含"
                
                # JSON格式完整步骤示例：
                [{"keyword": "打开浏览器", "args": ["chromium"]},
                {"keyword": "导航到", "args": ["https://example.com"]},
                {"keyword": "等待页面加载完成", "args": []},
                {"keyword": "定位并输入文本", "args": ["用户名输入框", "13600805241"]},
                {"keyword": "定位并输入文本", "args": ["密码输入框", "Aa123456"]},
                {"keyword": "点击", "args": ["登录按钮"]}]"""},
                {"role": "user", "content": json.dumps(steps, ensure_ascii=False)}
            ]
            
            response = self.call_llm_api(messages)
            if response:
                return self._process_json_response(response)
            return steps
        except Exception as e:
            logger.error(f"步骤验证失败: {str(e)}")
            return steps
    
    async def validate_test_steps_async(self, steps: List[Dict]) -> List[Dict]:
        """
        异步验证测试步骤
        
        Args:
            steps: 测试步骤列表
            
        Returns:
            List[Dict]: 验证后的测试步骤
        """
        try:
            messages = [
                {"role": "system", "content": """你是一个专业的自动化测试专家。你的任务是负责验证测试用例步骤的有效性，校验关键字必须是keyword_driver中存在且完全匹配的，如果校验不通过要给出具体原因并给出建议匹配的关键字,必须严格按照JSON格式返回匹配完成后的完整步骤内容。
                # 精确遵循规则的重要性
                - 重要提示: 测试框架会自动检测并拒绝执行非精确匹配的关键字
                - 每个错误的关键字会导致整个测试用例失败
                - 成功的测试案例取决于你对关键字的精确选择

                # 关键字选择三步法:
                步骤1: 从任务描述中识别原始关键字表述
                步骤2: 完全照原文使用，禁止改写或替换
                步骤3: 验证关键字是否存在于keyword_driver模块

                # 关键字匹配指令 - 三次确认机制
                首次确认: 识别任务描述中的关键字表述
                >> 从任务描述中提取的关键字应原样使用，不进行改写

                二次确认: 验证关键字是否有效
                >> 检查关键字是否存在于keyword_driver模块中

                最终确认: 在返回前验证每个步骤
                >> 再次检查是否精确匹配了任务描述的关键字表述

                例如:
                任务描述: "验证当前页面包含文本 首页"
                ➡️ 正确关键字: "验证当前页面包含文本"
                ❌ 错误关键字: "验证元素存在", "验证文本", "检查页面包含"
                
                # JSON格式完整步骤示例：
                [{"keyword": "打开浏览器", "args": ["chromium"]},
                {"keyword": "导航到", "args": ["https://example.com"]},
                {"keyword": "等待页面加载完成", "args": []},
                {"keyword": "定位并输入文本", "args": ["用户名输入框", "13600805241"]},
                {"keyword": "定位并输入文本", "args": ["密码输入框", "Aa123456"]},
                {"keyword": "点击", "args": ["登录按钮"]}]"""},
                {"role": "user", "content": json.dumps(steps, ensure_ascii=False)}
            ]
            
            response = await self.call_llm_api_async(messages)
            if response:
                return self._process_json_response(response)
            return steps
        except Exception as e:
            logger.error(f"异步步骤验证失败: {str(e)}")
            return steps
    
    def _process_json_response(self, response: str) -> List[Dict]:
        """处理JSON响应，支持自动修复常见格式错误"""
        try:
            return json.loads(response)
        except json.JSONDecodeError as e:
            # 尝试从Markdown代码块中提取JSON
            json_match = re.search(r'(?:```json\s*)?([\[\{](?:.*?[\]\}])?)', response, re.IGNORECASE | re.DOTALL)
            if json_match:
                try:
                    # 语法完整性检查
                    json_str = json_match.group(1).strip()
                    if not json_str.endswith((']', '}')):
                        json_str += ']' if json_str.startswith('[') else '}'
                    # 自动修复常见格式错误
                    json_str = re.sub(r'(\w+)(\s*:\s*)(\w+)', r'"\1"\2"\3"', json_str)
                    # 修复未闭合对象和数组
                    json_str = re.sub(r'\{([^{}]*)$', r'{\1}', json_str, flags=re.MULTILINE)
                    json_str = re.sub(r'\[([^]]*)$', r'[\1]', json_str, flags=re.MULTILINE)
                    # 修复未闭合字符串
                    json_str = re.sub(r'("[^"]*)(?<!\\)$', r'\1"', json_str)
                    # 修复键值对格式
                    json_str = re.sub(r'(?<=\{|,)\s*([^{}\[\],:]+?)\s*:', r'"\1":', json_str)
                    # 删除多余逗号
                    json_str = re.sub(r',(\s*[\}\])', r'\1', json_str)
                    return json.loads(json_str)
                except json.JSONDecodeError as inner_e:
                    logger.error(f"JSON解析失败: {str(inner_e)}\n原始响应片段: {response[:200]}")
                
            logger.error(f"JSON解析失败: {str(e)}，原始响应: {response[:200]}")
            
            # 使用更健壮的自然语言解析方法
            steps = []
            lines = response.split('\n')
            step_pattern = re.compile(r'(keyword|args)\s*[=:]\s*["\']?(.*?)["\']?')
            json_pattern = re.compile(r'(?:\{.*?\}|\[.*?\])', re.DOTALL)
            
            for line in lines:
                try:
                    # 尝试提取显式JSON结构
                    json_match = json_pattern.search(line)
                    if json_match:
                        step = json.loads(json_match.group())
                        steps.append(step)
                    else:
                        # 解析关键字格式步骤
                        if ':' in line and step_pattern.search(line):
                            parts = line.split(':', 1)
                            keyword = parts[0].strip()
                            args = [arg.strip() for arg in parts[1].split(',') if arg.strip()]
                            steps.append({"keyword": keyword, "args": args})
                except Exception as parse_error:
                    logger.info(f"步骤解析失败: {line}，错误: {str(parse_error)}")
            
            return steps
    
    def extract_json(self, response_text: str) -> Dict:
        """增强版JSON解析，支持自动修复常见格式错误"""
        try:
            # 尝试直接解析完整响应
            return json.loads(response_text)
        except json.JSONDecodeError:
            # 尝试从Markdown代码块中提取JSON
            json_match = re.search(r'```(?:json)?\s*([\s\S]*?)```', response_text)
            if json_match:
                try:
                    return json.loads(json_match.group(1).strip())
                except json.JSONDecodeError:
                    pass
            
            # 尝试从响应文本中提取JSON对象或数组
            json_obj_match = re.search(r'(\{[\s\S]*\})', response_text)
            if json_obj_match:
                try:
                    return json.loads(json_obj_match.group(1))
                except json.JSONDecodeError:
                    pass
            
            # 如果以上方法都失败，尝试构造一个简单的字典
            logger.warning("无法解析JSON响应，将返回空字典")
            return {}
    
    async def analyze_element_locators_async(self, prompt: str) -> List[Dict]:
        """
        异步分析元素定位器
        
        Args:
            prompt: 提示文本
            
        Returns:
            List[Dict]: 元素定位器列表
        """
        try:
            messages = [
                {"role": "system", "content": """你是一位使用playwright做Web元素定位的专家，精通playwright支持的所有定位方式。
                请分析以下文本，提取可能的元素定位策略。
                根据元素描述，提供多种定位方法。
                请以JSON数组格式返回，每个元素包含"selector_type"和"selector"字段。"""},
                {"role": "user", "content": prompt}
            ]
            
            response = await self.call_llm_api_async(messages)
            if response:
                return self._process_json_response(response)
            return []
        except Exception as e:
            logger.error(f"异步分析元素定位器失败: {str(e)}")
            return []
    
    async def generate_test_case_async(self, description: str) -> Dict[str, Any]:
        """
        异步生成测试用例
        
        Args:
            description: 测试用例描述
            
        Returns:
            Dict[str, Any]: 测试用例信息
        """
        try:
            messages = [
                {"role": "system", "content": """你是一位专业的自动化测试专家。
                请根据提供的描述，生成完整的测试用例步骤。
                每个步骤应包含明确的关键字和参数，符合关键字驱动测试框架的规范。
                请以JSON格式返回，包含测试用例名称、描述和步骤列表。
                步骤列表中的每个步骤包含"keyword"和"args"字段。"""},
                {"role": "user", "content": f"请为以下测试需求生成完整的测试用例步骤: {description}"}
            ]
            
            response = await self.call_llm_api_async(messages)
            if not response:
                return {"name": "未生成测试用例", "description": description, "steps": []}
            
            try:
                return json.loads(response)
            except json.JSONDecodeError:
                # 尝试提取结构化数据
                test_case = {"name": "自动生成的测试用例", "description": description, "steps": []}
                
                # 尝试从响应中提取步骤
                lines = response.split('\n')
                current_step = {}
                steps = []
                
                for line in lines:
                    line = line.strip()
                    if not line:
                        continue
                    
                    # 检查是否是步骤标记行
                    step_match = re.match(r'(步骤\s*\d+|Step\s*\d+)[:：]?\s*(.*)', line)
                    if step_match and current_step:
                        steps.append(current_step)
                        current_step = {"keyword": step_match.group(2), "args": []}
                    elif ":" in line or "：" in line:
                        parts = re.split(r'[：:]', line, 1)
                        key = parts[0].strip()
                        value = parts[1].strip() if len(parts) > 1 else ""
                        
                        if key.lower() in ["关键字", "keyword"]:
                            current_step = {"keyword": value, "args": []}
                        elif key.lower() in ["参数", "args", "arguments"]:
                            if current_step:
                                current_step["args"] = [arg.strip() for arg in value.split(",")]
                        elif current_step and "keyword" in current_step:
                            steps.append(current_step)
                            current_step = {}
                
                if current_step and "keyword" in current_step:
                    steps.append(current_step)
                
                test_case["steps"] = steps
                return test_case
                
        except Exception as e:
            logger.error(f"异步生成测试用例失败: {str(e)}")
            return {"name": "生成失败", "description": description, "steps": []}
    
    async def optimize_element_locators_async(self, page_source: str, target_element_description: str) -> Dict[str, str]:
        """
        异步优化元素定位器
        
        Args:
            page_source: 页面源代码
            target_element_description: 目标元素描述
            
        Returns:
            Dict[str, str]: 优化后的元素定位器
        """
        try:
            # 为防止过大的页面内容导致token超限，进行截断
            max_content_length = 50000  # 最大内容长度
            if len(page_source) > max_content_length:
                page_source = page_source[:max_content_length] + "... [内容过长已截断]"
            
            # 构建提示
            messages = [
                {"role": "system", "content": """你是一个专业的Web元素定位专家。
                根据提供的HTML内容和元素描述，请提供最佳的定位器策略。
                请按优先级返回以下定位器方式: id, name, text, aria-label, placeholder, class, xPath, CSS选择器。
                仅返回JSON格式的结果，不需要解释。
                格式如下：
                {
                    "id": "元素ID",
                    "name": "元素name属性",
                    "text": "元素文本内容",
                    "aria-label": "元素aria-label属性",
                    "placeholder": "元素placeholder属性",
                    "class": "元素class属性",
                    "xpath": "元素的xpath路径",
                    "css": "元素的CSS选择器"
                }
                如果某种定位方式不适用，可以省略该字段。"""},
                {"role": "user", "content": f"根据以下HTML内容，请为描述为'{target_element_description}'的元素提供最佳定位器:\n\n{page_source}"}
            ]
            
            # 调用大模型API
            response_text = await self.call_llm_api_async(messages)
            
            if not response_text:
                logger.warning("优化元素定位器失败: 大模型返回空响应")
                return {}
            
            # 解析JSON响应
            return self.extract_json(response_text)
        except Exception as e:
            logger.error(f"异步优化元素定位器失败: {str(e)}")
            return {}