# -*- coding: utf-8 -*-
"""
图像识别模块

此模块负责实现基于图像识别的元素定位功能，使用OpenCV进行图像处理和匹配。
"""

import os
import logging
from typing import Tuple, Optional, Dict, List

import cv2
import numpy as np
from config.config import LOCATOR_CONFIG

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ImageRecognition:
    """
    图像识别类
    
    提供基于图像的元素定位功能，使用模板匹配和特征匹配等技术。
    """
    
    def __init__(self, similarity_threshold: float = None):
        """
        初始化图像识别模块
        
        Args:
            similarity_threshold: 图像匹配相似度阈值，默认使用配置中的值
        """
        self.similarity_threshold = similarity_threshold or LOCATOR_CONFIG['screenshot_similarity_threshold']
        logger.info(f"图像识别模块初始化完成，相似度阈值: {self.similarity_threshold}")
    
    def find_element_by_template(self, screenshot_path: str, template_path: str) -> Optional[Tuple[int, int, int, int]]:
        """
        使用模板匹配在截图中查找元素
        
        Args:
            screenshot_path: 页面截图路径
            template_path: 元素模板图片路径
            
        Returns:
            Optional[Tuple[int, int, int, int]]: 元素位置(x, y, width, height)，如果未找到则返回None
        """
        try:
            # 读取图片
            screenshot = cv2.imread(screenshot_path)
            template = cv2.imread(template_path)
            
            if screenshot is None or template is None:
                logger.error(f"无法读取图片: {screenshot_path} 或 {template_path}")
                return None
            
            # 转换为灰度图
            screenshot_gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)
            template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
            
            # 获取模板尺寸
            h, w = template_gray.shape
            
            # 执行模板匹配
            result = cv2.matchTemplate(screenshot_gray, template_gray, cv2.TM_CCOEFF_NORMED)
            
            # 获取最佳匹配位置
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            # 如果相似度高于阈值，返回位置信息
            if max_val >= self.similarity_threshold:
                x, y = max_loc
                return (x, y, w, h)
            else:
                logger.warning(f"未找到匹配元素，最佳匹配相似度 {max_val} 低于阈值 {self.similarity_threshold}")
                return None
        except Exception as e:
            logger.error(f"模板匹配失败: {str(e)}")
            return None
    
    def find_element_by_features(self, screenshot_path: str, template_path: str) -> Optional[Tuple[int, int, int, int]]:
        """
        使用特征匹配在截图中查找元素，适用于元素有旋转、缩放等变化的情况
        
        Args:
            screenshot_path: 页面截图路径
            template_path: 元素模板图片路径
            
        Returns:
            Optional[Tuple[int, int, int, int]]: 元素位置(x, y, width, height)，如果未找到则返回None
        """
        try:
            # 读取图片
            screenshot = cv2.imread(screenshot_path)
            template = cv2.imread(template_path)
            
            if screenshot is None or template is None:
                logger.error(f"无法读取图片: {screenshot_path} 或 {template_path}")
                return None
            
            # 转换为灰度图
            screenshot_gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)
            template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
            
            # 初始化SIFT特征检测器
            sift = cv2.SIFT_create()
            
            # 检测关键点和描述符
            kp1, des1 = sift.detectAndCompute(template_gray, None)
            kp2, des2 = sift.detectAndCompute(screenshot_gray, None)
            
            # 如果没有足够的特征点，返回None
            if des1 is None or des2 is None or len(kp1) < 2 or len(kp2) < 2:
                logger.warning("未检测到足够的特征点")
                return None
            
            # 使用FLANN匹配器进行特征匹配
            FLANN_INDEX_KDTREE = 1
            index_params = dict(algorithm=FLANN_INDEX_KDTREE, trees=5)
            search_params = dict(checks=50)
            flann = cv2.FlannBasedMatcher(index_params, search_params)
            matches = flann.knnMatch(des1, des2, k=2)
            
            # 应用比率测试筛选良好匹配
            good_matches = []
            for m, n in matches:
                if m.distance < 0.7 * n.distance:
                    good_matches.append(m)
            
            # 如果良好匹配点数量足够多
            if len(good_matches) > 10:
                # 获取匹配点坐标
                src_pts = np.float32([kp1[m.queryIdx].pt for m in good_matches]).reshape(-1, 1, 2)
                dst_pts = np.float32([kp2[m.trainIdx].pt for m in good_matches]).reshape(-1, 1, 2)
                
                # 计算单应性矩阵
                M, mask = cv2.findHomography(src_pts, dst_pts, cv2.RANSAC, 5.0)
                h, w = template_gray.shape
                
                # 计算模板在原图中的位置
                pts = np.float32([[0, 0], [0, h-1], [w-1, h-1], [w-1, 0]]).reshape(-1, 1, 2)
                dst = cv2.perspectiveTransform(pts, M)
                
                # 计算边界框
                x_coords = [pt[0][0] for pt in dst]
                y_coords = [pt[0][1] for pt in dst]
                x = min(x_coords)
                y = min(y_coords)
                w = max(x_coords) - x
                h = max(y_coords) - y
                
                return (int(x), int(y), int(w), int(h))
            else:
                logger.warning(f"未找到足够的特征匹配点，只有 {len(good_matches)} 个良好匹配")
                return None
        except Exception as e:
            logger.error(f"特征匹配失败: {str(e)}")
            return None
    
    def save_element_template(self, screenshot_path: str, element_rect: Tuple[int, int, int, int], template_path: str) -> bool:
        """
        从截图中提取元素区域并保存为模板
        
        Args:
            screenshot_path: 页面截图路径
            element_rect: 元素区域(x, y, width, height)
            template_path: 保存模板的路径
            
        Returns:
            bool: 保存是否成功
        """
        try:
            # 读取截图
            screenshot = cv2.imread(screenshot_path)
            if screenshot is None:
                logger.error(f"无法读取截图: {screenshot_path}")
                return False
            
            # 提取元素区域
            x, y, w, h = element_rect
            element_img = screenshot[y:y+h, x:x+w]
            
            # 确保目录存在
            os.makedirs(os.path.dirname(template_path), exist_ok=True)
            
            # 保存模板
            cv2.imwrite(template_path, element_img)
            logger.info(f"已保存元素模板到: {template_path}")
            return True
        except Exception as e:
            logger.error(f"保存元素模板失败: {str(e)}")
            return False
    
    def capture_element_from_user_selection(self, screenshot_path: str, template_path: str) -> Optional[Tuple[int, int, int, int]]:
        """
        从用户选择的区域捕获元素模板
        
        注意：此方法在实际项目中需要与UI交互，这里仅作为示例框架
        
        Args:
            screenshot_path: 页面截图路径
            template_path: 保存模板的路径
            
        Returns:
            Optional[Tuple[int, int, int, int]]: 选择的元素区域，如果取消则返回None
        """
        # 注意：这个方法在实际项目中需要实现与用户界面的交互
        # 这里仅作为示例框架，实际实现可能需要使用如Tkinter等GUI库
        # 或者在Web界面中实现选择功能
        
        # 模拟用户选择区域的结果
        logger.info("此方法需要在实际项目中实现用户交互界面")
        return None