# -*- coding: utf-8 -*-
"""
DOM视图模块

此模块定义DOM树和元素的数据模型，用于表示网页DOM结构。
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Union


@dataclass
class DOMBaseNode:
    """DOM节点基类"""
    is_visible: bool = False
    parent: Optional['DOMBaseNode'] = None


@dataclass
class DOMTextNode:
    """文本节点类，表示DOM中的文本内容"""
    text: str  # 必须的参数放在前面
    is_visible: bool = False
    parent: Optional['DOMBaseNode'] = None


@dataclass
class CoordinateSet:
    """坐标集"""
    x: float
    y: float
    width: float
    height: float


@dataclass
class ViewportInfo:
    """视口信息"""
    width: int
    height: int


@dataclass
class DOMElementNode:
    """
    元素节点类，表示DOM中的HTML元素
    
    属性:
        tag_name: 标签名
        xpath: 元素的XPath路径
        attributes: 元素属性字典
        children: 子元素列表
        is_interactive: 是否可交互
        is_top_element: 是否是顶层元素
        is_in_viewport: 是否在视口内
        shadow_root: 是否有Shadow DOM
        highlight_index: 元素高亮索引
        viewport_coordinates: 视口坐标
        page_coordinates: 页面坐标
        viewport_info: 视口信息
    """
    tag_name: str
    xpath: str
    attributes: Dict[str, str]
    children: List[Any] = field(default_factory=list)
    is_visible: bool = False
    is_interactive: bool = False
    is_top_element: bool = False
    is_in_viewport: bool = False
    shadow_root: bool = False
    highlight_index: Optional[int] = None
    viewport_coordinates: Optional[CoordinateSet] = None
    page_coordinates: Optional[CoordinateSet] = None
    viewport_info: Optional[ViewportInfo] = None
    parent: Optional[Any] = None
    
    def clickable_elements_to_string(self, include_attributes: bool = False) -> str:
        """
        将当前节点下所有可点击元素转换为字符串表示
        
        参数:
            include_attributes: 是否包含元素属性
            
        返回:
            str: 可点击元素的字符串表示
        """
        lines = []
        self._collect_clickable_elements(self, lines, include_attributes)
        return "\n".join(lines)
    
    def _collect_clickable_elements(self, node: 'DOMElementNode', lines: List[str], include_attributes: bool) -> None:
        """
        递归收集所有可点击元素
        
        参数:
            node: DOM元素节点
            lines: 收集结果的行列表
            include_attributes: 是否包含元素属性
        """
        if node.is_interactive and node.highlight_index is not None:
            element_text = self._get_element_text(node)
            attrs_text = self._get_attributes_text(node) if include_attributes else ""
            
            lines.append(f"[{node.highlight_index}]<{node.tag_name}>{element_text}{attrs_text}</{node.tag_name}>")
        
        for child in node.children:
            if isinstance(child, DOMElementNode):
                self._collect_clickable_elements(child, lines, include_attributes)
    
    def _get_element_text(self, node: 'DOMElementNode') -> str:
        """获取元素的文本内容"""
        text_parts = []
        
        for child in node.children:
            if isinstance(child, DOMTextNode) and child.is_visible:
                text_parts.append(child.text)
        
        return " ".join(text_parts)
    
    def _get_attributes_text(self, node: 'DOMElementNode') -> str:
        """获取元素的属性文本"""
        attr_parts = []
        
        important_attrs = ["id", "name", "type", "value", "placeholder", "role", "aria-label"]
        for attr in important_attrs:
            if attr in node.attributes:
                attr_parts.append(f'{attr}="{node.attributes[attr]}"')
        
        if attr_parts:
            return f" ({', '.join(attr_parts)})"
        return ""


# 选择器映射类型，将highlight_index映射到DOM元素节点
SelectorMap = Dict[int, DOMElementNode]


@dataclass
class DOMState:
    """
    DOM状态类，包含DOM树和选择器映射
    
    属性:
        element_tree: DOM元素树
        selector_map: 选择器映射
    """
    element_tree: DOMElementNode
    selector_map: SelectorMap
