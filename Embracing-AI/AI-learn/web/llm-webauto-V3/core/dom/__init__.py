# -*- coding: utf-8 -*-
"""
DOM操作模块

提供DOM分析、处理和元素定位功能
"""

import logging

logger = logging.getLogger(__name__)

from .service import DOMService
from .views import (
    DOMBaseNode,
    DOMElementNode,
    DOMTextNode,
    DOMState,
    SelectorMap,
    ViewportInfo,
    CoordinateSet
)

__all__ = [
    'DOMService',
    'DOMBaseNode',
    'DOMElementNode',
    'DOMTextNode',
    'DOMState',
    'SelectorMap',
    'ViewportInfo',
    'CoordinateSet'
]
