# config.py
import os
from dotenv import load_dotenv
import json
from typing import Dict, Any

# 加载环境变量
load_dotenv()

# 应用全局配置
APP_CONFIG = {
    "environment": "sit",  # 可选: dev, sit, uat, prod
    "base_url": "https://hy-sit.1233s2b.com",
    "user_credentials": {
        "mobile": "13600805241",
        "password": "Aa123456",
    },
    "environment": "prod",  # 可选: dev, sit, uat, prod
    "base_url": "https://trade.1233s2b.com",
    "user_credentials": {
        "mobile": "80550549",
        "password": "aA123456",
    }
}

# 大模型配置
LLM_CONFIG = {
    "api_type": "openai",
    "api_key": os.getenv("OPENAI_API_KEY", "sk-qnhrgxkfjdhaiwbdblefelnfratgyjqnustbusxkrkehfllu"),
    "api_base": os.getenv("OPENAI_API_BASE", "https://api.siliconflow.cn/v1"),
    "model": os.getenv("OPENAI_MODEL", "Qwen/Qwen2.5-14B-Instruct"),
    "temperature": 0.7,
    "max_tokens": 4096,
    "top_p": 0.95,  # 保持输出的多样性但不至于过于发散
}

# 浏览器配置
BROWSER_CONFIG = {
    # 基本设置
    "default_browser": "chromium",
    "headless": False,
    "slow_mo": 100,
    "maximize": True,  # 启用浏览器窗口最大化
    "fullscreen": True,  # 启用全屏模式（特别适用于macOS）
    
    # 超时和等待设置
    "timeout": 30,
    "polling_interval": 0.5,
    "retry_times": 3,
    
    # 视图和行为设置
    "viewport": {
        "width": 1440,
        "height": 900
    },
    "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36",
    
    # 文件和路径设置
    "screenshot_dir": os.path.join(os.getcwd(), "screenshots"),
    "video_dir": os.path.join(os.getcwd(), "videos"),
    # "download_dir": os.path.join(os.getcwd(), "downloads"),
    "cache_dir": os.path.join(os.getcwd(), ".browser_cache"),
    
    # 功能开关
    "save_screenshot_on_failure": True,
    "save_screenshot_on_step": False,
    "use_cache": False,
}

# 元素定位配置
LOCATOR_CONFIG = {
    # 是否启用本地元素库
    'enable_local_locator': True,
    
    # 是否在本地元素库失败后尝试实时定位
    'fallback_to_realtime': True,
    
    # 元素库文件路径
    'locators_file': os.path.join(os.path.dirname(os.path.abspath(__file__)), 'locators.json'),
    
    # 元素定位失败的重试次数
    'retry_count': 3,
    
    # 元素可见性检查超时时间(毫秒)
    'visibility_timeout': 3000,
    
    # 定位器权重计算参数
    'weight_factors': {
        'success_rate': 0.7,  # 成功率权重
        'stability': 0.2,     # 稳定性权重
        'custom': 0.1         # 自定义权重
    },

    # 图像识别配置
    'screenshot_similarity_threshold': 0.8,  # 图像识别相似度阈值
    'enable_image_recognition': True,        # 是否启用图像识别
    'image_recognition_timeout': 5000,       # 图像识别超时时间(毫秒)
    'image_recognition_retry': 3,            # 图像识别重试次数

    # 实时定位相关配置
    'enable_realtime_locating': True,  # 启用实时元素定位
    'try_local_locator_first': True,   # 优先使用本地元素库
    'highlight_elements': True,        # 高亮显示可交互元素
    'viewport_expansion': 500,         # 视口扩展像素数
    'add_dynamic_locators': True,      # 是否添加动态定位器到元素库
    
    # 文本匹配配置
    'min_similarity_threshold': 0.7,   # 文本匹配的最小相似度阈值
    
    # 增强定位配置
    'enable_enhanced_locating': True,  # 是否启用增强定位策略
    
    # 智能定位配置
    'enhanced_locating': {
        'enable_synonym_matching': True,        # 是否启用文本同义词匹配
        'enable_nested_text_analysis': True,    # 是否启用嵌套文本分析
        'enable_dom_structure_analysis': True,  # 是否启用DOM结构分析
        'auto_add_smart_locators': True,        # 是否自动添加智能定位器到本地元素库
        'smart_locator_priority': 2,            # 智能定位器的默认优先级
        
        # 特殊元素类型的处理配置
        'element_type_handlers': {
            'button': True,     # 按钮特殊处理
            'input': True,      # 输入框特殊处理
            'checkbox': True,   # 复选框特殊处理
            'select': True,     # 下拉框特殊处理
            'link': True        # 链接特殊处理
        }
    }
}

# 测试配置
TEST_CONFIG = {
    # 基本测试设置
    "default_timeout": 10000,
    "retry_attempts": 3,
    "retry_delay": 1000,
    
    # 日志设置
    "log_level": "INFO",
    
    # 测试报告设置
    "report_dir": os.path.join(os.getcwd(), "reports"),
    "report_format": "html",
    
    # 重试策略
    "retry_on_failure": True,
    "max_retries": 3,
    
    # 并行执行设置
    "parallel": False,
    "max_workers": 4,
    
    # 测试数据设置
    "data_dir": os.path.join(os.getcwd(), "test_data"),
}

# 确保目录存在
def ensure_directories():
    """确保必要的目录存在"""
    directories = [
        BROWSER_CONFIG["screenshot_dir"],
        BROWSER_CONFIG["video_dir"],
        # BROWSER_CONFIG["download_dir"],
        TEST_CONFIG["report_dir"],
        # TEST_CONFIG["data_dir"],
    ]
    
    # 如果启用缓存，确保缓存目录存在
    if BROWSER_CONFIG["use_cache"]:
        directories.append(BROWSER_CONFIG["cache_dir"])
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)

# 初始化时创建目录
ensure_directories()