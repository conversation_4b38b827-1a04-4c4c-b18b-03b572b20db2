# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# 虚拟环境
venv/
ENV/
.env

# 测试输出
screenshots/*
!screenshots/.gitkeep
videos/*
!videos/.gitkeep
reports/*
*.json

# 日志文件
*.log
txt
logs/*

# 备份文件
*.bak
*.backup
*.bak[0-9]

# IDE
.idea/
.vscode/
*.swp
*.swo

# 系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 项目特定文件
*.pyc
__pycache__/
.pytest_cache/
.coverage
htmlcov/

# 敏感配置文件
config/secrets.json
config/credentials.json