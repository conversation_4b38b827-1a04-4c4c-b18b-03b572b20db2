# 自然语言驱动的Web自动化测试框架

本项目是一个基于自然语言理解的Web自动化测试框架，结合了大模型(LLM)的能力，实现了更加智能的测试用例生成、执行和元素定位。该框架支持同步和异步操作，能够处理复杂的Web交互场景。

## 🌟 特性

- 🤖 LLM驱动：利用大模型的强大自然语言理解能力生成测试用例和优化执行
- 🎭 Playwright支持：底层基于Playwright实现跨浏览器的自动化测试
- 🔍 智能元素定位：自动分析页面结构，推荐最优的元素定位策略
- 🔄 实时元素定位：当预定义元素定位失败时，自动进行DOM分析和实时定位
- 📑 关键字驱动：支持通过自然语言关键字控制测试流程
- 🖼️ 视觉辅助：支持图像识别辅助元素定位
- 🧠 测试代理：基于LLM的自主测试代理，能够理解自然语言任务并执行测试
- ⏱️ 智能异步等待：点击操作后自动等待页面加载和URL变化

## 📚 框架结构

项目整体结构如下：

```
llm-webauto/
├── config/             # 配置文件目录
├── core/               # 核心功能模块
│   ├── agent_prompt.py      # 测试代理提示词管理
│   ├── agent_service.py     # 测试代理服务实现
│   ├── element_manager.py   # 元素管理模块
│   ├── element_manager_realtime.py # 实时元素定位模块
│   ├── element_locating_utils.py   # 元素定位工具
│   ├── dom/                 # DOM分析模块
│   │   ├── service.py       # DOM服务
│   │   ├── views.py         # DOM视图定义
│   │   └── buildDomTree.js  # 浏览器端DOM树构建脚本
│   ├── image_recognition.py # 图像识别模块
│   ├── keyword_driver.py    # 关键字驱动模块
│   └── llm_integration.py   # 大模型集成模块
├── docs/               # 文档目录
├── screenshots/        # 截图目录
├── videos/             # 视频录制目录
├── logs/               # 日志目录
├── reports/            # 测试报告目录
├── run_agent_test.py   # 测试代理执行入口
└── requirements.txt    # 项目依赖
```

## 🚀 快速开始

### 环境设置

1. 安装依赖：

```bash
pip install -r requirements.txt
playwright install
```

2. 配置大模型API：

编辑 `config/config.py` 文件，设置你的API密钥：

```python
LLM_CONFIG = {
    'api_key': 'your-api-key',
    'api_base': 'https://api.openai.com/v1',  # 可选，默认为OpenAI
    'model': 'gpt-4',
    'temperature': 0.2,
    'max_tokens': 4000
}
```

### 运行测试用例

#### 使用自然语言测试代理

```bash
# 使用默认示例测试
python run_agent_test.py

# 指定测试任务描述
python run_agent_test.py -t "打开Chrome浏览器，访问百度，搜索Playwright"

# 从文件读取测试任务描述
python run_agent_test.py -f your_test_description.txt

# 指定截图保存目录
python run_agent_test.py -s screenshots/custom_dir

# 设置最大步骤数
python run_agent_test.py -m 50

# 禁用实时元素定位（使用本地元素库）
python run_agent_test.py -dr
```

## 🧠 测试代理功能

测试代理是本框架的核心创新功能，它能够：

1. 理解并执行用自然语言描述的测试任务
2. 自动规划测试步骤并执行
3. 智能处理异常情况和页面变化
4. 维护测试执行状态和记忆
5. 生成测试报告和截图

测试代理基于大模型，通过封装好的提示词工程，可以：

- 自动分析页面元素和布局
- 选择合适的交互方式和元素定位策略
- 验证操作执行结果
- 完成复杂的测试流程

### 代理工作流程

```
自然语言任务描述 → 代理规划步骤 → 执行单个步骤 → 
获取页面状态 → 分析执行结果 → 规划下一步 → ... → 完成测试

执行调用链路
入口点: main.py中的main()函数解析命令行参数
执行流程:
main() → asyncio.run(run_test_and_handle_result())
run_test_and_handle_result() → await run_test()
run_test() → 初始化TestAgent对象 → await agent.run()
agent.run() → await self._parse_task() → 对每个步骤执行await self._execute_step()
```

## 🔍 智能元素定位系统

该框架采用了多层次的元素定位策略，确保在各种复杂Web环境中能够准确识别和操作元素。

### 1. 元素定位核心流程

元素定位采用多级定位策略：

1. **本地元素库查找**：首先在预定义的本地元素库中查找元素定位器
2. **实时元素定位**：当本地定位失败时，启用实时DOM分析定位
   - 提取DOM树结构并分析可交互元素
   - 生成多种定位策略（ID、文本、角色、CSS等）
   - 尝试定位并验证元素可见性和可交互性
3. **动态更新元素库**：将成功的定位策略保存到本地元素库中
4. **自动修复机制**：对于频繁失败的元素定位器，自动分析和修复

### 2. DOM树构建与解析

1. **DOM结构分析**：使用JavaScript注入浏览器，构建完整DOM树
2. **元素属性提取**：分析元素的属性、样式、位置和交互特性
3. **可见性检测**：使用几何和样式分析确保元素可见
4. **交互性验证**：验证元素是否可点击、可输入等

### 3. 实时元素定位优势

- **动态适应页面变化**：能够处理动态加载和AJAX更新的页面
- **自学习能力**：记录成功的定位策略，不断优化定位库
- **多策略并行**：同时尝试多种定位方法，提高成功率
- **语义理解**：结合元素文本和上下文信息进行智能匹配
- **视觉辅助**：在传统定位方法失败时，可结合图像识别

## ⏱️ 智能异步等待机制

框架实现了智能的异步等待机制，特别是在点击操作后，能够有效处理页面导航和内容刷新：

1. **点击后自动等待**：
   - 点击元素后自动等待页面DOM内容加载
   - 等待网络活动完成，确保AJAX请求结束
   - 检测URL变化，识别页面跳转

2. **URL变化检测**：
   - 点击前记录当前URL
   - 点击后监控URL变化，包括路径和片段(fragment)
   - 当检测到URL变化时，清除旧高亮并重建DOM分析

3. **DOM状态同步**：
   - 点击后等待一段时间确保DOM更新
   - 强制清除所有高亮元素，避免旧页面高亮干扰
   - 重新分析DOM结构，更新元素定位信息

4. **智能恢复机制**：
   - 当操作失败时，自动分析原因
   - 提供替代步骤和恢复策略
   - 支持重试和降级操作

## 📝 自定义测试

### 创建关键字

可以在 `core/keyword_driver.py` 中扩展自定义关键字：

```python
def my_custom_keyword(self, param1, param2):
    """
    自定义关键字实现
    """
    # 实现自定义逻辑
    pass
```

### 创建测试用例

创建一个新的测试文件，使用关键字驱动的方式：

```python
from core.test_case_base import TestCase

def create_my_test_case():
    steps = [
        {"keyword": "打开浏览器", "args": ["chromium"]},
        {"keyword": "导航到", "args": ["https://example.com"]},
        {"keyword": "等待元素可见", "args": ["登录按钮"]},
        {"keyword": "点击", "args": ["登录按钮"]},
        {"keyword": "等待页面加载完成", "args": []},
        {"keyword": "验证当前页面包含文本", "args": ["登录成功"]},
    ]
    return TestCase("我的测试", steps)
```

## 📊 测试报告

测试代理执行后会在截图目录下生成 Markdown 格式的测试报告，包含：

- 测试任务描述
- 执行统计信息
- 每个步骤的详细信息和截图
- 执行结果评估

## 🛠️ 开发和贡献

欢迎贡献代码和提出建议！请确保代码符合项目的代码风格和测试要求。

## 📄 许可证

本项目采用 MIT 许可证。

## 📞 联系方式

如有任何问题或建议，请提交 Issue 或联系项目维护者。

## 使用Streamlit界面

本项目提供了一个基于Streamlit的用户界面，使测试执行更加便捷。

### 安装依赖

确保已安装所有依赖包：

```bash
pip install -r requirements.txt
```

### 启动Streamlit应用

运行以下命令启动Streamlit界面：

```bash
streamlit run app.py
```

界面将在浏览器中自动打开，通常地址为 http://localhost:8501

### 界面功能

1. **测试设置**：
   - 选择浏览器（目前仅支持Chromium）
   - 选择模型提供商和模型（仅展示，不影响测试执行）
   - 输入测试任务描述
   - 配置高级选项（无头模式、截图保存、最大步骤数等）

2. **执行测试**：点击"执行测试"按钮后，系统会调用run_agent_test.py脚本执行测试任务

3. **查看报告**：界面右侧可以查看历史测试报告和截图

Streamlit界面简化了测试执行过程，无需手动输入命令行参数，适合非技术人员使用。