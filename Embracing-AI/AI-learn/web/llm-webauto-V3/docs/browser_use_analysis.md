# Browser-Use项目分析报告

Browser-Use是一个将AI代理与浏览器连接的项目，它能够使AI模型控制浏览器执行复杂的交互任务。下面将详细分析其实现原理和核心流程。

## 架构概览

Browser-Use项目由以下几个核心模块组成：

1. **Agent模块**：AI代理的主要实现，负责与LLM交互并规划执行任务
2. **Browser模块**：浏览器控制和配置的核心实现
3. **Controller模块**：执行具体浏览器操作的控制层
4. **DOM模块**：DOM树构建、元素定位和高亮的核心实现

这些模块协同工作，形成了一个完整的浏览器自动化控制系统。

## 1. 实时元素定位

### 核心实现流程

1. **DOM元素定位机制**：
   
   Browser-Use使用多种方法来定位DOM元素：
   
   - **索引定位**：每个可交互元素被分配一个唯一的索引号，使LLM可以通过索引引用元素
   - **CSS选择器定位**：支持使用CSS选择器精确定位元素
   - **XPath定位**：支持使用XPath表达式定位元素
   - **文本内容定位**：可以通过元素包含的文本内容来定位元素

2. **元素定位实现**：

   在`browser_use/browser/context.py`中，`BrowserContext`类实现了多种元素定位方法：
   
   ```python
   async def get_locate_element(self, element: DOMElementNode) -> Optional[ElementHandle]:
       # 基于DOM节点定位元素
   
   async def get_locate_element_by_xpath(self, xpath: str) -> Optional[ElementHandle]:
       # 使用XPath定位元素
   
   async def get_locate_element_by_css_selector(self, css_selector: str) -> Optional[ElementHandle]:
       # 使用CSS选择器定位元素
   
   async def get_locate_element_by_text(self, text: str, nth: Optional[int] = 0, element_type: Optional[str] = None) -> Optional[ElementHandle]:
       # 使用文本内容定位元素
   ```

3. **元素定位优化**：

   - 使用缓存机制避免重复查询DOM元素
   - 实现可重试的元素定位策略，应对页面变化情况
   - 构建增强的CSS选择器，增加元素定位的准确性

## 2. 实时DOM构建分析及索引高亮

### DOM树构建流程

1. **DOM树提取**：

   在`browser_use/dom/service.py`中，`DomService`类实现了DOM树的提取和分析：
   
   ```python
   async def get_clickable_elements(
       self,
       highlight_elements: bool = True,
       focus_element: int = -1,
       viewport_expansion: int = 0,
   ) -> DOMState:
       # 获取可点击元素并构建DOM树
   ```

2. **DOM树构建流程**：

   DOM树构建通过浏览器JavaScript注入实现：
   
   - 使用`buildDomTree.js`脚本在浏览器中执行DOM分析
   - 递归遍历DOM结构，构建包含交互属性的树状结构
   - 识别可交互和可见元素，分配唯一索引
   - 优化性能：使用缓存、仅处理关键元素等策略

3. **元素高亮实现**：

   高亮可交互元素以增强视觉辅助：
   
   ```javascript
   // buildDomTree.js中的实现
   function highlightElement(element, index, parentIframe = null) {
     // 创建高亮容器和元素
     // 为每个可交互元素创建高亮框
     // 根据元素位置和大小调整高亮框
   }
   ```

4. **元素索引和选择器映射**：

   - 创建DOM元素到索引的映射(`selector_map`)
   - 每个交互元素被分配唯一的索引号
   - 索引与DOM元素保持映射，用于后续操作

### DOM视图模型

DOM视图模型定义了DOM树的数据结构：

```python
@dataclass(frozen=False)
class DOMBaseNode:
    is_visible: bool
    parent: Optional['DOMElementNode']

@dataclass(frozen=False)
class DOMTextNode(DOMBaseNode):
    text: str
    type: str = 'TEXT_NODE'
    # 文本节点特有方法...

@dataclass(frozen=False)
class DOMElementNode(DOMBaseNode):
    tag_name: str
    xpath: str
    attributes: Dict[str, str]
    children: List[DOMBaseNode]
    is_interactive: bool = False
    is_top_element: bool = False
    is_in_viewport: bool = False
    shadow_root: bool = False
    highlight_index: Optional[int] = None
    # 更多属性和方法...
```

## 3. LLM提示词工程

### 系统提示词设计

1. **系统提示词结构**：

   在`browser_use/agent/system_prompt.md`中定义了完整的系统提示词，包括：
   
   - 输入格式说明：描述任务、当前URL、打开的标签页、交互元素等
   - 响应规则：规定JSON响应格式、操作规则等
   - 元素交互规则：如何与页面元素交互
   - 导航和错误处理：如何处理导航和错误情况
   - 任务完成规则：何时完成任务以及如何报告结果
   - 视觉上下文：如何理解页面布局和元素标签

2. **提示词包装器**：

   ```python
   class SystemPrompt:
       def __init__(
           self,
           action_description: str,
           max_actions_per_step: int = 10,
           override_system_message: Optional[str] = None,
           extend_system_message: Optional[str] = None,
       ):
           # 构建系统提示词
   ```

### 状态表示与信息传递

1. **浏览器状态表示**：

   每次状态更新时，构建一个包含以下信息的消息：
   
   ```python
   def get_user_message(self, use_vision: bool = True) -> HumanMessage:
       # 构建包含当前状态的用户消息
       state_description = f"""
   [Task history memory ends]
   [Current state starts here]
   The following is one-time information - if you need to remember it write it to memory:
   Current url: {self.state.url}
   Available tabs:
   {self.state.tabs}
   Interactive elements from top layer of the current page inside the viewport:
   {elements_text}
   {step_info_description}
   """
   ```

2. **视觉信息集成**：

   结合视觉信息（屏幕截图）和文本结构：
   
   ```python
   if self.state.screenshot and use_vision == True:
       # 为视觉模型格式化消息
       return HumanMessage(
           content=[
               {'type': 'text', 'text': state_description},
               {
                   'type': 'image_url',
                   'image_url': {'url': f'data:image/png;base64,{self.state.screenshot}'},
               },
           ]
       )
   ```

### 代理决策和行动执行

1. **代理决策流程**：

   在`agent/service.py`中，`Agent`类实现了核心决策流程：
   
   ```python
   async def step(self, step_info: Optional[AgentStepInfo] = None) -> None:
       # 获取浏览器状态
       state = await self.browser_context.get_state()
       
       # 添加状态消息
       self._message_manager.add_state_message(state, self.state.last_result, step_info, self.settings.use_vision)
       
       # 可能运行规划器
       if self.settings.planner_llm and self.state.n_steps % self.settings.planner_interval == 0:
           plan = await self._run_planner()
           self._message_manager.add_plan(plan, position=-1)
       
       # 获取LLM的下一步操作
       model_output = await self.get_next_action(input_messages)
       
       # 执行操作
       result: list[ActionResult] = await self.multi_act(model_output.action)
   ```

2. **操作执行控制**：

   在`controller/service.py`中，`Controller`类注册和实现了各种浏览器操作：
   
   ```python
   @self.registry.action('Click element by index', param_model=ClickElementAction)
   async def click_element_by_index(params: ClickElementAction, browser: BrowserContext):
       # 点击指定索引的元素
   
   @self.registry.action('Input text into a input interactive element', param_model=InputTextAction)
   async def input_text(params: InputTextAction, browser: BrowserContext, has_sensitive_data: bool = False):
       # 在输入元素中输入文本
   ```

## 整体交互流程

完整的浏览器交互流程如下：

1. **初始化**：
   - 创建`Agent`实例，配置LLM模型和浏览器
   - 初始化提示词和控制器

2. **任务执行循环**：
   - 获取当前浏览器状态（DOM树、屏幕截图等）
   - 构建包含状态信息的提示词
   - 调用LLM获取下一步操作
   - 解析LLM响应，提取操作序列
   - 执行操作序列
   - 更新状态，记录历史

3. **DOM分析与元素交互**：
   - 注入JavaScript代码分析DOM结构
   - 构建DOM树，标记可交互元素
   - 为可交互元素分配索引，创建映射
   - 高亮显示可交互元素（可选）
   - 基于LLM决策执行元素交互（点击、输入等）

4. **任务完成**：
   - 在达成目标或达到最大步数时完成任务
   - 生成最终报告，总结任务执行结果

## 核心技术亮点

1. **实时元素定位**：
   - 多种定位策略（索引、选择器、XPath、文本）
   - 缓存和优化机制，提高定位效率
   - 处理页面变化和动态元素

2. **DOM构建与高亮**：
   - 高效的DOM树构建和分析
   - 智能标记和高亮可交互元素
   - 视觉反馈与索引映射

3. **LLM提示词工程**：
   - 结构化的系统提示词设计
   - 丰富的状态表示与信息传递
   - 结合视觉和文本的多模态信息

4. **操作执行与控制**：
   - 丰富的浏览器操作集
   - 灵活的操作组合和序列执行
   - 错误处理和恢复机制

## 总结

Browser-Use项目通过实时元素定位、DOM构建分析与高亮、精心设计的LLM提示词，实现了AI代理对浏览器的有效控制。其核心创新点在于将DOM树分析、元素索引与LLM决策紧密结合，使AI能够理解网页结构并执行精确的交互操作。这一架构为AI浏览器自动化提供了强大的基础，能够处理各种复杂的网页交互任务。 

## 同步与异步设计分析

Browser-Use项目充分利用了Python的异步编程特性，同时在适当的场景保留了同步实现。这种设计对于浏览器自动化至关重要，因为浏览器交互本质上是高度异步的，涉及网络请求、DOM操作和用户交互等多种异步场景。下面将详细分析项目中的同步和异步设计。

### 同步与异步函数分类

#### 核心异步实现

1. **浏览器交互核心操作**：
   - 几乎所有与浏览器直接交互的函数都使用异步实现
   - 页面导航、元素点击、输入文本等操作都使用`async/await`模式

   ```python
   # browser/context.py
   async def navigate(self, url: str) -> None:
       # 异步实现页面导航
   
   async def click_element(self, element: ElementHandle) -> None:
       # 异步实现元素点击
   
   async def input_text(self, element: ElementHandle, text: str) -> None:
       # 异步实现文本输入
   ```

2. **DOM树构建与分析**：
   - DOM树的提取和分析是通过异步方式实现的
   - JavaScript执行和DOM操作本质上是异步的

   ```python
   # dom/service.py
   async def get_clickable_elements(self, highlight_elements: bool = True) -> DOMState:
       # 异步获取可点击元素
   
   async def _build_dom_tree(self, highlight_elements: bool) -> tuple[DOMElementNode, SelectorMap]:
       # 异步构建DOM树
   ```

3. **代理决策与行动执行**：
   - 代理的核心决策循环是异步的
   - LLM调用和操作执行都通过异步实现

   ```python
   # agent/service.py
   async def run(self) -> AgentHistoryList:
       # 异步运行代理
   
   async def step(self, step_info: Optional[AgentStepInfo] = None) -> None:
       # 异步执行单个步骤
   
   async def get_next_action(self, input_messages: List[BaseMessage]) -> AgentOutput:
       # 异步获取下一步操作
   ```

4. **控制器操作执行**：
   - 所有注册的操作都是异步函数
   - 使用装饰器注册的操作保持异步特性

   ```python
   # controller/service.py
   @self.registry.action('Click element by index', param_model=ClickElementAction)
   async def click_element_by_index(params: ClickElementAction, browser: BrowserContext):
       # 异步点击元素
   ```

#### 主要同步实现

1. **数据模型和视图**：
   - 使用同步实现的数据模型类（Pydantic模型、数据类等）
   - 视图类通常是同步的，用于数据表示和转换

   ```python
   # dom/views.py
   @dataclass(frozen=False)
   class DOMElementNode(DOMBaseNode):
       # 同步实现的DOM元素节点类
   
   # agent/views.py
   class AgentState(BaseModel):
       # 同步实现的代理状态模型
   ```

2. **工具函数和辅助方法**：
   - 许多不涉及I/O的工具函数使用同步实现
   - 数据处理、格式化、验证等功能多为同步

   ```python
   # utils.py
   def format_url(url: str) -> str:
       # 同步URL格式化
   
   def validate_parameters(params: dict) -> bool:
       # 同步参数验证
   ```

3. **性能计时和日志记录**：
   - 性能测量装饰器有同步和异步两种版本
   - 日志记录多使用同步实现

   ```python
   # utils.py
   def time_execution_sync(name):
       # 同步函数的计时装饰器
   
   def time_execution_async(name):
       # 异步函数的计时装饰器
   ```

4. **配置和初始化**：
   - 配置解析和初始化通常是同步的
   - 环境变量处理、默认配置设置等

   ```python
   # browser/browser.py
   class BrowserConfig(BaseModel):
       # 同步的浏览器配置类
   
   # agent/service.py
   def __init__(self, task: str, llm: BaseChatModel, ...):
       # 同步的初始化方法
   ```

### 异步设计模式和技术

1. **上下文管理器模式**：
   - 浏览器和会话管理使用异步上下文管理器
   - 使用`async with`语法确保资源正确释放

   ```python
   # 在browser/context.py中
   async def __aenter__(self):
       """Async context manager entry"""
       await self._initialize_session()
       return self

   async def __aexit__(self, exc_type, exc_val, exc_tb):
       """Async context manager exit"""
       await self.close()
   ```

2. **异步迭代器模式**：
   - 用于处理异步数据流和分页内容
   
   ```python
   # 异步迭代器示例
   async for page in browser_context.get_pages():
       # 处理每个页面
   ```

3. **基于任务的并发**：
   - 使用`asyncio.gather`和`asyncio.create_task`处理并发操作
   
   ```python
   # 并发执行多个异步任务
   results = await asyncio.gather(
       browser_context.navigate(url),
       browser_context.get_state(),
       return_exceptions=True
   )
   ```

4. **超时和错误处理**：
   - 使用`asyncio.wait_for`实现超时控制
   - 异步异常处理保证错误能够被正确捕获和处理
   
   ```python
   # 带超时的异步操作
   try:
       result = await asyncio.wait_for(
           browser_context.wait_for_navigation(), 
           timeout=5.0
       )
   except asyncio.TimeoutError:
       # 处理超时
   ```

### 同步与异步的关系和协作

1. **同步包装异步**：
   - 提供同步API以便与同步代码集成
   - 使用`asyncio.run`在同步环境中执行异步代码
   
   ```python
   # 在同步环境中运行异步函数
   def run_agent(task: str) -> None:
       asyncio.run(agent.run())
   ```

2. **异步调用同步**：
   - 异步代码中调用同步函数时避免阻塞事件循环
   - 对于CPU密集型同步函数，使用线程池执行器
   
   ```python
   # 在异步代码中运行同步CPU密集型函数
   result = await asyncio.to_thread(cpu_intensive_function, arg1, arg2)
   ```

3. **混合异步/同步API**：
   - 提供同步和异步版本的API以适应不同使用场景
   - 异步版本通常以`_async`后缀命名
   
   ```python
   # 同步/异步API对
   def get_element(self, selector: str) -> Element:
       # 同步版本，内部使用asyncio.run
   
   async def get_element_async(self, selector: str) -> Element:
       # 原生异步版本
   ```

### 模块异步设计分析

1. **Agent模块**：
   - 主要接口和核心功能都是异步的
   - 状态管理和配置使用同步实现
   - LLM交互完全异步化

2. **Browser模块**：
   - 几乎所有浏览器交互操作都是异步的
   - 浏览器配置和设置使用同步实现
   - 资源管理（打开、关闭）使用异步上下文管理器

3. **Controller模块**：
   - 所有注册的操作都是异步函数
   - 操作注册机制本身是同步的
   - 控制流（条件判断、循环等）在异步函数内部实现

4. **DOM模块**：
   - DOM树构建和分析是异步的
   - DOM数据结构和表示是同步的
   - JavaScript代码注入和执行使用异步API

5. **工具和辅助模块**：
   - 文件操作如保存对话、截图等使用异步实现
   - 数据处理和转换主要使用同步实现
   - 性能计时有同步和异步两种装饰器

### 设计思考与最佳实践

1. **性能考虑**：
   - I/O密集型操作（网络请求、浏览器交互）使用异步以提高并发性能
   - CPU密集型操作（数据处理、计算）使用同步以避免事件循环阻塞
   - 使用异步缓存机制减少重复I/O操作

2. **代码可读性与维护性**：
   - 一致的异步模式应用于所有浏览器交互
   - 清晰区分同步和异步API，避免混淆
   - 使用类型提示增强代码可读性和IDE支持

3. **错误处理策略**：
   - 异步操作中的错误处理使用`try/except`和错误传播
   - 关键操作添加超时控制，防止无限等待
   - 资源释放通过异步上下文管理器保证

4. **可扩展性设计**：
   - 模块化的异步设计允许轻松添加新功能
   - 操作注册机制支持动态扩展浏览器控制能力
   - 异步代理设计便于集成不同的LLM和浏览器后端

### 总结

Browser-Use项目采用了混合的同步/异步架构设计，其中：

- **核心浏览器交互**全部使用异步实现，包括导航、元素操作、DOM分析等
- **数据模型和表示层**主要使用同步实现，简化数据结构操作
- **LLM交互和决策循环**采用异步设计，适应现代LLM API的异步特性
- **工具函数和辅助方法**根据其I/O密集程度选择同步或异步实现

这种设计充分利用了Python异步特性的优势，特别适合浏览器自动化这类高度I/O密集型的应用场景。通过异步实现核心交互逻辑，项目能够高效处理并发操作、网络延迟和用户交互，同时保持代码的可读性和可维护性。 

## 执行过程GIF生成设计

Browser-Use项目实现了任务执行过程的GIF生成功能，使用户能够直观地查看和分享AI代理如何完成任务的整个流程。这一功能通过捕获每个步骤的截图并合成动画GIF，为任务执行提供了视觉记录。

### 设计思想与目标

1. **可视化交互历史**：
   - 将抽象的AI代理行为转化为直观可视的操作记录
   - 帮助用户理解AI代理如何一步步完成任务
   - 便于分享、展示和调试AI代理行为

2. **教学和演示价值**：
   - 为新用户提供直观的使用示例
   - 展示AI代理处理复杂任务的能力
   - 支持创建教程和演示材料

3. **调试和审计**：
   - 记录每个步骤的执行状态，便于回溯和调试
   - 可视化验证AI代理行为是否符合预期
   - 提供任务执行的完整历史记录

### 实现架构

GIF生成功能主要由以下组件组成：

1. **截图捕获系统**：
   - 在每个代理步骤中捕获浏览器状态
   - 存储为base64编码的图像数据

2. **历史记录管理**：
   - 维护代理执行步骤的完整历史
   - 存储每个步骤的状态、截图和决策

3. **GIF合成引擎**：
   - 将截图序列合成为动画GIF
   - 添加文本说明和视觉装饰

### 核心实现分析

1. **截图捕获**：

   在`browser_use/browser/context.py`中实现了截图功能：

   ```python
   async def take_screenshot(self) -> str:
       """
       Returns a base64 encoded screenshot of the current page.
       """
       page = await self.get_current_page()
       # 使用Playwright捕获截图
       screenshot = await page.screenshot(
           type='png',
           full_page=False,
           clip=None,
       )
       # 转换为base64格式
       screenshot_b64 = base64.b64encode(screenshot).decode('utf-8')
       # 返回base64编码的截图
       return screenshot_b64
   ```

   在获取浏览器状态时，会自动捕获截图：

   ```python
   async def get_state(self) -> BrowserState:
       # 捕获当前页面的截图
       screenshot_b64 = await self.take_screenshot()
       
       # 构建并返回浏览器状态，包含截图
       return BrowserState(
           url=url,
           tabs=tabs_info,
           element_tree=dom_state.element_tree,
           selector_map=dom_state.selector_map,
           screenshot=screenshot_b64,
           # 其他状态数据...
       )
   ```

2. **GIF生成实现**：

   在`browser_use/agent/gif.py`中定义了GIF生成的核心功能：

   ```python
   def create_history_gif(
       task: str,
       history: AgentHistoryList,
       output_path: str = 'agent_history.gif',
       duration: int = 3000,
       show_goals: bool = True,
       show_task: bool = True,
       show_logo: bool = False,
       # 其他参数...
   ) -> None:
       """Create a GIF from the agent's history with overlaid task and goal text."""
       # 从历史记录中提取截图
       if not history.history or not history.history[0].state.screenshot:
           logger.warning('No history or first screenshot to create GIF from')
           return

       # 加载字体和准备图像处理
       from PIL import Image, ImageFont
       images = []

       # 添加任务帧（如果启用）
       if show_task and task:
           task_frame = _create_task_frame(
               task,
               history.history[0].state.screenshot,
               title_font,
               regular_font,
               logo,
               line_spacing,
           )
           images.append(task_frame)

       # 处理每个历史记录项
       for i, item in enumerate(history.history, 1):
           if not item.state.screenshot:
               continue

           # 将base64截图转换为PIL图像
           img_data = base64.b64decode(item.state.screenshot)
           image = Image.open(io.BytesIO(img_data))

           # 添加步骤和目标叠加层
           if show_goals and item.model_output:
               image = _add_overlay_to_image(
                   image=image,
                   step_number=i,
                   goal_text=item.model_output.current_state.next_goal,
                   regular_font=regular_font,
                   title_font=title_font,
                   margin=margin,
                   logo=logo,
               )

           images.append(image)

       # 保存GIF
       if images:
           images[0].save(
               output_path,
               save_all=True,
               append_images=images[1:],
               duration=duration,
               loop=0,
               optimize=False,
           )
           logger.info(f'Created GIF at {output_path}')
   ```

3. **叠加信息处理**：

   为GIF添加步骤编号和目标描述：

   ```python
   def _add_overlay_to_image(
       image: 'Image.Image',
       step_number: int,
       goal_text: str,
       regular_font: 'ImageFont.FreeTypeFont',
       title_font: 'ImageFont.FreeTypeFont',
       margin: int,
       logo: Optional['Image.Image'] = None,
       # 其他参数...
   ) -> 'Image.Image':
       """Add step number and goal overlay to an image."""
       from PIL import Image, ImageDraw

       # 创建透明图层用于绘制文本
       image = image.convert('RGBA')
       txt_layer = Image.new('RGBA', image.size, (0, 0, 0, 0))
       draw = ImageDraw.Draw(txt_layer)
       
       # 添加步骤编号（左下角）
       if display_step:
           step_text = str(step_number)
           # 为步骤编号绘制圆角背景
           # ...绘制代码...
           
           # 绘制步骤编号
           draw.text(
               (x_step, y_step),
               step_text,
               font=title_font,
               fill=text_color,
           )

       # 添加目标文本（右下角）
       wrapped_goal = _wrap_text(goal_text, regular_font, max_width)
       # ...绘制目标文本代码...

       # 合并图层并返回
       return Image.alpha_composite(image, txt_layer)
   ```

### 集成到代理流程

GIF生成功能在Agent类的执行流程中集成：

```python
# 在agent/service.py中
async def run(self) -> AgentHistoryList:
    # 执行代理任务
    # ...执行代码...
    
    # 在完成所有步骤后，如果启用了GIF生成，则创建GIF
    if self.settings.generate_gif:
        output_path = 'agent_history.gif'
        if isinstance(self.settings.generate_gif, str):
            output_path = self.settings.generate_gif
        
        # 调用GIF生成函数
        create_history_gif(
            task=self.task,
            history=self.history,
            output_path=output_path,
        )
    
    return self.history
```

### 用户配置选项

用户可以通过Agent初始化参数控制GIF生成：

```python
agent = Agent(
    task="Compare the price of gpt-4o and DeepSeek-V3",
    llm=ChatOpenAI(model="gpt-4o"),
    # 启用GIF生成并指定输出路径
    generate_gif="execution_history.gif",
    # 或仅启用GIF生成，使用默认路径
    # generate_gif=True,
)
```

GIF生成功能也可以在配置中完全禁用（默认）：

```python
# 禁用GIF生成
generate_gif=False
```

### 技术细节与优化

1. **字体处理**：
   - 动态选择可用的字体
   - 根据平台提供适当的字体选项
   - 动态调整字体大小以适应不同长度的文本

2. **文本换行优化**：
   - 实现智能文本换行以保持可读性
   - 动态计算文本边界和显示位置

3. **图像合成优化**：
   - 使用PIL的alpha合成实现透明叠加
   - 优化GIF压缩和帧持续时间

4. **错误处理**：
   - 优雅地处理缺少截图的情况
   - 提供详细的日志信息
   - 在缺少依赖时提供有用的错误信息

### 示例应用场景

1. **演示与教学**：
   - 创建展示AI自动化能力的演示
   - 构建教程来解释代理如何完成任务

2. **调试与开发**：
   - 捕获和复现错误
   - 分析代理决策过程

3. **社区分享**：
   - 轻松分享代理执行过程
   - 展示项目案例和用例

### 总结

Browser-Use项目的GIF生成功能是一个优雅的设计，它将复杂的AI代理行为转化为直观可视的记录。通过捕获每个步骤的截图并添加上下文信息，用户可以清晰地看到代理如何逐步完成任务。这一功能不仅增强了项目的演示能力，也为调试、分享和教学提供了有力支持。

GIF生成功能采用了模块化设计，与核心代理功能解耦，使用户可以根据需要启用或禁用此功能。这种设计展示了项目在核心功能之外，对用户体验和可视化的重视。 

# Browser-Use项目提示词工程分析

## 1. 提示词设计概述

Browser-Use项目采用了精心设计的提示词工程方案，确保AI代理能够理解网页状态、制定计划并执行有效的浏览器操作。提示词设计主要包括以下几个关键组件：

1. **系统提示词**：定义代理的核心角色、任务范围和行为规则
2. **状态表示**：结构化的网页状态信息，包括URL、可交互元素等
3. **规划器提示词**：用于长期规划和分析任务进度的专门提示
4. **输出格式化**：标准化的JSON响应格式，确保代理输出的一致性

## 2. 系统提示词设计

系统提示词在`system_prompt.md`中定义，设计思路如下：

```
You are an AI agent designed to automate browser tasks. Your goal is to accomplish the ultimate task following the rules.
```

### 核心指令组件

1. **角色定义**：将AI定义为浏览器自动化代理
2. **输入格式说明**：详细描述代理将接收的信息格式，包括任务、URL、交互元素等
3. **响应规则**：严格规定输出JSON格式和行为准则
4. **元素交互指南**：如何与网页元素互动的具体规则
5. **导航与错误处理**：处理网页导航和异常情况的策略
6. **任务完成条件**：明确何时任务应被视为完成

### 输出格式规范

系统提示词要求代理遵循特定的JSON输出格式：

```json
{
  "current_state": {
    "evaluation_previous_goal": "Success|Failed|Unknown - [评估]",
    "memory": "[状态记忆]",
    "next_goal": "[下一步目标]"
  },
  "action": [
    {"action_name": {"parameter": "value"}}
  ]
}
```

这种严格的格式确保代理输出可以被系统可靠解析并执行。

## 3. LLM交互数据结构

### 输入数据结构

1. **系统消息**：定义代理角色和行为规则
   ```python
   SystemMessage(content=prompt)
   ```

2. **用户状态消息**：包含当前浏览器状态
   ```python
   def get_user_message(self, use_vision: bool = True) -> HumanMessage:
       state_description = f"""
   [Task history memory ends]
   [Current state starts here]
   Current url: {self.state.url}
   Available tabs: {self.state.tabs}
   Interactive elements: {elements_text}
   {step_info_description}
   """
   ```

3. **视觉信息集成**：
   ```python
   HumanMessage(
       content=[
           {'type': 'text', 'text': state_description},
           {'type': 'image_url', 'image_url': {'url': f'data:image/png;base64,{self.state.screenshot}'}},
       ]
   )
   ```

### 输出数据结构

核心输出结构由`AgentOutput`和相关模型定义：

```python
class AgentBrain(BaseModel):
    """Current state of the agent"""
    evaluation_previous_goal: str
    memory: str
    next_goal: str

class AgentOutput(BaseModel):
    """Output model for agent"""
    current_state: AgentBrain
    action: list[ActionModel]
```

其中`ActionModel`是一个动态构建的模型，根据注册的操作自动生成对应的数据结构。这使代理能够输出包含特定操作参数的结构化指令。

### 自定义操作扩展

Browser-Use设计了灵活的操作注册机制，允许动态扩展代理可用的操作：

```python
@staticmethod
def type_with_custom_actions(custom_actions: Type[ActionModel]) -> Type['AgentOutput']:
    """Extend actions with custom actions"""
    model_ = create_model(
        'AgentOutput',
        __base__=AgentOutput,
        action=(
            list[custom_actions],
            Field(..., description='List of actions to execute', json_schema_extra={'min_items': 1}),
        ),
        __module__=AgentOutput.__module__,
    )
    return model_
```

这种设计使代理能够适应不同的浏览器操作场景。

## 4. 步骤生成逻辑

Browser-Use采用了循环的步骤生成模式，通过以下流程为AI代理生成下一步操作：

### 状态采集

1. **获取浏览器状态**：
   ```python
   state = await self.browser_context.get_state()
   ```

2. **构建状态消息**：
   ```python
   self._message_manager.add_state_message(state, self.state.last_result, step_info, self.settings.use_vision)
   ```

### 决策生成

1. **调用LLM**：
   ```python
   async def get_next_action(self, input_messages: list[BaseMessage]) -> AgentOutput:
       structured_llm = self.llm.with_structured_output(self.AgentOutput, include_raw=True)
       response = await structured_llm.ainvoke(input_messages)
   ```

2. **解析结构化输出**：
   ```python
   parsed: AgentOutput | None = response['parsed']
   ```

### 响应验证

代码实现了严格的响应验证机制：

```python
# 尝试解析JSON
try:
    parsed_json = json.loads(response.content)
    parsed = self.AgentOutput(**parsed_json)
except Exception as e:
    # 验证失败处理
```

### 错误处理和重试机制

提示词工程包含了完善的错误处理指导：

1. 如何处理操作失败
2. 导航错误的恢复策略
3. 处理验证码和弹窗的方法

## 5. 规划执行逻辑

Browser-Use实现了独特的规划器功能，用于长期任务规划和进度评估。

### 规划器提示词

规划器使用专门的提示词，定义了规划代理的角色和期望输出：

```python
def get_system_message(self) -> SystemMessage:
    return SystemMessage(
        content="""You are a planning agent that helps break down tasks into smaller steps and reason about the current state.
Your role is to:
1. Analyze the current state and history
2. Evaluate progress towards the ultimate goal
3. Identify potential challenges or roadblocks
4. Suggest the next high-level steps to take

Your output format should be always a JSON object with the following fields:
{
    "state_analysis": "Brief analysis of the current state and what has been done so far",
    "progress_evaluation": "Evaluation of progress towards the ultimate goal (as percentage and description)",
    "challenges": "List any potential challenges or roadblocks",
    "next_steps": "List 2-3 concrete next steps to take",
    "reasoning": "Explain your reasoning for the suggested next steps"
}
"""
    )
```

### 规划执行流程

规划器在代理执行过程中按指定间隔运行：

```python
# 在agent/service.py中
if self.settings.planner_llm and self.state.n_steps % self.settings.planner_interval == 0:
    plan = await self._run_planner()
    self._message_manager.add_plan(plan, position=-1)
```

规划器运行逻辑：

```python
async def _run_planner(self) -> Optional[str]:
    """Run the planner to analyze state and suggest next steps"""
    # 创建规划器消息历史
    planner_messages = [
        PlannerPrompt(self.controller.registry.get_prompt_description()).get_system_message(),
        *self._message_manager.get_messages()[1:],
    ]
    
    # 可选地移除视觉信息
    if not self.settings.use_vision_for_planner and self.settings.use_vision:
        # 处理消息，移除图像内容
    
    # 调用规划器LLM
    response = await self.settings.planner_llm.ainvoke(planner_messages)
    
    # 解析和记录规划结果
    plan = str(response.content)
    try:
        plan_json = json.loads(plan)
        logger.info(f'Planning Analysis:\n{json.dumps(plan_json, indent=4)}')
    except json.JSONDecodeError:
        logger.info(f'Planning Analysis:\n{plan}')
    
    return plan
```

### 规划器与执行器的协调

1. **步骤分解**：规划器将大型任务分解为可管理的子任务
2. **进度评估**：持续评估任务完成进度和遇到的挑战
3. **战略调整**：在执行过程中提供高级指导，调整操作策略

规划结果被添加到消息历史中，作为代理决策的额外上下文。这种方式使代理能够在保持短期操作精确性的同时，维持长期目标的一致性。

## 6. 记忆管理

Browser-Use实现了结构化的记忆管理系统，确保代理能够跟踪重要信息：

### 记忆字段

在代理输出中，`memory`字段专门用于记录关键信息：

```json
"memory": "Description of what has been done and what you need to remember. Be very specific. Count here ALWAYS how many times you have done something and how many remain."
```

### 记忆指导原则

提示词明确指导代理如何使用记忆：

1. **计数跟踪**：记录已完成项目和剩余项目的数量
2. **具体细节**：保持记忆具体而非抽象
3. **关键决策**：记录影响后续步骤的重要决策

### 历史记录结构

完整的代理历史通过`AgentHistoryList`类维护：

```python
class AgentHistoryList(BaseModel):
    """List of agent history items"""
    history: list[AgentHistory]
    
    # 各种用于分析和检索历史的方法
    def urls(self) -> list[str | None]:
        """Get all unique URLs from history"""
    
    def model_thoughts(self) -> list[AgentBrain]:
        """Get all agent thoughts from history"""
        
    def screenshots(self) -> list[str | None]:
        """Get all screenshots from history"""
```

## 7. 提示词工程最佳实践

通过分析Browser-Use的提示词设计，可以总结出以下最佳实践：

1. **明确的角色定义**：清晰定义代理的角色和能力范围
2. **结构化输出格式**：严格规定JSON响应格式，确保可靠解析
3. **多模态信息融合**：文本与视觉信息的有效结合
4. **渐进式指导**：从大局到细节的分层指导
5. **错误处理策略**：明确的错误处理和恢复指南
6. **记忆管理**：结构化的信息记录和重要数据跟踪
7. **模块化提示设计**：将系统提示、状态表示和规划提示分离

## 8. 提示词工程的优化方向

Browser-Use的提示词工程仍有几个可能的优化方向：

1. **自适应提示**：根据任务类型动态调整提示词内容
2. **错误学习**：从失败尝试中学习并优化提示策略
3. **提示词压缩**：减少提示词长度，降低token消耗
4. **域特定优化**：为特定网站类型开发专用提示模板

Browser-Use项目展示了先进的提示词工程实践，通过结构化的系统提示、灵活的状态表示和独特的规划器设计，成功实现了AI代理对浏览器的有效控制。其提示词设计充分考虑了输入/输出结构、错误处理、记忆管理和长期规划等关键因素，为浏览器自动化代理提供了强大的基础。 