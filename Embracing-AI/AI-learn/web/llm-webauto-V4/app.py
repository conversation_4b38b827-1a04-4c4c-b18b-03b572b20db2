import streamlit as st
import subprocess
import os
from pathlib import Path
import time

# 设置页面标题和布局 - 必须是第一个Streamlit命令
st.set_page_config(
    page_title="LLM Web自动化测试平台",
    page_icon="🌐",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# 设置Apple风格的自定义CSS样式
st.markdown("""
<style>
    /* 基础布局调整 */
    .block-container {
        padding-top: 1.5rem;
        padding-bottom: 1rem;
        max-width: 1200px;
        margin: 0 auto;
    }
    
    /* 标题样式 - 使用Apple风格的SF Pro Display字体或备用字体 */
    h1, h2, h3, h4, h5, h6 {
        color: #000000;
        font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Helvetica, Arial, sans-serif;
        font-weight: 500;
        letter-spacing: -0.022em;
    }
    
    h2 {
        margin-top: 0;
        margin-bottom: 1rem;
        font-size: 1.8rem;
    }
    
    h3 {
        margin-top: 0.8rem;
        margin-bottom: 0.8rem;
        font-size: 1.3rem;
    }
    
    /* 减少组件间距 */
    .stMarkdown {
        margin-bottom: 0.8rem;
    }
    
    /* 组件样式优化 - Apple风格 */
    .stSelectbox > div > div, .stTextArea > div > div {
        padding-top: 0;
        padding-bottom: 0;
    }
    
    /* 表单元素样式 - Apple风格圆角和阴影 */
    .stSelectbox [data-baseweb="select"], 
    .stTextArea textarea, 
    .stNumberInput input {
        min-height: 2.2rem;
        border-radius: 10px !important;
        border-color: #e2e2e2 !important;
        background-color: #f5f5f7 !important;
        transition: all 0.2s ease;
    }
    
    /* 表单元素聚焦状态 */
    .stSelectbox [data-baseweb="select"]:focus, 
    .stTextArea textarea:focus, 
    .stNumberInput input:focus {
        border-color: #0066cc !important;
        box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.2) !important;
    }
    
    /* 标签字体调整 */
    label {
        font-weight: 400;
        color: #1d1d1f;
        font-size: 0.9rem;
        letter-spacing: -0.01em;
    }
    
    /* 分隔线样式 */
    hr {
        margin-top: 1rem;
        margin-bottom: 1rem;
        border-color: #e2e2e2;
    }
    
    /* Expander样式优化 - Apple风格 */
    .streamlit-expanderHeader {
        padding: 0.7rem 1rem;
        font-weight: 500;
        color: #000000;
        background-color: #f5f5f7;
        border-radius: 10px;
        box-shadow: none;
        border: 1px solid #e2e2e2;
    }
    
    /* 按钮样式优化 - Apple风格 */
    .stButton button {
        padding: 0.5rem 1rem;
        font-weight: 500;
        border-radius: 20px;
        transition: all 0.2s ease;
        font-size: 0.9rem;
        letter-spacing: -0.01em;
        box-shadow: none;
    }
    
    /* 主按钮样式 - Apple蓝色 */
    .stButton [data-baseweb="button"][kind="primary"] {
        background-color: #0066cc;
        border: none;
    }
    
    .stButton [data-baseweb="button"][kind="primary"]:hover {
        background-color: #0055b3;
    }
    
    /* 减少文本底部空白 */
    .stMarkdown p {
        margin-bottom: 0.5rem;
    }
    
    /* 卡片样式 - Apple风格 */
    .card {
        background-color: #ffffff;
        border-radius: 12px;
        padding: 1.2rem;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
        margin-bottom: 1.2rem;
        border: 1px solid #e2e2e2;
    }
    
    /* 标签和选项卡样式 - Apple风格 */
    .stTabs [data-baseweb="tab-list"] {
        gap: 2px;
        background-color: #f5f5f7;
        border-radius: 10px;
        padding: 3px;
    }
    
    .stTabs [data-baseweb="tab"] {
        padding: 0.6rem 1.2rem;
        font-weight: 500;
        border-radius: 8px;
        margin: 2px;
    }
    
    .stTabs [aria-selected="true"] {
        background-color: #ffffff;
        color: #0066cc;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    
    /* 信息框样式 - Apple风格 */
    .stAlert {
        border-radius: 10px;
        border: 1px solid #e2e2e2;
    }
    
    /* 成功信息样式 */
    .element-container .stAlert[data-baseweb="notification"][kind="success"] {
        background-color: #e4f9eb;
        border-left-color: #34c759;
    }
    
    /* 错误信息样式 */
    .element-container .stAlert[data-baseweb="notification"][kind="error"] {
        background-color: #ffeaec;
        border-left-color: #ff3b30;
    }
    
    /* 提示信息样式 */
    .element-container .stAlert[data-baseweb="notification"][kind="info"] {
        background-color: #e6f2ff;
        border-left-color: #0066cc;
    }
    
    /* 日志区域样式 */
    .log-area, .stCodeBlock {
        margin-top: 1rem;
        background-color: #f5f5f7;
        border-radius: 10px;
        padding: 1rem;
        max-height: 400px;
        overflow-y: auto;
        font-family: SFMono-Regular, Menlo, Monaco, Consolas, monospace;
        font-size: 0.9rem;
        border: 1px solid #e2e2e2;
    }
    
    /* 复选框样式 - Apple风格 */
    .stCheckbox [data-baseweb="checkbox"] {
        padding: 5px;
    }
    
    .stCheckbox [data-baseweb="checkbox"] [data-baseweb="checkbox"] {
        border-radius: 5px;
        border-color: #bebebe;
    }
    
    .stCheckbox [data-baseweb="checkbox"][aria-checked="true"] {
        background-color: #0066cc;
        border-color: #0066cc;
    }
    
    /* 输入框样式 */
    div[data-baseweb="base-input"] {
        border-radius: 10px;
    }
    
    /* 视频播放器样式 */
    .video-container {
        margin-top: 1.2rem;
        margin-bottom: 1.2rem;
        border-radius: 12px;
        overflow: hidden;
    }
    
    /* 图片样式 */
    .stImage img {
        border-radius: 12px;
    }
    
    /* 切换按钮样式 - Apple风格 */
    .toggle-btn {
        display: flex;
        align-items: center;
        background-color: #f5f5f7;
        border: 1px solid #e2e2e2;
        border-radius: 20px;
        padding: 6px 12px;
        cursor: pointer;
        transition: all 0.2s ease;
        margin-bottom: 1rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        font-weight: 500;
        color: #1d1d1f;
        font-size: 0.9rem;
    }
    
    .toggle-btn:hover {
        background-color: #e9e9eb;
    }
    
    .toggle-btn .icon {
        margin-right: 8px;
        transition: transform 0.3s ease;
    }
    
    /* 任务描述卡片样式 */
    .task-card {
        background-color: #ffffff;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
        margin-bottom: 1.5rem;
        border: 1px solid #e2e2e2;
        transition: all 0.3s ease;
    }
    
    /* 任务描述文本域样式 */
    .task-description textarea {
        font-size: 1rem !important;
        padding: 12px !important;
        min-height: 120px !important;
    }
    
    /* 任务描述区域样式 */
    .task-description {
        margin-top: -0.5rem;
    }
    
    /* 平滑过渡效果 */
    .layout-transition {
        transition: all 0.3s ease-in-out;
    }
    
    /* Agent设置区域 */
    .agent-settings {
        background-color: #ffffff;
        border-radius: 12px;
        padding: 1.2rem;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
        border: 1px solid #e2e2e2;
        margin-right: 1rem;
    }
</style>
""", unsafe_allow_html=True)

# 页面标题 - 使用更小的标题
st.markdown("<h2>🌐 LLM Web自动化测试平台</h2>", unsafe_allow_html=True)
st.markdown("<hr/>", unsafe_allow_html=True)

# 初始化session_state以存储数据
if 'last_report' not in st.session_state:
    st.session_state.last_report = None
if 'last_execution_log' not in st.session_state:
    st.session_state.last_execution_log = ""
if 'last_video' not in st.session_state:
    st.session_state.last_video = None
# 初始化Agent设置的展开/收起状态（默认收起）
if 'agent_expanded' not in st.session_state:
    st.session_state.agent_expanded = False

# 创建主选项卡布局
main_tabs = st.tabs(["任务执行", "测试报告"])

# 第一个主选项卡：任务执行
with main_tabs[0]:
    # 创建切换Agent设置的按钮
    col_toggle = st.columns([1, 7])
    with col_toggle[0]:
        toggle_icon = "▶" if st.session_state.agent_expanded else "◀"
        if st.button(f"{toggle_icon}Agent设置", key="toggle_agent"):
            st.session_state.agent_expanded = not st.session_state.agent_expanded
            st.rerun()
    
    # 根据Agent设置的展开状态动态调整布局
    if st.session_state.agent_expanded:
        # 展开状态：左侧显示Agent设置，右侧显示任务描述
        col_agent, col_task = st.columns([1, 2])
        
        with col_agent:
            st.markdown('<div class="agent-settings">', unsafe_allow_html=True)
            st.markdown("<h3>⚙️ Agent设置</h3>", unsafe_allow_html=True)
            
            # 浏览器选择
            st.markdown("<small>浏览器</small>", unsafe_allow_html=True)
            browser = st.selectbox(
                "浏览器选择",
                ["Chromium"],
                index=0
            )
            
            # 模型提供商选择
            st.markdown("<small>模型提供商</small>", unsafe_allow_html=True)
            model_provider = st.selectbox(
                "模型提供商",
                ["Alibaba"],
                index=0
            )
            
            # 模型选择
            st.markdown("<small>模型</small>", unsafe_allow_html=True)
            model = st.selectbox(
                "模型选择",
                ["Qwen/Qwen2.5-14B-Instruct"],
                index=0
            )
            
            # 高级选项（折叠）
            with st.expander("高级选项", expanded=False):
                c1, c2 = st.columns(2)
                with c1:
                    headless = st.checkbox("无头模式", value=False)
                    disable_realtime = st.checkbox("禁用实时元素定位", value=False)
                    force_complete = st.checkbox("强制完成", help="即使执行未完成也生成报告", value=True)
                with c2:
                    save_screenshots = st.checkbox("保存截图", value=True)
                    record_video = st.checkbox("录制视频", value=True)
                    max_steps = st.number_input("最大步骤数", min_value=5, max_value=100, value=80, step=1)
            
            st.markdown('</div>', unsafe_allow_html=True)
        
        with col_task:
            # 任务描述区域
            st.markdown('<div class="task-card">', unsafe_allow_html=True)
            # st.markdown("<h3>📝 任务描述</h3>", unsafe_allow_html=True)
            
            # 任务描述输入框
            st.markdown('<div class="task-description">', unsafe_allow_html=True)
            task_description = st.text_area(
                "",
                height=200,
                placeholder="请输入您想要执行的任务，例如：'谷歌搜索人工智能'、'在淘宝上搜索iPhone 15并比较价格'"
            )
            st.markdown('</div>', unsafe_allow_html=True)
            
            # 执行按钮
            execute_button = st.button("▶️ 执行", type="primary", use_container_width=True)
            st.markdown('</div>', unsafe_allow_html=True)
    else:
        # 收起状态：只显示任务描述，占据全宽
        st.markdown('<div class="task-card">', unsafe_allow_html=True)
        # st.markdown("<h3>📝 任务描述</h3>", unsafe_allow_html=True)
        
        # 任务描述输入框
        st.markdown('<div class="task-description">', unsafe_allow_html=True)
        task_description = st.text_area(
            "",
            height=200,
            placeholder="请输入您想要执行的任务，例如：'谷歌搜索人工智能'、'在淘宝上搜索iPhone 15并比较价格'"
        )
        st.markdown('</div>', unsafe_allow_html=True)
        
        # 执行按钮
        execute_button = st.button("▶️ 执行", type="primary", use_container_width=True)
        st.markdown('</div>', unsafe_allow_html=True)
    
    # 在布局之后显示日志输出区域
    st.markdown("<h3>🖥️ 执行日志</h3>", unsafe_allow_html=True)
    log_placeholder = st.empty()
    
    # 处理执行按钮点击
    if execute_button:
        if not task_description:
            st.error("请输入任务描述")
        else:
            # 构建命令
            cmd = ["python", "main.py", "-t", task_description]
            
            # 添加高级选项
            if 'headless' in locals() and headless:
                cmd.append("--headless")
            if 'save_screenshots' in locals() and save_screenshots:
                cmd.append("--screenshots")
            if 'disable_realtime' in locals() and disable_realtime:
                cmd.append("--disable-realtime")
            if 'force_complete' in locals() and force_complete:
                cmd.append("--force-complete")
            if 'record_video' in locals() and record_video:
                cmd.append("--record-video")
            if 'max_steps' in locals():
                cmd.extend(["--max-steps", str(max_steps)])
            
            # 显示执行命令
            with st.expander("执行命令", expanded=False):
                st.code(" ".join(cmd))
            
            # 清空日志区域并显示执行中信息
            log_placeholder.info("测试执行中...")
            
            try:
                # 执行命令
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    universal_newlines=True,
                    bufsize=1
                )
                
                # 实时显示输出
                output = ""
                for line in iter(process.stdout.readline, ""):
                    output += line
                    # 使用代码块格式显示日志
                    log_placeholder.code(output)
                    st.session_state.last_execution_log = output
                
                process.wait()
                
                # 查找最新报告
                reports_dir = Path("reports")
                if reports_dir.exists():
                    report_files = sorted(list(reports_dir.glob("*.md")), key=os.path.getmtime, reverse=True)
                    if report_files:
                        st.session_state.last_report = report_files[0]
                
                # 查找最新视频
                videos_dir = Path("videos")
                if videos_dir.exists() and 'record_video' in locals() and record_video:
                    video_files = sorted(list(videos_dir.glob("*.webm")), key=os.path.getmtime, reverse=True)
                    if video_files:
                        st.session_state.last_video = video_files[0]
                
                if process.returncode == 0:
                    st.success("测试执行完成")
                else:
                    st.error(f"测试执行失败，返回码：{process.returncode}")
                
            except Exception as e:
                st.error(f"执行过程中发生错误: {str(e)}")
    
    # 如果有之前的执行日志，显示它
    elif st.session_state.last_execution_log:
        log_placeholder.code(st.session_state.last_execution_log)

# 第二个主选项卡：测试报告
with main_tabs[1]:
    # 创建子选项卡
    report_tabs = st.tabs(["历史报告", "测试截图"])
    
    # 历史报告子选项卡
    with report_tabs[0]:
        st.markdown("<h3>📊 测试报告</h3>", unsafe_allow_html=True)
        
        # 检查报告目录
        reports_dir = Path("reports")
        if reports_dir.exists():
            report_files = sorted(list(reports_dir.glob("*.md")), key=os.path.getmtime, reverse=True)
            
            if report_files:
                # 创建两列布局
                report_col1, report_col2 = st.columns([1, 3])
                
                with report_col1:
                    # 显示报告列表
                    st.markdown("<h4>报告列表</h4>", unsafe_allow_html=True)
                    
                    # 默认选择最近的报告，或者用户上次执行的报告
                    default_index = 0
                    if st.session_state.last_report in report_files:
                        default_index = report_files.index(st.session_state.last_report)
                    
                    selected_report = st.selectbox(
                        "选择报告",
                        report_files,
                        index=default_index,
                        format_func=lambda x: f"{x.stem} ({time.ctime(os.path.getmtime(x))})"
                    )
                    
                    # 显示创建时间
                    st.info(f"创建时间: {time.ctime(os.path.getmtime(selected_report))}")
                    
                    # 查找对应的视频文件
                    videos_dir = Path("videos")
                    matching_video = None
                    if videos_dir.exists():
                        # 根据报告时间找到相近时间点的视频
                        report_time = os.path.getmtime(selected_report)
                        video_files = list(videos_dir.glob("*.webm"))
                        
                        if video_files:
                            # 查找时间最接近的视频
                            closest_video = min(video_files, key=lambda x: abs(os.path.getmtime(x) - report_time))
                            # 如果时间差在5分钟内，认为是匹配的视频
                            if abs(os.path.getmtime(closest_video) - report_time) < 300:
                                matching_video = closest_video
                    
                    # 显示视频播放按钮（如果有）
                    if matching_video:
                        st.markdown("<h4>查看录制视频</h4>", unsafe_allow_html=True)
                        # 创建视频播放器
                        video_file = open(matching_video, 'rb')
                        video_bytes = video_file.read()
                        st.video(video_bytes)
                
                with report_col2:
                    # 显示选中的报告
                    st.markdown("<h4>报告内容</h4>", unsafe_allow_html=True)
                    with open(selected_report, "r", encoding="utf-8") as f:
                        report_content = f.read()
                        st.markdown(report_content, unsafe_allow_html=True)
            else:
                st.info("没有找到测试报告")
        else:
            st.info("报告目录不存在")
    
    # 测试截图子选项卡
    with report_tabs[1]:
        st.markdown("<h3>📸 测试截图</h3>", unsafe_allow_html=True)
        
        # 检查截图目录
        screenshots_dir = Path("screenshots")
        if screenshots_dir.exists():
            screenshot_files = sorted(list(screenshots_dir.glob("*.png")), key=os.path.getmtime, reverse=True)
            
            if screenshot_files:
                # 创建两列布局
                screenshot_col1, screenshot_col2 = st.columns([1, 3])
                
                with screenshot_col1:
                    st.markdown("<h4>截图列表</h4>", unsafe_allow_html=True)
                    
                    selected_screenshot = st.selectbox(
                        "选择截图",
                        screenshot_files,
                        format_func=lambda x: f"{x.stem} ({time.ctime(os.path.getmtime(x))})"
                    )
                    
                    # 显示创建时间和文件大小
                    file_size = os.path.getsize(selected_screenshot) / 1024  # KB
                    st.info(f"创建时间: {time.ctime(os.path.getmtime(selected_screenshot))}")
                    st.info(f"文件大小: {file_size:.1f} KB")
                
                with screenshot_col2:
                    # 显示选中的截图
                    st.markdown("<h4>截图预览</h4>", unsafe_allow_html=True)
                    st.image(str(selected_screenshot), caption=selected_screenshot.stem, use_container_width=True)
            else:
                st.info("没有找到测试截图")
        else:
            st.info("截图目录不存在")

# 页脚
st.markdown("<hr/><small style='text-align: center; display: block; color: #86868b; font-size: 0.85rem; padding: 1rem 0;'>LLM Web自动化测试平台 © 2025</small>", unsafe_allow_html=True) 