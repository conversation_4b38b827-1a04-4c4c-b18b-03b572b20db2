# LLM-WebAuto-V3 项目架构分析

## 项目概述

LLM-WebAuto-V3 是一个基于大语言模型(LLM)的Web自动化测试框架，它结合了传统的关键字驱动测试方法与现代的大语言模型技术，实现了更智能、更灵活的Web自动化测试。该框架能够理解自然语言测试指令，自动生成测试步骤，并执行测试用例，同时具备元素定位自优化、图像识别等高级功能。

## 核心组件分析

### 1. 配置管理模块 (config)

- **config.py**: 负责管理全局配置，包括应用配置、大模型配置、浏览器配置和元素定位配置等。
- **locators.json**: 存储元素定位库，包含各个页面元素的定位信息。

### 2. 关键字驱动引擎 (core/keyword_driver.py)

`KeywordDriver` 类是框架的核心，负责解析和执行关键字命令，调用相应的功能模块。主要功能包括：

- 注册关键字映射，将自然语言关键字映射到类方法
- 提供浏览器操作、元素操作、等待操作等基本功能
- 支持异步操作和Playwright特有的定位器方法
- 集成元素管理器，提供稳定的元素定位能力

### 3. 元素管理模块

#### 3.1 基础元素管理器 (core/element_manager.py)

`ElementManager` 类负责管理和维护元素定位库，主要功能包括：

- 加载和查询元素定位库
- 根据权重选择最优定位器
- 生成和优化元素定位策略
- 集成大模型技术，自动维护元素定位信息

#### 3.2 实时元素管理器 (core/element_manager_realtime.py)

`RealtimeElementManager` 类继承自 `ElementManager`，提供实时元素定位功能，能够在元素定位失败时动态生成新的定位策略。

### 4. DOM服务模块 (core/dom)

- **service.py**: `DomService` 类负责DOM树的构建和元素定位，是实时元素定位功能的核心。
- **views.py**: 定义DOM树和元素的数据模型，用于表示网页DOM结构。
- **buildDomTree.js**: JavaScript代码，用于在浏览器中构建DOM树。

### 5. 大模型集成模块 (core/llm_integration.py)

`LLMIntegration` 类负责与大模型API交互，提供元素定位优化、测试用例生成等功能：

- 设置和调用大模型API
- 验证测试步骤有效性
- 生成元素定位策略
- 分析页面结构和元素关系

### 6. 图像识别模块 (core/image_recognition.py)

`ImageRecognition` 类提供基于图像的元素定位功能，使用OpenCV进行图像处理和匹配：

- 模板匹配查找元素
- 特征匹配查找元素
- 保存和管理元素模板

### 7. 大模型Agent服务 (core/agent_service.py)

`TestAgent` 类是框架的高级接口，负责解析自然语言测试指令并执行测试用例：

- 解析自然语言任务为测试步骤
- 执行测试步骤
- 处理测试失败和恢复
- 生成测试报告

### 8. Agent提示词模块 (core/agent_prompt.py)

- **SystemPrompt**: 定义系统提示词，指导大模型的行为
- **AgentMessagePrompt**: 构建用户消息提示词，包含页面状态和任务信息

## 项目架构图

```
+---------------------------+
|        TestAgent          |
|    (agent_service.py)     |
+------------+--------------+
             |
             v
+------------+--------------+
|     KeywordDriver         |
|   (keyword_driver.py)     |
+------------+--------------+
             |
     +-------+-------+
     |               |
     v               v
+----+----+    +-----+------+
|ElementMgr|    |LLMIntegrate|
|  (.py)   |    |   (.py)    |
+----+----+    +-----+------+
     |               |
     v               v
+----+----+    +-----+------+
|DOMService|    |ImageRecog  |
| (dom/)   |    |   (.py)    |
+---------+    +------------+
```

## 执行流程图

```
+----------------+    +----------------+    +----------------+
| 用户输入自然语 |    | TestAgent解析 |    | KeywordDriver  |
| 言测试指令     | -> | 生成测试步骤  | -> | 执行测试步骤   |
+----------------+    +----------------+    +-------+--------+
                                                    |
                                                    v
+----------------+    +----------------+    +----------------+
| 生成测试报告   |    | 处理测试失败  |    | ElementManager |
| 输出执行结果   | <- | 尝试恢复      | <- | 定位页面元素   |
+----------------+    +----------------+    +----------------+
```

## 关键技术点

1. **大模型驱动测试**：使用大模型理解自然语言测试指令，自动生成和执行测试步骤。

2. **智能元素定位**：结合传统定位策略与大模型分析，实现更稳定的元素定位。

3. **实时DOM分析**：通过JavaScript注入分析页面DOM结构，支持复杂页面元素定位。

4. **图像识别辅助**：使用OpenCV进行图像处理和匹配，解决传统定位方法难以处理的场景。

5. **自适应恢复机制**：测试失败时，使用大模型分析原因并生成恢复步骤。

## 总结

LLM-WebAuto-V3项目是一个创新的Web自动化测试框架，它通过结合大语言模型技术与传统自动化测试方法，实现了更智能、更灵活的测试能力。框架的模块化设计使其具有良好的扩展性和可维护性，能够适应各种复杂的Web测试场景。