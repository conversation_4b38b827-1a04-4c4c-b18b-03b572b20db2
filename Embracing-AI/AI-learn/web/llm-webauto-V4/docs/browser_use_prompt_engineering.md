# Browser-Use项目提示词工程分析

## 1. 提示词设计概述

Browser-Use项目采用了精心设计的提示词工程方案，确保AI代理能够理解网页状态、制定计划并执行有效的浏览器操作。提示词设计主要包括以下几个关键组件：

1. **系统提示词**：定义代理的核心角色、任务范围和行为规则
2. **状态表示**：结构化的网页状态信息，包括URL、可交互元素等
3. **规划器提示词**：用于长期规划和分析任务进度的专门提示
4. **输出格式化**：标准化的JSON响应格式，确保代理输出的一致性

## 2. 系统提示词设计

系统提示词在`system_prompt.md`中定义，设计思路如下：

```
You are an AI agent designed to automate browser tasks. Your goal is to accomplish the ultimate task following the rules.
```

### 核心指令组件

1. **角色定义**：将AI定义为浏览器自动化代理
2. **输入格式说明**：详细描述代理将接收的信息格式，包括任务、URL、交互元素等
3. **响应规则**：严格规定输出JSON格式和行为准则
4. **元素交互指南**：如何与网页元素互动的具体规则
5. **导航与错误处理**：处理网页导航和异常情况的策略
6. **任务完成条件**：明确何时任务应被视为完成

### 输出格式规范

系统提示词要求代理遵循特定的JSON输出格式：

```json
{
  "current_state": {
    "evaluation_previous_goal": "Success|Failed|Unknown - [评估]",
    "memory": "[状态记忆]",
    "next_goal": "[下一步目标]"
  },
  "action": [
    {"action_name": {"parameter": "value"}}
  ]
}
```

这种严格的格式确保代理输出可以被系统可靠解析并执行。

## 3. LLM交互数据结构

### 输入数据结构

1. **系统消息**：定义代理角色和行为规则
   ```python
   SystemMessage(content=prompt)
   ```

2. **用户状态消息**：包含当前浏览器状态
   ```python
   def get_user_message(self, use_vision: bool = True) -> HumanMessage:
       state_description = f"""
   [Task history memory ends]
   [Current state starts here]
   Current url: {self.state.url}
   Available tabs: {self.state.tabs}
   Interactive elements: {elements_text}
   {step_info_description}
   """
   ```

3. **视觉信息集成**：
   ```python
   HumanMessage(
       content=[
           {'type': 'text', 'text': state_description},
           {'type': 'image_url', 'image_url': {'url': f'data:image/png;base64,{self.state.screenshot}'}},
       ]
   )
   ```

### 输出数据结构

核心输出结构由`AgentOutput`和相关模型定义：

```python
class AgentBrain(BaseModel):
    """Current state of the agent"""
    evaluation_previous_goal: str
    memory: str
    next_goal: str

class AgentOutput(BaseModel):
    """Output model for agent"""
    current_state: AgentBrain
    action: list[ActionModel]
```

其中`ActionModel`是一个动态构建的模型，根据注册的操作自动生成对应的数据结构。这使代理能够输出包含特定操作参数的结构化指令。

### 自定义操作扩展

Browser-Use设计了灵活的操作注册机制，允许动态扩展代理可用的操作：

```python
@staticmethod
def type_with_custom_actions(custom_actions: Type[ActionModel]) -> Type['AgentOutput']:
    """Extend actions with custom actions"""
    model_ = create_model(
        'AgentOutput',
        __base__=AgentOutput,
        action=(
            list[custom_actions],
            Field(..., description='List of actions to execute', json_schema_extra={'min_items': 1}),
        ),
        __module__=AgentOutput.__module__,
    )
    return model_
```

这种设计使代理能够适应不同的浏览器操作场景。

## 4. 步骤生成逻辑

Browser-Use采用了循环的步骤生成模式，通过以下流程为AI代理生成下一步操作：

### 状态采集

1. **获取浏览器状态**：
   ```python
   state = await self.browser_context.get_state()
   ```

2. **构建状态消息**：
   ```python
   self._message_manager.add_state_message(state, self.state.last_result, step_info, self.settings.use_vision)
   ```

### 决策生成

1. **调用LLM**：
   ```python
   async def get_next_action(self, input_messages: list[BaseMessage]) -> AgentOutput:
       structured_llm = self.llm.with_structured_output(self.AgentOutput, include_raw=True)
       response = await structured_llm.ainvoke(input_messages)
   ```

2. **解析结构化输出**：
   ```python
   parsed: AgentOutput | None = response['parsed']
   ```

### 响应验证

代码实现了严格的响应验证机制：

```python
# 尝试解析JSON
try:
    parsed_json = json.loads(response.content)
    parsed = self.AgentOutput(**parsed_json)
except Exception as e:
    # 验证失败处理
```

### 错误处理和重试机制

提示词工程包含了完善的错误处理指导：

1. 如何处理操作失败
2. 导航错误的恢复策略
3. 处理验证码和弹窗的方法

## 5. 规划执行逻辑

Browser-Use实现了独特的规划器功能，用于长期任务规划和进度评估。

### 规划器提示词

规划器使用专门的提示词，定义了规划代理的角色和期望输出：

```python
def get_system_message(self) -> SystemMessage:
    return SystemMessage(
        content="""You are a planning agent that helps break down tasks into smaller steps and reason about the current state.
Your role is to:
1. Analyze the current state and history
2. Evaluate progress towards the ultimate goal
3. Identify potential challenges or roadblocks
4. Suggest the next high-level steps to take

Your output format should be always a JSON object with the following fields:
{
    "state_analysis": "Brief analysis of the current state and what has been done so far",
    "progress_evaluation": "Evaluation of progress towards the ultimate goal (as percentage and description)",
    "challenges": "List any potential challenges or roadblocks",
    "next_steps": "List 2-3 concrete next steps to take",
    "reasoning": "Explain your reasoning for the suggested next steps"
}
"""
    )
```

### 规划执行流程

规划器在代理执行过程中按指定间隔运行：

```python
# 在agent/service.py中
if self.settings.planner_llm and self.state.n_steps % self.settings.planner_interval == 0:
    plan = await self._run_planner()
    self._message_manager.add_plan(plan, position=-1)
```

规划器运行逻辑：

```python
async def _run_planner(self) -> Optional[str]:
    """Run the planner to analyze state and suggest next steps"""
    # 创建规划器消息历史
    planner_messages = [
        PlannerPrompt(self.controller.registry.get_prompt_description()).get_system_message(),
        *self._message_manager.get_messages()[1:],
    ]
    
    # 可选地移除视觉信息
    if not self.settings.use_vision_for_planner and self.settings.use_vision:
        # 处理消息，移除图像内容
    
    # 调用规划器LLM
    response = await self.settings.planner_llm.ainvoke(planner_messages)
    
    # 解析和记录规划结果
    plan = str(response.content)
    try:
        plan_json = json.loads(plan)
        logger.info(f'Planning Analysis:\n{json.dumps(plan_json, indent=4)}')
    except json.JSONDecodeError:
        logger.info(f'Planning Analysis:\n{plan}')
    
    return plan
```

### 规划器与执行器的协调

1. **步骤分解**：规划器将大型任务分解为可管理的子任务
2. **进度评估**：持续评估任务完成进度和遇到的挑战
3. **战略调整**：在执行过程中提供高级指导，调整操作策略

规划结果被添加到消息历史中，作为代理决策的额外上下文。这种方式使代理能够在保持短期操作精确性的同时，维持长期目标的一致性。

## 6. 记忆管理

Browser-Use实现了结构化的记忆管理系统，确保代理能够跟踪重要信息：

### 记忆字段

在代理输出中，`memory`字段专门用于记录关键信息：

```json
"memory": "Description of what has been done and what you need to remember. Be very specific. Count here ALWAYS how many times you have done something and how many remain."
```

### 记忆指导原则

提示词明确指导代理如何使用记忆：

1. **计数跟踪**：记录已完成项目和剩余项目的数量
2. **具体细节**：保持记忆具体而非抽象
3. **关键决策**：记录影响后续步骤的重要决策

### 历史记录结构

完整的代理历史通过`AgentHistoryList`类维护：

```python
class AgentHistoryList(BaseModel):
    """List of agent history items"""
    history: list[AgentHistory]
    
    # 各种用于分析和检索历史的方法
    def urls(self) -> list[str | None]:
        """Get all unique URLs from history"""
    
    def model_thoughts(self) -> list[AgentBrain]:
        """Get all agent thoughts from history"""
        
    def screenshots(self) -> list[str | None]:
        """Get all screenshots from history"""
```

## 7. 提示词工程最佳实践

通过分析Browser-Use的提示词设计，可以总结出以下最佳实践：

1. **明确的角色定义**：清晰定义代理的角色和能力范围
2. **结构化输出格式**：严格规定JSON响应格式，确保可靠解析
3. **多模态信息融合**：文本与视觉信息的有效结合
4. **渐进式指导**：从大局到细节的分层指导
5. **错误处理策略**：明确的错误处理和恢复指南
6. **记忆管理**：结构化的信息记录和重要数据跟踪
7. **模块化提示设计**：将系统提示、状态表示和规划提示分离

## 8. 提示词工程的优化方向

Browser-Use的提示词工程仍有几个可能的优化方向：

1. **自适应提示**：根据任务类型动态调整提示词内容
2. **错误学习**：从失败尝试中学习并优化提示策略
3. **提示词压缩**：减少提示词长度，降低token消耗
4. **域特定优化**：为特定网站类型开发专用提示模板

## 总结

Browser-Use项目展示了先进的提示词工程实践，通过结构化的系统提示、灵活的状态表示和独特的规划器设计，成功实现了AI代理对浏览器的有效控制。其提示词设计充分考虑了输入/输出结构、错误处理、记忆管理和长期规划等关键因素，为浏览器自动化代理提供了强大的基础。 