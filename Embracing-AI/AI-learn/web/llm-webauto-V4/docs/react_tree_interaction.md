# React树形下拉框交互指南

本文档介绍如何使用llm-webauto-V3框架处理React动态创建的树形下拉框组件，特别是部门/组织选择类型的交互场景。

## 功能概述

已添加以下核心功能来支持React树形下拉框交互：

1. **展开树形下拉框** - 点击输入框触发React组件展开树形结构
2. **选择树形节点** - 在展开的树形结构中查找并点击特定文本的节点
3. **部门选择器** - 专门用于处理部门/组织树形选择框的高级方法

这些功能采用了多种技术：
- DOM分析和JavaScript注入
- 多种定位策略组合
- 自适应节点查找和父节点展开
- 专门的UI模式识别

## 使用方法

### 方法一：两步操作（展开 + 选择）

```python
# 1. 先展开树形下拉框
await keyword_driver.expand_tree_dropdown("部门/组织")

# 2. 然后选择节点
await keyword_driver.select_tree_node("江西市开宇船业有限公司")
```

### 方法二：一步完成部门选择

```python
# 一步完成部门选择操作
await keyword_driver.select_department_from_tree("科技中心")
```

### 通过关键字驱动调用

```python
# 在测试脚本或Agent中使用以下关键字
steps = [
    {"keyword": "选择部门", "args": ["科技中心"]},
    # 或者
    {"keyword": "选择组织", "args": ["质量保障中心"]},
    # 或者分步操作
    {"keyword": "展开树形下拉框", "args": ["部门/组织"]},
    {"keyword": "选择树形节点", "args": ["用户体验中心"]}
]
```

## 功能详解

### 1. 展开树形下拉框

`expand_tree_dropdown(element_key, wait_time=1)`

- **element_key**: 输入框元素的定位标识，可以是文本、CSS选择器等
- **wait_time**: 展开后等待时间(秒)，确保DOM树完全加载

功能特点：
- 自动查找和点击输入框元素
- 利用JavaScript检测树形结构是否成功展开
- 如果首次点击失败，自动重试
- 自动更新DOM状态以确保后续操作准确

### 2. 选择树形节点

`select_tree_node(node_text, expand_parent=True, partial_match=True)`

- **node_text**: 节点文本
- **expand_parent**: 是否自动展开父节点以找到子节点
- **partial_match**: 是否允许部分文本匹配

功能特点：
- 支持完全匹配和部分文本匹配
- 自动展开父节点寻找子节点
- 使用多种定位策略提高成功率
- 处理AJAX动态加载的节点

### 3. 部门选择器

`select_department_from_tree(department_name)`

- **department_name**: 部门/组织名称

功能特点：
- 专门针对部门/组织树形选择框优化
- 智能识别输入框位置和类型
- 自动查找相关标签和输入元素
- 支持特定UI模式和常见框架类名
- 结合地理位置分析提高定位准确性

## 实现原理

1. **多层定位策略**:
   - 文本匹配：基于节点文本内容
   - 属性匹配：利用aria-label、title等属性
   - 组合定位：使用父子关系定位
   - CSS选择器：基于类名和结构

2. **DOM分析和JavaScript注入**:
   - 注入辅助函数扩展浏览器能力
   - 使用TreeWalker遍历DOM树
   - 基于文本内容查找节点

3. **父节点展开处理**:
   - 识别展开/折叠控件特征
   - 自动点击展开控件
   - 等待动画完成后继续操作

4. **特定UI框架适配**:
   - 支持Ant Design组件库的树形控件
   - 支持通用React树形组件模式
   - 兼容自定义树形组件

## 使用场景示例

### 场景1：员工信息管理

```python
# 导航到员工信息页面
await driver.navigate_to("https://example.com/employee-form")

# 选择部门
await driver.select_department_from_tree("科技中心")

# 填写其他信息
await driver.input_text("姓名", "张三")
await driver.input_text("手机号", "13800138000")
```

### 场景2：组织架构管理

```python
# 导航到组织架构管理页面
await driver.navigate_to("https://example.com/org-management")

# 找到部门选择器
await driver.expand_tree_dropdown("部门搜索")

# 选择特定部门
await driver.select_tree_node("技术研发部")

# 点击操作按钮
await driver.click_element("查看详情")
```

## 常见问题

1. **找不到下拉框输入元素**
   - 尝试使用更多的定位策略，如CSS选择器或XPath
   - 检查页面结构，确认元素是否有特殊属性

2. **树形结构未完全加载**
   - 增加wait_time参数的值，给予更多加载时间
   - 使用wait_for_element_visible确保组件可见

3. **无法选中特定节点**
   - 使用partial_match=True允许部分文本匹配
   - 检查节点文本是否包含隐藏字符或特殊格式

4. **父节点未展开**
   - 手动先展开父节点，再选择子节点
   - 调整DOM分析等待时间

## 后续优化方向

1. 添加对更多树形组件库的支持（如Element UI、Material UI等）
2. 优化节点查找算法，提高速度和准确性
3. 增加节点状态检测功能，如是否已选中、是否已禁用等
4. 支持多选树形组件和级联选择器 