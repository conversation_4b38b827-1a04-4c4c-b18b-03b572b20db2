@startuml
title LLM-WebAuto-V3 系统架构图

package "配置管理" {
  [config.py] as Config
  [locators.json] as Locators
}

package "核心组件" {
  [keyword_driver.py] as KeywordDriver
  [element_manager.py] as ElementManager
  [element_manager_realtime.py] as RealtimeElementManager
  [llm_integration.py] as LLMIntegration
  [image_recognition.py] as ImageRecognition
  [agent_service.py] as TestAgent
  [agent_prompt.py] as AgentPrompt
  
  package "DOM服务" {
    [dom/service.py] as DomService
    [dom/views.py] as DomViews
    [dom/buildDomTree.js] as DomJS
  }
}

package "输出管理" {
  [reports/] as Reports
  [screenshots/] as Screenshots
  [logs/] as Logs
}

' 组件关系
TestAgent --> KeywordDriver : 调用
TestAgent --> AgentPrompt : 使用
KeywordDriver --> ElementManager : 使用
KeywordDriver --> LLMIntegration : 调用
KeywordDriver --> ImageRecognition : 调用
ElementManager --> LLMIntegration : 调用
ElementManager --> ImageRecognition : 调用
ElementManager --> Locators : 读取/更新
RealtimeElementManager --|> ElementManager : 继承
RealtimeElementManager --> DomService : 使用
DomService --> DomViews : 使用
DomService --> DomJS : 执行

Config --> KeywordDriver : 配置
Config --> ElementManager : 配置
Config --> LLMIntegration : 配置
Config --> ImageRecognition : 配置

TestAgent --> Reports : 生成
TestAgent --> Screenshots : 保存
TestAgent --> Logs : 记录

@enduml