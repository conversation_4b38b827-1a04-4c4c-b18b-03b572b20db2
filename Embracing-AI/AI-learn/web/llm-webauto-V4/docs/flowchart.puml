@startuml
title 自动化测试执行流程图

start
:初始化测试用例;
:调用TestCase.setup();
:LLM验证测试步骤有效性;
if (步骤有效?) then (是)
  :创建KeywordDriver实例;
else (否)
  :使用原始测试步骤;
endif
:初始化浏览器配置;

repeat
  :执行单个测试步骤;
  :调用KeywordDriver.run_keyword();
  if (关键字存在?) then (是)
    :通过ElementManager定位元素;
    if (元素可见?) then (是)
      :执行对应操作;
      :LLM实时验证操作合理性;
    else (否)
      :调用ImageRecognition进行图像匹配;
      if (匹配成功?) then (是)
        :执行图像点击操作;
      else (否)
        :截图记录错误;
        :记录元素定位日志;
      endif
    endif
  else (否)
    :记录未知关键字错误;
    :标记步骤失败;
  endif

backward:失败重试?;
repeat while (重试次数未用完?) is (是) not (否)
->否;

:汇总测试结果;
:调用TestCase.teardown();
:关闭浏览器;
:生成测试报告;

stop

@enduml