/**
 * 浏览器DOM树构建和元素分析脚本
 * 
 * 该脚本在浏览器环境中执行，负责:
 * 1. 递归遍历DOM树
 * 2. 识别和标记可交互元素
 * 3. 为交互元素分配唯一的highlight_index
 * 4. 生成元素的XPath
 * 5. 分析元素的可见性和交互性
 * 6. 构建元素映射关系
 * 
 * 这是Browser-Use项目中DOM解析的核心组件，供Python后端调用
 */
(
  args = {
    doHighlightElements: true,  // 是否在页面上高亮显示可交互元素
    focusHighlightIndex: -1,    // 要特别聚焦的元素索引，-1表示不聚焦任何元素
    viewportExpansion: 0,       // 视口扩展像素数，控制超出当前视口的元素是否包含
    debugMode: false,           // 是否启用调试模式（性能指标收集）
  }
) => {
  const { doHighlightElements, focusHighlightIndex, viewportExpansion, debugMode } = args;
  let highlightIndex = 0; // 重置高亮索引计数器，为每个可交互元素分配唯一索引

  // 添加计时堆栈以处理递归过程中的性能测量
  const TIMING_STACK = {
    nodeProcessing: [],  // 节点处理时间堆栈
    treeTraversal: [],   // 树遍历时间堆栈
    highlighting: [],    // 元素高亮时间堆栈
    current: null        // 当前计时操作
  };

  /**
   * 将当前时间压入指定类型的计时堆栈
   * @param {string} type - 计时类型
   */
  function pushTiming(type) {
    TIMING_STACK[type] = TIMING_STACK[type] || [];
    TIMING_STACK[type].push(performance.now());
  }

  /**
   * 从指定类型的计时堆栈弹出时间并计算持续时间
   * @param {string} type - 计时类型
   * @returns {number} 操作持续时间（毫秒）
   */
  function popTiming(type) {
    const start = TIMING_STACK[type].pop();
    const duration = performance.now() - start;
    return duration;
  }

  // 仅在调试模式下初始化性能指标跟踪
  const PERF_METRICS = debugMode ? {
    buildDomTreeCalls: 0,  // buildDomTree函数调用次数
    timings: {             // 各函数执行时间
      buildDomTree: 0,  //递归便利页面所有元素，创建一个包含元素属性、样式、位置和交互特性的树结构
      highlightElement: 0,
      isInteractiveElement: 0,
      isElementVisible: 0,
      isTopElement: 0,
      isInExpandedViewport: 0,
      isTextNodeVisible: 0,
      getEffectiveScroll: 0,
    },
    cacheMetrics: {        // 缓存命中率相关指标
      boundingRectCacheHits: 0,
      boundingRectCacheMisses: 0,
      computedStyleCacheHits: 0,
      computedStyleCacheMisses: 0,
      getBoundingClientRectTime: 0,
      getComputedStyleTime: 0,
      boundingRectHitRate: 0,
      computedStyleHitRate: 0,
      overallHitRate: 0,
    },
    nodeMetrics: {         // 节点处理统计
      totalNodes: 0,       // 总节点数
      processedNodes: 0,   // 处理的节点数
      skippedNodes: 0,     // 跳过的节点数
    },
    buildDomTreeBreakdown: {  // buildDomTree函数性能细分
      totalTime: 0,
      totalSelfTime: 0,
      buildDomTreeCalls: 0,
      domOperations: {        // DOM操作耗时
        getBoundingClientRect: 0,
        getComputedStyle: 0,
      },
      domOperationCounts: {   // DOM操作次数
        getBoundingClientRect: 0,
        getComputedStyle: 0,
      }
    }
  } : null;

  /**
   * 简单的计时助手函数，仅在调试模式下测量函数执行时间
   * @param {Function} fn - 要测量的函数
   * @returns {Function} 包装后的函数
   */
  function measureTime(fn) {
    if (!debugMode) return fn;
    return function (...args) {
      const start = performance.now();
      const result = fn.apply(this, args);
      const duration = performance.now() - start;
      return result;
    };
  }

  /**
   * 测量DOM操作执行时间的助手函数
   * @param {Function} operation - 要执行的DOM操作
   * @param {string} name - 操作名称
   * @returns {*} 操作结果
   */
  function measureDomOperation(operation, name) {
    if (!debugMode) return operation();

    const start = performance.now();
    const result = operation();
    const duration = performance.now() - start;

    if (PERF_METRICS && name in PERF_METRICS.buildDomTreeBreakdown.domOperations) {
      PERF_METRICS.buildDomTreeBreakdown.domOperations[name] += duration;
      PERF_METRICS.buildDomTreeBreakdown.domOperationCounts[name]++;
    }

    return result;
  }

  // 添加顶级缓存机制，减少重复的昂贵DOM操作
  const DOM_CACHE = {
    boundingRects: new WeakMap(),   // 元素边界矩形缓存
    computedStyles: new WeakMap(),  // 计算样式缓存
    clearCache: () => {             // 清除缓存的方法
      DOM_CACHE.boundingRects = new WeakMap();
      DOM_CACHE.computedStyles = new WeakMap();
    }
  };

  /**
   * 获取缓存的元素边界矩形，如不存在则计算并缓存
   * @param {Element} element - DOM元素
   * @returns {DOMRect|null} 元素的边界矩形或null
   */
  function getCachedBoundingRect(element) {
    if (!element) return null;

    // 检查缓存中是否已存在
    if (DOM_CACHE.boundingRects.has(element)) {
      if (debugMode && PERF_METRICS) {
        PERF_METRICS.cacheMetrics.boundingRectCacheHits++;
      }
      return DOM_CACHE.boundingRects.get(element);
    }

    if (debugMode && PERF_METRICS) {
      PERF_METRICS.cacheMetrics.boundingRectCacheMisses++;
    }

    // 计算边界矩形并测量性能（如果在调试模式）
    let rect;
    if (debugMode) {
      const start = performance.now();
      rect = element.getBoundingClientRect();
      const duration = performance.now() - start;
      if (PERF_METRICS) {
        PERF_METRICS.buildDomTreeBreakdown.domOperations.getBoundingClientRect += duration;
        PERF_METRICS.buildDomTreeBreakdown.domOperationCounts.getBoundingClientRect++;
      }
    } else {
      rect = element.getBoundingClientRect();
    }

    // 缓存结果
    if (rect) {
      DOM_CACHE.boundingRects.set(element, rect);
    }
    return rect;
  }

  /**
   * 获取缓存的元素计算样式，如不存在则计算并缓存
   * @param {Element} element - DOM元素
   * @returns {CSSStyleDeclaration|null} 元素的计算样式或null
   */
  function getCachedComputedStyle(element) {
    if (!element) return null;

    // 检查缓存中是否已存在
    if (DOM_CACHE.computedStyles.has(element)) {
      if (debugMode && PERF_METRICS) {
        PERF_METRICS.cacheMetrics.computedStyleCacheHits++;
      }
      return DOM_CACHE.computedStyles.get(element);
    }

    if (debugMode && PERF_METRICS) {
      PERF_METRICS.cacheMetrics.computedStyleCacheMisses++;
    }

    // 获取计算样式并测量性能（如果在调试模式）
    let style;
    if (debugMode) {
      const start = performance.now();
      style = window.getComputedStyle(element);
      const duration = performance.now() - start;
      if (PERF_METRICS) {
        PERF_METRICS.buildDomTreeBreakdown.domOperations.getComputedStyle += duration;
        PERF_METRICS.buildDomTreeBreakdown.domOperationCounts.getComputedStyle++;
      }
    } else {
      style = window.getComputedStyle(element);
    }

    // 缓存结果
    if (style) {
      DOM_CACHE.computedStyles.set(element, style);
    }
    return style;
  }

  /**
   * Hash map of DOM nodes indexed by their highlight index.
   *
   * @type {Object<string, any>}
   */
  const DOM_HASH_MAP = {};

  const ID = { current: 0 };

  const HIGHLIGHT_CONTAINER_ID = "playwright-highlight-container";

  /**
   * Highlights an element in the DOM and returns the index of the next element.
   */
  function highlightElement(element, index, parentIframe = null) {
    if (!element) return index;

    try {
      // Create or get highlight container
      let container = document.getElementById(HIGHLIGHT_CONTAINER_ID);
      if (!container) {
        container = document.createElement("div");
        container.id = HIGHLIGHT_CONTAINER_ID;
        container.style.position = "fixed";
        container.style.pointerEvents = "none";
        container.style.top = "0";
        container.style.left = "0";
        container.style.width = "100%";
        container.style.height = "100%";
        container.style.zIndex = "2147483647";
        document.body.appendChild(container);
      }

      // Get element position
      const rect = measureDomOperation(
        () => element.getBoundingClientRect(),
        'getBoundingClientRect'
      );

      if (!rect) return index;

      // Generate a color based on the index
      const colors = [
        "#FF0000",
        "#00FF00",
        "#0000FF",
        "#FFA500",
        "#800080",
        "#008080",
        "#FF69B4",
        "#4B0082",
        "#FF4500",
        "#2E8B57",
        "#DC143C",
        "#4682B4",
      ];
      const colorIndex = index % colors.length;
      const baseColor = colors[colorIndex];
      const backgroundColor = baseColor + "1A"; // 10% opacity version of the color

      // Create highlight overlay
      const overlay = document.createElement("div");
      overlay.style.position = "fixed";
      overlay.style.border = `2px solid ${baseColor}`;
      overlay.style.backgroundColor = backgroundColor;
      overlay.style.pointerEvents = "none";
      overlay.style.boxSizing = "border-box";

      // Get element position
      let iframeOffset = { x: 0, y: 0 };

      // If element is in an iframe, calculate iframe offset
      if (parentIframe) {
        const iframeRect = parentIframe.getBoundingClientRect();
        iframeOffset.x = iframeRect.left;
        iframeOffset.y = iframeRect.top;
      }

      // Calculate position
      const top = rect.top + iframeOffset.y;
      const left = rect.left + iframeOffset.x;

      overlay.style.top = `${top}px`;
      overlay.style.left = `${left}px`;
      overlay.style.width = `${rect.width}px`;
      overlay.style.height = `${rect.height}px`;

      // Create and position label
      const label = document.createElement("div");
      label.className = "playwright-highlight-label";
      label.style.position = "fixed";
      label.style.background = baseColor;
      label.style.color = "white";
      label.style.padding = "1px 4px";
      label.style.borderRadius = "4px";
      label.style.fontSize = `${Math.min(12, Math.max(8, rect.height / 2))}px`;
      label.textContent = index;

      const labelWidth = 20;
      const labelHeight = 16;

      let labelTop = top + 2;
      let labelLeft = left + rect.width - labelWidth - 2;

      if (rect.width < labelWidth + 4 || rect.height < labelHeight + 4) {
        labelTop = top - labelHeight - 2;
        labelLeft = left + rect.width - labelWidth;
      }

      label.style.top = `${labelTop}px`;
      label.style.left = `${labelLeft}px`;

      // Add to container
      container.appendChild(overlay);
      container.appendChild(label);

      // Update positions on scroll
      const updatePositions = () => {
        const newRect = element.getBoundingClientRect();
        let newIframeOffset = { x: 0, y: 0 };

        if (parentIframe) {
          const iframeRect = parentIframe.getBoundingClientRect();
          newIframeOffset.x = iframeRect.left;
          newIframeOffset.y = iframeRect.top;
        }

        const newTop = newRect.top + newIframeOffset.y;
        const newLeft = newRect.left + newIframeOffset.x;

        overlay.style.top = `${newTop}px`;
        overlay.style.left = `${newLeft}px`;
        overlay.style.width = `${newRect.width}px`;
        overlay.style.height = `${newRect.height}px`;

        let newLabelTop = newTop + 2;
        let newLabelLeft = newLeft + newRect.width - labelWidth - 2;

        if (newRect.width < labelWidth + 4 || newRect.height < labelHeight + 4) {
          newLabelTop = newTop - labelHeight - 2;
          newLabelLeft = newLeft + newRect.width - labelWidth;
        }

        label.style.top = `${newLabelTop}px`;
        label.style.left = `${newLabelLeft}px`;
      };

      window.addEventListener('scroll', updatePositions);
      window.addEventListener('resize', updatePositions);

      return index + 1;
    } finally {
      popTiming('highlighting');
    }
  }

  /**
   * 返回元素的XPath路径字符串
   * 
   * 从当前元素开始，向上遍历DOM树，生成唯一的XPath路径。
   * 路径格式为: tagname1/tagname2[index]/tagname3
   * 索引表示在相同标签名称的兄弟元素中的位置（从1开始）。
   * 
   * @param {Element} element - 需要生成XPath的DOM元素
   * @param {boolean} stopAtBoundary - 是否在Shadow DOM或iframe边界停止
   * @returns {string} 元素的XPath路径
   */
  function getXPathTree(element, stopAtBoundary = true) {
    const segments = []; // 存储XPath路径片段
    let currentElement = element;

    while (currentElement && currentElement.nodeType === Node.ELEMENT_NODE) {
      // 在Shadow DOM或iframe边界处停止，因为这些是独立的DOM上下文
      if (
        stopAtBoundary &&
        (currentElement.parentNode instanceof ShadowRoot ||
          currentElement.parentNode instanceof HTMLIFrameElement)
      ) {
        break;
      }

      // 计算当前元素在同名兄弟元素中的索引位置
      let index = 0;
      let sibling = currentElement.previousSibling;
      while (sibling) {
        if (
          sibling.nodeType === Node.ELEMENT_NODE &&
          sibling.nodeName === currentElement.nodeName
        ) {
          index++; // 找到一个同名兄弟元素，索引加1
        }
        sibling = sibling.previousSibling;
      }

      // 生成当前元素的XPath片段
      const tagName = currentElement.nodeName.toLowerCase();
      // 只有当索引大于0时，才添加索引，否则省略
      const xpathIndex = index > 0 ? `[${index + 1}]` : "";
      segments.unshift(`${tagName}${xpathIndex}`); // 添加到路径片段数组的开头

      // 移动到父元素，继续构建路径
      currentElement = currentElement.parentNode;
    }

    // 将所有路径片段用斜杠连接，形成完整的XPath
    return segments.join("/");
  }

  /**
   * 检查文本节点是否可见
   * 
   * 通过多种方法判断文本节点的可见性：
   * 1. 检查文本范围的边界矩形尺寸
   * 2. 检查文本是否在（扩展）视口内
   * 3. 检查父元素的可见性状态
   * 
   * @param {Text} textNode - 要检查的文本节点
   * @returns {boolean} 文本节点是否可见
   */
  function isTextNodeVisible(textNode) {
    try {
      // 创建一个范围，包含文本节点的内容
      const range = document.createRange();
      range.selectNodeContents(textNode);
      const rect = range.getBoundingClientRect();

      // 检查文本是否有实际尺寸（宽度和高度都大于0）
      if (rect.width === 0 || rect.height === 0) {
        return false;
      }

      // 检查文本是否在视口范围内（考虑视口扩展）
      const isInViewport = !(
        rect.bottom < -viewportExpansion || // 在视口上方
        rect.top > window.innerHeight + viewportExpansion || // 在视口下方
        rect.right < -viewportExpansion || // 在视口左侧
        rect.left > window.innerWidth + viewportExpansion // 在视口右侧
      );

      // 获取并检查父元素的可见性
      const parentElement = textNode.parentElement;
      if (!parentElement) return false;

      try {
        // 优先使用checkVisibility API（如果浏览器支持）
        return isInViewport && parentElement.checkVisibility({
          checkOpacity: true, // 检查不透明度
          checkVisibilityCSS: true, // 检查CSS可见性属性
        });
      } catch (e) {
        // 如果不支持checkVisibility，则手动检查关键的CSS属性
        const style = window.getComputedStyle(parentElement);
        return isInViewport &&
          style.display !== 'none' && // 不是display:none
          style.visibility !== 'hidden' && // 不是visibility:hidden
          style.opacity !== '0'; // 不是完全透明
      }
    } catch (e) {
      console.warn('Error checking text node visibility:', e);
      return false;
    }
  }

  /**
   * 检查元素是否应该被包含在DOM树中
   * 
   * 过滤掉不相关或不需要处理的元素类型，提高性能。
   * 一些常见的容器元素总是被接受，而一些特殊的元素类型会被跳过。
   * 
   * @param {Element} element - 要检查的DOM元素
   * @returns {boolean} 元素是否应该被接受和处理
   */
  function isElementAccepted(element) {
    if (!element || !element.tagName) return false;

    // 始终接受body和常见容器元素
    const alwaysAccept = new Set([
      "body", "div", "main", "article", "section", "nav", "header", "footer"
    ]);
    const tagName = element.tagName.toLowerCase();

    if (alwaysAccept.has(tagName)) return true;

    // 拒绝处理的元素类型列表
    const leafElementDenyList = new Set([
      "svg",       // SVG图形元素，通常不需要交互
      "script",    // JavaScript脚本
      "style",     // CSS样式
      "link",      // 外部资源链接
      "meta",      // 元数据
      "noscript",  // 不支持脚本时显示的内容
      "template",  // 模板元素
    ]);

    return !leafElementDenyList.has(tagName);
  }

  /**
   * 检查元素是否可见
   * 
   * 通过检查元素的样式属性和尺寸来判断元素是否实际可见。
   * 一个可见的元素必须有非零的尺寸，且不被CSS隐藏。
   * 
   * @param {Element} element - 要检查的DOM元素
   * @returns {boolean} 元素是否可见
   */
  function isElementVisible(element) {
    const style = getCachedComputedStyle(element);
    
    // 特殊处理文件上传组件和日期控件
    const tagName = element.tagName.toLowerCase();
    const type = element.getAttribute('type')?.toLowerCase();
    const role = element.getAttribute('role')?.toLowerCase();
    const id = element.id?.toLowerCase() || '';
    const classList = Array.from(element.classList || []).join(' ').toLowerCase();
    
    // 对特定表单控件和文件上传组件进行特殊处理
    const isSpecialFormControl = 
      (tagName === 'input' && type === 'file') || // 文件上传输入框
      (id.includes('photo') || classList.includes('photo') || (element.parentElement && element.parentElement.id?.includes('photo'))) || // 照片/图片上传相关元素
      (id.includes('validtime') || id.includes('valid-time') || classList.includes('date-picker')) || // 日期控件
      (tagName === 'input' && type === 'checkbox') || // 复选框
      role === 'checkbox' || // ARIA复选框
      element.closest('.ant-checkbox-wrapper') !== null || // Ant Design复选框容器
      element.closest('.ant-picker') !== null || // Ant Design日期选择器
      element.closest('.ant-picker-range') !== null; // Ant Design日期范围选择器
    
    // 对特定元素放宽可见性要求
    if (isSpecialFormControl) {
      return true; // 特殊控件始终视为可见
    }
    
    return (
      element.offsetWidth > 0 &&              // 元素有宽度
      element.offsetHeight > 0 &&             // 元素有高度
      style.visibility !== "hidden" &&        // 不是visibility:hidden
      style.display !== "none"                // 不是display:none
    );
  }

  /**
   * 检查元素是否可交互
   * 
   * 这是一个复杂的函数，通过多种启发式规则判断元素是否可交互。
   * 使用多层检查来识别各种可交互元素，包括：
   * 1. 标准交互元素（按钮、链接等）
   * 2. ARIA角色和属性
   * 3. 事件处理器
   * 4. 特殊类和自定义属性
   * 5. Cookie横幅和同意对话框
   * 
   * @param {Element} element - 要检查的DOM元素
   * @returns {boolean} 元素是否可交互
   */
  function isInteractiveElement(element) {
    // 基本类型检查
    if (!element || element.nodeType !== Node.ELEMENT_NODE) {
      return false;
    }

    // 获取元素的标签名、ID和类名以进行更精确的检测
    const tagName = element.tagName.toLowerCase();
    const id = element.id?.toLowerCase() || '';
    const classList = Array.from(element.classList || []).join(' ').toLowerCase();
    const type = element.getAttribute('type')?.toLowerCase();
    const placeholder = element.getAttribute('placeholder')?.toLowerCase() || '';
    
    // 特殊处理的UI组件标识
    const isAntDesignComponent = 
      element.closest('.ant-upload') !== null || // Ant Design上传组件
      element.closest('.ant-picker') !== null || // Ant Design日期选择器
      element.closest('.ant-picker-range') !== null || // Ant Design日期范围选择器
      element.closest('.ant-checkbox-wrapper') !== null || // Ant Design复选框
      element.closest('.ant-input-affix-wrapper') !== null; // Ant Design输入框
    
    // 特殊处理身份证相关组件
    const isIdCardComponent = 
      id.includes('idphoto') || 
      id.includes('id-photo') || 
      classList.includes('idphoto') || 
      classList.includes('id-photo') ||
      (element.parentElement && (
        element.parentElement.id?.includes('idphoto') || 
        element.parentElement.id?.includes('id-photo')
      ));
    
    // 特殊处理证件有效期相关组件
    const isDateComponent = 
      id.includes('validtime') || 
      id.includes('valid-time') || 
      classList.includes('date-picker') ||
      classList.includes('ant-picker') ||
      placeholder.includes('请选择'); // 通常日期选择器的placeholder
    
    // 特殊处理复选框和其label
    const isCheckboxComponent = 
      (tagName === 'input' && type === 'checkbox') ||
      (tagName === 'label' && element.querySelector('input[type="checkbox"]')) ||
      element.getAttribute('role') === 'checkbox' ||
      classList.includes('checkbox');
    
    // 特殊处理签发机关输入框
    const isIssueOrgInput = 
      id.includes('issueorg') || 
      id.includes('issue-org') ||
      placeholder.includes('签发机关') ||
      placeholder.includes('请输入');
    
    // 如果是我们特别关注的组件，直接返回true
    if (isAntDesignComponent || isIdCardComponent || isDateComponent || isCheckboxComponent || isIssueOrgInput) {
      return true;
    }

    // Cookie横幅元素的特殊处理
    // 这些元素在现代网站上很常见，通常需要用户交互
    const isCookieBannerElement =
      (typeof element.closest === 'function') && (
        element.closest('[id*="onetrust"]') ||          // OnesTrust cookie管理器
        element.closest('[class*="onetrust"]') ||       // 使用类选择器
        element.closest('[data-nosnippet="true"]') ||   // Google nosnippet属性（常用于Cookie提示）
        element.closest('[aria-label*="cookie"]')       // 带有cookie相关aria-label的元素
      );

    if (isCookieBannerElement) {
      // 检查是否是横幅内的按钮或交互元素
      if (
        element.tagName.toLowerCase() === 'button' ||
        element.getAttribute('role') === 'button' ||
        element.onclick ||
        element.getAttribute('onclick') ||
        (element.classList && (
          element.classList.contains('ot-sdk-button') ||    // OnesTrust按钮类
          element.classList.contains('accept-button') ||    // 接受按钮通用类
          element.classList.contains('reject-button')       // 拒绝按钮通用类
        )) ||
        element.getAttribute('aria-label')?.toLowerCase().includes('accept') ||  // 带accept的aria-label
        element.getAttribute('aria-label')?.toLowerCase().includes('reject')     // 带reject的aria-label
      ) {
        return true;
      }
    }

    // 基本交互元素和角色集合
    // 这些是HTML标准定义的天然可交互元素
    const interactiveElements = new Set([
      "a", "button", "details", "embed", "input", "menu", "menuitem",
      "object", "select", "textarea", "canvas", "summary", "dialog",
      "banner"
    ]);

    // 可交互的ARIA角色集合
    // 这些角色明确表示元素是可交互的
    const interactiveRoles = new Set(['button-icon', 'dialog', 'button-text-icon-only', 'treeitem', 'alert', 'grid', 'progressbar', 'radio', 'checkbox', 'menuitem', 'option', 'switch', 'dropdown', 'scrollbar', 'combobox', 'a-button-text', 'button', 'region', 'textbox', 'tabpanel', 'tab', 'click', 'button-text', 'spinbutton', 'a-button-inner', 'link', 'menu', 'slider', 'listbox', 'a-dropdown-button', 'button-icon-only', 'searchbox', 'menuitemradio', 'tooltip', 'tree', 'menuitemcheckbox']);

    // 获取元素的基本属性
    const role = element.getAttribute("role");             // ARIA角色
    const ariaRole = element.getAttribute("aria-role");    // 备用ARIA角色属性
    const tabIndex = element.getAttribute("tabindex");     // tabindex属性

    // 检查特定的类名
    // 这些类名通常表示自定义交互组件
    const hasAddressInputClass = element.classList && (
      element.classList.contains("address-input__container__input") ||
      element.classList.contains("nav-btn") ||
      element.classList.contains("pull-left")
    );

    // 下拉菜单元素的增强检测
    if (element.classList && (
      element.classList.contains('dropdown-toggle') ||          // Bootstrap下拉菜单
      element.getAttribute('data-toggle') === 'dropdown' ||     // 数据属性触发下拉
      element.getAttribute('aria-haspopup') === 'true'          // ARIA弹出菜单标记
    )) {
      return true;
    }

    // 基本角色/属性检查
    // 对最常见的交互元素类型进行快速检查
    const hasInteractiveRole =
      hasAddressInputClass ||
      interactiveElements.has(tagName) ||                      // 标准交互元素
      interactiveRoles.has(role) ||                            // 有交互角色
      interactiveRoles.has(ariaRole) ||                        // 有交互ARIA角色
      (tabIndex !== null &&                                    // 有tabindex
        tabIndex !== "-1" &&                                   // 且不是-1（可聚焦但不能用Tab键访问）
        element.parentElement?.tagName.toLowerCase() !== "body") || // 不是直接附加到body的tabindex
      element.getAttribute("data-action") === "a-dropdown-select" || // Amazon下拉选择器
      element.getAttribute("data-action") === "a-dropdown-button";   // Amazon下拉按钮

    if (hasInteractiveRole) return true;

    // Cookie横幅和同意UI的附加检查
    // 这些UI元素需要特别处理，因为它们通常是动态注入且需要用户操作
    const isCookieBanner =
      element.id?.toLowerCase().includes('cookie') ||
      element.id?.toLowerCase().includes('consent') ||
      element.id?.toLowerCase().includes('notice') ||
      (element.classList && (
        element.classList.contains('otCenterRounded') ||       // OneTrust特定类
        element.classList.contains('ot-sdk-container')         // OneTrust容器
      )) ||
      element.getAttribute('data-nosnippet') === 'true' ||
      element.getAttribute('aria-label')?.toLowerCase().includes('cookie') ||
      element.getAttribute('aria-label')?.toLowerCase().includes('consent') ||
      (element.tagName.toLowerCase() === 'div' && (
        element.id?.includes('onetrust') ||
        (element.classList && (
          element.classList.contains('onetrust') ||
          element.classList.contains('cookie') ||
          element.classList.contains('consent')
        ))
      ));

    if (isCookieBanner) return true;

    // 对Cookie横幅内按钮的额外检查
    // 这些按钮可能没有标准的交互标记，但需要用户点击
    const isInCookieBanner = typeof element.closest === 'function' && element.closest(
      '[id*="cookie"],[id*="consent"],[class*="cookie"],[class*="consent"],[id*="onetrust"]'
    );

    if (isInCookieBanner && (
      element.tagName.toLowerCase() === 'button' ||
      element.getAttribute('role') === 'button' ||
      (element.classList && element.classList.contains('button')) ||
      element.onclick ||
      element.getAttribute('onclick')
    )) {
      return true;
    }

    // 获取计算样式
    const style = window.getComputedStyle(element);

    // 检查事件处理器
    // 有点击处理器的元素几乎总是可交互的
    const hasClickHandler =
      element.onclick !== null ||                             // 原生onclick处理器
      element.getAttribute("onclick") !== null ||             // onclick属性
      element.hasAttribute("ng-click") ||                     // AngularJS点击指令
      element.hasAttribute("@click") ||                       // Vue点击指令
      element.hasAttribute("v-on:click");                     // Vue事件绑定

    /**
     * 安全地获取元素的事件监听器
     * 尝试使用getEventListeners API，如果不可用则回退到检查on*属性
     * @param {Element} el - 要检查的元素
     * @returns {Object} 事件监听器映射
     */
    function getEventListeners(el) {
      try {
        return window.getEventListeners?.(el) || {};
      } catch (e) {
        // getEventListeners不可用，创建一个模拟的监听器对象
        const listeners = {};
        const eventTypes = [
          "click",
          "mousedown",
          "mouseup",
          "touchstart",
          "touchend",
          "keydown",
          "keyup",
          "focus",
          "blur",
        ];

        // 检查每种事件类型的on*处理器
        for (const type of eventTypes) {
          const handler = el[`on${type}`];
          if (handler) {
            listeners[type] = [{ listener: handler, useCapture: false }];
          }
        }
        return listeners;
      }
    }

    // 检查点击相关事件
    const listeners = getEventListeners(element);
    const hasClickListeners =
      listeners &&
      (listeners.click?.length > 0 ||           // 有click监听器
        listeners.mousedown?.length > 0 ||      // 有mousedown监听器
        listeners.mouseup?.length > 0 ||        // 有mouseup监听器
        listeners.touchstart?.length > 0 ||     // 有touchstart监听器
        listeners.touchend?.length > 0);        // 有touchend监听器

    // 检查ARIA属性
    // 这些属性通常表示元素的交互状态
    const hasAriaProps =
      element.hasAttribute("aria-expanded") ||   // 表示可展开/折叠
      element.hasAttribute("aria-pressed") ||    // 表示可按压（如切换按钮）
      element.hasAttribute("aria-selected") ||   // 表示可选择
      element.hasAttribute("aria-checked");      // 表示可选中

    // 检查元素是否可编辑
    const isContentEditable = element.getAttribute("contenteditable") === "true" ||
      element.isContentEditable ||
      element.id === "tinymce" ||                // TinyMCE编辑器
      element.classList.contains("mce-content-body") ||  // TinyMCE内容区
      (element.tagName.toLowerCase() === "body" && element.getAttribute("data-id")?.startsWith("mce_")); // TinyMCE内容体

    // 检查元素是否可拖动
    const isDraggable =
      element.draggable || element.getAttribute("draggable") === "true";

    // 综合所有检查结果
    return (
      hasAriaProps ||
      hasClickHandler ||
      hasClickListeners ||
      isDraggable ||
      isContentEditable
    );
  }

  /**
   * Checks if an element is the topmost element at its position.
   */
  function isTopElement(element) {
    const rect = getCachedBoundingRect(element);

    // 获取元素的标签名、ID和类名以进行更精确的检测
    const tagName = element.tagName.toLowerCase();
    const id = element.id?.toLowerCase() || '';
    const classList = Array.from(element.classList || []).join(' ').toLowerCase();

    // 对特定UI组件放宽顶层元素的判断
    const isSpecialUIComponent = 
      element.closest('.ant-upload') !== null || // Ant Design上传组件
      element.closest('.ant-picker') !== null || // Ant Design日期选择器
      element.closest('.ant-picker-range') !== null || // Ant Design日期范围选择器
      element.closest('.ant-checkbox-wrapper') !== null || // Ant Design复选框
      element.closest('.ant-input-affix-wrapper') !== null || // Ant Design输入框
      id.includes('idphoto') || // 身份证相关
      id.includes('validtime') || // 有效期相关
      id.includes('idvalidtype') || // 长期复选框
      id.includes('issueorg'); // 签发机关

    // 对特定组件直接返回true，认为其是顶层元素
    if (isSpecialUIComponent) {
      return true;
    }

    // If element is not in viewport, consider it top
    const isInViewport = (
      rect.left < window.innerWidth &&
      rect.right > 0 &&
      rect.top < window.innerHeight &&
      rect.bottom > 0
    );

    if (!isInViewport) {
      return true;
    }

    // Find the correct document context and root element
    let doc = element.ownerDocument;

    // If we're in an iframe, elements are considered top by default
    if (doc !== window.document) {
      return true;
    }

    // For shadow DOM, we need to check within its own root context
    const shadowRoot = element.getRootNode();
    if (shadowRoot instanceof ShadowRoot) {
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;

      try {
        const topEl = measureDomOperation(
          () => shadowRoot.elementFromPoint(centerX, centerY),
          'elementFromPoint'
        );
        if (!topEl) return false;

        let current = topEl;
        while (current && current !== shadowRoot) {
          if (current === element) return true;
          current = current.parentElement;
        }
        return false;
      } catch (e) {
        return true;
      }
    }

    // For elements in viewport, check if they're topmost
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;

    try {
      const topEl = document.elementFromPoint(centerX, centerY);
      if (!topEl) return false;

      let current = topEl;
      while (current && current !== document.documentElement) {
        if (current === element) return true;
        current = current.parentElement;
      }
      return false;
    } catch (e) {
      return true;
    }
  }

  /**
   * Checks if an element is within the expanded viewport.
   */
  function isInExpandedViewport(element, viewportExpansion) {
    if (viewportExpansion === -1) {
      return true;
    }

    const rect = getCachedBoundingRect(element);

    // Simple viewport check without scroll calculations
    return !(
      rect.bottom < -viewportExpansion ||
      rect.top > window.innerHeight + viewportExpansion ||
      rect.right < -viewportExpansion ||
      rect.left > window.innerWidth + viewportExpansion
    );
  }

  // Add this new helper function
  function getEffectiveScroll(element) {
    let currentEl = element;
    let scrollX = 0;
    let scrollY = 0;

    return measureDomOperation(() => {
      while (currentEl && currentEl !== document.documentElement) {
        if (currentEl.scrollLeft || currentEl.scrollTop) {
          scrollX += currentEl.scrollLeft;
          scrollY += currentEl.scrollTop;
        }
        currentEl = currentEl.parentElement;
      }

      scrollX += window.scrollX;
      scrollY += window.scrollY;

      return { scrollX, scrollY };
    }, 'scrollOperations');
  }

  // Add these helper functions at the top level
  function isInteractiveCandidate(element) {
    if (!element || element.nodeType !== Node.ELEMENT_NODE) return false;

    const tagName = element.tagName.toLowerCase();

    // Fast-path for common interactive elements
    const interactiveElements = new Set([
      "a", "button", "input", "select", "textarea", "details", "summary"
    ]);

    if (interactiveElements.has(tagName)) return true;

    // Quick attribute checks without getting full lists
    const hasQuickInteractiveAttr = element.hasAttribute("onclick") ||
      element.hasAttribute("role") ||
      element.hasAttribute("tabindex") ||
      element.hasAttribute("aria-") ||
      element.hasAttribute("data-action");

    return hasQuickInteractiveAttr;
  }

  function quickVisibilityCheck(element) {
    // Fast initial check before expensive getComputedStyle
    return element.offsetWidth > 0 &&
      element.offsetHeight > 0 &&
      !element.hasAttribute("hidden") &&
      element.style.display !== "none" &&
      element.style.visibility !== "hidden";
  }

  /**
   * Creates a node data object for a given node and its descendants.
   */
  function buildDomTree(node, parentIframe = null) {
    if (debugMode) PERF_METRICS.nodeMetrics.totalNodes++;

    if (!node || node.id === HIGHLIGHT_CONTAINER_ID) {
      if (debugMode) PERF_METRICS.nodeMetrics.skippedNodes++;
      return null;
    }

    // Special handling for root node (body)
    if (node === document.body) {
      const nodeData = {
        tagName: 'body',
        attributes: {},
        xpath: '/body',
        children: [],
      };

      // Process children of body
      for (const child of node.childNodes) {
        const domElement = buildDomTree(child, parentIframe);
        if (domElement) nodeData.children.push(domElement);
      }

      const id = `${ID.current++}`;
      DOM_HASH_MAP[id] = nodeData;
      if (debugMode) PERF_METRICS.nodeMetrics.processedNodes++;
      return id;
    }

    // Early bailout for non-element nodes except text
    if (node.nodeType !== Node.ELEMENT_NODE && node.nodeType !== Node.TEXT_NODE) {
      if (debugMode) PERF_METRICS.nodeMetrics.skippedNodes++;
      return null;
    }

    // Process text nodes
    if (node.nodeType === Node.TEXT_NODE) {
      const textContent = node.textContent.trim();
      if (!textContent) {
        if (debugMode) PERF_METRICS.nodeMetrics.skippedNodes++;
        return null;
      }

      // Only check visibility for text nodes that might be visible
      const parentElement = node.parentElement;
      if (!parentElement || parentElement.tagName.toLowerCase() === 'script') {
        if (debugMode) PERF_METRICS.nodeMetrics.skippedNodes++;
        return null;
      }

      const id = `${ID.current++}`;
      DOM_HASH_MAP[id] = {
        type: "TEXT_NODE",
        text: textContent,
        isVisible: isTextNodeVisible(node),
      };
      if (debugMode) PERF_METRICS.nodeMetrics.processedNodes++;
      return id;
    }

    // Quick checks for element nodes
    if (node.nodeType === Node.ELEMENT_NODE && !isElementAccepted(node)) {
      if (debugMode) PERF_METRICS.nodeMetrics.skippedNodes++;
      return null;
    }

    // Early viewport check - only filter out elements clearly outside viewport
    if (viewportExpansion !== -1) {
      const rect = getCachedBoundingRect(node);
      const style = getCachedComputedStyle(node);

      // Skip viewport check for fixed/sticky elements as they may appear anywhere
      const isFixedOrSticky = style && (style.position === 'fixed' || style.position === 'sticky');

      // Check if element has actual dimensions
      const hasSize = node.offsetWidth > 0 || node.offsetHeight > 0;

      if (!rect || (!isFixedOrSticky && !hasSize && (
        rect.bottom < -viewportExpansion ||
        rect.top > window.innerHeight + viewportExpansion ||
        rect.right < -viewportExpansion ||
        rect.left > window.innerWidth + viewportExpansion
      ))) {
        if (debugMode) PERF_METRICS.nodeMetrics.skippedNodes++;
        return null;
      }
    }

    // Process element node
    const nodeData = {
      tagName: node.tagName.toLowerCase(),
      attributes: {},
      xpath: getXPathTree(node, true),
      children: [],
    };

    // 检测特殊UI组件
    const isSpecialComponent = node.nodeType === Node.ELEMENT_NODE && (
      node.id?.toLowerCase().includes('idphoto') || // 身份证照片相关
      node.id?.toLowerCase().includes('validtime') || // 证件有效期相关
      node.id?.toLowerCase() === 'idvalidtype' || // 长期复选框
      node.id?.toLowerCase() === 'issueorg' || // 签发机关
      (node.classList && (
        Array.from(node.classList).some(cls => cls.includes('ant-upload')) || // 上传组件
        Array.from(node.classList).some(cls => cls.includes('ant-picker')) || // 日期选择器
        Array.from(node.classList).some(cls => cls.includes('ant-checkbox')) || // 复选框
        Array.from(node.classList).some(cls => cls.includes('ant-input')) // 输入框
      )) ||
      node.closest('.ant-upload') !== null || // 上传组件的子元素
      node.closest('.ant-picker') !== null || // 日期选择器的子元素
      node.closest('.ant-checkbox-wrapper') !== null || // 复选框的子元素
      node.closest('.ant-input-affix-wrapper') !== null // 输入框的子元素
    );

    // Get attributes for interactive elements or potential text containers
    if (isInteractiveCandidate(node) || isSpecialComponent || node.tagName.toLowerCase() === 'iframe' || node.tagName.toLowerCase() === 'body') {
      const attributeNames = node.getAttributeNames?.() || [];
      for (const name of attributeNames) {
        nodeData.attributes[name] = node.getAttribute(name);
      }
    }

    // Check interactivity
    if (node.nodeType === Node.ELEMENT_NODE) {
      // 对特殊组件单独处理
      if (isSpecialComponent) {
        nodeData.isVisible = true;
        nodeData.isTopElement = true;
        nodeData.isInteractive = true;
        nodeData.isInViewport = true;
        nodeData.highlightIndex = highlightIndex++;

        if (doHighlightElements) {
          if (focusHighlightIndex >= 0) {
            if (focusHighlightIndex === nodeData.highlightIndex) {
              highlightElement(node, nodeData.highlightIndex, parentIframe);
            }
          } else {
            highlightElement(node, nodeData.highlightIndex, parentIframe);
          }
        }
      } else {
        nodeData.isVisible = isElementVisible(node);
        if (nodeData.isVisible) {
          nodeData.isTopElement = isTopElement(node);
          if (nodeData.isTopElement) {
            nodeData.isInteractive = isInteractiveElement(node);
            if (nodeData.isInteractive) {
              nodeData.isInViewport = true;
              nodeData.highlightIndex = highlightIndex++;

              if (doHighlightElements) {
                if (focusHighlightIndex >= 0) {
                  if (focusHighlightIndex === nodeData.highlightIndex) {
                    highlightElement(node, nodeData.highlightIndex, parentIframe);
                  }
                } else {
                  highlightElement(node, nodeData.highlightIndex, parentIframe);
                }
              }
            }
          }
        }
      }
    }

    // Process children, with special handling for iframes and rich text editors
    if (node.tagName) {
      const tagName = node.tagName.toLowerCase();

      // Handle iframes
      if (tagName === "iframe") {
        try {
          const iframeDoc = node.contentDocument || node.contentWindow?.document;
          if (iframeDoc) {
            for (const child of iframeDoc.childNodes) {
              const domElement = buildDomTree(child, node);
              if (domElement) nodeData.children.push(domElement);
            }
          }
        } catch (e) {
          console.warn("Unable to access iframe:", e);
        }
      }
      // Handle rich text editors and contenteditable elements
      else if (
        node.isContentEditable ||
        node.getAttribute("contenteditable") === "true" ||
        node.id === "tinymce" ||
        node.classList.contains("mce-content-body") ||
        (tagName === "body" && node.getAttribute("data-id")?.startsWith("mce_"))
      ) {
        // Process all child nodes to capture formatted text
        for (const child of node.childNodes) {
          const domElement = buildDomTree(child, parentIframe);
          if (domElement) nodeData.children.push(domElement);
        }
      }
      // Handle shadow DOM
      else if (node.shadowRoot) {
        nodeData.shadowRoot = true;
        for (const child of node.shadowRoot.childNodes) {
          const domElement = buildDomTree(child, parentIframe);
          if (domElement) nodeData.children.push(domElement);
        }
      }
      // Handle regular elements
      else {
        for (const child of node.childNodes) {
          const domElement = buildDomTree(child, parentIframe);
          if (domElement) nodeData.children.push(domElement);
        }
      }
    }

    // Skip empty anchor tags
    if (nodeData.tagName === 'a' && nodeData.children.length === 0 && !nodeData.attributes.href) {
      if (debugMode) PERF_METRICS.nodeMetrics.skippedNodes++;
      return null;
    }

    const id = `${ID.current++}`;
    DOM_HASH_MAP[id] = nodeData;
    if (debugMode) PERF_METRICS.nodeMetrics.processedNodes++;
    return id;
  }

  // After all functions are defined, wrap them with performance measurement
  // Remove buildDomTree from here as we measure it separately
  highlightElement = measureTime(highlightElement);
  isInteractiveElement = measureTime(isInteractiveElement);
  isElementVisible = measureTime(isElementVisible);
  isTopElement = measureTime(isTopElement);
  isInExpandedViewport = measureTime(isInExpandedViewport);
  isTextNodeVisible = measureTime(isTextNodeVisible);
  getEffectiveScroll = measureTime(getEffectiveScroll);

  const rootId = buildDomTree(document.body);

  // Clear the cache before starting
  DOM_CACHE.clearCache();

  // Only process metrics in debug mode
  if (debugMode && PERF_METRICS) {
    // Convert timings to seconds and add useful derived metrics
    Object.keys(PERF_METRICS.timings).forEach(key => {
      PERF_METRICS.timings[key] = PERF_METRICS.timings[key] / 1000;
    });

    Object.keys(PERF_METRICS.buildDomTreeBreakdown).forEach(key => {
      if (typeof PERF_METRICS.buildDomTreeBreakdown[key] === 'number') {
        PERF_METRICS.buildDomTreeBreakdown[key] = PERF_METRICS.buildDomTreeBreakdown[key] / 1000;
      }
    });

    // Add some useful derived metrics
    if (PERF_METRICS.buildDomTreeBreakdown.buildDomTreeCalls > 0) {
      PERF_METRICS.buildDomTreeBreakdown.averageTimePerNode =
        PERF_METRICS.buildDomTreeBreakdown.totalTime / PERF_METRICS.buildDomTreeBreakdown.buildDomTreeCalls;
    }

    PERF_METRICS.buildDomTreeBreakdown.timeInChildCalls =
      PERF_METRICS.buildDomTreeBreakdown.totalTime - PERF_METRICS.buildDomTreeBreakdown.totalSelfTime;

    // Add average time per operation to the metrics
    Object.keys(PERF_METRICS.buildDomTreeBreakdown.domOperations).forEach(op => {
      const time = PERF_METRICS.buildDomTreeBreakdown.domOperations[op];
      const count = PERF_METRICS.buildDomTreeBreakdown.domOperationCounts[op];
      if (count > 0) {
        PERF_METRICS.buildDomTreeBreakdown.domOperations[`${op}Average`] = time / count;
      }
    });

    // Calculate cache hit rates
    const boundingRectTotal = PERF_METRICS.cacheMetrics.boundingRectCacheHits + PERF_METRICS.cacheMetrics.boundingRectCacheMisses;
    const computedStyleTotal = PERF_METRICS.cacheMetrics.computedStyleCacheHits + PERF_METRICS.cacheMetrics.computedStyleCacheMisses;

    if (boundingRectTotal > 0) {
      PERF_METRICS.cacheMetrics.boundingRectHitRate = PERF_METRICS.cacheMetrics.boundingRectCacheHits / boundingRectTotal;
    }

    if (computedStyleTotal > 0) {
      PERF_METRICS.cacheMetrics.computedStyleHitRate = PERF_METRICS.cacheMetrics.computedStyleCacheHits / computedStyleTotal;
    }

    if ((boundingRectTotal + computedStyleTotal) > 0) {
      PERF_METRICS.cacheMetrics.overallHitRate =
        (PERF_METRICS.cacheMetrics.boundingRectCacheHits + PERF_METRICS.cacheMetrics.computedStyleCacheHits) /
        (boundingRectTotal + computedStyleTotal);
    }
  }

  return debugMode ?
    { rootId, map: DOM_HASH_MAP, perfMetrics: PERF_METRICS } :
    { rootId, map: DOM_HASH_MAP };
};
