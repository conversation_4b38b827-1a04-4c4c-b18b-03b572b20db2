#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
DOM服务模块

此模块负责DOM树的构建和元素定位，是实时元素定位功能的核心。
"""

import gc
import os
import json
import logging
import time
from typing import Dict, List, Optional, Tuple, Any, Union
from pathlib import Path

from playwright.sync_api import Page, ElementHandle, Locator
from playwright.async_api import Page as AsyncPage

from .views import DOMBaseNode, DOMElementNode, DOMTextNode, DOMState, SelectorMap, ViewportInfo, CoordinateSet

# 获取特定的自定义日志记录器，不使用root logger
logger = logging.getLogger(__name__)


class DOMService:
    """
    DOM服务类
    
    负责提取页面DOM元素信息，用于元素定位和智能分析。
    """
    
    def __init__(self, keyword_driver):
        """
        初始化DOM服务
        
        Args:
            keyword_driver: 关键字驱动引擎实例
        """
        self.keyword_driver = keyword_driver
        self.page = self.keyword_driver.page
        self.async_page = self.keyword_driver.async_page
        self.dom_elements = []
        self.last_update = 0
        
        # 使用单一路径加载
        script_path = os.path.join(os.path.dirname(__file__), "buildDomTree.js")
        if os.path.exists(script_path):
            with open(script_path, 'r', encoding='utf-8') as f:
                self.js_code = f.read()
                logger.info(f"DOM分析脚本已加载: {script_path}")
        else:
            raise ValueError(f"DOM分析脚本不存在: {script_path}")
        
        logger.info("DOM服务初始化完成")
    
    def get_clickable_elements(self, highlight_elements: bool = True, viewport_expansion: int = 0) -> DOMState:
        """
        获取页面可点击元素(同步方法)
        
        Args:
            highlight_elements: 是否高亮显示元素
            viewport_expansion: 视口扩展像素数(用于包含视口外的元素)
            
        Returns:
            DOMState: DOM状态对象
        """
        try:
            logger.info("开始获取可点击元素...")
            
            # 检查页面是否已加载
            if not self.page:
                logger.error("页面未加载，无法获取DOM信息")
                return None
            
            # 构建DOM树
            root_element, selector_map = self._build_dom_tree(highlight_elements, viewport_expansion)
            
            if not root_element:
                logger.error("DOM树构建失败")
                return None
            
            # 创建DOM状态对象
            dom_state = DOMState(
                element_tree=root_element,
                selector_map=selector_map
            )
            
            # 计算统计信息
            interactive_elements = []
            viewport_elements = []
            
            if hasattr(root_element, "find_interactive_elements"):
                interactive_elements = root_element.find_interactive_elements()
            
            if hasattr(root_element, "find_elements_in_viewport"):
                viewport_elements = root_element.find_elements_in_viewport()
            
            logger.info(f"DOM分析完成: 找到 {len(interactive_elements)} 个可交互元素，"
                       f"{len(viewport_elements)} 个可见元素")
            
            self.last_update = time.time()
            return dom_state
            
        except Exception as e:
            logger.error(f"获取可点击元素失败: {str(e)}")
            return None

    def _build_dom_tree(self, highlight_elements: bool = True, viewport_expansion: int = 0) -> Tuple[Optional[DOMElementNode], Dict]:
        """
        构建DOM树 (同步方法)
        
        Args:
            highlight_elements: 是否高亮显示元素
            viewport_expansion: 视口扩展像素数
            
        Returns:
            Tuple: (根元素节点, 选择器映射)
        """
        try:
            if not self.page:
                logger.error("页面未加载，无法构建DOM树")
                return None, {}
            
            # 配置DOM分析参数
            args = {
                "doHighlightElements": highlight_elements,
                "focusHighlightIndex": -1,
                "viewportExpansion": viewport_expansion,
                "debugMode": False
            }
            
            # 执行JavaScript并获取结果
            logger.info("执行DOM分析脚本...")
            start_time = time.time()
            
            try:
                # 使用同步API执行脚本
                result = self.page.evaluate(self.js_code, args)
                logger.info(f"DOM脚本执行完成，耗时: {time.time() - start_time:.2f}秒")
            except Exception as js_error:
                logger.error(f"DOM分析脚本执行失败: {str(js_error)}")
                return None, {}
            
            if not result:
                logger.error("DOM分析脚本返回空结果")
                return None, {}
            
            # 使用同步方法构建DOM树
            root_element, selector_map = self._construct_dom_tree_sync(result)
            
            if root_element:
                logger.info(f"DOM树构建成功")
                return root_element, selector_map
            else:
                logger.error("DOM树构建失败")
                return None, {}
                
        except Exception as e:
            logger.error(f"DOM树构建失败: {str(e)}")
            return None, {}
    
    def _construct_dom_tree_sync(self, eval_page: dict) -> Tuple[Optional[DOMElementNode], Dict]:
        """
        从JavaScript DOM评估结果同步构建DOM树
        
        Args:
            eval_page: JavaScript DOM评估结果
            
        Returns:
            Tuple: (根元素节点, 选择器映射)
        """
        js_node_map = eval_page.get('map', {})
        js_root_id = eval_page.get('rootId')
        
        if not js_node_map or js_root_id is None:
            logger.error("DOM树数据不完整")
            return None, {}
        
        selector_map = {}
        node_map = {}
        
        # 处理所有节点
        for id, node_data in js_node_map.items():
            node, children_ids = self._parse_node(node_data)
            if node is None:
                continue
                
            node_map[id] = node
            
            # 添加到选择器映射
            if isinstance(node, DOMElementNode) and node.highlight_index is not None:
                selector_map[node.highlight_index] = node
        
        # 构建树结构 - 自底向上
        for id, node in node_map.items():
            if isinstance(node, DOMElementNode):
                node_data = js_node_map[id]
                children_ids = node_data.get('children', [])
                
                for child_id in children_ids:
                    if child_id not in node_map:
                        continue
                        
                    child_node = node_map[child_id]
                    child_node.parent = node
                    node.children.append(child_node)
        
        # 获取根节点
        root_element = node_map.get(str(js_root_id))
        
        # 清理内存
        del node_map
        del js_node_map
        gc.collect()
        
        if not root_element or not isinstance(root_element, DOMElementNode):
            logger.error("无法解析DOM树")
            return None, {}
            
        return root_element, selector_map

    def _parse_node(
        self,
        node_data: Dict
    ) -> Tuple[Optional[DOMBaseNode], List[str]]:
        """
        解析单个DOM节点
        
        参数:
            node_data: 节点数据
            
        返回:
            Tuple[Optional[DOMBaseNode], List[str]]: 节点对象和子节点ID列表
        """
        if not node_data:
            return None, []
        
        # 处理文本节点
        if node_data.get('type') == 'TEXT_NODE':
            text_node = DOMTextNode(
                text=node_data['text'],
                is_visible=node_data['isVisible'],
                parent=None
            )
            return text_node, []
        
        # 处理视口信息
        viewport_info = None
        if 'viewport' in node_data:
            viewport_info = ViewportInfo(
                width=node_data['viewport']['width'],
                height=node_data['viewport']['height']
            )
        
        # 处理元素节点
        element_node = DOMElementNode(
            tag_name=node_data['tagName'],
            xpath=node_data['xpath'],
            attributes=node_data.get('attributes', {}),
            children=[],
            is_visible=node_data.get('isVisible', False),
            is_interactive=node_data.get('isInteractive', False),
            is_top_element=node_data.get('isTopElement', False),
            is_in_viewport=node_data.get('isInViewport', False),
            highlight_index=node_data.get('highlightIndex'),
            shadow_root=node_data.get('shadowRoot', False),
            parent=None,
            viewport_info=viewport_info
        )
        
        # 获取子节点ID列表
        children_ids = node_data.get('children', [])
        
        return element_node, children_ids
    
    async def locate_element(self, element_key):
        """
        在页面上定位元素，使用多种策略按优先级顺序尝试
        
        @param element_key: 元素的描述关键字
        @return: 定位到的元素或None
        """
        # 优化定位策略顺序，首先尝试最可靠的定位方式
        strategies = [
            self._locate_by_id,           # 首先按ID定位 - 最精确的方式
            self._locate_by_name,         # 其次按name属性定位
            self._locate_by_placeholder,  # 按placeholder定位，对搜索框特别有效
            self._locate_by_label,        # 按关联label定位
            self._locate_by_role,         # 按ARIA角色定位
            self._locate_by_css,          # 使用CSS选择器
            self._locate_by_xpath,        # 使用XPath
            self._locate_by_text          # 最后才使用文本定位 - 最不精确
        ]
        
        # 处理元素关键字，使其更具通用性
        normalized_key = await self._normalize_element_key(element_key)
        
        # 对于搜索框特殊处理，增加常见搜索框ID识别
        if '搜索' in normalized_key or 'search' in normalized_key.lower():
            search_box = await self._try_common_search_box_locators()
            if search_box:
                return search_box
        
        # 按顺序尝试各种定位策略
        for strategy in strategies:
            element = await strategy(normalized_key)
            if element:
                return element
        
        return None
    
    async def _normalize_element_key(self, element_key):
        """规范化元素关键字，提高匹配成功率"""
        # 不再执行同义词替换，严格保留原始元素名称
        return element_key
    
    async def _try_common_search_box_locators(self):
        """尝试常见搜索引擎的搜索框定位器"""
        # 常见搜索框的ID和选择器
        common_search_selectors = [
            '#kw',                 # 百度搜索框ID
            'input[name="wd"]',    # 百度搜索框name
            '#q',                  # 通用搜索ID
            'input[name="q"]',     # Google搜索框
            'input[type="search"]', # 通用搜索类型
            'input[placeholder*="搜索"]', # 包含搜索的placeholder
            'input[placeholder*="search"]' # 英文搜索placeholder
        ]
        
        for selector in common_search_selectors:
            try:
                element = await self.async_page.query_selector(selector)
                if element:
                    return element
            except:
                continue
        
        return None
    
    def _generate_locators(self, element: DOMElementNode) -> List[Dict[str, Any]]:
        """
        根据DOM元素生成多种Playwright支持的定位器
        
        参数:
            element: DOM元素节点
            
        返回:
            List[Dict[str, Any]]: 按优先级排序的定位器列表
        """
        locators = []
        print("------------------",element)
        
        # 1. ID选择器 (优先级最高)
        if 'id' in element.attributes and element.attributes['id']:
            locators.append({
                'type': 'id',
                'value': element.attributes['id'],
                'priority': 1
            })
        
        # 2. 文本内容定位
        text_content = self._get_element_text(element)
        if text_content:
            locators.append({
                'type': 'text',
                'value': text_content,
                'priority': 2
            })
        
        # 3. 标签文本定位
        if element.tag_name == 'input' and 'label' in element.attributes:
            locators.append({
                'type': 'label',
                'value': element.attributes['label'],
                'priority': 3
            })
        
        # 4. 占位符文本定位
        if 'placeholder' in element.attributes and element.attributes['placeholder']:
            locators.append({
                'type': 'placeholder',
                'value': element.attributes['placeholder'],
                'priority': 4
            })
        
        # 5. Alt文本定位 (针对图片)
        if element.tag_name == 'img' and 'alt' in element.attributes:
            locators.append({
                'type': 'alt_text',
                'value': element.attributes['alt'],
                'priority': 5
            })
        
        # 6. 测试ID定位
        for test_attr in ['data-testid', 'data-test-id', 'data-test']:
            if test_attr in element.attributes:
                locators.append({
                    'type': 'test_id',
                    'value': element.attributes[test_attr],
                    'priority': 6
                })
        
        # 7. 标题属性定位
        if 'title' in element.attributes:
            locators.append({
                'type': 'title',
                'value': element.attributes['title'],
                'priority': 7
            })
        
        # 8. ARIA角色定位
        if 'role' in element.attributes:
            locators.append({
                'type': 'role',
                'value': {'role': element.attributes['role'], 'exact': True},
                'priority': 8
            })
        
        # 9. Name属性定位
        if 'name' in element.attributes:
            locators.append({
                'type': 'name',
                'value': element.attributes['name'],
                'priority': 9
            })
        
        # 10. 构建CSS选择器定位
        css_selector = self._build_css_selector(element)
        locators.append({
            'type': 'css',
            'value': css_selector,
            'priority': 10
        })
        
        # 11. XPath定位 (最低优先级但通用性最强)
        locators.append({
            'type': 'xpath',
            'value': element.xpath,
            'priority': 11
        })
        
        # 按优先级排序
        locators.sort(key=lambda x: x['priority'])
        
        return locators

    def _build_css_selector(self, element: DOMElementNode) -> str:
        """构建CSS选择器的辅助方法"""
        tag_name = element.tag_name.lower()
        selector_parts = [tag_name]
        
        # 添加类名
        if 'class' in element.attributes and element.attributes['class']:
            classes = element.attributes['class'].split()
            selector_parts.append(f".{'.'.join(classes)}")
        
        # 添加其他有用属性
        for attr in ['name', 'type', 'role', 'placeholder']:
            if attr in element.attributes and element.attributes[attr]:
                selector_parts.append(f"[{attr}='{element.attributes[attr]}']")
        
        return "".join(selector_parts)

    def _get_element_text(self, element: DOMElementNode) -> str:
        """获取元素的文本内容"""
        text_parts = []
        
        for child in element.children:
            if isinstance(child, DOMTextNode) and child.is_visible:
                text_parts.append(child.text)
        
        return " ".join(text_parts).strip()

    async def get_dom_elements_async(self) -> List[Dict[str, Any]]:
        """
        异步获取页面上所有可交互的DOM元素
        
        Returns:
            List[Dict[str, Any]]: DOM元素列表
        """
        try:
            # 检查页面状态
            if not self.async_page:
                logger.warning("DOM服务调用失败: 页面不存在")
                return []
                
            # 验证页面JavaScript执行能力
            try:
                result = await self.async_page.evaluate("1+1")
                if result != 2:
                    logger.error("页面无法正确执行JavaScript代码")
                    return []
            except Exception as e:
                logger.error(f"页面JavaScript执行失败: {str(e)}")
                return []
            
            # 执行DOM分析脚本 - 使用简单的元素分析脚本而不是buildDomTree.js
            simple_script = """
            () => {
                const elements = [];
                try {
                    const interactiveElements = Array.from(document.querySelectorAll('a, button, input, select, textarea, [role="button"]'));
                    interactiveElements.forEach((el, index) => {
                        elements.push({
                            index: index,
                            tagName: el.tagName.toLowerCase(),
                            text: el.innerText || el.textContent || '',
                            isVisible: true
                        });
                    });
                } catch (e) {
                    return { error: e.toString() };
                }
                return elements;
            }
            """
            elements = await self.async_page.evaluate(simple_script)
            
            # 处理结果
            if not elements or not isinstance(elements, list):
                logger.warning("DOM分析脚本未返回有效元素列表")
                return []
                
            logger.info(f"DOM分析完成，找到 {len(elements)} 个元素")
            return elements
            
        except Exception as e:
            logger.error(f"DOM元素提取失败: {str(e)}")
            return []

    async def get_clickable_elements_async(self, highlight_elements=True, focus_element=-1, viewport_expansion=0):
        """
        异步获取可点击元素
        
        Args:
            highlight_elements: 是否高亮显示元素
            focus_element: 要聚焦的元素索引 (-1表示无)
            viewport_expansion: 视口扩展像素
            
        Returns:
            DOMState: DOM状态对象，包含元素树和选择器映射
        """
        if not self.async_page:
            logger.error("异步页面未初始化")
            return None

        # 确保JavaScript可执行
        try:
            if await self.async_page.evaluate("1+1") != 2:
                raise ValueError("页面无法正确执行JavaScript代码")
        except Exception as e:
            logger.error(f"无法评估JavaScript: {str(e)}")
            return None
        
        # 构建参数
        args = {
            "doHighlightElements": highlight_elements,
            "focusHighlightIndex": focus_element,
            "viewportExpansion": viewport_expansion,
            "debugMode": False
        }
        
        # 执行DOM分析
        try:
            result = await self.async_page.evaluate(self.js_code, args)
            element_tree, selector_map = await self._construct_dom_tree(result)
            return DOMState(element_tree=element_tree, selector_map=selector_map)
        except Exception as e:
            logger.error(f"DOM分析失败: {str(e)}")
            return None

    async def _construct_dom_tree(self, eval_page):
        """
        从JavaScript DOM评估结果异步构建DOM树
        
        Args:
            eval_page: JavaScript DOM评估结果
            
        Returns:
            Tuple: (根元素节点, 选择器映射)
        """
        js_node_map = eval_page.get('map', {})
        js_root_id = eval_page.get('rootId')
        
        if not js_node_map or js_root_id is None:
            logger.error("DOM树数据不完整")
            return None, {}
        
        selector_map = {}
        node_map = {}
        
        # 处理所有节点
        for id, node_data in js_node_map.items():
            node, children_ids = self._parse_node(node_data)
            if node is None:
                continue
                
            node_map[id] = node
            
            # 添加到选择器映射
            if isinstance(node, DOMElementNode) and node.highlight_index is not None:
                selector_map[node.highlight_index] = node
        
        # 构建树结构 - 自底向上
        for id, node in node_map.items():
            if isinstance(node, DOMElementNode):
                node_data = js_node_map[id]
                children_ids = node_data.get('children', [])
                
                for child_id in children_ids:
                    if child_id not in node_map:
                        continue
                        
                    child_node = node_map[child_id]
                    child_node.parent = node
                    node.children.append(child_node)
        
        # 获取根节点
        root_element = node_map.get(str(js_root_id))
        
        # 清理内存
        del node_map
        del js_node_map
        gc.collect()
        
        if not root_element or not isinstance(root_element, DOMElementNode):
            logger.error("无法解析DOM树")
            return None, {}
            
        return root_element, selector_map

    async def locate_element_async(self, element_key):
        """
        异步定位元素
        
        根据元素关键字异步定位DOM元素
        
        Args:
            element_key: 元素关键字
            
        Returns:
            定位到的元素，如果未找到则返回None
        """
        try:
            # 规范化元素关键字
            element_key = await self._normalize_element_key(element_key)
            
            # 获取DOM状态
            dom_state = await self.get_clickable_elements_async(highlight_elements=True)
            if not dom_state or not dom_state.selector_map:
                logger.warning("无法获取DOM状态，元素定位失败")
                return None
                
            # 查找关键字匹配项
            best_match = None
            best_score = 0
            
            # 分析节点文本和属性，查找最佳匹配
            for node_id, node_data in dom_state.selector_map.items():
                # 提取节点文本和属性
                node_text = ""
                node_type = ""
                
                if hasattr(node_data, 'get_all_text_till_next_clickable_element'):
                    node_text = node_data.get_all_text_till_next_clickable_element().lower()
                
                if hasattr(node_data, 'tag_name'):
                    node_type = node_data.tag_name.lower()
                
                # 计算匹配分数 - 完全匹配得分高，部分匹配得分中等
                score = 0
                if element_key.lower() == node_text:
                    score = 100  # 完全匹配
                elif element_key.lower() in node_text:
                    score = 70   # 包含匹配
                elif node_text in element_key.lower():
                    score = 50   # 被包含匹配
                
                # 考虑元素类型的匹配
                if '按钮' in element_key and (node_type == 'button' or node_data.attributes.get('role', '') == 'button'):
                    score += 20
                elif '输入框' in element_key and (node_type == 'input' or node_type == 'textarea'):
                    score += 20
                elif '链接' in element_key and node_type == 'a':
                    score += 20
                
                # 更新最佳匹配
                if score > best_score:
                    best_score = score
                    best_match = node_data
            
            # 如果找到匹配，返回定位器
            if best_match and best_score >= 50:
                # 使用最佳匹配的选择器
                xpath = best_match.xpath if hasattr(best_match, 'xpath') else None
                
                if xpath:
                    logger.info(f"找到元素 '{element_key}' 的匹配，分数: {best_score}")
                    
                    # 根据元素类型选择不同的定位策略
                    page = self.async_page
                    if not page:
                        logger.error("无法获取有效的页面对象")
                        return None
                        
                    # 尝试多种定位策略
                    try:
                        # 首先尝试使用XPath
                        element = await page.query_selector(f"xpath={xpath}")
                        if element:
                            return element
                            
                        # 如果XPath失败，尝试CSS
                        css_selector = self._generate_css_selector(best_match)
                        if css_selector:
                            element = await page.query_selector(css_selector)
                            if element:
                                return element
                                
                    except Exception as e:
                        logger.error(f"使用选择器定位元素失败: {str(e)}")
                
            # 如果是搜索框，尝试常用的搜索框定位器
            if '搜索' in element_key or 'search' in element_key.lower():
                return await self._try_common_search_box_locators_async()
                
            logger.warning(f"未找到与 '{element_key}' 匹配的元素")
            return None
            
        except Exception as e:
            logger.error(f"定位元素失败: {str(e)}")
            return None
            
    async def _try_common_search_box_locators_async(self):
        """
        异步尝试常见的搜索框定位器
        
        Returns:
            定位到的搜索框元素，如果未找到则返回None
        """
        page = self.async_page
        if not page:
            return None
            
        # 常见的搜索框选择器
        search_selectors = [
            "input[type='search']",
            "input[name='q']",
            "input[name='query']",
            "input[name='search']",
            "input[placeholder='搜索' i]",
            "input[placeholder*='search' i]",
            ".search-input",
            "#search"
        ]
        
        # 尝试所有选择器
        for selector in search_selectors:
            try:
                element = await page.query_selector(selector)
                if element:
                    logger.info(f"找到搜索框: {selector}")
                    return element
            except Exception:
                continue
                
        # 尝试百度搜索框 (百度特殊处理)
        try:
            element = await page.query_selector("#kw")
            if element:
                logger.info("找到百度搜索框: #kw")
                return element
        except Exception:
            pass
            
        return None
