# -*- coding: utf-8 -*-
"""
大模型Agent服务模块

此模块提供基于大模型的大模型Agent服务，用于解析自然语言测试指令并执行测试用例。
"""

import os
import re
import asyncio
import logging
import json
import time
import base64
from typing import Dict, List, Any, Optional, Union, Callable, Tuple
from datetime import datetime
from pathlib import Path

try:
    from openai import OpenAI, AsyncOpenAI
except ImportError:
    logging.error("请安装 openai: pip install openai")
    raise

from core.browser.keyword import KeywordDriver
from config.config import BROWSER_CONFIG, TEST_CONFIG, LLM_CONFIG
from core.agent.prompt import SystemPrompt, AgentMessagePrompt

# 获取特定的自定义日志记录器，不使用root logger
logger = logging.getLogger(__name__)

class TestAgent:
    """
    大模型Agent类，负责解析自然语言测试指令并执行测试用例
    """
    def __init__(self, task: str, config: Dict = None, max_steps: int = 20, 
                save_screenshots: bool = True, api_key: str = None, api_base: str = None):
        """
        初始化大模型Agent
        
        Args:
            task: 测试任务描述
            config: 配置信息
            max_steps: 最大步骤数
            save_screenshots: 是否保存截图
            api_key: API密钥
            api_base: API基础URL
        """
        self.task = task
        self.config = config or {}
        self.max_steps = max_steps
        self.save_screenshots = save_screenshots
        self.current_step = 0
        self.test_results = []
        
        # 初始化系统提示词
        self.system_prompt = SystemPrompt()
        
        # 初始化关键字驱动引擎
        self.keyword_driver = KeywordDriver(self.config)
        
        # 初始化大模型客户端相关配置
        # 存储LLM相关配置，优先使用传入参数，其次使用LLM_CONFIG，最后使用环境变量
        self.model = LLM_CONFIG.get('model', 'Qwen/Qwen2.5-14B-Instruct')
        self.temperature = LLM_CONFIG.get('temperature', 0.7)
        self.max_tokens = LLM_CONFIG.get('max_tokens', 4096)
        self.top_p = LLM_CONFIG.get('top_p', 0.95)
        
        # 初始化OpenAI客户端
        self.async_client = AsyncOpenAI(
            api_key=api_key or LLM_CONFIG.get('api_key') or os.getenv('OPENAI_API_KEY'),
            base_url=api_base or LLM_CONFIG.get('api_base') or os.getenv('OPENAI_API_BASE')
        )
        
        logger.info(f"大模型Agent初始化完成，使用模型: {self.model}")
    
    async def run(self) -> Dict[str, Any]:
        """
        运行测试任务
        
        解析自然语言任务，生成并执行测试步骤
        
        Returns:
            Dict[str, Any]: 测试执行结果
        """
        # 初始化current_state
        self.current_state = {
            "evaluation_previous_step": "首次执行 - 无前置步骤",
            "memory": f"当前任务: {self.task}\n已完成步骤: 0\n剩余步骤数: {len(self.test_results)}",
            "next_goal": "执行第一个测试步骤"
        }
        
        try:
            # 检查视频录制配置
            if self.config.get('browser', {}).get('record_video', False):
                logger.info("将启用视频录制功能")
                await self.keyword_driver.set_video_recording(True)
            
            # 解析测试任务
            test_steps = await self._parse_task()
            if not test_steps:
                return {"success": False, "error": "无法解析测试任务"}
            
            # 执行测试步骤
            for i, step in enumerate(test_steps):
                if i >= self.max_steps:
                    logger.warning(f"已达到最大步骤数 {self.max_steps}，停止执行")
                    break
                
                self.current_step = i + 1
                result = await self._execute_step(step)
                self.test_results.append(result)
                
                # 更新current_state
                self.current_state = {
                    "evaluation_previous_step": f"{'成功' if result['success'] else '失败'} - {result.get('message', '无详细信息')}",
                    "memory": f"当前任务: {self.task}\n已完成步骤: {i+1}\n剩余步骤数: {len(test_steps) - (i+1)}",
                    "next_goal": f"执行步骤: {test_steps[i+1] if i+1 < len(test_steps) else '完成测试'}"
                }
                
                # 如果步骤执行失败，尝试使用大模型恢复
                if not result.get("success"):
                    logger.warning(f"步骤 {i+1} 执行失败，尝试恢复")
                    recovery_steps = await self._generate_recovery_steps(step, result.get("error"))
                    
                    if recovery_steps:
                        logger.info(f"生成了 {len(recovery_steps)} 个恢复步骤")
                        for j, recovery_step in enumerate(recovery_steps):
                            if i + j + 1 > self.max_steps:
                                break
                                
                            logger.info(f"执行恢复步骤 {j+1}: {recovery_step}")
                            recovery_result = await self._execute_step(recovery_step)
                            self.test_results.append(recovery_result)
                            
                            # 如果恢复成功，继续执行
                            if recovery_result.get("success"):
                                break
            
            # 明确标记测试完成
            self.current_state["evaluation_previous_step"] = "完成所有步骤"
            self.current_state["next_goal"] = "生成测试报告"
            
            # 汇总测试结果
            success = all(result.get("success") for result in self.test_results)
            final_result = {
                "success": success,
                "steps": len(self.test_results),
                "results": self.test_results,
                "task": self.task,
                "timestamp": datetime.now().isoformat(),
                "current_state": self.current_state  # 添加到报告中
            }
            
            return final_result
        
        except Exception as e:
            logger.exception(f"测试执行过程中发生错误: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "task": self.task,
                "steps": self.current_step,
                "results": self.test_results,
                "timestamp": datetime.now().isoformat(),
                "current_state": self.current_state  # 添加到报告中
            }
        finally:
            # 关闭浏览器
            if self.keyword_driver.browser:
                await self.keyword_driver.close_browser_async()
    
    async def _parse_task(self) -> List[str]:
        """
        解析自然语言任务为测试步骤
        
        使用大模型将自然语言任务转换为明确的测试步骤
        
        Returns:
            List[str]: 测试步骤列表
        """
        logger.info(f"大模型Agent开始解析测试任务: {self.task}")
        
        # 使用 AgentMessagePrompt 构建消息
        page_state = {
            "url": None,
            "elements": []
        }
        
        # 获取当前浏览器状态（如果有的话）
        try:
            if self.keyword_driver.page:
                # 使用异步方法获取URL和页面元素
                page_state["url"] = await self.keyword_driver.get_current_url_async()
                page_state["elements"] = await self.keyword_driver.get_page_elements_async()
        except Exception as e:
            logger.warning(f"获取当前页面状态失败: {str(e)}")
            # 继续使用空状态
        
        message_prompt = AgentMessagePrompt(
            page_state=page_state,
            task=self.task
        )
        
        try:
            # 构建系统提示词
            system_prompt_content = self.system_prompt.get_system_message()["content"]
            # 添加一次性生成所有步骤的指导
            system_prompt_content += "\n\n请注意：请一次性生成完成整个测试任务的所有步骤，不要只生成前两个步骤。确保test_steps中包含完成任务所需的全部指令序列。"
            
            # 使用系统提示词和消息提示词
            messages = [
                {"role": "system", "content": system_prompt_content},
                message_prompt.get_user_message()
            ]
            
            # 额外添加一个明确的用户指令
            messages.append({
                "role": "user", 
                "content": f"请为以下任务生成完整的测试步骤序列（不要只生成前两个步骤）：\n{self.task}\n\n任务可能包含：打开浏览器、导航到URL、输入表单字段、点击按钮、验证元素等多个步骤。"
            })
            
            # # 异步调用LLM API
            # response = await self.async_client.chat.completions.create(
            #     model=self.model,
            #     messages=messages,
            #     temperature=self.temperature,
            #     max_tokens=self.max_tokens,
            #     top_p=self.top_p
            # )
            # logger.info('-----------')
            # logger.info(response)
            # logger.info('-----------')
            
            # # 提取步骤
            # response_text = response.choices[0].message.content
            # logger.info(f"大模型Agent的响应: {response_text}")
            # steps = self._extract_steps(response_text)
            

            # 使用流式API获取完整响应
            logger.info("使用流式API获取响应...")
            response_text = await self._get_streamed_response(messages)
            if not response_text:
                logger.error("获取流式响应失败")
                return []
            logger.info(f"大模型Agent的响应: {response_text}")
            # 尝试修复和解析JSON
            steps = self._extract_steps_with_repair(response_text)
            logger.info(f"大模型Agent成功解析得到 {len(steps)} 个测试步骤, 具体步骤如下: {steps}")
            


            if not steps:
                logger.warning(f"无法从大模型Agent的响应中提取步骤: {response_text}")
                return []

            return steps
            
        except Exception as e:
            logger.error(f"大模型Agent解析任务失败: {str(e)}")
            return []
    
    def _extract_steps(self, response_text: str) -> List[str]:
        """
        从响应文本中提取测试步骤
        
        Args:
            response_text: 响应文本
            
        Returns:
            List[str]: 测试步骤列表
        """
        try:
            # 尝试解析 JSON 响应
            response_data = json.loads(response_text)
            
            # 检查响应格式是否正确
            if isinstance(response_data, dict) and 'test_steps' in response_data:
                steps = response_data['test_steps']
                if isinstance(steps, list):
                    # 处理可能的嵌套数组情况
                    if len(steps) > 0 and isinstance(steps[0], list):
                        steps = steps[0]  # 取第一个数组的内容
                        logger.warning("检测到嵌套数组结构，已提取内部步骤")
                    
                    # 将每个步骤转换为字符串格式
                    formatted_steps = []
                    for step in steps:
                        if isinstance(step, dict) and 'keyword' in step and 'args' in step:
                            # 构建步骤字符串
                            keyword = step['keyword']
                            
                            # 特殊处理"等待X秒"格式
                            if keyword.startswith("等待") and keyword.endswith("秒"):
                                try:
                                    seconds = int(''.join(filter(str.isdigit, keyword)))
                                    formatted_steps.append(f"等待 {seconds}")
                                    continue
                                except (ValueError, TypeError):
                                    pass  # 如果解析失败，使用常规处理方法
                            
                            # 对于定位并输入文本命令，只使用前两个参数
                            if keyword == "定位并输入文本" and len(step['args']) > 2:
                                args = ' '.join(str(arg) for arg in step['args'][:2])
                            else:
                                args = ' '.join(str(arg) for arg in step['args'])
                            formatted_steps.append(f"{keyword} {args}".strip())
                    logger.info(f"构建步骤字符串: {formatted_steps}")
                    return formatted_steps
            
            # 如果 JSON 格式不符合预期，尝试使用原有的文本解析方法
            logger.warning("响应不是预期的 JSON 格式，尝试使用文本解析")
            
            # 移除代码块标记
            clean_text = re.sub(r'```.*\n', '', response_text)
            clean_text = re.sub(r'```', '', clean_text)
            
            # 匹配数字编号的行
            step_lines = re.findall(r'^\s*\d+\.\s*(.*?)\s*$', clean_text, re.MULTILINE)
            
            # 如果没有匹配到带编号的行，尝试按行分割
            if not step_lines:
                step_lines = [line.strip() for line in clean_text.split('\n') if line.strip()]
            
            # 处理"等待X秒"格式的步骤
            processed_steps = []
            for step in step_lines:
                # 检查是否为"等待X秒"格式
                wait_match = re.match(r'^等待\s*(\d+)\s*秒$', step)
                if wait_match:
                    processed_steps.append(f"等待 {wait_match.group(1)}")
                else:
                    processed_steps.append(step)
            
            return processed_steps
            
        except json.JSONDecodeError:
            logger.warning("响应不是有效的 JSON 格式，使用文本解析")
            # 使用原有的文本解析方法
            clean_text = re.sub(r'```.*\n', '', response_text)
            clean_text = re.sub(r'```', '', clean_text)
            step_lines = re.findall(r'^\s*\d+\.\s*(.*?)\s*$', clean_text, re.MULTILINE)
            if not step_lines:
                step_lines = [line.strip() for line in clean_text.split('\n') if line.strip()]
                
            # 处理"等待X秒"格式的步骤
            processed_steps = []
            for step in step_lines:
                # 检查是否为"等待X秒"格式
                wait_match = re.match(r'^等待\s*(\d+)\s*秒$', step)
                if wait_match:
                    processed_steps.append(f"等待 {wait_match.group(1)}")
                else:
                    processed_steps.append(step)
            
            return processed_steps
        except Exception as e:
            logger.error(f"解析步骤时发生错误: {str(e)}")
            return []
    
    async def _execute_step(self, step: str) -> Dict[str, Any]:
        """
        执行测试步骤
        
        Args:
            step: 测试步骤
            
        Returns:
            Dict[str, Any]: 执行结果
        """
        step_start_time = time.time()
        screenshot_path = None
        
        try:
            # 解析步骤
            parsed_step = self._parse_step(step)
            
            # 如果无法解析，返回失败
            if not parsed_step:
                logger.error(f"无法解析步骤: {step}")
                return {
                    "step": step,
                    "success": False,
                    "error": f"无法解析步骤: {step}",
                    "duration": time.time() - step_start_time
                }
            
            # 执行关键字命令
            logger.info(f"执行步骤: {step}")
            
            # 使用异步方法执行关键字
            result = await self.keyword_driver.run_keyword_async(
                parsed_step["keyword"], *parsed_step["args"]
            )
            
            # 处理可能的截图路径
            if self.save_screenshots:
                # 异步获取截图
                screenshot_b64 = await self.keyword_driver.screenshot_async(return_base64=True)
                
                if screenshot_b64:
                    # 生成截图文件名和路径
                    screenshots_dir = os.path.join(os.getcwd(), "screenshots")
                    os.makedirs(screenshots_dir, exist_ok=True)
                    
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    screenshot_path = os.path.join(
                        screenshots_dir, 
                        f"step_{self.current_step}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
                    )
                    
                    # 解码和保存截图
                    try:
                        img_data = base64.b64decode(screenshot_b64)
                        with open(screenshot_path, "wb") as f:
                            f.write(img_data)
                        logger.info(f"截图已保存: {screenshot_path}")
                    except Exception as e:
                        logger.error(f"保存截图失败: {str(e)}")
                        screenshot_path = None
            
            # 步骤执行成功
            return {
                "step": step,
                "success": True,
                "result": result,
                "screenshot": screenshot_path,
                "duration": time.time() - step_start_time
            }
            
        except Exception as e:
            logger.error(f"执行步骤失败: {step} - {str(e)}")
            
            # 保存失败截图（如果启用）
            if self.save_screenshots:
                try:
                    # 使用异步方法获取截图
                    screenshot_b64 = await self.keyword_driver.screenshot_async(return_base64=True)
                    
                    if screenshot_b64:
                        # 保存错误截图
                        screenshots_dir = os.path.join(os.getcwd(), "screenshots")
                        os.makedirs(screenshots_dir, exist_ok=True)
                        
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        screenshot_path = os.path.join(
                            screenshots_dir, 
                            f"error_step_{self.current_step}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
                        )
                        
                        # 解码和保存截图
                        img_data = base64.b64decode(screenshot_b64)
                        with open(screenshot_path, "wb") as f:
                            f.write(img_data)
                        logger.info(f"错误截图已保存: {screenshot_path}")
                except Exception as screenshot_err:
                    logger.error(f"保存错误截图失败: {str(screenshot_err)}")
            
            # 步骤执行失败
            return {
                "step": step,
                "success": False,
                "error": str(e),
                "screenshot": screenshot_path,
                "duration": time.time() - step_start_time
            }
    
    def _parse_step(self, step_str: str) -> Dict[str, Any]:
        """
        解析步骤字符串为关键字和参数
        
        Args:
            step_str: 步骤字符串，如"打开浏览器 chrome"
            
        Returns:
            Dict: 包含关键字和参数的字典
        """
        try:
            # 特殊处理"等待X秒"格式
            wait_match = re.match(r'^等待\s*(\d+)\s*秒$', step_str)
            if wait_match:
                logger.info(f"检测到等待格式: '{step_str}' -> 等待 {wait_match.group(1)}")
                return {
                    "keyword": "等待",
                    "args": [int(wait_match.group(1))]
                }
                
            # 提取关键字和参数
            parts = step_str.strip().split(" ", 1)
            keyword = parts[0]
            args = []
            
            if len(parts) > 1 and parts[1].strip():
                # 处理带引号的参数和不带引号的参数
                params_str = parts[1].strip()
                
                # 特殊处理等待关键字
                if keyword == "等待" or keyword == "wait":
                    try:
                        # 尝试将参数转换为整数
                        args = [int(params_str)]
                    except ValueError:
                        # 如果无法转换为整数，则保留原始字符串
                        args = [params_str]
                else:
                    # 其他关键字按照原有逻辑处理
                    args = self._extract_arguments(params_str)
            
            logger.info(f"解析步骤: {step_str} -> 关键字: {keyword}, 参数: {args}")
            return {"keyword": keyword, "args": args}
            
        except Exception as e:
            logger.error(f"解析步骤 '{step_str}' 失败: {str(e)}")
            raise ValueError(f"无法解析步骤: {step_str}") from e
    
    def _extract_arguments(self, params_str: str) -> List[Any]:
        """
        从参数字符串中提取参数列表
        
        Args:
            params_str: 参数字符串
            
        Returns:
            List[Any]: 参数列表
        """
        # 处理带引号的参数
        args = []
        
        # 处理带引号的情况
        in_quotes = False
        quote_type = None
        current_arg = ""
        
        for char in params_str:
            if char in ['"', "'"]:
                # 处理引号
                if not in_quotes:
                    # 开始引号
                    in_quotes = True
                    quote_type = char
                elif char == quote_type:
                    # 结束引号
                    in_quotes = False
                    quote_type = None
                    args.append(current_arg)
                    current_arg = ""
                else:
                    # 在另一种引号内的引号
                    current_arg += char
            elif char.isspace() and not in_quotes:
                # 空格分隔符，但不在引号内
                if current_arg:
                    args.append(current_arg)
                    current_arg = ""
            else:
                # 普通字符
                current_arg += char
        
        # 添加最后一个参数
        if current_arg:
            args.append(current_arg)
        
        # 如果解析失败或者没有参数，则简单按空格分割
        if not args:
            args = [arg.strip() for arg in params_str.split(' ') if arg.strip()]
            
        return args
    
    async def _generate_recovery_steps(self, failed_step: str, error: str) -> List[str]:
        """
        生成恢复步骤
        
        当步骤执行失败时，使用大模型生成恢复步骤
        
        Args:
            failed_step: 失败的步骤
            error: 错误信息
            
        Returns:
            List[str]: 恢复步骤列表
        """
        logger.info(f"大模型Agent为失败步骤生成恢复策略: {failed_step}")
        logger.info(f"错误信息: {error}")
        
        # 收集当前页面信息用于提供上下文
        page_info = await self._get_current_page_info()
        
        # 构建提示
        prompt = f"""
作为测试自动化专家，请帮助我解决以下测试步骤执行过程中遇到的问题，并生成替代步骤。

失败的步骤: {failed_step}
错误信息: {error}

当前页面信息:
URL: {page_info.get('url', '未知')}
标题: {page_info.get('title', '未知')}

页面上的关键元素:
{json.dumps(page_info.get('elements', []), ensure_ascii=False, indent=2)[:500]}

请分析失败原因，并生成1-3个替代步骤来解决这个问题。考虑以下可能的原因:
1. 元素定位失败：可能需要（a）等待元素加载或使用不同的选择器，(b)当前存在弹出窗口（比如iframe），(c)元素被其他元素遮挡，(d)元素在页面底部，需要滚动页面
2. 导航问题：检查URL
3. 状态同步问题：可能需要等待页面加载完成
4. 输入验证问题：可能需要先清除文本再输入

重要：请只返回准确的关键字命令步骤，每行一个步骤，格式如下:
打开浏览器 chromium
导航到 https://example.com
等待页面加载完成
定位并输入文本 用户名输入框 testuser
"""
        
        try:
            # 使用流式响应获取恢复步骤
            response_text = await self._get_streamed_response([{"role": "user", "content": prompt}])
            
            if not response_text:
                logger.error("获取恢复步骤的流式响应失败")
                return ["等待页面加载完成", "刷新页面"]
            
            # 提取恢复步骤
            recovery_steps = []
            
            # 直接按行分割，过滤空行和非步骤内容
            lines = [line.strip() for line in response_text.split('\n') if line.strip()]
            
            # 收集看起来像步骤的行
            for line in lines:
                # 跳过解释性文本和标题
                if (line.startswith('#') or 
                    line.startswith('注意:') or 
                    line.startswith('分析:') or 
                    '分析' in line[:10] or 
                    '原因' in line[:10] or
                    line.startswith('-') or
                    line.startswith('*')):
                    continue
                    
                # 移除数字编号
                step = re.sub(r'^\d+[\.\)]\s*', '', line)
                
                # 如果是有效步骤，添加到列表
                if ' ' in step or step in ['刷新页面', '等待页面加载完成']:
                    recovery_steps.append(step)
            
            logger.info(f"大模型Agent生成了 {len(recovery_steps)} 个恢复步骤: {recovery_steps}")
            return recovery_steps
            
        except Exception as e:
            logger.error(f"大模型Agent生成恢复步骤失败: {str(e)}")
            # 返回一些基本的恢复步骤作为备选
            return ["等待页面加载完成", "刷新页面"]

    async def _get_current_page_info(self) -> Dict[str, Any]:
        """获取当前页面信息"""
        page_info = {}
        try:
            # 获取页面状态
            page_info["url"] = await self.keyword_driver.get_current_url_async() if self.keyword_driver.page else "未知"
            
            # 异步获取页面元素
            if self.keyword_driver.page:
                # 获取页面元素
                elements = await self.keyword_driver.get_page_elements_async()
                page_info["elements"] = elements[:20]  # 限制数量，避免提示词过长
                
                # 尝试获取页面标题
                try:
                    page_info["title"] = await self.keyword_driver.page.title()
                except Exception:
                    page_info["title"] = "未知"
            else:
                page_info = {"url": "未知", "title": "未知", "elements": []}
        except Exception as e:
            logger.warning(f"获取页面信息失败: {str(e)}")
            # 出错时使用基本信息
            page_info = {"url": "未知", "title": "未知", "elements": []}
        
        return page_info

    # 在现有方法下添加新的流式响应方法
    async def _get_streamed_response(self, messages: List[Dict[str, Any]]) -> str:
        """
        使用流式API获取完整响应
        
        Args:
            messages: 消息列表
            
        Returns:
            str: 完整的响应文本
        """
        full_response = ""
        try:
            stream = await self.async_client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=self.temperature,
                max_tokens=self.max_tokens,
                top_p=self.top_p,
                stream=True  # 启用流式响应
            )
            
            logger.info(f"开始接收流式响应...")
            async for chunk in stream:
                # 提取并添加新的内容
                if chunk.choices and chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    full_response += content
            
            logger.info(f"流式响应接收完成，总字符数: {len(full_response)},完整响应内容如下：{full_response}")
            return full_response
        except Exception as e:
            logger.error(f"流式API调用失败: {str(e)}")
            return ""

    def _extract_steps_with_repair(self, response_text: str) -> List[str]:
        """
        增强版步骤提取方法，包含JSON修复功能
        
        Args:
            response_text: 响应文本
            
        Returns:
            List[str]: 提取的步骤列表
        """
        logger.info(f"开始提取并修复步骤，响应长度: {len(response_text)}")
        
        # 首先尝试直接解析完整JSON
        try:
            response_data = json.loads(response_text)
            
            # 检查响应格式是否正确
            if isinstance(response_data, dict) and 'test_steps' in response_data:
                steps = response_data['test_steps']
                if isinstance(steps, list):
                    # 处理可能的嵌套数组情况
                    if len(steps) > 0 and isinstance(steps[0], list):
                        steps = steps[0]  # 取第一个数组的内容
                        logger.warning("检测到嵌套数组结构，已提取内部步骤")
                    
                    # 将每个步骤转换为字符串格式
                    formatted_steps = []
                    for step in steps:
                        if isinstance(step, dict) and 'keyword' in step and 'args' in step:
                            # 构建步骤字符串
                            keyword = step['keyword']
                            
                            # 特殊处理"等待X秒"格式
                            if keyword.startswith("等待") and keyword.endswith("秒"):
                                try:
                                    seconds = int(''.join(filter(str.isdigit, keyword)))
                                    formatted_steps.append(f"等待 {seconds}")
                                    continue
                                except (ValueError, TypeError):
                                    pass
                        
                            # 处理参数
                            if keyword == "定位并输入文本" and len(step['args']) > 2:
                                args = ' '.join(str(arg) for arg in step['args'][:2])
                            else:
                                args = ' '.join(str(arg) for arg in step['args'])
                            formatted_steps.append(f"{keyword} {args}".strip())
                    
                    logger.info(f"成功提取 {len(formatted_steps)} 个测试步骤")
                    return formatted_steps
        
        except json.JSONDecodeError:
            logger.warning("JSON解析失败，尝试修复结构...")
        except Exception as e:
            logger.warning(f"处理JSON时出错: {str(e)}")
        
        # 尝试从文本中提取和修复JSON结构
        # 1. 尝试提取test_steps部分
        test_steps_match = re.search(r'"test_steps"\s*:\s*(\[[\s\S]*?\])', response_text)
        if test_steps_match:
            try:
                # 修复并解析test_steps部分
                steps_json = test_steps_match.group(1)
                # 修复可能的格式问题
                steps_json = re.sub(r',\s*]', ']', steps_json)  # 移除尾部逗号
                steps = json.loads(steps_json)
                
                formatted_steps = []
                for step in steps:
                    if isinstance(step, dict) and 'keyword' in step and 'args' in step:
                        keyword = step['keyword']
                        args = ' '.join(str(arg) for arg in step['args'])
                        formatted_steps.append(f"{keyword} {args}".strip())
                
                if formatted_steps:
                    logger.info(f"通过部分提取成功获取 {len(formatted_steps)} 个测试步骤")
                    return formatted_steps
            except Exception as e:
                logger.warning(f"提取test_steps失败: {str(e)}")
        
        # 2. 尝试按行提取可能的步骤
        try:
            lines = response_text.split('\n')
            potential_steps = []
            
            for line in lines:
                if '"keyword":' in line and '"args":' in line:
                    try:
                        # 尝试构造和解析单个步骤
                        step_json = re.search(r'(\{.*"keyword".*"args".*\})', line)
                        if step_json:
                            step_data = json.loads(step_json.group(1))
                            if 'keyword' in step_data and 'args' in step_data:
                                keyword = step_data['keyword']
                                args = ' '.join(str(arg) for arg in step_data['args'])
                                potential_steps.append(f"{keyword} {args}".strip())
                    except:
                        # 如果无法解析JSON，尝试直接提取关键字和参数
                        keyword_match = re.search(r'"keyword"\s*:\s*"([^"]+)"', line)
                        args_match = re.search(r'"args"\s*:\s*\[(.*?)\]', line)
                        
                        if keyword_match:
                            keyword = keyword_match.group(1)
                            args = ""
                            if args_match:
                                # 简单处理参数提取
                                args_str = args_match.group(1)
                                args = re.sub(r'"', '', args_str)
                            potential_steps.append(f"{keyword} {args}".strip())
            
            if potential_steps:
                logger.info(f"通过行提取成功获取 {len(potential_steps)} 个测试步骤")
                return potential_steps
        except Exception as e:
            logger.warning(f"尝试按行提取步骤失败: {str(e)}")
        
        # 最后尝试使用默认的文本提取方法
        logger.warning("所有JSON修复尝试失败，使用文本提取")
        return self._extract_steps(response_text)