# -*- coding: utf-8 -*-
"""
扩展元素管理器模块

此模块扩展了原有的ElementManager，添加了实时元素定位功能。
可以优先使用本地元素库，在找不到元素时使用实时DOM分析定位元素。
"""

import os
import json
import time
import logging
import re
import asyncio
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime

from playwright.sync_api import Page, Locator, ElementHandle
from playwright.async_api import Locator as AsyncLocator

from config.config import LOCATOR_CONFIG
from core.browser.element_manager import ElementManager
from core.dom.service import DOMService
from core.dom.views import DOMElementNode, DOMState
from core.browser.element_locating_utils import ElementLocatingUtils

# 获取特定的自定义日志记录器，不使用root logger
logger = logging.getLogger(__name__)


class RealtimeElementManager(ElementManager):
    """
    实时元素管理器类
    
    扩展原有的ElementManager，添加实时元素定位功能。
    在找不到元素时，使用DOM分析进行实时定位。
    """
    
    def __init__(self, locators_file: str = None, keyword_driver=None):
        """
        初始化实时元素管理器
        
        参数:
            locators_file: 元素定位库文件路径
            keyword_driver: 关键字驱动实例
        """
        super().__init__(locators_file, keyword_driver)
        
        # 实时元素定位配置
        self.realtime_config = LOCATOR_CONFIG
        self.dom_service = None
        self.dom_state = None
        self.selector_map = {}
        self.page = None  # 初始化页面属性
        
        logger.info("实时元素管理器初始化完成")
    
    def _initialize_dom_service(self):
        """初始化DOM服务"""
        try:
            # 验证buildDomTree.js文件存在
            script_path = os.path.join(os.path.dirname(__file__), "dom", "buildDomTree.js")
            if not os.path.exists(script_path):
                logger.error(f"DOM分析脚本不存在: {script_path}")
                return False
            
            logger.info(f"DOM分析脚本已验证: {script_path}")
            return True
        except Exception as e:
            logger.error(f"初始化DOM服务失败: {str(e)}")
            return False
    
    def update_dom_state(self):
        """更新DOM状态"""
        try:
            # 确保DOM服务已初始化
            if not self.dom_service:
                self._init_dom_service()
            
            if not self.dom_service:
                logger.error("DOM服务初始化失败")
                return
            
            # 使用DOM服务获取可点击元素 - 确保使用同步方法
            try:
                self.dom_state = self.dom_service.get_clickable_elements(
                    highlight_elements=self.realtime_config.get('highlight_elements', True),
                    viewport_expansion=self.realtime_config.get('viewport_expansion', 500)
                )
                
                if self.dom_state and hasattr(self.dom_state, 'selector_map'):
                    self.selector_map = self.dom_state.selector_map
                    logger.info(f"DOM状态已更新，找到 {len(self.selector_map)} 个可交互元素")
                else:
                    logger.warning("DOM状态更新失败：未获取到选择器映射")
            except Exception as e:
                logger.error(f"更新DOM状态失败: {str(e)}")
        except Exception as e:
            logger.error(f"更新DOM状态失败: {str(e)}")
    
    async def _find_element_in_realtime(self, element_key: str) -> Optional[Locator]:
        """异步实时定位元素"""
        if not self.keyword_driver or not self.keyword_driver.page:
            logger.warning("浏览器页面未加载，无法进行实时元素定位")
            return None
        
        # 确保DOM状态已更新 - 使用异步方法更新DOM
        try:
            # 异步更新DOM状态
            await self.update_dom_state_async()
            
            if not self.dom_state:
                logger.warning("DOM状态未更新，无法定位元素")
                return None
            
            current_page = self.keyword_driver.page
            current_domain = self.keyword_driver.current_domain or self._get_current_domain()
            
            # 从ElementLocatingUtils获取定位器类型优先级映射
            locator_priority = ElementLocatingUtils.LOCATOR_TYPE_PRIORITY
            
            # 生成多种Playwright定位器
            locators_to_try = ElementLocatingUtils.generate_playwright_locators(element_key)
            
            # 确保包含所有类型的定位器
            existing_types = set(loc['type'] for loc in locators_to_try)
            
            # 确保所有类型的定位器都存在
            for locator_type, priority in locator_priority.items():
                if locator_type not in existing_types:
                    # 为缺失的定位器类型创建默认定位器
                    if locator_type == 'role':
                        # 根据元素名称推断可能的角色
                        role_value = 'button' if '按钮' in element_key else 'textbox' if '输入框' in element_key else 'generic'
                        locators_to_try.append({
                            'type': 'role',
                            'value': {'role': role_value, 'name': element_key, 'exact': False},
                            'priority': priority
                        })
                    elif locator_type == 'text':
                        locators_to_try.append({
                            'type': 'text',
                            'value': element_key,
                            'priority': priority
                        })
                    elif locator_type == 'label':
                        # 提取可能的标签文本
                        label_text = re.sub(r'按钮|输入框|框|链接', '', element_key).strip()
                        locators_to_try.append({
                            'type': 'label',
                            'value': label_text or element_key,
                            'priority': priority
                        })
                    elif locator_type == 'placeholder':
                        # 为输入框类元素添加placeholder定位器
                        if '输入框' in element_key or '文本框' in element_key:
                            placeholder_text = re.sub(r'输入框|文本框', '', element_key).strip()
                            placeholder_text = f"请输入{placeholder_text}" if placeholder_text else "请输入"
                            locators_to_try.append({
                                'type': 'placeholder',
                                'value': placeholder_text,
                                'priority': priority
                            })
                    elif locator_type == 'alt_text':
                        # 为图片类元素添加alt文本定位器
                        if '图片' in element_key or '图像' in element_key or 'image' in element_key.lower():
                            alt_text = re.sub(r'图片|图像|image', '', element_key, flags=re.IGNORECASE).strip()
                            locators_to_try.append({
                                'type': 'alt_text',
                                'value': alt_text or element_key,
                                'priority': priority
                            })
                    elif locator_type == 'test_id':
                        # 生成可能的测试ID
                        test_id = element_key.lower().replace(' ', '-')
                        locators_to_try.append({
                            'type': 'test_id',
                            'value': test_id,
                            'priority': priority
                        })
                    elif locator_type == 'title':
                        # 为可能有title属性的元素添加定位器
                        locators_to_try.append({
                            'type': 'title',
                            'value': element_key,
                            'priority': priority
                        })
                    elif locator_type == 'id':
                        # 生成可能的ID选择器
                        element_id = element_key.lower().replace(' ', '_')
                        locators_to_try.append({
                            'type': 'id',
                            'value': element_id,
                            'priority': priority
                        })
                    elif locator_type == 'name':
                        # 生成可能的name属性选择器
                        element_name = element_key.lower().replace(' ', '_')
                        locators_to_try.append({
                            'type': 'name',
                            'value': element_name,
                            'priority': priority
                        })
                    elif locator_type == 'css':
                        # 生成基本CSS选择器
                        locators_to_try.append({
                            'type': 'css',
                            'value': f"[data-testid='{element_key.lower()}'], [id='{element_key.lower()}'], [name='{element_key.lower()}']",
                            'priority': priority
                        })
                    elif locator_type == 'xpath':
                        # 生成基本XPath选择器
                        locators_to_try.append({
                            'type': 'xpath',
                            'value': f"//*[contains(text(), '{element_key}') or @id='{element_key.lower()}' or @name='{element_key.lower()}']",
                            'priority': priority
                        })
            
            # 为了减少日志量，只记录一条概述
            logger.info(f"将尝试 {len(locators_to_try)} 个不同的定位策略定位元素: {element_key}")
            
            # 保存所有成功的定位器
            successful_locators = []
            
            # 针对不同操作预期类型进行过滤
            operation_hint = ""
            if "输入框" in element_key or "文本框" in element_key:
                operation_hint = "input"
            elif "按钮" in element_key:
                operation_hint = "button"
            
            # 尝试所有定位器
            for locator_def in locators_to_try:
                try:
                    locator_type = locator_def.get('type')
                    locator_value = locator_def.get('value')
                    
                    # 创建定位器
                    if locator_type == 'css':
                        locator = current_page.locator(locator_value)
                    elif locator_type == 'xpath':
                        locator = current_page.locator(f"xpath={locator_value}")
                    elif locator_type == 'text':
                        locator = current_page.get_by_text(locator_value)
                    elif locator_type == 'role':
                        if isinstance(locator_value, dict):
                            try:
                                role = locator_value.get('role', '')
                                name = locator_value.get('name', '')
                                exact = locator_value.get('exact', False)
                                locator = current_page.get_by_role(role, name=name, exact=exact)
                            except:
                                # 回退到简单角色查找
                                locator = current_page.get_by_role(locator_value)
                        else:
                            locator = current_page.get_by_role(str(locator_value))
                    elif locator_type == 'label':
                        locator = current_page.get_by_label(locator_value)
                    elif locator_type == 'placeholder':
                        locator = current_page.get_by_placeholder(locator_value)
                    elif locator_type == 'alt_text':
                        locator = current_page.get_by_alt_text(locator_value)
                    elif locator_type == 'title':
                        locator = current_page.get_by_title(locator_value)
                    elif locator_type == 'test_id':
                        locator = current_page.get_by_test_id(locator_value)
                    elif locator_type == 'id':
                        locator = current_page.locator(f"#{locator_value}")
                    elif locator_type == 'name':
                        locator = current_page.locator(f"[name='{locator_value}']")
                    elif locator_type == 'has_text':
                        base_selector = locator_value.get('selector', '')
                        text = locator_value.get('text', '')
                        locator = current_page.locator(base_selector).filter(has_text=text)
                    else:
                        # 默认使用CSS选择器
                        locator = current_page.locator(locator_value)
                    
                    # 创建定位器后，验证元素类型是否符合预期
                    if operation_hint == "input":
                        # 验证是否为输入框
                        element_type = await locator.evaluate("el => el.tagName.toLowerCase()")
                        is_input = element_type in ["input", "textarea"] or await locator.get_attribute("contenteditable") == "true"
                        if not is_input:
                            logger.info(f"找到元素但不是输入框，跳过: {locator_type}={locator_value}")
                            continue
                    
                    # 验证定位器
                    # 使用异步方法直接调用
                    is_unique, is_visible, count = await ElementLocatingUtils.is_unique_and_visible(locator)
                    
                    if is_visible and (is_unique or count <= 3):
                        # 找到了可见且基本上唯一的元素
                        logger.info(f"找到匹配的元素: {locator_type}={locator_value}")
                        
                        # 如果找到多个元素，尝试选择最相关的一个
                        if count > 1:
                            locator = await ElementLocatingUtils.select_most_relevant_element(locator, element_key)
                            logger.info(f"从 {count} 个匹配项中选择最相关的元素")
                        
                        # 保存成功的定位器用于更新元素库
                        successful_locators.append({
                            'type': locator_type,
                            'value': locator_value,
                            'priority': locator_def.get('priority', 10)
                        })
                        
                        # 检查配置，直接保存成功的定位器到元素库
                        if self.realtime_config.get('add_dynamic_locators', True):
                            logger.info(f"将成功的定位器保存到元素库: {element_key}")
                            try:
                                await self.add_element_to_library_async(
                                    current_domain, element_key, [successful_locators[-1]]
                                )
                                logger.info(f"已将元素 '{element_key}' 添加到本地元素库")
                            except Exception as save_err:
                                logger.warning(f"添加元素到本地元素库失败: {str(save_err)}")
                        
                        # 如果已经找到一个元素，返回它
                        return locator
                        
                except Exception as e:
                    logger.info(f"尝试定位器失败 {locator_type}={locator_value}: {str(e)}")
            
            # 如果没有找到匹配的元素，尝试基于文本匹配
            if not successful_locators:
                try:
                    text_matches = await asyncio.to_thread(self._match_elements_by_text, element_key)
                    
                    if text_matches:
                        # 找到文本匹配的元素
                        best_match = text_matches[0]  # 取最佳匹配
                        element_node, similarity = best_match
                        
                        logger.info(f"基于文本找到匹配的元素，相似度: {similarity:.2f}")
                        
                        # 提取定位器
                        extracted_locators = await asyncio.to_thread(
                            self._extract_locators_from_dom_element, element_node, element_key
                        )
                        
                        if extracted_locators:
                            # 使用第一个提取的定位器
                            first_locator = extracted_locators[0]
                            locator_type = first_locator.get('type')
                            locator_value = first_locator.get('value')
                            
                            # 创建定位器
                            locator = current_page.locator(locator_value)
                            
                            # 如果定位成功且配置允许，保存到元素库
                            if self.realtime_config.get('add_dynamic_locators', True):
                                try:
                                    await self.add_element_to_library_async(
                                        current_domain, element_key, extracted_locators
                                    )
                                    logger.info(f"已将基于文本匹配的元素 '{element_key}' 添加到本地元素库")
                                except Exception as save_err:
                                    logger.warning(f"添加文本匹配元素到本地元素库失败: {str(save_err)}")
                            
                            return locator
                except Exception as e:
                    logger.error(f"基于文本匹配元素失败: {str(e)}")
            
            # 如果有成功的定位器但没有返回元素（罕见情况）
            if successful_locators and self.realtime_config.get('add_dynamic_locators', True):
                try:
                    await self.add_element_to_library_async(
                        current_domain, element_key, successful_locators
                    )
                    logger.info(f"已将所有成功的定位器添加到本地元素库: {element_key}")
                except Exception as save_err:
                    logger.warning(f"添加所有定位器到本地元素库失败: {str(save_err)}")
            
            return None
            
        except Exception as e:
            logger.error(f"实时定位元素失败: {str(e)}")
            return None
    
    def _match_elements_by_text(self, element_key: str) -> List[Tuple]:
        """
        通过文本内容匹配DOM元素
        
        参数:
            element_key: 元素标识
            
        返回:
            List[Tuple]: 匹配元素及其相似度列表
        """
        if not self.dom_state or not self.dom_state.element_tree:
            logger.warning("DOM树未构建，无法进行文本匹配")
            return []
        
        # 保留原始的element_key用于精确匹配
        original_key = element_key
        
        # 规范化关键字仅用于辅助计算相似度，不替代原始key
        normalized_key = element_key.lower().strip()
        
        # 提取元素关键部分（去除"按钮"、"输入框"等通用后缀）
        core_key = re.sub(r'按钮|输入框|链接|框|选择框', '', normalized_key).strip()
        
        # 匹配结果
        matches = []
        
        # 从selector_map中查找可能的匹配
        for _, element in self.dom_state.selector_map.items():
            # 1. 检查元素文本内容
            element_text = self._get_element_text(element)
            
            # 首先检查是否完全匹配原始element_key
            exact_match = False
            if element_text == original_key:
                text_similarity = 1.0
                exact_match = True
            else:
                # 如果不是完全匹配，则计算相似度
                element_text_lower = element_text.lower()
                text_similarity = self._calculate_text_similarity(element_text_lower, normalized_key)
            
            # 2. 检查元素属性
            attr_similarity = 0
            
            # 检查特定属性
            important_attrs = {
                'value': 3.0,        # 权重最高
                'placeholder': 2.5,  # 输入框占位符权重
                'title': 2.0,        # 提示文本权重
                'alt': 2.0,          # 图片替代文本
                'aria-label': 1.8,   # 辅助功能标签
                'name': 1.5,         # 名称属性
                'id': 1.5,           # ID属性
                'data-testid': 1.3,  # 测试ID
                'role': 1.2          # ARIA角色
            }
            
            for attr, weight in important_attrs.items():
                if attr in element.attributes:
                    attr_value = element.attributes[attr]
                    
                    # 首先检查是否与原始key完全匹配
                    if attr_value == original_key:
                        attr_similarity += 3.0 * weight
                        exact_match = True
                    else:
                        # 否则计算相似度
                        attr_value_lower = attr_value.lower()
                        attr_sim = self._calculate_text_similarity(attr_value_lower, normalized_key)
                        attr_sim_core = self._calculate_text_similarity(attr_value_lower, core_key)
                        attr_similarity += max(attr_sim, attr_sim_core) * weight
            
            # 3. 根据元素类型进行特殊处理
            type_bonus = 0
            
            # 如果找到完全匹配，增加额外加分
            if exact_match:
                type_bonus += 5.0
            
            # 输入框特殊处理 - 根据原始element_key精确判断
            if element.tag_name.lower() == 'input':
                # 搜索框特殊处理 - 确保精确匹配
                if '搜索' in original_key:
                    if ('type' in element.attributes and element.attributes['type'] == 'search') or \
                       ('name' in element.attributes and element.attributes['name'] in ['q', 'query', 'search', 'wd']):
                        type_bonus += 2.5
                    if 'id' in element.attributes and element.attributes['id'] in ['kw', 'search', 'q', 'query']:
                        type_bonus += 3.0
                
                # 按钮特殊处理 - 确保精确匹配
                if '按钮' in original_key or '提交' in original_key:
                    if ('type' in element.attributes and element.attributes['type'] in ['button', 'submit']):
                        type_bonus += 2.0
                    if 'id' in element.attributes and element.attributes['id'] in ['su', 'submit', 'btn']:
                        type_bonus += 3.0
                    if 'value' in element.attributes and original_key in element.attributes['value']:
                        type_bonus += 4.0
            
            # 按钮特殊处理 - 确保精确匹配
            if element.tag_name.lower() == 'button' and ('按钮' in original_key or '提交' in original_key):
                type_bonus += 2.0
            
            # 4. 计算总的相似度分数
            # 文本内容占40%，属性占50%，类型bonus占10%
            total_similarity = (text_similarity * 0.4) + (attr_similarity * 0.5) + (type_bonus * 0.1)
            
            if total_similarity > 0.3:  # 设置一个最低阈值
                matches.append((element, total_similarity))
        
        # 按相似度降序排序
        matches.sort(key=lambda x: x[1], reverse=True)
        
        # 日志记录
        if matches:
            top_matches = matches[:3]  # 只记录前3个最佳匹配
            match_info = [f"#{m[0].highlight_index}({m[1]:.2f})" for m in top_matches]
            logger.info(f"找到 {len(matches)} 个匹配元素 '{original_key}'，最佳匹配: {', '.join(match_info)}")
        else:
            logger.info(f"未找到匹配元素 '{original_key}'")
        
        return matches
        
    def _get_element_text(self, element) -> str:
        """获取元素的文本内容"""
        if not element or not hasattr(element, 'children'):
            return ""
            
        text_parts = []
        
        # 递归收集所有文本节点
        def collect_text(node):
            if hasattr(node, 'text'):  # 文本节点
                text_parts.append(node.text)
            elif hasattr(node, 'children'):  # 元素节点
                for child in node.children:
                    collect_text(child)
        
        collect_text(element)
        return " ".join(text_parts).strip()
    
    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """计算两个文本的相似度"""
        if not text1 or not text2:
            return 0.0
            
        text1 = text1.lower()
        text2 = text2.lower()
        
        # 完全匹配
        if text1 == text2:
            return 1.0
            
        # 包含关系
        if text2 in text1:
            return 0.8
        if text1 in text2:
            return 0.7
            
        # 计算编辑距离相似度
        try:
            import Levenshtein
            # 归一化的编辑距离
            max_len = max(len(text1), len(text2))
            if max_len == 0:
                return 0.0
            edit_distance = Levenshtein.distance(text1, text2)
            similarity = 1.0 - (edit_distance / max_len)
            return max(0.0, similarity)
        except ImportError:
            # 如果没有Levenshtein库，退化到简单的字符级匹配
            common_chars = set(text1) & set(text2)
            all_chars = set(text1) | set(text2)
            if not all_chars:
                return 0.0
            return len(common_chars) / len(all_chars)
            
    def _extract_locators_from_dom_element(self, element, element_key: str) -> List[Dict]:
        """
        从DOM元素提取定位器
        
        参数:
            element: DOM元素节点
            element_key: 元素标识
            
        返回:
            List[Dict]: 提取的定位器列表
        """
        locators = []
        
        # 提取属性并添加相应的定位器
        attributes = getattr(element, 'attributes', {})
        
        # 1. ID定位器 (优先级最高)
        if 'id' in attributes and attributes['id']:
            locators.append({
                'type': 'id',
                'value': attributes['id'],
                'priority': 1
            })
            locators.append({
                'type': 'css',
                'value': f"#{attributes['id']}",
                'priority': 2
            })
        
        # 2. name属性定位器
        if 'name' in attributes and attributes['name']:
            locators.append({
                'type': 'name',
                'value': attributes['name'],
                'priority': 3
            })
            locators.append({
                'type': 'css',
                'value': f"[name='{attributes['name']}']",
                'priority': 4
            })
        
        # 3. 输入框的placeholder定位器
        if 'placeholder' in attributes and attributes['placeholder']:
            locators.append({
                'type': 'placeholder',
                'value': attributes['placeholder'],
                'priority': 5
            })
            locators.append({
                'type': 'css',
                'value': f"[placeholder='{attributes['placeholder']}']",
                'priority': 6
            })
        
        # 4. 标题属性定位器
        if 'title' in attributes and attributes['title']:
            locators.append({
                'type': 'title',
                'value': attributes['title'],
                'priority': 7
            })
            locators.append({
                'type': 'css',
                'value': f"[title='{attributes['title']}']",
                'priority': 8
            })
        
        # 5. 图片alt文本定位器
        if element.tag_name.lower() == 'img' and 'alt' in attributes and attributes['alt']:
            locators.append({
                'type': 'alt_text',
                'value': attributes['alt'],
                'priority': 9
            })
            locators.append({
                'type': 'css',
                'value': f"img[alt='{attributes['alt']}']",
                'priority': 10
            })
        
        # 6. 测试ID定位器
        if 'data-testid' in attributes and attributes['data-testid']:
            locators.append({
                'type': 'test_id',
                'value': attributes['data-testid'],
                'priority': 11
            })
            locators.append({
                'type': 'css',
                'value': f"[data-testid='{attributes['data-testid']}']",
                'priority': 12
            })
        
        # 7. 标签文本定位器
        if element.tag_name.lower() == 'label' or 'for' in attributes:
            label_text = self._get_element_text(element)
            if label_text:
                locators.append({
                    'type': 'label',
                    'value': label_text,
                    'priority': 13
                })
        
        # 8. 类型和值的组合定位器
        if 'type' in attributes and 'value' in attributes:
            locators.append({
                'type': 'css',
                'value': f"input[type='{attributes['type']}'][value='{attributes['value']}']",
                'priority': 14
            })
        
        # 9. 角色定位器
        if 'role' in attributes and attributes['role']:
            role_value = attributes['role']
            element_text = self._get_element_text(element)
            locators.append({
                'type': 'role',
                'value': {'role': role_value, 'name': element_text if element_text else '', 'exact': False},
                'priority': 15
            })
        
        # 10. 从元素生成XPath
        if hasattr(element, 'xpath') and element.xpath:
            locators.append({
                'type': 'xpath',
                'value': element.xpath,
                'priority': 16
            })
        
        # 11. 文本内容定位
        element_text = self._get_element_text(element)
        if element_text:
            locators.append({
                'type': 'text',
                'value': element_text,
                'priority': 17
            })
        
        # 12. 特殊情况处理：百度搜索框和百度一下按钮
        # 这里基于DOM元素的实际属性识别特定元素
        if element.tag_name.lower() == 'input':
            if 'id' in attributes and attributes['id'] == 'kw':
                # 标记为百度搜索框
                locators.append({
                    'type': 'id',
                    'value': 'kw',
                    'priority': 1  # 最高优先级
                })
                locators.append({
                    'type': 'name',
                    'value': 'wd',
                    'priority': 2
                })
            elif 'id' in attributes and attributes['id'] == 'su':
                # 标记为百度一下按钮
                locators.append({
                    'type': 'id',
                    'value': 'su',
                    'priority': 1  # 最高优先级
                })
                if 'value' in attributes and attributes['value'] == '百度一下':
                    locators.append({
                        'type': 'value',
                        'value': '百度一下',
                        'priority': 2
                    })
                
        return locators

    def _init_dom_service(self):
        """
        初始化或更新DOM服务
        """
        try:
            if not self.dom_service:
                # 检查关键字驱动引擎是否有可用的页面
                if self.keyword_driver and self.keyword_driver.page:
                    # 直接创建DOM服务实例，不需要再次加载脚本
                    self.dom_service = DOMService(self.keyword_driver)
                    logger.info("DOM服务已初始化")
                else:
                    logger.warning("无法初始化DOM服务: 页面不存在")
        except Exception as e:
            logger.error(f"初始化DOM服务失败: {str(e)}")

    async def update_dom_state_async(self):
        """异步更新DOM状态"""
        try:
            # 确保DOM服务已初始化
            if not self.dom_service:
                success = await self._init_dom_service_async()
                if not success:
                    logger.error("DOM服务初始化失败")
                    return
            
            # 使用DOM服务获取可点击元素 - 使用异步方法
            try:
                self.dom_state = await self.dom_service.get_clickable_elements_async(
                    highlight_elements=self.realtime_config.get('highlight_elements', True),
                    viewport_expansion=self.realtime_config.get('viewport_expansion', 500)
                )
                
                if self.dom_state and hasattr(self.dom_state, 'selector_map'):
                    self.selector_map = self.dom_state.selector_map
                    logger.info(f"DOM状态已异步更新，找到 {len(self.selector_map)} 个可交互元素")
                else:
                    logger.warning("DOM状态异步更新失败：未获取到选择器映射")
            except Exception as e:
                logger.error(f"异步更新DOM状态失败: {str(e)}")
        except Exception as e:
            logger.error(f"异步更新DOM状态失败: {str(e)}")
    
    async def _init_dom_service_async(self):
        """异步初始化DOM服务"""
        try:
            if not self.dom_service and self.keyword_driver and self.keyword_driver.async_page:
                # 直接创建DOM服务实例，不再重复加载脚本
                self.dom_service = DOMService(self.keyword_driver)
                logger.info("DOM服务已异步初始化")
                return True
            return False
        except Exception as e:
            logger.error(f"异步初始化DOM服务失败: {str(e)}")
            return False
        
    async def find_element_in_realtime_async(self, element_key: str) -> Optional[AsyncLocator]:
        """异步实时定位元素"""
        if not self.keyword_driver or not self.keyword_driver.page:
            logger.warning("浏览器页面未加载，无法进行实时元素定位")
            return None
        
        # 确保DOM状态已更新 - 使用异步方法更新DOM
        try:
            # 异步更新DOM状态
            await self.update_dom_state_async()
            
            if not self.dom_state:
                logger.warning("DOM状态未更新，无法定位元素")
                return None
            
            current_page = self.keyword_driver.page
            current_domain = self.keyword_driver.current_domain or self._get_current_domain()
            
            # 从ElementLocatingUtils获取定位器类型优先级映射
            locator_priority = ElementLocatingUtils.LOCATOR_TYPE_PRIORITY
            
            # 生成多种Playwright定位器
            locators_to_try = ElementLocatingUtils.generate_playwright_locators(element_key)
            
            # 确保包含所有类型的定位器
            existing_types = set(loc['type'] for loc in locators_to_try)
            
            # 确保所有类型的定位器都存在
            for locator_type, priority in locator_priority.items():
                if locator_type not in existing_types:
                    # 为缺失的定位器类型创建默认定位器
                    if locator_type == 'role':
                        # 根据元素名称推断可能的角色
                        role_value = 'button' if '按钮' in element_key else 'textbox' if '输入框' in element_key else 'generic'
                        locators_to_try.append({
                            'type': 'role',
                            'value': {'role': role_value, 'name': element_key, 'exact': False},
                            'priority': priority
                        })
                    elif locator_type == 'text':
                        locators_to_try.append({
                            'type': 'text',
                            'value': element_key,
                            'priority': priority
                        })
                    elif locator_type == 'label':
                        # 提取可能的标签文本
                        label_text = re.sub(r'按钮|输入框|框|链接', '', element_key).strip()
                        locators_to_try.append({
                            'type': 'label',
                            'value': label_text or element_key,
                            'priority': priority
                        })
                    elif locator_type == 'placeholder':
                        # 为输入框类元素添加placeholder定位器
                        if '输入框' in element_key or '文本框' in element_key:
                            placeholder_text = re.sub(r'输入框|文本框', '', element_key).strip()
                            placeholder_text = f"请输入{placeholder_text}" if placeholder_text else "请输入"
                            locators_to_try.append({
                                'type': 'placeholder',
                                'value': placeholder_text,
                                'priority': priority
                            })
                    elif locator_type == 'alt_text':
                        # 为图片类元素添加alt文本定位器
                        if '图片' in element_key or '图像' in element_key or 'image' in element_key.lower():
                            alt_text = re.sub(r'图片|图像|image', '', element_key, flags=re.IGNORECASE).strip()
                            locators_to_try.append({
                                'type': 'alt_text',
                                'value': alt_text or element_key,
                                'priority': priority
                            })
                    elif locator_type == 'test_id':
                        # 生成可能的测试ID
                        test_id = element_key.lower().replace(' ', '-')
                        locators_to_try.append({
                            'type': 'test_id',
                            'value': test_id,
                            'priority': priority
                        })
                    elif locator_type == 'title':
                        # 为可能有title属性的元素添加定位器
                        locators_to_try.append({
                            'type': 'title',
                            'value': element_key,
                            'priority': priority
                        })
                    elif locator_type == 'id':
                        # 生成可能的ID选择器
                        element_id = element_key.lower().replace(' ', '_')
                        locators_to_try.append({
                            'type': 'id',
                            'value': element_id,
                            'priority': priority
                        })
                    elif locator_type == 'name':
                        # 生成可能的name属性选择器
                        element_name = element_key.lower().replace(' ', '_')
                        locators_to_try.append({
                            'type': 'name',
                            'value': element_name,
                            'priority': priority
                        })
                    elif locator_type == 'css':
                        # 生成基本CSS选择器
                        locators_to_try.append({
                            'type': 'css',
                            'value': f"[data-testid='{element_key.lower()}'], [id='{element_key.lower()}'], [name='{element_key.lower()}']",
                            'priority': priority
                        })
                    elif locator_type == 'xpath':
                        # 生成基本XPath选择器
                        locators_to_try.append({
                            'type': 'xpath',
                            'value': f"//*[contains(text(), '{element_key}') or @id='{element_key.lower()}' or @name='{element_key.lower()}']",
                            'priority': priority
                        })
            
            # 为了减少日志量，只记录一条概述
            logger.info(f"将尝试 {len(locators_to_try)} 个不同的定位策略定位元素: {element_key}")
            
            # 保存所有成功的定位器
            successful_locators = []
            
            # 针对不同操作预期类型进行过滤
            operation_hint = ""
            if "输入框" in element_key or "文本框" in element_key:
                operation_hint = "input"
            elif "按钮" in element_key:
                operation_hint = "button"
            
            # 尝试所有定位器
            for locator_def in locators_to_try:
                try:
                    locator_type = locator_def.get('type')
                    locator_value = locator_def.get('value')
                    
                    # 创建定位器
                    if locator_type == 'css':
                        locator = current_page.locator(locator_value)
                    elif locator_type == 'xpath':
                        locator = current_page.locator(f"xpath={locator_value}")
                    elif locator_type == 'text':
                        locator = current_page.get_by_text(locator_value)
                    elif locator_type == 'role':
                        if isinstance(locator_value, dict):
                            try:
                                role = locator_value.get('role', '')
                                name = locator_value.get('name', '')
                                exact = locator_value.get('exact', False)
                                locator = current_page.get_by_role(role, name=name, exact=exact)
                            except:
                                # 回退到简单角色查找
                                locator = current_page.get_by_role(locator_value)
                        else:
                            locator = current_page.get_by_role(str(locator_value))
                    elif locator_type == 'label':
                        locator = current_page.get_by_label(locator_value)
                    elif locator_type == 'placeholder':
                        locator = current_page.get_by_placeholder(locator_value)
                    elif locator_type == 'alt_text':
                        locator = current_page.get_by_alt_text(locator_value)
                    elif locator_type == 'title':
                        locator = current_page.get_by_title(locator_value)
                    elif locator_type == 'test_id':
                        locator = current_page.get_by_test_id(locator_value)
                    elif locator_type == 'id':
                        locator = current_page.locator(f"#{locator_value}")
                    elif locator_type == 'name':
                        locator = current_page.locator(f"[name='{locator_value}']")
                    elif locator_type == 'has_text':
                        base_selector = locator_value.get('selector', '')
                        text = locator_value.get('text', '')
                        locator = current_page.locator(base_selector).filter(has_text=text)
                    else:
                        # 默认使用CSS选择器
                        locator = current_page.locator(locator_value)
                    
                    # 创建定位器后，验证元素类型是否符合预期
                    if operation_hint == "input":
                        # 验证是否为输入框
                        element_type = await locator.evaluate("el => el.tagName.toLowerCase()")
                        is_input = element_type in ["input", "textarea"] or await locator.get_attribute("contenteditable") == "true"
                        if not is_input:
                            logger.info(f"找到元素但不是输入框，跳过: {locator_type}={locator_value}")
                            continue
                    
                    # 验证定位器
                    # 使用异步方法直接调用
                    is_unique, is_visible, count = await ElementLocatingUtils.is_unique_and_visible(locator)
                    
                    if is_visible and (is_unique or count <= 3):
                        # 找到了可见且基本上唯一的元素
                        logger.info(f"找到匹配的元素: {locator_type}={locator_value}")
                        
                        # 如果找到多个元素，尝试选择最相关的一个
                        if count > 1:
                            locator = await ElementLocatingUtils.select_most_relevant_element(locator, element_key)
                            logger.info(f"从 {count} 个匹配项中选择最相关的元素")
                        
                        # 保存成功的定位器用于更新元素库
                        successful_locators.append({
                            'type': locator_type,
                            'value': locator_value,
                            'priority': locator_def.get('priority', 10)
                        })
                        
                        # 检查配置，直接保存成功的定位器到元素库
                        if self.realtime_config.get('add_dynamic_locators', True):
                            try:
                                # 使用统一的异步方法保存元素到库
                                saved = await self.add_element_to_library_async(current_domain, element_key, [successful_locators[-1]])
                                if saved:
                                    logger.info(f"成功将定位器 {locator_type}={locator_value} 添加到元素库: '{element_key}'")
                                else:
                                    logger.warning(f"添加元素到本地元素库失败: '{element_key}'")
                            except Exception as save_err:
                                logger.warning(f"添加元素到本地元素库失败: {str(save_err)}")
                        
                        # 如果已经找到一个元素，返回它
                        return locator
                        
                except Exception as e:
                    logger.info(f"尝试定位器失败 {locator_type}={locator_value}: {str(e)}")
                    
            # 如果没有找到匹配的元素，尝试基于文本匹配
            if not successful_locators:
                try:
                    text_matches = await asyncio.to_thread(self._match_elements_by_text, element_key)
                    
                    if text_matches:
                        # 找到文本匹配的元素
                        best_match = text_matches[0]  # 取最佳匹配
                        element_node, similarity = best_match
                        
                        logger.info(f"基于文本找到匹配的元素，相似度: {similarity:.2f}")
                        
                        # 提取定位器
                        extracted_locators = await asyncio.to_thread(
                            self._extract_locators_from_dom_element, element_node, element_key
                        )
                        
                        if extracted_locators:
                            # 使用第一个提取的定位器
                            first_locator = extracted_locators[0]
                            locator_type = first_locator.get('type')
                            locator_value = first_locator.get('value')
                            
                            # 创建定位器
                            locator = current_page.locator(locator_value)
                            
                            # 如果定位成功且配置允许，保存到元素库
                            if self.realtime_config.get('add_dynamic_locators', True):
                                try:
                                    # 使用统一的异步方法保存元素到库
                                    saved = await self.add_element_to_library_async(current_domain, element_key, extracted_locators)
                                    if saved:
                                        logger.info(f"成功将基于文本匹配的元素 '{element_key}' 添加到元素库")
                                    else:
                                        logger.warning(f"添加文本匹配元素到本地元素库失败: '{element_key}'")
                                except Exception as save_err:
                                    logger.warning(f"添加文本匹配元素到本地元素库失败: {str(save_err)}")
                            
                            return locator
                except Exception as e:
                    logger.error(f"基于文本匹配元素失败: {str(e)}")
            
            # 如果有成功的定位器但没有返回元素（罕见情况）
            if successful_locators and self.realtime_config.get('add_dynamic_locators', True):
                try:
                    # 使用统一的异步方法保存所有成功的定位器
                    saved = await self.add_element_to_library_async(current_domain, element_key, successful_locators)
                    if saved:
                        logger.info(f"已将所有成功的定位器添加到元素库: '{element_key}'")
                    else:
                        logger.warning(f"添加所有定位器到本地元素库失败: '{element_key}'")
                except Exception as save_err:
                    logger.warning(f"添加所有定位器到本地元素库失败: {str(save_err)}")
            
            return None
            
        except Exception as e:
            logger.error(f"实时定位元素失败: {str(e)}")
            return None
            
    async def add_element_to_library_async(self, domain: str, element_key: str, locators: List[Dict]) -> bool:
        """异步将元素添加到元素库"""
        try:
            # 使用异步锁保护并发文件访问
            # 读取现有元素库
            current_lib = {}
            if os.path.exists(self.locators_file):
                async with asyncio.Lock():
                    current_lib = await asyncio.to_thread(self._read_locators_file)
            
            # 添加或更新元素定位器
            if domain not in current_lib:
                current_lib[domain] = {}
                
            if element_key not in current_lib[domain]:
                current_lib[domain][element_key] = {
                    "description": f"自动生成的定位器: {element_key}",
                    "locators": locators,
                    "last_updated": datetime.now().strftime("%Y-%m-%d"),
                    "success_rate": 1.0
                }
            else:
                # 更新现有元素的定位器
                existing_locators = current_lib[domain][element_key].get("locators", [])
                
                # 合并定位器，避免重复
                existing_types = set(loc.get("type") + ":" + str(loc.get("value")) for loc in existing_locators)
                
                for new_loc in locators:
                    loc_key = new_loc.get("type") + ":" + str(new_loc.get("value"))
                    if loc_key not in existing_types:
                        existing_locators.append(new_loc)
                        existing_types.add(loc_key)
                
                # 更新元素记录
                current_lib[domain][element_key]["locators"] = existing_locators
                current_lib[domain][element_key]["last_updated"] = datetime.now().strftime("%Y-%m-%d")
            
            # 写回文件
            async with asyncio.Lock():
                await asyncio.to_thread(self._write_locators_file, current_lib)
                
            logger.info(f"已将元素 '{element_key}' 的 {len(locators)} 个定位器异步添加到元素库")
            return True
            
        except Exception as e:
            logger.error(f"异步添加元素到元素库失败: {str(e)}")
            return False

    def get_available_domains(self):
        """获取元素库中所有可用的域名"""
        try:
            # 确保本地元素库已加载
            if not hasattr(self, 'locators') or not self.locators:
                self._load_locators()
            
            # 返回所有域名
            return list(self.locators.keys())
        except Exception as e:
            logger.error(f"获取可用域名失败: {str(e)}")
            return []

    async def _locate_with_locators(self, element_key: str, locators: Dict) -> Optional[AsyncLocator]:
        """
        使用给定的定位器列表尝试定位元素
        
        Args:
            element_key: 元素标识
            locators: 定位器字典
            
        Returns:
            可选的AsyncLocator元素
        """
        # 确保从keyword_driver获取page
        if self.keyword_driver and self.keyword_driver.page:
            self.page = self.keyword_driver.page
            
        if not self.page:
            logger.error("无法定位元素，页面未初始化")
            return None
            
        # 调用父类方法
        return await super()._locate_with_locators(element_key, locators)