# -*- coding: utf-8 -*-
"""
元素管理器模块

此模块负责管理和维护元素定位库，包括元素的查找、更新和优化。
结合大模型技术，实现元素定位策略的自动维护和优化。
"""

import os
import json
import time
from datetime import datetime
import logging
from typing import Dict, List, Any, Optional, Tuple, Union
import re
from urllib.parse import urlparse
import asyncio
from playwright.async_api import Locator as AsyncLocator
import threading

from config.config import LOCATOR_CONFIG
from core.agent.llm import LLMIntegration
from core.dom.vision import ImageRecognition

from copy import deepcopy

# 获取特定的自定义日志记录器，不使用root logger
logger = logging.getLogger(__name__)

class ElementManager:
    """
    元素管理器类
    
    负责元素定位库的加载、查询、更新和优化，支持多种定位策略，
    并结合大模型技术自动维护元素定位信息。
    """
    
    def __init__(self, locators_file: str = None, keyword_driver=None):
        self.locators_file = locators_file or LOCATOR_CONFIG['locators_file']
        self.keyword_driver = keyword_driver  # 新增关键字驱动实例引用
        self.locators = self._load_locators()
        self.llm = LLMIntegration()  # 修复错误的空元组初始化
        self.image_recognition = ImageRecognition()
        self.similarity_threshold = LOCATOR_CONFIG['screenshot_similarity_threshold']
        self.page = None  # 初始化页面属性
        logger.info(f"元素管理器初始化完成，已加载 {len(self.locators)} 个页面的元素定位信息")
    
    def _load_locators(self) -> Dict:
        """
        从文件加载本地元素库
        """
        try:
            return self._load_base_locators()
        except Exception as e:
            logger.error(f"加载本地元素库失败: {str(e)}")
            return {}

    def _load_base_locators(self) -> Dict:
        """加载基础定位器配置"""
        try:
            with open(self.locators_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载基础定位器配置失败: {str(e)}")
            return {}
            
    def _get_current_domain(self) -> str:
        """获取当前域名，支持处理重定向情况"""
        # 尝试从keyword_driver的page对象获取当前URL
        if self.keyword_driver and self.keyword_driver.page:
            try:
                current_url = self.keyword_driver.page.url
                if current_url:
                    # 从URL解析出域名
                    parsed_url = urlparse(current_url)
                    if parsed_url.netloc:
                        logger.info(f"从URL '{current_url}' 提取域名: {parsed_url.netloc}")
                        
                        # 如果关键字驱动器有记录当前域名，但与实际不一致，记录差异
                        if hasattr(self.keyword_driver, 'current_domain') and self.keyword_driver.current_domain and self.keyword_driver.current_domain != parsed_url.netloc:
                            logger.warning(f"检测到域名不匹配：记录的域名为 {self.keyword_driver.current_domain}，实际域名为 {parsed_url.netloc}，可能发生了重定向")
                            # 更新关键字驱动器记录的当前域名
                            self.keyword_driver.current_domain = parsed_url.netloc
                        
                        return parsed_url.netloc
            except Exception as e:
                logger.warning(f"从URL提取域名失败: {str(e)}")
        
        # 回退到环境变量或默认值
        logger.error("元素管理器获取当前域名失败！")
        return None

    def get_element_locators(self, element_key: str) -> Optional[Dict]:
        """
        智能匹配定位器
        
        基于查询路径智能搜索元素定位器，支持域名和元素双级结构。
        
        Args:
            element_key: 元素标识
            current_domain: 当前域名，默认使用环境变量
            
        Returns:
            Optional[Dict]: 元素定位器配置，如果未找到则返回None
        """
        try:
            current_domain = self._get_current_domain()
            
            logger.info(f"查找元素: domain={current_domain}, element={element_key}")
            
            # 在当前域名下查找元素定位器
            if current_domain and element_key in self.locators.get(current_domain, {}):
                logger.info(f"匹配到域名{current_domain}下的元素: {element_key}")
                return self.locators[current_domain][element_key]
            
            # 记录查找结果
            logger.warning(f"未找到元素定位器: domain={current_domain}, element={element_key}")
            
            return None
        except Exception as e:
            logger.error(f"获取元素定位器失败: {str(e)}")
            return None

    def _select_best_locator(self, locators: Dict) -> Dict:
        """
        选择最优的定位器
        
        根据成功率、稳定性、特异性和用户自定义权重计算各定位器的得分，
        选择得分最高的定位器。
        
        Args:
            locators: 所有可用的定位器配置
            
        Returns:
            Dict: 评分最高的定位器配置
        """
        try:
            if not locators or 'versions' not in locators or not locators['versions']:
                logger.warning("定位器为空或格式不正确，无法选择最优定位器")
                return {}
                
            # 计算各定位器类型的基础权重
            type_weights = {
                'id': 1.0,         # ID选择器 - 最稳定
                'name': 0.9,       # name属性
                'css': 0.8,        # CSS选择器
                'xpath': 0.7,      # XPath - 较不稳定
                'text': 0.6,       # 文本 - 最不稳定
                'role': 0.75,      # ARIA角色
                'label': 0.85,     # 标签文本 - 相对稳定
                'placeholder': 0.8, # 占位符文本
                'alt_text': 0.75,  # 图片alt文本
                'title': 0.7,      # 标题属性
                'test_id': 0.95,   # 测试ID - 非常稳定
                'default': 0.5     # 默认值
            }
            
            best_locator = None
            highest_score = -1
            
            for version_id, locator in locators['versions'].items():
                # 基本属性获取
                success_rate = locator.get('success_rate', 0.5)
                locator_type = locator.get('type', 'default')
                stability_weight = type_weights.get(locator_type, type_weights['default'])
                
                # 优先级转换为权重 (优先级越低，权重越高)
                priority = locator.get('priority', 5)
                priority_weight = (11 - priority) / 10  # 转换为0.1-1.0范围
                
                # 计算定位器特异性得分
                specificity = 0.5  # 默认值
                if locator_type == 'id':
                    specificity = 1.0  # ID通常是唯一的
                elif locator_type == 'test_id':
                    specificity = 0.95  # 测试ID通常也是唯一的
                elif locator_type == 'css' and '#' in locator.get('value', ''):
                    specificity = 0.9  # 含ID的CSS选择器
                elif locator_type == 'xpath' and 'id(' in locator.get('value', ''):
                    specificity = 0.9  # 含ID的XPath
                elif locator_type == 'name':
                    specificity = 0.85  # name属性通常也较为特异
                elif locator_type == 'label':
                    specificity = 0.8  # 标签文本通常特异性较好
                elif locator_type == 'placeholder':
                    specificity = 0.75  # 占位符文本特异性中等
                elif locator_type == 'alt_text':
                    specificity = 0.7  # 图片alt文本特异性中等
                elif locator_type == 'title':
                    specificity = 0.65  # 标题属性特异性中等
                elif locator_type == 'role':
                    # 角色定位器的特异性取决于是否提供了name
                    if isinstance(locator.get('value'), dict) and locator.get('value', {}).get('name'):
                        specificity = 0.75  # 带name的角色定位器特异性更高
                    else:
                        specificity = 0.6  # 仅角色定位器特异性较低
                elif locator_type == 'text':
                    # 文本定位器的特异性取决于文本长度
                    text_value = locator.get('value', '')
                    if isinstance(text_value, str):
                        text_length = len(text_value)
                        if text_length > 20:
                            specificity = 0.7  # 长文本特异性较高
                        elif text_length > 10:
                            specificity = 0.65  # 中等文本特异性中等
                        else:
                            specificity = 0.6  # 短文本特异性较低
                    
                # 获取用户自定义权重，默认为0.5
                custom_weight = locator.get('weight', 0.5)
                
                # 组合各因素计算总分
                # 成功率占70%，稳定性占15%，特异性占10%，用户自定义权重占5%
                total_score = (
                    success_rate * 0.7 +
                    stability_weight * 0.15 +
                    specificity * 0.1 +
                    custom_weight * 0.05
                )
                
                # 应用优先级调整
                total_score *= priority_weight
                
                if total_score > highest_score:
                    highest_score = total_score
                    best_locator = locator
            
            if best_locator:
                logger.info(f"选择最优定位器: 类型={best_locator.get('type')}, 值={best_locator.get('value')[:30]}..., 得分={highest_score:.2f}")
                return best_locator
            else:
                logger.warning("未找到合适的定位器")
                return {}
                
        except Exception as e:
            logger.error(f"选择最优定位器失败: {str(e)}")
            # 回退到简单算法
            return max(locators['versions'].values(), 
                      key=lambda x: x.get('weight', 0.5) * 0.7 + x.get('success_rate', 0.5) * 0.3)
    
    def _save_locators(self) -> bool:
        """
        保存元素定位库到文件
        
        实现实时、增量更新机制：
        1. 读取当前文件内容并与内存合并
        2. 使用文件锁避免并发写入冲突
        3. 确保程序异常退出时也能保留最新修改
        4. 当读取失败时，尝试从备份文件恢复
        
        Returns:
            bool: 保存是否成功
        """
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.locators_file), exist_ok=True)
            
            # 使用文件锁避免并发写入问题
            lock_file = f"{self.locators_file}.lock"
            max_wait_time = 5  # 最大等待锁时间（秒）
            wait_interval = 0.2  # 轮询间隔（秒）
            wait_time = 0
            
            # 尝试获取文件锁
            while os.path.exists(lock_file) and wait_time < max_wait_time:
                time.sleep(wait_interval)
                wait_time += wait_interval
            
            # 如果等待超时，记录警告但仍继续执行
            if os.path.exists(lock_file):
                logger.warning(f"获取文件锁超时，可能存在并发写入风险: {lock_file}")
                # 尝试删除可能的过期锁
                try:
                    if os.path.getmtime(lock_file) < time.time() - 60:  # 如果锁文件超过60秒
                        os.remove(lock_file)
                        logger.info("已删除可能的过期锁文件")
                except:
                    pass
            
            # 创建锁文件
            try:
                with open(lock_file, 'w') as f:
                    f.write(str(os.getpid()))
            except Exception as e:
                logger.warning(f"创建锁文件失败，但将继续保存操作: {str(e)}")
            
            try:
                # 先读取现有的locators文件内容
                existing_locators = {}
                read_success = False
                
                # 尝试读取主文件
                if os.path.exists(self.locators_file):
                    try:
                        with open(self.locators_file, 'r', encoding='utf-8') as f:
                            existing_locators = json.load(f)
                        logger.info(f"读取到现有元素定位库: {len(existing_locators)} 个域名")
                        read_success = True
                    except Exception as e:
                        logger.warning(f"读取现有元素定位库失败: {str(e)}，尝试从备份恢复")
                        
                # 如果主文件读取失败，尝试从备份文件恢复
                if not read_success:
                    backup_file = os.path.join(os.path.dirname(self.locators_file), "locators copy.json")
                    if os.path.exists(backup_file):
                        try:
                            with open(backup_file, 'r', encoding='utf-8') as f:
                                existing_locators = json.load(f)
                            logger.info(f"从备份文件恢复元素定位库成功: {len(existing_locators)} 个域名")
                            read_success = True
                        except Exception as backup_e:
                            logger.error(f"从备份文件恢复元素定位库失败: {str(backup_e)}，将创建新文件")
                
                # 合并现有的和新的locators
                merged_locators = {}
                
                # 首先复制所有现有条目
                for domain, domain_elements in existing_locators.items():
                    merged_locators[domain] = domain_elements.copy()
                
                # 然后合并/更新自内存中的locators
                for domain, domain_elements in self.locators.items():
                    if domain not in merged_locators:
                        # 如果域名不存在，直接添加整个域名及其所有元素
                        merged_locators[domain] = domain_elements.copy()
                    else:
                        # 如果域名已存在，合并元素
                        for element_key, element_data in domain_elements.items():
                            merged_locators[domain][element_key] = element_data.copy()
                
                # 在写入前先创建备份
                if os.path.exists(self.locators_file) and read_success:
                    backup_file = f"{self.locators_file}.bak"
                    try:
                        with open(self.locators_file, 'r', encoding='utf-8') as src:
                            content = src.read()
                        with open(backup_file, 'w', encoding='utf-8') as dst:
                            dst.write(content)
                        logger.info(f"已创建元素定位库备份: {backup_file}")
                    except Exception as be:
                        logger.warning(f"创建备份文件失败: {str(be)}")
                
                # 临时文件策略，确保写入过程中断也不会损坏原文件
                temp_file = f"{self.locators_file}.tmp"
                
                # 写入临时文件
                with open(temp_file, 'w', encoding='utf-8') as f:
                    json.dump(merged_locators, f, ensure_ascii=False, indent=2)
                
                # 确认临时文件写入成功后，替换原文件
                if os.path.exists(temp_file):
                    # 在Windows上可能需要先删除目标文件
                    if os.path.exists(self.locators_file):
                        try:
                            os.remove(self.locators_file)
                        except:
                            pass
                    
                    os.rename(temp_file, self.locators_file)
                
                # 更新内存中的locators为合并后的版本
                self.locators = merged_locators
                
                logger.info(f"元素定位库已实时更新保存到 {self.locators_file}，包含 {len(merged_locators)} 个域名")
                return True
                
            finally:
                # 无论如何都要删除锁文件
                try:
                    if os.path.exists(lock_file):
                        os.remove(lock_file)
                except Exception as e:
                    logger.warning(f"删除锁文件失败: {str(e)}")
                
        except Exception as e:
            logger.error(f"保存元素定位库失败: {str(e)}")
            return False
    
    async def _find_element(self, element_key: str) -> Any:
        """
        查找元素
        
        Args:
            element_key: 元素标识
            
        Returns:
            Any: 定位到的元素，如果未找到则返回None
        """
        # 先尝试在本地元素库中查找
        locators = self.get_element_locators(element_key)
        
        if locators:
            # 有本地定位器，使用本地定位器
            return await self._locate_with_locators(locators, element_key)
        else:
            # 如果是RealtimeElementManager的实例且配置了回退到实时定位
            if hasattr(self, '_find_element_in_realtime') and LOCATOR_CONFIG.get('fallback_to_realtime', True):
                logger.info(f"元素 {element_key} 在本地元素库中不存在，尝试实时定位")
                # 确保方法是异步调用
                return await self._find_element_in_realtime(element_key)
            else:
                logger.error(f"元素 {element_key} 在本地元素库中不存在")
                return None

    async def _locate_with_locators(self, element_key: str, locators: Dict) -> Optional[AsyncLocator]:
        """
        使用给定的定位器列表尝试定位元素
        
        Args:
            element_key: 元素标识
            locators: 定位器字典
            
        Returns:
            可选的AsyncLocator元素
        """
        # 确保从keyword_driver获取page
        if self.keyword_driver and self.keyword_driver.page:
            self.page = self.keyword_driver.page
            
        if not self.page:
            logger.error("无法定位元素，页面未初始化")
            return None
            
        if not locators or not isinstance(locators, dict) or 'locators' not in locators:
            logger.error(f"无效的定位器格式: {locators}")
            return None
            
        # 记录开始查找元素
        logger.info(f"开始使用定位器查找元素: {element_key}")
        logger.debug(f"定位器详细信息: {locators}")
        
        # 尝试使用每个定位器
        found_element = None
        error_messages = []
        
        for locator_info in locators.get('locators', []):
            try:
                locator_type = locator_info.get('type')
                locator_value = locator_info.get('value')
                
                if not locator_type or not locator_value:
                    logger.warning(f"跳过无效的定位器: {locator_info}")
                    continue
                    
                logger.info(f"尝试使用 {locator_type}={locator_value} 定位元素")
                
                # 根据定位器类型使用不同的定位方法
                if locator_type == 'css':
                    element = self.page.locator(f"css={locator_value}")
                elif locator_type == 'xpath':
                    element = self.page.locator(f"xpath={locator_value}")
                elif locator_type == 'text':
                    element = self.page.locator(f"text={locator_value}")
                elif locator_type == 'role':
                    element = self.page.get_by_role(locator_value)
                elif locator_type == 'id':
                    element = self.page.locator(f"#{locator_value}")
                elif locator_type == 'name':
                    element = self.page.get_by_role("*", name=locator_value)
                elif locator_type == 'label':
                    element = self.page.get_by_label(locator_value)
                elif locator_type == 'placeholder':
                    element = self.page.get_by_placeholder(locator_value)
                elif locator_type == 'alt_text':
                    element = self.page.get_by_alt_text(locator_value)
                elif locator_type == 'title':
                    element = self.page.get_by_title(locator_value)
                elif locator_type == 'test_id':
                    element = self.page.get_by_test_id(locator_value)
                else:
                    logger.warning(f"不支持的定位器类型: {locator_type}")
                    continue
                
                # 检查元素是否存在并可见
                if element:
                    count = await element.count()
                    if count > 0:
                        try:
                            visible = await element.is_visible()
                            if visible:
                                logger.info(f"元素 {element_key} 找到并可见: {locator_type}={locator_value}")
                                found_element = element
                                break
                            else:
                                logger.info(f"元素 {element_key} 找到但不可见: {locator_type}={locator_value}")
                        except Exception as e:
                            logger.warning(f"检查元素可见性出错: {str(e)}")
                            error_messages.append(f"定位器 {locator_type}={locator_value} 检查可见性失败: {str(e)}")
                    else:
                        logger.info(f"元素 {element_key} 未找到: {locator_type}={locator_value}")
                else:
                    logger.info(f"使用 {locator_type}={locator_value} 未能创建定位器")
            
            except Exception as e:
                logger.warning(f"使用定位器 {locator_type}={locator_value} 查找元素时出错: {str(e)}")
                error_messages.append(f"定位器 {locator_type}={locator_value} 查找失败: {str(e)}")
        
        # 如果找到元素，直接返回
        if found_element:
            return found_element
            
        # 未找到元素，记录详细信息
        logger.warning(f"未能找到元素: {element_key}")
        if error_messages:
            logger.debug(f"查找过程中的错误: {error_messages}")
        
        return None

    async def find_element(self, element_key: str) -> Any:
        """
        查找元素的公共接口
        
        参数:
            element_key: 元素标识
            
        返回:
            找到的元素
        """
        return await self._find_element(element_key)

    def get_element_selector(self, element_key: str) -> str:
        """
        获取元素的完整定位选择器
        
        Args:
            element_key: 元素标识
        
        Returns:
            str: 格式化后的定位选择器
        """
        element_info = self.get_element_locators(element_key)
        if not element_info:
            raise ValueError(f"元素 {element_key} 不存在")
        
        try:
            sorted_locators = sorted(element_info['locators'], key=lambda x: x['priority'])
            for locator in sorted_locators:
                if locator['type'] == 'css':
                    return locator['value']
                elif locator['type'] == 'xpath':
                    return f"xpath={locator['value']}"
                elif locator['type'] == 'id':
                    return f"#{locator['value']}"
                elif locator['type'] == 'text':
                    return f"text={locator['value']}"
                elif locator['type'] == 'role':
                    if isinstance(locator['value'], dict):
                        role = locator['value'].get('role', '')
                        name = locator['value'].get('name', '')
                        return f"role={role} name={name}"
                    else:
                        return f"role={locator['value']}"
                elif locator['type'] == 'name':
                    return f"[name='{locator['value']}']"
                elif locator['type'] == 'label':
                    return f"label={locator['value']}"
                elif locator['type'] == 'placeholder':
                    return f"placeholder={locator['value']}"
                elif locator['type'] == 'alt_text':
                    return f"alt={locator['value']}"
                elif locator['type'] == 'title':
                    return f"title={locator['value']}"
                elif locator['type'] == 'test_id':
                    return f"data-testid={locator['value']}"
                elif locator['type'] == 'image':
                    return os.path.join(os.path.dirname(self.locators_file), 'templates', locator['value'])
            raise RuntimeError(f"未找到有效定位器: {element_key}")
        except Exception as e:
            error_msg = f"获取选择器失败: {str(e)}"
            logger.error(error_msg)
            self.update_element_failure(element_key, error_msg, 
                                     f"{LOCATOR_CONFIG['screenshot_dir']}/{element_key}_error_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
            raise

    def get_all_locators(self, element_key: str) -> List[Dict]:
        """
        获取元素的所有可用定位器（按优先级排序）
        
        Args:
            element_key: 元素标识
        
        Returns:
            按优先级排序的定位器列表
        """
        locators = []
        current_domain = getattr(self.keyword_driver, 'current_domain', None) or self._get_current_domain()
        logger.info(f"获取定位器 - 当前域名: {current_domain}, 元素: {element_key}")
        
        # 只检查当前域名下的定位器
        if current_domain and current_domain in self.locators:
            # 检查域名+元素（通用元素）
            if element_key in self.locators[current_domain]:
                locators.extend(self.locators[current_domain][element_key].get('locators', []))
                logger.info(f"找到域名级通用元素定位器: {current_domain}.{element_key}")
        
        # 去重并按优先级排序
        seen = set()
        unique_locators = []
        for loc in locators:
            key = (loc['type'], loc['value'])
            if key not in seen:
                seen.add(key)
                unique_locators.append(loc)
        
        result = sorted(unique_locators, key=lambda x: x.get('priority', 999))
        logger.info(f"最终获取到的定位器列表: {result}")
        return result

    def get_locator_versions(self, element_key: str) -> List[Dict]:
        """
        获取元素的定位器历史版本
        
        Args:
            element_key: 元素标识符
        """
        return [loc['history'] for page in self.locators.values() 
                for loc in page.values() if loc['element_key'] == element_key]

    def record_element_state(self, report: Dict) -> None:
        """
        记录元素状态到定位器配置文件
        
        Args:
            report: 包含元素状态信息的报告
        """
        try:
            error_data = {
                'timestamp': datetime.now().isoformat(),
                'element_key': report['element_key'],
                # 'page_key': report.get('page_key'),
                'dom_snapshot': report['dom_snapshot'],
                'screenshot': report['screenshot_path'],
                'locators': report['locator_versions']
            }
            
            # 调用大模型分析定位失败原因
            analysis_prompt = f"分析元素定位失败原因，当前DOM结构：{report['dom_snapshot'][:2000]}..."
            optimization_suggestions = self.llm._call_llm_api([{"role": "user", "content": analysis_prompt}])
            
            error_data['analysis'] = optimization_suggestions
            
            # 将错误数据追加到locators.json
            if os.path.exists(self.locators_file):
                with open(self.locators_file, 'r+', encoding='utf-8') as f:
                    data = json.load(f)
                    data.setdefault('error_history', []).append(error_data)
                    f.seek(0)
                    json.dump(data, f, ensure_ascii=False, indent=2)
            logger.info(f"元素状态已记录：{report['element_key']}")
        except Exception as e:
            logger.error(f"记录元素状态失败：{str(e)}")

    def optimize_locators(self, element_key: str, report: Dict) -> None:
        """
        根据错误报告优化定位器配置
        
        分析元素定位失败的原因，并通过多种策略优化定位器配置：
        1. 分析DOM结构变化，检测元素位置和属性变化
        2. 利用大模型分析定位失败原因，生成更稳定的定位策略
        3. 基于历史成功率动态调整定位器优先级
        4. 尝试生成新的备选定位器
        5. 移除长期失败的定位器
        
        Args:
            element_key: 元素标识符
            report: 包含错误信息的报告，包括DOM快照、截图路径、历史定位器等
        """
        try:
            if not element_key:
                logger.warning(f"缺少元素标识，无法优化定位器")
                return
                
            # 确认元素在本地元素库中存在
            current_domain = getattr(self.keyword_driver, 'current_domain', None) or self._get_current_domain()
            element_info = self.get_element_locators(element_key)
            if not element_info:
                logger.warning(f"元素 {element_key} 在本地元素库中不存在，无法优化")
                return
                
            # 1. 分析DOM快照，检测元素变化
            dom_snapshot = report.get('dom_snapshot', '')
            
            # 2. 使用大模型分析失败原因
            if dom_snapshot and len(dom_snapshot) > 0:
                analysis_prompt = f"""
                分析元素定位失败原因并提供优化建议。
                
                元素: {element_key}
                当前定位器: {element_info.get('locators', [])}
                DOM快照片段: {dom_snapshot[:2000]}...
                
                请分析:
                1. 元素是否存在但定位器不准确
                2. 元素是否变化（属性、位置、结构）
                3. 元素是否在iframe中
                4. 元素是否由JavaScript动态生成
                5. 最稳定的定位方式是什么
                """
                
                optimization_suggestions = self.llm._call_llm_api([{"role": "user", "content": analysis_prompt}])
                logger.info(f"大模型优化建议: {optimization_suggestions}")
                
                # 3. 根据分析结果调整现有定位器
                # TODO: 实现基于大模型建议的定位器调整逻辑
                
            # 4. 基于历史成功率调整定位器优先级
            locators = element_info.get('locators', [])
            if locators:
                # 降低所有失败定位器的优先级
                for locator in locators:
                    locator['priority'] = min(10, locator['priority'] + 1)
                
                # 确保定位器按优先级排序
                element_info['locators'] = sorted(locators, key=lambda x: x.get('priority', 999))
                
                logger.info(f"已调整定位器优先级: {element_key}")
                
            # 5. 移除持续失败的定位器
            failure_history = element_info.get('failure_history', [])
            if len(failure_history) > 5:  # 如果失败次数超过5次
                # 检查是否有持续失败的定位器
                # TODO: 实现移除频繁失败定位器的逻辑
                pass
                
            # 6. 尝试生成新的定位器
            # TODO: 基于DOM分析生成新的备选定位器
            
            # 保存更新后的定位器配置
            self._save_locators()
            logger.info(f"已优化元素定位器: {element_key}")
                
        except Exception as e:
            logger.error(f"优化定位器失败: {str(e)}")
            # 出错不影响主流程

    def update_element_failure(self, element_key: str, dom_snapshot: str, screenshot_path: str) -> None:
        """
        更新元素失败信息
        
        记录元素定位失败的情况，包括错误信息、DOM快照和页面截图，
        用于后续分析和修复定位问题。
        
        Args:
            element_key: 元素标识
            dom_snapshot: DOM快照或错误信息
            screenshot_path: 页面截图路径
        """
        try:
            # 注意：此方法在同步上下文中运行
            
            current_domain = self._get_current_domain()
            # 确认元素是否存在于当前域名下
            element_exists = current_domain in self.locators and element_key in self.locators[current_domain]
            
            # 创建失败记录
            failure_record = {
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "domain": current_domain,
                "element_key": element_key,
                "error": str(dom_snapshot),
                "screenshot": screenshot_path,
                "resolved": False
            }
            
            # 如果元素存在，更新其失败记录
            if element_exists:
                element_info = self.locators[current_domain][element_key]
                
                # 确保failures数组存在
                if 'failures' not in element_info:
                    element_info['failures'] = []
                
                # 添加失败记录
                element_info['failures'].append(failure_record)
                
                # 限制失败记录数量，保留最近的10条
                element_info['failures'] = element_info['failures'][-10:]
                
                # 更新元素的成功率
                total_attempts = element_info.get('total_attempts', 0) + 1
                successful_attempts = element_info.get('successful_attempts', 0)
                element_info['total_attempts'] = total_attempts
                element_info['success_rate'] = successful_attempts / total_attempts if total_attempts > 0 else 0
                
                # 保存更新
                self._save_locators()
                logger.info(f"已更新元素 {element_key} 的失败记录")
            else:
                logger.warning(f"无法更新失败记录: 元素 {element_key} 不存在于当前域名 {current_domain} 下")
            
            # 异步分析失败并提出修复建议
            # 注意：这里可能需要使用线程池避免阻塞
            import threading
            repair_thread = threading.Thread(
                target=self.repair_locator, 
                args=(element_key, dom_snapshot,)
            )
            repair_thread.daemon = True
            repair_thread.start()
            
        except Exception as e:
            logger.error(f"更新元素失败信息时发生错误: {str(e)}")

    async def update_element_failure_async(self, element_key: str, dom_snapshot: str, screenshot_path: str) -> None:
        """
        异步更新元素失败信息
        
        异步版本的update_element_failure，方便在异步上下文中调用。
        
        Args:
            element_key: 元素标识
            dom_snapshot: DOM快照或错误信息
            screenshot_path: 页面截图路径
        """
        try:
            # 在线程池中执行同步方法
            await asyncio.to_thread(
                self.update_element_failure,
                element_key,
                dom_snapshot,
                screenshot_path
            )
        except Exception as e:
            logger.error(f"异步更新元素失败信息时发生错误: {str(e)}")

    def update_element_success(self, page_key: str, element_key: str, locator_type: str, locator_value: str) -> None:
        """更新元素定位成功记录
        
        Args:
            page_key: 页面标识
            element_key: 元素标识
            locator_type: 定位器类型
            locator_value: 定位器值
        """
        element_data = self.locators[page_key][element_key]
        element_data['success_rate'] = min(1.0, element_data.get('success_rate', 0.9) + 0.1)
        element_data['last_success'] = datetime.now().isoformat()
        
        # 更新定位器优先级
        for locator in element_data['locators']:
            if locator['type'] == locator_type and locator['value'] == locator_value:
                locator['priority'] = max(1, locator['priority'] - 1)  # 提高成功定位器的优先级
            else:
                locator['priority'] = min(10, locator['priority'] + 1)  # 降低其他定位器的优先级
        
        logger.info(f"元素定位成功更新: {page_key}.{element_key} 成功率提升至{element_data['success_rate']:.1f}")

    def get_best_locator(self, element_key: str) -> Optional[Tuple[str, str]]:
        """
        获取元素的最佳定位方式
        
        按照以下规则排序：
        1. 成功率降序（成功率高的优先）
        2. 优先级升序（数字小的优先）
        
        Args:
            element_key: 元素标识符
            
        Returns:
            Optional[Tuple[str, str]]: 定位类型和定位值的元组，如果没有找到则返回None
        """
        try:
            # 获取元素定位信息
            element_info = self.get_element_locators(element_key)
            logger.info(f"获取元素定位信息: {element_info}")
            
            if not element_info or not element_info.get('locators'):
                return None
                
            # 按成功率降序和优先级升序排序定位器
            sorted_locators = sorted(element_info['locators'], 
                                   key=lambda x: (-x.get('success_rate', 0), x.get('priority', 999)))
            
            # 返回最佳定位器
            if sorted_locators:
                logger.info(f"排序后的定位器列表: {sorted_locators}")
                best_locator = sorted_locators[0]
                return best_locator['type'], best_locator['value']
                
            return None
        except Exception as e:
            logger.error(f"获取最佳定位器失败: {str(e)}")
            return None

    def wait_for_element_visible(self, element_key: str, page_key: str = None, timeout: int = 30) -> bool:
        try:
            # 等待页面加载完成
            self.page.wait_for_load_state("networkidle")
            logger.info(f"页面加载完成，开始查找元素: {element_key}")
            
            # 获取当前域名
            current_domain = self._get_current_domain()
            logger.info(f"当前域名: {current_domain}")
            
            # 获取元素定位器
            locator = self.get_element_locators(element_key)
            logger.info(f"获取到的元素定位器: {locator}")
            
            if not locator:
                logger.error(f"未找到元素定位器配置: page={page_key}, element={element_key}, domain={current_domain}")
                self.save_screenshot(f"element_not_found_{element_key}")
                return False
            
            # 获取所有可用的定位器
            locators = []
            if isinstance(locator, dict) and 'locators' in locator:
                # 支持所有Playwright定位器类型
                supported_types = ['css', 'xpath', 'text', 'id', 'name', 'label', 
                                  'placeholder', 'alt_text', 'title', 'test_id', 'role']
                for loc in locator['locators']:
                    if loc['type'] in supported_types:
                        locators.append((loc['type'], loc['value']))
            return True
        except Exception as e:
            logger.error(f"等待元素可见失败: {str(e)}")
            return False
            
    async def _find_element_enhanced(self, element_key: str, page_key: str = None) -> Any:
        """
        增强的元素查找方法
        
        使用智能定位器策略查找元素，结合本地定位器库和实时定位能力：
        1. 首先尝试所有本地定位器
        2. 根据上下文和历史记录智能生成备选定位器
        3. 使用多种定位策略并行尝试定位元素
        4. 验证最终定位结果并更新定位器库
        
        Args:
            element_key: 元素标识
            page_key: 页面标识（可选）
            
        Returns:
            成功定位的元素，如果未找到则返回None
        """
        try:
            # 确认浏览器和页面已初始化
            if not self.keyword_driver or not self.keyword_driver.page:
                logger.warning("浏览器未初始化，无法定位元素")
                return None
                
            # 先尝试常规方法定位
            element = await self._find_element(element_key)
            if element:
                return element
                
            # 如果常规方法失败，尝试使用智能定位方法
            logger.info(f"常规定位失败，尝试智能定位元素: {element_key}")
            
            # 获取当前域名
            current_domain = getattr(self.keyword_driver, 'current_domain', None) or self._get_current_domain()
            current_page = self.keyword_driver.page
            
            # 从ElementLocatingUtils中获取所有定位器类型优先级
            locator_priorities = ElementLocatingUtils.LOCATOR_TYPE_PRIORITY
            
            # 生成多种智能定位器尝试查找
            locators_to_try = []
            
            # 1. ID定位器 - 根据元素关键字转换为可能的ID
            possible_id = element_key.lower().replace(' ', '_').replace('-', '_')
            locators_to_try.append({
                'type': 'id',
                'value': possible_id,
                'priority': locator_priorities.get('id', 1)
            })
            
            # 2. 文本定位器 - 直接使用元素关键字
            locators_to_try.append({
                'type': 'text',
                'value': element_key,
                'priority': locator_priorities.get('text', 2)
            })
            
            # 3. 标签定位器 - 处理可能作为标签的元素关键字
            if ' ' not in element_key and len(element_key) <= 30:
                locators_to_try.append({
                    'type': 'label',
                    'value': element_key,
                    'priority': locator_priorities.get('label', 3)
                })
            
            # 4. 占位符定位器 - 对于输入框类型的元素
            if '输入框' in element_key or '文本框' in element_key:
                placeholder_text = element_key.replace('输入框', '').replace('文本框', '').strip()
                if placeholder_text:
                    locators_to_try.append({
                        'type': 'placeholder',
                        'value': placeholder_text,
                        'priority': locator_priorities.get('placeholder', 4)
                    })
                    locators_to_try.append({
                        'type': 'placeholder',
                        'value': f"请输入{placeholder_text}",
                        'priority': locator_priorities.get('placeholder', 4) + 1
                    })
            
            # 5. 图片alt文本定位器 - 对于图片类型的元素
            if '图片' in element_key or '图像' in element_key:
                alt_text = element_key.replace('图片', '').replace('图像', '').strip()
                if alt_text:
                    locators_to_try.append({
                        'type': 'alt_text',
                        'value': alt_text,
                        'priority': locator_priorities.get('alt_text', 5)
                    })
            
            # 6. 测试ID定位器 - 根据元素关键字转换为可能的测试ID
            test_id = element_key.lower().replace(' ', '-')
            locators_to_try.append({
                'type': 'test_id',
                'value': test_id,
                'priority': locator_priorities.get('test_id', 6)
            })
            
            # 7. 标题属性定位器
            locators_to_try.append({
                'type': 'title',
                'value': element_key,
                'priority': locator_priorities.get('title', 7)
            })
            
            # 8. 角色定位器 - 根据元素关键字推断可能的角色
            role_value = 'button'
            if '按钮' in element_key:
                role_value = 'button'
            elif '输入框' in element_key or '文本框' in element_key:
                role_value = 'textbox'
            elif '链接' in element_key:
                role_value = 'link'
            elif '复选框' in element_key:
                role_value = 'checkbox'
            elif '单选' in element_key:
                role_value = 'radio'
            elif '下拉' in element_key:
                role_value = 'combobox'
            
            locators_to_try.append({
                'type': 'role',
                'value': {'role': role_value, 'name': element_key, 'exact': False},
                'priority': locator_priorities.get('role', 8)
            })
            
            # 9. Name属性定位器
            name_value = element_key.lower().replace(' ', '_')
            locators_to_try.append({
                'type': 'name',
                'value': name_value,
                'priority': locator_priorities.get('name', 9)
            })
            
            # 10. CSS选择器 - 组合多种属性选择器
            css_selector = f"[id*='{name_value}'], [name='{name_value}'], [data-testid='{test_id}']"
            locators_to_try.append({
                'type': 'css',
                'value': css_selector,
                'priority': locator_priorities.get('css', 10)
            })
            
            # 11. XPath - 通用的文本包含选择器
            locators_to_try.append({
                'type': 'xpath',
                'value': f"//*[contains(text(), '{element_key}')]",
                'priority': locator_priorities.get('xpath', 11)
            })
            
            # 按优先级排序定位器
            sorted_locators = sorted(locators_to_try, key=lambda x: x.get('priority', 999))
            
            # 优先尝试优先级最高的几个定位器
            for locator_info in sorted_locators[:5]:  # 只尝试前5个最优定位器
                locator_type = locator_info.get('type')
                locator_value = locator_info.get('value')
                
                if not locator_type or not locator_value:
                    continue
                    
                logger.info(f"尝试增强定位器: {locator_type}={locator_value}")
                
                try:
                    # 根据定位器类型创建Playwright定位器
                    if locator_type == 'css':
                        playwright_locator = self.keyword_driver.page.locator(locator_value)
                    elif locator_type == 'xpath':
                        playwright_locator = self.keyword_driver.page.locator(f'xpath={locator_value}')
                    elif locator_type == 'text':
                        # Playwright的text选择器
                        playwright_locator = self.keyword_driver.page.get_by_text(locator_value)
                    elif locator_type == 'role':
                        if isinstance(locator_value, dict):
                            role = locator_value.get('role', '')
                            name = locator_value.get('name', '')
                            exact = locator_value.get('exact', False)
                            playwright_locator = self.keyword_driver.page.get_by_role(role, name=name, exact=exact)
                        else:
                            # 尝试解析序列化的JSON
                            try:
                                role_dict = json.loads(locator_value)
                                role = role_dict.get('role', '')
                                name = role_dict.get('name', '')
                                exact = role_dict.get('exact', False)
                                playwright_locator = self.keyword_driver.page.get_by_role(role, name=name, exact=exact)
                            except:
                                playwright_locator = self.keyword_driver.page.get_by_role(str(locator_value))
                    elif locator_type == 'id':
                        # 处理ID定位器
                        playwright_locator = self.keyword_driver.page.locator(f'#{locator_value}')
                    elif locator_type == 'name':
                        # 处理name属性定位器
                        playwright_locator = self.keyword_driver.page.locator(f'[name="{locator_value}"]')
                    elif locator_type == 'label':
                        # 使用label定位相关元素
                        playwright_locator = self.keyword_driver.page.get_by_label(locator_value)
                    elif locator_type == 'placeholder':
                        # 使用placeholder定位
                        playwright_locator = self.keyword_driver.page.get_by_placeholder(locator_value)
                    elif locator_type == 'alt_text':
                        # 使用alt文本定位
                        playwright_locator = self.keyword_driver.page.get_by_alt_text(locator_value)
                    elif locator_type == 'title':
                        # 使用title属性定位
                        playwright_locator = self.keyword_driver.page.get_by_title(locator_value)
                    elif locator_type == 'test_id':
                        # 使用测试ID定位
                        playwright_locator = self.keyword_driver.page.get_by_test_id(locator_value)
                    else:
                        # 默认使用CSS选择器
                        playwright_locator = self.keyword_driver.page.locator(locator_value)
                    
                    # 验证匹配到的元素
                    count = await playwright_locator.count()
                    if count == 0:
                        logger.info(f"定位器 {locator_type}={locator_value} 未找到元素")
                        continue
                    
                    # 检查元素可见性
                    is_visible = False
                    try:
                        is_visible = await playwright_locator.first.is_visible()
                    except Exception:
                        pass
                    
                    if not is_visible:
                        logger.info(f"定位器 {locator_type}={locator_value} 找到的元素不可见")
                        continue
                    
                    # 成功找到匹配的可见元素
                    logger.info(f"增强定位成功: {locator_type}={locator_value}")
                    
                    # 如果启用了动态定位器添加，将成功的定位器保存到元素库
                    if self.realtime_config.get('add_dynamic_locators', False):
                        await self._add_smart_locator_to_library(element_key, page_key, {
                            'type': locator_type,
                            'value': locator_value if not isinstance(locator_value, dict) else json.dumps(locator_value)
                        })
                    
                    return playwright_locator
                    
                except Exception as e:
                    logger.info(f"使用智能定位器 {locator_type}={locator_value} 时出错: {str(e)}")
                    continue
            
            # 如果智能定位也失败了，可以考虑尝试使用大模型生成定位器
            # TODO: 实现基于大模型的定位器生成
            
            return None
            
        except Exception as e:
            logger.error(f"增强定位元素失败: {str(e)}")
            return None