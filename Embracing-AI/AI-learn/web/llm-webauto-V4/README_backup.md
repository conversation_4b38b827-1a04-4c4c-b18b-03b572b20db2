# 自然语言驱动的Web自动化测试框架

本项目是一个基于自然语言理解的Web自动化测试框架，结合了大模型(LLM)的能力，实现了更加智能的测试用例生成、执行和元素定位。

## 🌟 特性

- 🤖 LLM驱动：利用大模型的强大自然语言理解能力生成测试用例和优化执行
- 🎭 Playwright支持：底层基于Playwright实现跨浏览器的自动化测试
- 🔍 智能元素定位：自动分析页面结构，推荐最优的元素定位策略
- 📑 关键字驱动：支持通过自然语言关键字控制测试流程
- 🖼️ 视觉辅助：支持图像识别辅助元素定位
- 🧠 测试代理：基于LLM的自主测试代理，能够理解自然语言任务并执行测试

## 📚 框架结构

项目整体结构如下：

```
llm-webauto/
├── config/             # 配置文件目录
├── core/               # 核心功能模块
│   ├── agent_prompt.py      # 测试代理提示词管理
│   ├── agent_service.py     # 测试代理服务实现
│   ├── element_manager.py   # 元素管理模块
│   ├── image_recognition.py # 图像识别模块
│   ├── keyword_driver.py    # 关键字驱动模块
│   ├── llm_integration.py   # 大模型集成模块
│   └── test_case_base.py    # 测试用例基类
├── docs/               # 文档目录
├── screenshots/        # 截图目录
├── tests/              # 测试用例目录
│   ├── agent_test.py        # 测试代理示例
│   └── login_test.py        # 登录测试示例
├── videos/             # 视频录制目录
├── run_agent_test.py   # 测试代理执行入口
└── run_login_test.py   # 登录测试执行入口
```

## 🚀 快速开始

### 环境设置

1. 安装依赖：

```bash
pip install -r requirements.txt
playwright install
```

2. 配置大模型API：

编辑 `config/config.py` 文件，设置你的API密钥：

```python
LLM_CONFIG = {
    'api_key': 'your-api-key',
    'api_base': 'https://api.openai.com/v1',  # 可选，默认为OpenAI
    'model': 'gpt-4',
    'temperature': 0.2,
    'max_tokens': 4000
}
```

### 运行测试用例

#### 使用传统测试用例

```bash
python run_login_test.py
```

#### 使用自然语言测试代理

```bash
# 使用默认示例测试
python run_agent_test.py

# 指定测试任务描述
python run_agent_test.py -t "打开Chrome浏览器，访问百度，搜索Playwright"

# 从文件读取测试任务描述
python run_agent_test.py -f your_test_description.txt

# 指定截图保存目录
python run_agent_test.py -s screenshots/custom_dir

# 设置最大步骤数
python run_agent_test.py -m 50
```

## 🧠 测试代理功能

测试代理是本框架的核心创新功能，它能够：

1. 理解并执行用自然语言描述的测试任务
2. 自动规划测试步骤并执行
3. 智能处理异常情况和页面变化
4. 维护测试执行状态和记忆
5. 生成测试报告和截图

测试代理基于大模型，通过封装好的提示词工程，可以：

- 自动分析页面元素和布局
- 选择合适的交互方式和元素定位策略
- 验证操作执行结果
- 完成复杂的测试流程

### 代理工作流程

```
自然语言任务描述 → 代理规划步骤 → 执行单个步骤 → 
获取页面状态 → 分析执行结果 → 规划下一步 → ... → 完成测试
```

## 📝 自定义测试

### 创建关键字

可以在 `core/keyword_driver.py` 中扩展自定义关键字：

```python
def my_custom_keyword(self, param1, param2):
    """
    自定义关键字实现
    """
    # 实现自定义逻辑
    pass
```

### 创建测试用例

创建一个新的测试文件，例如：

```python
from core.test_case_base import TestCase

def create_my_test_case():
    steps = [
        {"keyword": "打开浏览器", "args": ["chromium"]},
        {"keyword": "导航到", "args": ["https://example.com"]},
        # 更多步骤...
    ]
    return TestCase("我的测试", steps)
```

## 📊 测试报告

测试代理执行后会在截图目录下生成 Markdown 格式的测试报告，包含：

- 测试任务描述
- 执行统计信息
- 每个步骤的详细信息和截图
- 执行结果评估

## 🛠️ 开发和贡献

欢迎贡献代码和提出建议！请确保代码符合项目的代码风格和测试要求。

## 📄 许可证

本项目采用 MIT 许可证。

## 📞 联系方式

如有任何问题或建议，请提交 Issue 或联系项目维护者。


## 目前没有元素定位的具体逻辑，后面更新的时候帮我按现有格式梳理出来调用链路
元素定位核心流程：
当无法从本地元素库找到元素时，调用 _find_element_in_realtime 方法
该方法使用 ElementLocatingUtils.generate_playwright_locators 生成多种可能的定位器
尝试这些定位器，找到成功的定位器
对于成功的定位器，调用 add_element_locators 保存到本地元素库


根据trade.1233s2b.com登录页面的DOM解析和定位器生成流程，我来详细讲解整个过程：

## 一、DOM树构建与解析过程

1. **初始化DOM服务**
   - `_initialize_dom_service()`方法启动DOM服务
   - 加载buildDomTree.js脚本用于DOM树分析

2. **DOM树解析**
   - 当调用`update_dom_state()`时，系统通过JavaScript执行`buildDomTree()`函数
   - `buildDomTree()`函数递归遍历页面所有元素，创建一个包含元素属性、样式、位置和交互特性的树结构
   - 每个节点都被分配唯一索引，并且包含标签类型、属性、可见性等关键信息

3. **元素可见性和交互性分析**
   - `isElementVisible()`函数检查元素是否可见
   - `isInteractiveElement()`函数判断元素是否可交互
   - `quickVisibilityCheck()`提供快速的可见性检测

## 二、元素匹配与定位策略

1. **文本匹配**
   - `_match_elements_by_text()`方法通过文本内容进行模糊匹配
   - 使用编辑距离算法计算相似度
   - 分析元素属性如id、name、placeholder等增强匹配精度

2. **定位器提取**
   - `_extract_locators_from_dom_element()`方法分析DOM元素，生成多种定位器策略
   - 优先分析元素的标识性属性：
     - ID属性：最高优先级
     - 文本内容：用于按钮、链接等
     - 标签属性：如name、placeholder等
     - ARIA角色：增强可访问性支持

## 三、利用标签信息生成Playwright定位器

对于登录页面元素（如用户名输入框），系统会：

1. **分析标签特性**
   - 识别`<input>`标签的类型(如type="text")
   - 提取相关属性(如id="mobile"，placeholder="请输入账号")

2. **生成多层次定位器**：
   ```json
   {
     "type": "id",
     "value": "mobile",
     "priority": 1
   },
   {
     "type": "placeholder", 
     "value": "请输入账号",
     "priority": 4
   },
   {
     "type": "css",
     "value": "input#mobile, input#username, input#account", 
     "priority": 10
   }
   ```

3. **ARIA角色定位**
   - 提取元素的role属性(如textbox)和相关名称
   - 生成角色定位器：`{"role": "textbox", "name": "账号输入框", "exact": false}`

## 四、定位器验证和保存过程

1. **验证定位器有效性**
   - 尝试使用定位器定位元素
   - 检查定位器的唯一性和元素可见性
   - 验证是否能正确匹配目标元素

2. **优化定位器集合**
   - 基于成功率、稳定性和特异性排序
   - 移除无效或低质量定位器
   - 确保覆盖多种定位策略以增强鲁棒性

3. **保存到本地元素库**
   - `add_element_locators()`方法将验证通过的定位器添加到locators.json
   - 按照域名和元素标识符组织
   - 记录定位器类型、值、优先级和成功率

## 五、实际应用举例

以登录页面的手机号输入框为例：

1. **DOM解析发现**：`<input id="mobile" placeholder="请输入账号" type="text">`

2. **生成的定位器集**：
   - ID选择器: `#mobile`
   - 占位符定位器: `[placeholder="请输入账号"]`
   - CSS选择器: `input[type="text"]:first-child`
   - 角色定位器: `{role: 'textbox', name: '账号'}`

3. **定位器验证**：
   - 尝试`page.locator('#mobile')`定位元素
   - 确认唯一性和可见性
   - 记录定位成功率

系统充分利用了元素标签信息来生成定位器，不仅考虑基本属性，还分析了元素的交互特性、上下文位置和ARIA可访问性特征，形成了一套全面而稳健的元素定位策略。
