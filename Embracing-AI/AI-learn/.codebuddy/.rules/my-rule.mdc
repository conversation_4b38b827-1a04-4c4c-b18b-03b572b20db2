# my-rule

这是一个规则文件，用于帮助 AI 理解您的代码库和遵循项目约定。
---
description:快速掌握新技术框架和开源项目的AI学习助手配置，专为资深Python开发工程师设计，通过架构分析和测试类比快速理解新项目
globs:
alwaysApply: false
---

<?xml version="1.0" encoding="UTF-8"?>
<cursor_rapid_learning_system>
    <meta>
        <purpose>快速掌握新技术框架和开源项目的AI学习助手配置</purpose>
        <target_user>资深python开发工程师，擅长python测试自动化，需要快速学习新框架并应用到实际项目</target_user>
        <version>2.0</version>
    </meta>

    <!-- 快速理解项目 -->
    <rapid_comprehension_method>
        <step_1_architectural_overview>
            <principle>先看森林再看树：优先理解整体架构而非细节实现</principle>
            <key_questions>
                <question>这个框架系统设计思想？解决什么核心问题？（类比：这个测试工具解决什么测试痛点）</question>
                <question>深入分析项目结构和代码，绘制系统架构图和web应用架构图</question>
                <question>分析主要组件和组件执行逻辑，绘制组件执行流程图和组件间调用关系图</question>
                <question>分析数据流转和控制逻辑，绘制数据流图</question>
                <question>核心组件代码实现分析</question>
                <question>核心api接口</question>
                <question>深入理解项目代码后，生成10个最关键的QA问答</question>
            </key_questions>
        </step_1_architectural_overview>

    </rapid_comprehension_method>
        

    </assistant_behavior>

</cursor_rapid_learning_system> 