# AutoGen 多智能体框架学习项目

## 🎯 项目简介

这是一个基于微软 AutoGen 框架的多智能体协作系统学习项目。通过丰富的示例代码和场景演示，帮助开发者快速掌握多智能体协作的核心概念和实际应用。

### 📊 项目特色

- **渐进式学习**：从基础双智能体到复杂多智能体系统
- **实际场景导向**：专门针对测试自动化等实际应用场景
- **完整的代码示例**：可直接运行的演示代码
- **详细的学习指导**：系统性的学习计划和最佳实践

## 🏗️ 项目结构

```
autogen-demo/
├── README.md                          # 项目说明文档
├── learning_plan.md                   # 详细学习计划
├── myteam.py                          # 模型客户端配置
├── test.py                           # 基础双智能体示例
├── enhanced_demo.py                   # 增强版多智能体演示
├── demo2.py                          # 工具调用和记忆管理示例
├── test_automation_scenarios.py       # 测试自动化专门场景
└── requirements.txt                   # 项目依赖
```

## 🚀 快速开始

### 1. 环境安装

```bash
# 安装 AutoGen 框架
pip install autogen-agentchat[openai]

# 或者使用项目依赖文件
pip install -r requirements.txt
```

### 2. 模型配置

在 `myteam.py` 中配置你的 AI 模型 API：

```python
# 配置你的 API Key 和模型服务
model_client1 = OpenAIChatCompletionClient(
    model="your-model-name",
    base_url="your-api-endpoint",
    api_key="your-api-key",
    # ... 其他配置
)
```

### 3. 运行基础示例

```bash
# 运行基础双智能体对话
python test.py

# 运行增强版多智能体演示
python enhanced_demo.py

# 运行测试自动化场景
python test_automation_scenarios.py
```

## 📚 示例说明

### 🌟 基础示例 (`test.py`)

**功能**：最简单的双智能体协作示例
- **primary**：创作智能体，负责生成内容
- **critic**：评审智能体，提供反馈和批准

```python
# 智能体团队配置
team = RoundRobinGroupChat(
    [primary_agent, critic_agent],
    termination_condition=TextMentionTermination("APPROVE")
)

# 执行任务
await Console(team.run_stream(task="写一首关于秋天的短诗"))
```

### 🚀 增强版演示 (`enhanced_demo.py`)

**功能**：展示多种协作模式和高级特性
- **RoundRobinGroupChat**：轮询式协作
- **SelectorGroupChat**：智能选择式协作
- **多种终止条件**：灵活的流程控制
- **错误处理机制**：系统容错能力

### 🔧 工具调用示例 (`demo2.py`)

**功能**：演示智能体工具调用和记忆管理
- **工具集成**：天气查询等外部工具调用
- **记忆管理**：智能体状态和上下文保持
- **多轮对话**：连续任务执行

```python
# 智能体配置工具
primary_agent = AssistantAgent(
    "primary",
    model_client=model_client,
    tools=[get_weather],        # 绑定工具函数
    memory=[usemad],           # 配置记忆管理
)
```

### 🧪 测试自动化场景 (`test_automation_scenarios.py`)

**功能**：专门针对测试自动化领域的应用示例

#### 场景1：智能测试用例生成
- **需求分析师**：分析功能需求，识别测试点
- **测试用例设计师**：设计完整的测试用例
- **测试审查员**：审查测试用例质量

#### 场景2：自动化测试代码生成
- **UI自动化专家**：生成UI自动化测试代码
- **API自动化专家**：生成API测试代码
- **测试架构师**：设计测试框架架构

#### 场景3：测试结果智能分析
- **数据分析师**：分析测试执行结果
- **问题识别专家**：识别和分类问题
- **改进建议生成器**：提供优化建议

## 🎓 学习路径

### 第1周：基础掌握
- [ ] 理解 AutoGen 核心概念
- [ ] 掌握智能体角色定义
- [ ] 学会配置团队协作模式
- [ ] 熟悉终止条件设置

### 第2周：实际应用
- [ ] 应用到测试自动化场景
- [ ] 设计复杂的多智能体工作流
- [ ] 实现工具调用和外部集成
- [ ] 优化系统性能

### 第3周：高级特性
- [ ] 掌握记忆管理和状态持久化
- [ ] 实现并发执行和异步处理
- [ ] 建立监控和调试机制
- [ ] 总结最佳实践

详细学习计划请参考 [learning_plan.md](learning_plan.md)

## 🔧 核心概念解析

### 智能体 (AssistantAgent)
```python
# 智能体就像测试用例，有明确的职责和行为
agent = AssistantAgent(
    name="agent_name",           # 智能体标识
    model_client=model_client,   # AI模型接口
    system_message="角色定义",    # 行为指导
    tools=[tool_function],       # 可用工具
)
```

### 团队协作 (GroupChat)
```python
# 团队协作就像测试套件，组织多个智能体有序执行
team = RoundRobinGroupChat(
    participants=[agent1, agent2],      # 参与智能体
    termination_condition=condition,     # 结束条件
    max_turns=10,                       # 最大轮次
)
```

### 终止条件 (Termination Conditions)
- **TextMentionTermination**：检测特定文本触发结束
- **MaxMessageTermination**：达到最大消息数时结束
- **ExternalTermination**：外部控制结束时机

## 🛠️ 配置说明

### 模型支持
- ✅ OpenAI GPT 系列
- ✅ 智谱 GLM 系列
- ✅ 硅基流动 Qwen 系列
- ✅ 其他兼容 OpenAI API 的模型

### 环境要求
- Python 3.8+
- autogen-agentchat
- asyncio 支持

## 🎯 实际应用案例

### 测试自动化领域
- **测试用例智能生成**：从需求到测试用例的自动化生成
- **测试代码审查**：多专家智能体协作审查测试代码
- **测试结果分析**：智能化测试报告分析和改进建议

### 其他应用领域
- **代码review**：多角色代码审查流程
- **文档生成**：协作式技术文档编写
- **需求分析**：多视角需求分析和验证

## 🐛 故障排除

### 常见问题

1. **API 连接失败**
   ```bash
   # 检查网络连接和 API Key 配置
   # 确认模型服务可用性
   ```

2. **智能体无响应**
   ```python
   # 检查 system_message 是否清晰
   # 确认终止条件设置合理
   # 添加 max_turns 防止死循环
   ```

3. **工具调用失败**
   ```python
   # 确认工具函数签名正确
   # 检查函数返回值格式
   # 验证工具绑定配置
   ```

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request！

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🔗 相关资源

- [AutoGen 官方文档](https://microsoft.github.io/autogen/)
- [AutoGen GitHub 仓库](https://github.com/microsoft/autogen)
- [测试自动化最佳实践](https://testautomationu.applitools.com/)

## 📞 联系方式

如有问题或建议，欢迎通过 Issue 联系我们！

---

�� **开始你的多智能体协作之旅吧！** 