import asyncio
from autogen_agentchat.agents import Assistant<PERSON><PERSON>
from autogen_agentchat.base import TaskResult
from autogen_agentchat.conditions import ExternalTermination, TextMentionTermination
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_agentchat.ui import Console
from myteam import model_client1,model_client2
async def main():
    model_clientA = model_client1
    model_clientB=model_client2
    primary_agent = AssistantAgent(
        "primary",
        model_client=model_clientA,
        system_message="你是一个有用的 AI 助手.",
    )
    critic_agent = AssistantAgent(
        "critic",
        model_client=model_clientB,
        system_message="提供建设性的反馈。当您的反馈得到处理时，请回复“APPROVE”。",
    )
    text_termination = TextMentionTermination("APPROVE")
    team = RoundRobinGroupChat(
        [primary_agent, critic_agent],
        termination_condition=text_termination, 
    )
    await <PERSON>sole(team.run_stream(task="写一首关于秋天的短诗"))
if __name__ == "__main__":
    asyncio.run(main())