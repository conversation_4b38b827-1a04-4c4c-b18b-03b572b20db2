import asyncio
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.base import TaskResult
from autogen_agentchat.conditions import ExternalTermination, TextMentionTermination, MaxMessageTermination
from autogen_agentchat.teams import RoundRobinGroupChat, SelectorGroupChat
from autogen_agentchat.ui import Console
from myteam import model_client1, model_client2

async def basic_two_agent_demo():
    """基础双智能体演示 - 类比：简单的测试用例对"""
    print("=== 基础双智能体演示 ===")
    
    # 主要执行者 - 类比：主测试用例
    primary_agent = AssistantAgent(
        "primary",
        model_client=model_client1,
        system_message="你是文坛大师，负责创作内容。",
    )
    
    # 评论者 - 类比：验证测试用例
    critic_agent = AssistantAgent(
        "critic",
        model_client=model_client2,
        system_message="你是著名作家刘震云，以你的语言风格和文学智慧对作品进行批判性及建设性的反馈。当你认为作品满意时，请回复'APPROVE'。",
    )
    
    # 终止条件 - 类比：测试断言
    text_termination = TextMentionTermination("APPROVE")
    
    # 团队配置 - 类比：测试套件
    team = RoundRobinGroupChat(
        [primary_agent, critic_agent],
        termination_condition=text_termination,
        max_turns=10  # 防止无限循环
    )
    
    # 执行任务
    await Console(team.run_stream(task="写一句当下激励中年男人的处世格言"))

async def advanced_multi_agent_demo():
    """高级多智能体演示 - 类比：复杂测试场景"""
    print("\n=== 高级多智能体演示 ===")
    
    # 需求分析师 - 类比：测试需求分析
    analyst_agent = AssistantAgent(
        "analyst",
        model_client=model_client1,
        system_message="你是一个需求分析师，负责分析和澄清需求。",
    )
    
    # 开发者 - 类比：功能实现
    developer_agent = AssistantAgent(
        "developer", 
        model_client=model_client2,
        system_message="你是一个Python开发者，负责编写代码解决方案。",
    )
    
    # 测试工程师 - 类比：质量保证
    tester_agent = AssistantAgent(
        "tester",
        model_client=model_client1,
        system_message="你是一个测试工程师，负责审查代码质量和测试覆盖。当代码质量满意时回复'QUALITY_APPROVED'。",
    )
    
    # 使用选择器模式 - 类比：智能测试调度
    team = SelectorGroupChat(
        [analyst_agent, developer_agent, tester_agent],
        model_client=model_client1,
        termination_condition=TextMentionTermination("QUALITY_APPROVED"),
        max_turns=15
    )
    
    task = "开发一个简单的用户登录验证函数，要求包含输入验证和错误处理"
    await Console(team.run_stream(task=task))

async def testing_specific_scenario():
    """测试自动化专门场景演示"""
    print("\n=== 测试自动化场景演示 ===")
    
    # 测试用例设计师
    test_designer = AssistantAgent(
        "test_designer",
        model_client=model_client1,
        system_message="你是一个测试用例设计师，专门设计全面的测试用例。",
    )
    
    # 自动化测试工程师
    automation_engineer = AssistantAgent(
        "automation_engineer",
        model_client=model_client2,
        system_message="你是一个自动化测试工程师，负责将测试用例转换为自动化脚本。",
    )
    
    # 测试报告分析师
    report_analyst = AssistantAgent(
        "report_analyst",
        model_client=model_client1,
        system_message="你是一个测试报告分析师，负责分析测试结果并提供改进建议。当分析完成时回复'ANALYSIS_COMPLETE'。",
    )
    
    team = RoundRobinGroupChat(
        [test_designer, automation_engineer, report_analyst],
        termination_condition=TextMentionTermination("ANALYSIS_COMPLETE"),
        max_turns=12
    )
    
    task = "为一个电商网站的用户注册功能设计完整的测试方案，包括功能测试、边界测试和异常测试"
    await Console(team.run_stream(task=task))

async def error_handling_demo():
    """错误处理和容错演示"""
    print("\n=== 错误处理演示 ===")
    
    try:
        # 使用消息限制防止无限循环
        agent1 = AssistantAgent(
            "agent1",
            model_client=model_client1,
            system_message="你是一个助手，但你总是很困惑并且会问很多问题。",
        )
        
        agent2 = AssistantAgent(
            "agent2", 
            model_client=model_client2,
            system_message="你是一个助手，但你也很困惑。",
        )
        
        # 使用最大消息数限制
        team = RoundRobinGroupChat(
            [agent1, agent2],
            termination_condition=MaxMessageTermination(5),  # 最多5轮对话
        )
        
        await Console(team.run_stream(task="讨论一个简单的数学问题"))
        
    except Exception as e:
        print(f"捕获到错误: {e}")
        print("这演示了如何处理智能体协作中的异常情况")

async def main():
    """主函数 - 演示所有场景"""
    print("🤖 AutoGen 多智能体框架演示")
    print("="*50)
    
    # 按顺序执行所有演示
    await basic_two_agent_demo()
    # await advanced_multi_agent_demo()
    # await testing_specific_scenario()
    # await error_handling_demo()
    
    print("\n✅ 所有演示完成！")
    print("💡 学习要点：")
    print("1. 智能体角色定义就像测试用例的职责分离")
    print("2. 团队配置类似测试套件的组织方式")
    print("3. 终止条件相当于测试的断言机制")
    print("4. 错误处理确保系统的鲁棒性")

if __name__ == "__main__":
    asyncio.run(main()) 