from autogen_ext.models.openai import OpenAIChatCompletionClient
model_client1 = OpenAIChatCompletionClient(
    model="glm-4-air",
    base_url="https://open.bigmodel.cn/api/paas/v4",
    api_key="********",
    model_info={
        "vision": False,
        "function_calling": True,
        "json_output": True,
        "family": "unknown",
    },
)
model_client2 = OpenAIChatCompletionClient(
    model="Qwen/Qwen3-8B",
    base_url="https://api.siliconflow.cn/v1",
    api_key="********",
    model_info={
        "vision": False,
        "function_calling": True,
        "json_output": True,
        "family": "unknown",
    },
)