import asyncio
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.ui import Console
from autogen_core.memory import *
from autogen_ext.models.openai import OpenAIChatCompletionClient
model_client1 = OpenAIChatCompletionClient(    
    model="glm-4-air",
    base_url="https://open.bigmodel.cn/api/paas/v4",
    api_key="********",
    model_info={
        "vision": False,
        "function_calling": True,
        "json_output": True,
        "family": "unknown",
    },
)
usemad=ListMemory()
async def user_memory_add():#记忆存储

    await usemad.add(MemoryContent(content="The weather should be in metric units", mime_type=MemoryMimeType.TEXT))
    await usemad.add(MemoryContent(content="Meal recipe must be vegan", mime_type=MemoryMimeType.TEXT))
#------------------------------------------------------------#
# 定义一个用来获取天气的工具tool
async def get_weather(city: str, units: str = "imperial") -> str:
    if units == "imperial":
        return f"The weather in {city} is 73 °F and Sunny."
    elif units == "metric":
        return f"The weather in {city} is 23 °C and Sunny."
    else:
        return f"Sorry, I don't know the weather in {city}."
async def main():
    model_client = model_client1
    primary_agent = AssistantAgent(
        "primary",
        model_client=model_client,
        system_message="你是一个有用的 AI 助手.",
        tools=[get_weather],
        memory=[usemad],
    )
     # 任务一：使用工具 get_weather 查询纽约天气
    await user_memory_add()
    stream = primary_agent.run_stream(task="What is the weather in fuzhou?")
    await Console(stream)
    message = await primary_agent._model_context.get_messages()
    for item in message:
        print(item)
    print("-" * 50)
    # 任务二：向LLM进行轮询，要求写一个肉汤食谱
    stream = primary_agent.run_stream(task="Write brief meal recipe with broth")
    await Console(stream)
if __name__ == "__main__":
    asyncio.run(main())