"""
独立的AutoGen客户端
职责：作为客户端，连接到已经运行的mcp_server，发起任务并与之实时通信。

类比：测试框架中运行测试用例的执行脚本。
"""

import asyncio
import json
import logging
from typing import Dict, Any, List

import httpx
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.conditions import TextMentionTermination
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_agentchat.ui import Console

import os
import dotenv
dotenv.load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(levelname)s - %(message)s',  # 简化格式，去掉时间戳和模块名
    handlers=[
        logging.StreamHandler()
    ]
)

# 设置不同模块的日志级别，过滤掉内部详细事件
logging.getLogger("autogen_core.events").setLevel(logging.ERROR)     # 完全过滤内部事件日志
logging.getLogger("autogen_core").setLevel(logging.ERROR)            # 完全过滤核心框架日志
logging.getLogger("autogen_agentchat.events").setLevel(logging.ERROR)  # 过滤聊天事件日志
logging.getLogger("httpx").setLevel(logging.ERROR)                   # 完全过滤HTTP请求日志

logger = logging.getLogger(__name__)

# ===== MCP Client 部分 =====
class MCPClient:
    """
    MCP 客户端，用于与MCP Server通信
    类比：测试工具客户端，负责调用远程测试资源
    """
    
    def __init__(self, server_url: str = "http://127.0.0.1:8000"):
        self.server_url = server_url
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def search_knowledge(self, query: str) -> List[Dict[str, Any]]:
        """
        通过SSE方式搜索知识
        类比：在测试过程中查询测试数据或配置信息
        """
        try:
            logger.info(f"MCPClient: 正在向 {self.server_url}/search 发送查询: {query}")
            results = []
            
            async with self.client.stream("GET", f"{self.server_url}/search", params={"query": query}) as response:
                response.raise_for_status()  # 如果响应状态码不是2xx，则抛出异常
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data = json.loads(line[6:])
                        logger.info(f"MCPClient: 收到SSE事件: {data}")
                        if data["type"] == "result" and "data" in data:
                            results.append(data["data"])
                        elif data["type"] == "end":
                            break
            
            logger.info(f"MCPClient: 查询 '{query}' 完成，共收到 {len(results)} 条结果。")
            return results
            
        except httpx.HTTPStatusError as e:
            logger.error(f"搜索请求失败，状态码: {e.response.status_code} - {e.response.text}")
            return []
        except httpx.RequestError as e:
            logger.error(f"连接MCP Server失败: {e}. 请确保 mcp_server.py 正在运行。")
            return []
        except Exception as e:
            logger.error(f"处理搜索时发生未知错误: {e}")
            return []
    
    async def close(self):
        """关闭客户端"""
        await self.client.aclose()


# ===== AutoGen配置 =====
def create_model_client():
    """创建模型客户端"""
    try:
        from autogen_ext.models.openai import OpenAIChatCompletionClient
        from autogen_core.models import ModelFamily
        
        model_client = OpenAIChatCompletionClient(
            model=os.getenv("MODEL"),
            base_url=os.getenv("BASE_URL"),
            api_key=os.getenv("API_KEY"),
            model_info={"function_calling": True, "vision": False, "json_output": True, "family": ModelFamily.UNKNOWN},
        )
        print("✅ 成功创建模型客户端")
        return model_client
    except ImportError:
        print("⚠️  autogen-ext 未安装，无法创建模型客户端")
        return None  # 返回None以便主程序处理

def search_mcp_knowledge_sync(query: str) -> str:
    """同步版本的MCP搜索工具，用于AutoGen function calling"""
    logger.info(f"同步工具: 接收到查询 '{query}'")
    try:
        return asyncio.run(search_mcp_knowledge(query))
    except Exception as e:
        logger.error(f"同步工具: 运行异步搜索时出错: {e}")
        return f"搜索过程中出现错误：{str(e)}"

async def search_mcp_knowledge(query: str) -> str:
    """异步MCP搜索工具"""
    client = MCPClient()
    try:
        results = await client.search_knowledge(query)
        if not results:
            return f"未找到关于'{query}'的相关信息"
        
        formatted_results = []
        for result in results:
            topic = result.get("topic", "未知")
            data = result.get("data", {})
            formatted_results.append(
                f"\n**{topic.upper()}相关信息：**\n"
                f"- 描述：{data.get('description', '暂无描述')}\n"
                f"- 特点：{', '.join(data.get('features', []))}\n"
                f"- 应用场景：{', '.join(data.get('use_cases', []))}"
            )
        return "\n".join(formatted_results)
    finally:
        await client.close()


async def check_server_connection():
    """检查MCP Server连接状态"""
    print("🔍 正在检查MCP Server状态...")
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://127.0.0.1:8000/")
            if response.status_code == 200:
                print("✅ MCP Server连接成功！")
                return True
            else:
                raise httpx.RequestError("服务器返回非200状态码")
    except httpx.RequestError:
        print("❌ 无法连接到MCP Server。请确保您已在另一个终端中运行 `python mcp_server.py`。")
        return False


async def execute_task(task: str, mcp_team: RoundRobinGroupChat):
    """执行单个任务"""
    print("\n" + "🔍 " + "="*58)
    print(f"📝 执行任务: {task}")
    print("="*60)
    
    # 运行工作流
    await Console(mcp_team.run_stream(task=task))
    
    print("\n" + "✅ " + "="*58)
    print("🎉 任务执行完成！")
    print("="*60)


def print_welcome():
    """打印欢迎信息"""
    print("\n" + "="*60)
    print("🚀 AutoGen MCP 交互式客户端")
    print("="*60)
    print("💡 使用说明:")
    print("   - 输入您的任务，系统将通过MCP Server搜索信息并分析")
    print("   - 输入 'quit'、'exit' 或 'q' 退出程序")
    print("   - 输入 'help' 查看帮助信息")
    print("="*60)


def print_help():
    """打印帮助信息"""
    print("\n" + "="*50)
    print("📖 帮助信息")
    print("="*50)
    print("示例任务:")
    print("   • 搜索关于Python的信息")
    print("   • 了解MCP协议的特点和应用")
    print("   • 分析AutoGen框架的优势")
    print("   • 比较不同测试框架的特点")
    print("\n命令:")
    print("   • quit/exit/q - 退出程序")
    print("   • help - 显示此帮助信息")
    print("="*50)


# ===== 主程序 =====
async def main():
    """主函数 - 启动交互式AutoGen客户端"""
    print("🚀 AutoGen客户端正在启动...")
    
    # 检查MCP Server是否在线
    if not await check_server_connection():
        return

    # 创建模型客户端
    model_client = create_model_client()
    if model_client is None:
        print("❌ 无法创建模型客户端，演示中止。请安装 `autogen-ext[openai]`。")
        return

    # 创建智能体团队（只创建一次，可复用）
    knowledge_searcher = AssistantAgent(
        name="knowledge_searcher",
        model_client=model_client,
        system_message="你是一个知识搜索助手，专门通过调用`search_mcp_knowledge_sync`函数从MCP Server搜索信息。搜索完成后简要说明搜索结果，然后回复 'SEARCH_COMPLETE'。",
        tools=[search_mcp_knowledge_sync],
    )
    
    result_analyzer = AssistantAgent(
        name="result_analyzer",
        model_client=model_client,
        system_message="你是一个结果分析专家，负责分析前面智能体提供的搜索结果，并给出深入的技术见解和应用建议。分析完成后回复 'ANALYSIS_COMPLETE'。",
    )
    
    mcp_team = RoundRobinGroupChat(
        [knowledge_searcher, result_analyzer],
        termination_condition=TextMentionTermination("ANALYSIS_COMPLETE"),
        max_turns=6,
    )
    
    # 显示欢迎信息
    print_welcome()
    
    # 交互式循环
    try:
        while True:
            print("\n" + "-"*60)
            user_input = input("💬 请输入您的任务 (输入 'help' 查看帮助): ").strip()
            
            # 处理特殊命令
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 感谢使用，再见！")
                break
            elif user_input.lower() == 'help':
                print_help()
                continue
            elif not user_input:
                print("⚠️  请输入有效的任务内容")
                continue
            
            # 执行任务
            try:
                await execute_task(user_input, mcp_team)
            except KeyboardInterrupt:
                print("\n⏸️  任务被用户中断")
                continue
            except Exception as e:
                logger.error(f"执行任务时发生错误: {e}")
                print(f"❌ 任务执行失败: {e}")
                continue
                
    except KeyboardInterrupt:
        print("\n👋 用户手动停止客户端。")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("\n👋 用户手动停止客户端。") 