"""
独立的MCP Server
职责：作为一个持续运行的服务器，为AutoGen客户端提供工具和数据服务。

类比：测试框架中独立部署的测试数据服务或API Mock服务。
"""

import asyncio
import json
import logging
import uvicorn
from datetime import datetime
from typing import Dict, Any, Optional
from fastapi import FastAPI, HTTPException
from fastapi.responses import StreamingResponse
from starlette.middleware.cors import CORSMiddleware

from persistent_memory import FileMemory, FileMemoryConfig, MemoryContent, MemoryMimeType

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ===== MCP Server 实现 =====
class SimpleMCPServer:
    """
    简单的MCP Server实现
    通过FastAPI和SSE提供流式数据接口。
    """
    
    def __init__(self):
        self.app = FastAPI(title="Simple MCP Server")
        self.setup_middleware()
        self.setup_knowledge_base()
        self.setup_memory_system()
        self.setup_routes()
    
    def setup_middleware(self):
        """配置中间件，例如CORS"""
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
    def setup_knowledge_base(self):
        """模拟知识库数据"""
        self.knowledge_base = {
            "python": {
                "description": "Python是一种解释型、高级编程语言",
                "features": ["简单易学", "功能强大", "生态丰富"],
                "use_cases": ["Web开发", "数据分析", "机器学习", "自动化测试"]
            },
            "autogen": {
                "description": "AutoGen是微软开发的多智能体对话框架",
                "features": ["多智能体协作", "自动化对话", "可扩展性"],
                "use_cases": ["智能客服", "代码生成", "测试自动化", "知识问答"]
            },
            "mcp": {
                "description": "MCP (Model Context Protocol是一种连接AI模型和工具的协议",
                "features": ["标准化接口", "工具集成", "上下文管理"],
                "use_cases": ["AI工具链", "模型增强", "服务集成"]
            }
        }
    
    def setup_memory_system(self):
        """设置内存系统"""
        self.memory_instances: Dict[str, FileMemory] = {}
        self.default_session_id = "default"
        
        # 创建默认内存实例
        default_config = FileMemoryConfig(session_id=self.default_session_id)
        self.memory_instances[self.default_session_id] = FileMemory(default_config)
    
    def setup_routes(self):
        """设置API路由"""
        
        @self.app.get("/")
        async def root():
            return {"message": "Simple MCP Server is running and waiting for clients."}
        
        @self.app.get("/search")
        async def search_stream(query: str):
            """
            SSE搜索接口
            类比：测试数据查询服务，通过流式方式返回结果
            """
            async def generate_search_results():
                logger.info(f"接收到搜索请求: {query}")
                yield "data: " + json.dumps({"type": "start", "message": f"开始搜索: {query}"}) + "\n\n"
                
                await asyncio.sleep(0.5)
                
                results = []
                for key, value in self.knowledge_base.items():
                    # 改进搜索逻辑：支持更灵活的匹配
                    query_lower = query.lower()
                    key_lower = key.lower()
                    desc_lower = value["description"].lower()
                    
                    # 方式1：原有的完整匹配
                    exact_match = query_lower in key_lower or query_lower in desc_lower
                    
                    # 方式2：分词匹配 - 查询词的任意部分匹配
                    partial_match = False
                    query_words = query_lower.split()
                    for word in query_words:
                        if len(word) >= 2:  # 只考虑长度>=2的词，避免过短的匹配
                            if word in key_lower or word in desc_lower:
                                partial_match = True
                                break
                    
                    # 方式3：特殊匹配 - 处理常见的组合词
                    special_match = False
                    if "autogen" in query_lower and ("框架" in query_lower or "framework" in query_lower):
                        if "autogen" in key_lower or "autogen" in desc_lower:
                            special_match = True
                    
                    if exact_match or partial_match or special_match:
                        results.append({"topic": key, "data": value})
                
                if results:
                    yield "data: " + json.dumps({"type": "progress", "message": f"找到 {len(results)} 个结果"}) + "\n\n"
                    await asyncio.sleep(0.3)
                    
                    for result in results:
                        yield "data: " + json.dumps({"type": "result", "data": result}) + "\n\n"
                        await asyncio.sleep(0.2)
                else:
                    yield "data: " + json.dumps({"type": "result", "message": "未找到相关结果"}) + "\n\n"
                
                logger.info(f"搜索完成: {query}")
                yield "data: " + json.dumps({"type": "end", "message": "搜索完成"}) + "\n\n"
            
            return StreamingResponse(generate_search_results(), media_type="text/event-stream")
        
        # ===== 内存管理API =====
        
        @self.app.post("/memory/add")
        async def add_memory(content: str, session_id: Optional[str] = None, metadata: Optional[Dict[str, Any]] = None):
            """添加记忆内容"""
            try:
                session_id = session_id or self.default_session_id
                
                # 获取或创建内存实例
                if session_id not in self.memory_instances:
                    config = FileMemoryConfig(session_id=session_id)
                    self.memory_instances[session_id] = FileMemory(config)
                
                memory = self.memory_instances[session_id]
                
                # 创建记忆内容
                memory_content = MemoryContent(
                    content=content,
                    mime_type=MemoryMimeType.TEXT,
                    metadata=metadata or {}
                )
                
                await memory.add(memory_content)
                
                return {
                    "status": "success",
                    "message": "记忆内容已添加",
                    "session_id": session_id,
                    "memory_count": len(memory._contents)
                }
            except Exception as e:
                logger.error(f"添加记忆失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/memory/query")
        async def query_memory(query: str = "", session_id: Optional[str] = None):
            """查询记忆内容"""
            try:
                session_id = session_id or self.default_session_id
                
                if session_id not in self.memory_instances:
                    return {"results": [], "message": "会话不存在"}
                
                memory = self.memory_instances[session_id]
                result = await memory.query(query)
                
                # 格式化结果
                formatted_results = []
                for item in result.results:
                    formatted_results.append({
                        "content": item.content,
                        "mime_type": item.mime_type.value if hasattr(item.mime_type, 'value') else item.mime_type,
                        "metadata": item.metadata
                    })
                
                return {
                    "status": "success",
                    "results": formatted_results,
                    "count": len(formatted_results),
                    "session_id": session_id
                }
            except Exception as e:
                logger.error(f"查询记忆失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/memory/sessions")
        async def list_sessions():
            """列出所有会话"""
            try:
                sessions = []
                for session_id, memory in self.memory_instances.items():
                    sessions.append(memory.get_session_info())
                
                return {
                    "status": "success",
                    "sessions": sessions,
                    "count": len(sessions)
                }
            except Exception as e:
                logger.error(f"获取会话列表失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.delete("/memory/clear")
        async def clear_memory(session_id: Optional[str] = None):
            """清空记忆内容"""
            try:
                session_id = session_id or self.default_session_id
                
                if session_id not in self.memory_instances:
                    return {"message": "会话不存在"}
                
                memory = self.memory_instances[session_id]
                await memory.clear()
                
                return {
                    "status": "success",
                    "message": "记忆内容已清空",
                    "session_id": session_id
                }
            except Exception as e:
                logger.error(f"清空记忆失败: {e}")
                raise HTTPException(status_code=500, detail=str(e))

def run_server(host="127.0.0.1", port=8000):
    """实例化并运行MCP Server"""
    server_instance = SimpleMCPServer()
    logger.info(f"🚀 MCP Server 正在启动，访问 http://{host}:{port}")
    logger.info("请在另一个终端中运行 autogen_client.py 来与它交互。")
    uvicorn.run(server_instance.app, host=host, port=port, log_level="info")

if __name__ == "__main__":
    run_server() 