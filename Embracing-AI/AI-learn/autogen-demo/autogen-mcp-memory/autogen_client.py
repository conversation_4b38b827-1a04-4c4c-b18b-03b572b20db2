"""
独立的AutoGen客户端
职责：作为客户端，连接到已经运行的mcp_server，发起任务并与之实时通信。

类比：测试框架中运行测试用例的执行脚本。
"""

import asyncio
import json
import logging
from typing import Dict, Any, List, Optional

import httpx
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.conditions import TextMentionTermination
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_agentchat.ui import Console

from persistent_memory import FileMemory, FileMemoryConfig, MemoryContent, MemoryMimeType

import os
import dotenv
dotenv.load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(levelname)s - %(message)s',  # 简化格式，去掉时间戳和模块名
    handlers=[
        logging.StreamHandler()
    ]
)

# 设置不同模块的日志级别，过滤掉内部详细事件
logging.getLogger("autogen_core.events").setLevel(logging.ERROR)     # 完全过滤内部事件日志
logging.getLogger("autogen_core").setLevel(logging.ERROR)            # 完全过滤核心框架日志
logging.getLogger("autogen_agentchat.events").setLevel(logging.ERROR)  # 过滤聊天事件日志
logging.getLogger("httpx").setLevel(logging.ERROR)                   # 完全过滤HTTP请求日志

logger = logging.getLogger(__name__)

# ===== MCP Client 部分 =====
class MCPClient:
    """
    MCP 客户端，用于与MCP Server通信
    类比：测试工具客户端，负责调用远程测试资源
    """
    
    def __init__(self, server_url: str = "http://127.0.0.1:8000"):
        self.server_url = server_url
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def search_knowledge(self, query: str) -> List[Dict[str, Any]]:
        """
        通过SSE方式搜索知识
        类比：在测试过程中查询测试数据或配置信息
        """
        try:
            logger.info(f"MCPClient: 正在向 {self.server_url}/search 发送查询: {query}")
            results = []
            
            async with self.client.stream("GET", f"{self.server_url}/search", params={"query": query}) as response:
                response.raise_for_status()  # 如果响应状态码不是2xx，则抛出异常
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data = json.loads(line[6:])
                        logger.info(f"MCPClient: 收到SSE事件: {data}")
                        if data["type"] == "result" and "data" in data:
                            results.append(data["data"])
                        elif data["type"] == "end":
                            break
            
            logger.info(f"MCPClient: 查询 '{query}' 完成，共收到 {len(results)} 条结果。")
            return results
            
        except httpx.HTTPStatusError as e:
            logger.error(f"搜索请求失败，状态码: {e.response.status_code} - {e.response.text}")
            return []
        except httpx.RequestError as e:
            logger.error(f"连接MCP Server失败: {e}. 请确保 mcp_server.py 正在运行。")
            return []
        except Exception as e:
            logger.error(f"处理搜索时发生未知错误: {e}")
            return []
    
    async def add_memory(self, content: str, session_id: Optional[str] = None, metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """添加记忆内容到MCP服务器"""
        try:
            params = {"content": content}
            if session_id:
                params["session_id"] = session_id
            if metadata:
                params["metadata"] = json.dumps(metadata)
            
            response = await self.client.post(f"{self.server_url}/memory/add", params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"添加记忆失败: {e}")
            return {"status": "error", "message": str(e)}
    
    async def query_memory(self, query: str = "", session_id: Optional[str] = None) -> Dict[str, Any]:
        """从MCP服务器查询记忆内容"""
        try:
            params = {"query": query}
            if session_id:
                params["session_id"] = session_id
            
            response = await self.client.get(f"{self.server_url}/memory/query", params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"查询记忆失败: {e}")
            return {"status": "error", "message": str(e)}
    
    async def list_sessions(self) -> Dict[str, Any]:
        """获取所有会话列表"""
        try:
            response = await self.client.get(f"{self.server_url}/memory/sessions")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"获取会话列表失败: {e}")
            return {"status": "error", "message": str(e)}
    
    async def clear_memory(self, session_id: Optional[str] = None) -> Dict[str, Any]:
        """清空记忆内容"""
        try:
            params = {}
            if session_id:
                params["session_id"] = session_id
            
            response = await self.client.delete(f"{self.server_url}/memory/clear", params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"清空记忆失败: {e}")
            return {"status": "error", "message": str(e)}
    
    async def close(self):
        """关闭客户端"""
        await self.client.aclose()


# ===== AutoGen配置 =====
def create_model_client():
    """创建模型客户端"""
    try:
        from autogen_ext.models.openai import OpenAIChatCompletionClient
        from autogen_core.models import ModelFamily
        
        model_client = OpenAIChatCompletionClient(
            model=os.getenv("MODEL"),
            base_url=os.getenv("BASE_URL"),
            api_key=os.getenv("API_KEY"),
            model_info={"function_calling": True, "vision": False, "json_output": True, "family": ModelFamily.UNKNOWN},
        )
        print("✅ 成功创建模型客户端")
        return model_client
    except ImportError:
        print("⚠️  autogen-ext 未安装，无法创建模型客户端")
        return None  # 返回None以便主程序处理

def search_mcp_knowledge_sync(query: str) -> str:
    """同步版本的MCP搜索工具，用于AutoGen function calling"""
    logger.info(f"同步工具: 接收到查询 '{query}'")
    try:
        return asyncio.run(search_mcp_knowledge(query))
    except Exception as e:
        logger.error(f"同步工具: 运行异步搜索时出错: {e}")
        return f"搜索过程中出现错误：{str(e)}"

async def search_mcp_knowledge(query: str) -> str:
    """异步MCP搜索工具"""
    client = MCPClient()
    try:
        results = await client.search_knowledge(query)
        if not results:
            return f"未找到关于'{query}'的相关信息"
        
        formatted_results = []
        for result in results:
            topic = result.get("topic", "未知")
            data = result.get("data", {})
            formatted_results.append(
                f"\n**{topic.upper()}相关信息：**\n"
                f"- 描述：{data.get('description', '暂无描述')}\n"
                f"- 特点：{', '.join(data.get('features', []))}\n"
                f"- 应用场景：{', '.join(data.get('use_cases', []))}"
            )
        return "\n".join(formatted_results)
    finally:
        await client.close()

def add_memory_sync(content: str, session_id: Optional[str] = None) -> str:
    """同步版本的记忆添加工具"""
    logger.info(f"记忆工具: 添加内容 '{content[:50]}...'")
    try:
        return asyncio.run(add_memory_to_mcp(content, session_id))
    except Exception as e:
        logger.error(f"记忆工具: 添加记忆时出错: {e}")
        return f"添加记忆时出现错误：{str(e)}"

async def add_memory_to_mcp(content: str, session_id: Optional[str] = None) -> str:
    """异步记忆添加工具"""
    client = MCPClient()
    try:
        result = await client.add_memory(content, session_id)
        if result.get("status") == "success":
            return f"记忆内容已成功添加，当前会话共有 {result.get('memory_count', 0)} 条记忆"
        else:
            return f"添加记忆失败: {result.get('message', '未知错误')}"
    finally:
        await client.close()

def query_memory_sync(query: str = "", session_id: Optional[str] = None) -> str:
    """同步版本的记忆查询工具"""
    logger.info(f"记忆工具: 查询 '{query}'")
    try:
        return asyncio.run(query_memory_from_mcp(query, session_id))
    except Exception as e:
        logger.error(f"记忆工具: 查询记忆时出错: {e}")
        return f"查询记忆时出现错误：{str(e)}"

async def query_memory_from_mcp(query: str = "", session_id: Optional[str] = None) -> str:
    """异步记忆查询工具"""
    client = MCPClient()
    try:
        result = await client.query_memory(query, session_id)
        if result.get("status") == "success":
            memories = result.get("results", [])
            if not memories:
                return "未找到相关记忆内容"
            
            formatted_memories = []
            for i, memory in enumerate(memories, 1):
                content = memory.get("content", "")
                metadata = memory.get("metadata", {})
                timestamp = metadata.get("timestamp", "未知时间")
                formatted_memories.append(f"{i}. [{timestamp}] {content}")
            
            return f"找到 {len(memories)} 条相关记忆:\n" + "\n".join(formatted_memories)
        else:
            return f"查询记忆失败: {result.get('message', '未知错误')}"
    finally:
        await client.close()


async def check_server_connection():
    """检查MCP Server连接状态"""
    print("🔍 正在检查MCP Server状态...")
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://127.0.0.1:8000/")
            if response.status_code == 200:
                print("✅ MCP Server连接成功！")
                return True
            else:
                raise httpx.RequestError("服务器返回非200状态码")
    except httpx.RequestError:
        print("❌ 无法连接到MCP Server。请确保您已在另一个终端中运行 `python mcp_server.py`。")
        return False


async def execute_task(task: str, mcp_team: RoundRobinGroupChat):
    """执行单个任务"""
    print("\n" + "🔍 " + "="*58)
    print(f"📝 执行任务: {task}")
    print("="*60)
    
    # 运行工作流
    await Console(mcp_team.run_stream(task=task))
    
    print("\n" + "✅ " + "="*58)
    print("🎉 任务执行完成！")
    print("="*60)


def print_welcome():
    """打印欢迎信息"""
    print("\n" + "="*60)
    print("🚀 AutoGen MCP 交互式客户端")
    print("="*60)
    print("💡 使用说明:")
    print("   - 输入您的任务，系统将通过MCP Server搜索信息并分析")
    print("   - 如果本地知识库没有结果，系统将自动从互联网搜索并更新知识库")
    print("   - 输入 'quit'、'exit' 或 'q' 退出程序")
    print("   - 输入 'help' 查看帮助信息")
    print("="*60)


def print_help():
    """打印帮助信息"""
    print("\n" + "="*50)
    print("📖 帮助信息")
    print("="*50)
    print("示例任务:")
    print("   • 搜索关于Python的信息")
    print("   • 了解Model Context Protocol协议的特点和应用")
    print("   • 分析AutoGen框架的优势")
    print("   • 比较不同测试框架的特点")
    print("   • 搜索最新的人工智能技术趋势（将触发互联网搜索）")
    print("   • 记住我偏好简洁的回答")
    print("   • 查询我们之前的对话历史")
    print("\n命令:")
    print("   • quit/exit/q - 退出程序")
    print("   • help - 显示此帮助信息")
    print("   • memory - 显示记忆管理命令")
    print("="*50)

def print_memory_help():
    """打印记忆管理帮助信息"""
    print("\n" + "="*50)
    print("🧠 记忆管理命令")
    print("="*50)
    print("记忆功能:")
    print("   • 系统会自动保存重要的对话内容")
    print("   • 支持查询历史对话和偏好设置")
    print("   • 记忆内容持久化存储在服务器端")
    print("   • 支持会话级别的记忆隔离")
    print("\n智能体功能:")
    print("   • knowledge_searcher - 本地知识搜索")
    print("   • web_search - 互联网搜索与知识更新")
    print("   • result_analyzer - 结果分析")
    print("   • memory_manager - 记忆管理")
    print("="*50)


# ===== 主程序 =====

async def main():
    """主函数 - 启动交互式AutoGen客户端"""
    print("🚀 AutoGen客户端正在启动...")
    
    # 检查MCP Server是否在线
    if not await check_server_connection():
        return

    # 创建模型客户端
    model_client = create_model_client()
    if model_client is None:
        print("❌ 无法创建模型客户端，演示中止。请安装 `autogen-ext[openai]`。")
        return

    # 创建智能体团队（只创建一次，可复用）
    knowledge_searcher = AssistantAgent(
        name="knowledge_searcher",
        model_client=model_client,
        system_message="你是一个知识搜索助手，专注于从本地知识库搜索信息。你的职责是通过调用`search_mcp_knowledge_sync`函数从MCP Server搜索信息。如果本地知识库有结果，简要说明搜索结果，然后回复 'SEARCH_COMPLETE'。如果本地知识库没有结果，你应该明确表示未找到结果，并回复 'NO_LOCAL_RESULTS'，将任务转交给 web_search 智能体。",
        tools=[search_mcp_knowledge_sync],
    )
    
    def web_search(query: str) -> str:
        """网络搜索工具，用于在互联网上搜索信息"""
        logger.info(f"网络搜索工具: 接收到查询 '{query}'")
        try:
            # 这里可以实现实际的网络搜索逻辑，例如调用搜索API
            # 简单示例实现，实际应用中应替换为真实的搜索API调用
            return f"在互联网上搜索 '{query}' 的结果：\n1. 相关信息A\n2. 相关信息B\n3. 相关信息C"
        except Exception as e:
            logger.error(f"网络搜索工具: 搜索时出错: {e}")
            return f"搜索过程中出现错误：{str(e)}"
    
    web_search_agent = AssistantAgent(
        name="web_search",
        model_client=model_client,
        system_message="你是一个互联网搜索专家，负责在本地知识库没有结果时从互联网获取信息。当你看到消息中包含 'NO_LOCAL_RESULTS' 时，这是你的行动信号。你必须立即使用 web_search 工具在互联网上搜索用户查询的相关内容。获取到信息后，你必须使用 add_memory_sync 工具将这些新知识保存到本地记忆中，确保知识库持续更新。搜索完成后简要说明搜索结果，然后回复 'SEARCH_COMPLETE'。",
        tools=[web_search, add_memory_sync],
    )
    
    result_analyzer = AssistantAgent(
        name="result_analyzer",
        model_client=model_client,
        system_message="你是一个结果分析专家，负责分析前面智能体提供的搜索结果，并给出深入的技术见解和应用建议。分析完成后，你应该总结你的分析，并明确地将任务移交给记忆管理员，例如回复 '分析完成，请memory_manager归档'。",
    )
    
    memory_manager = AssistantAgent(
        name="memory_manager",
        model_client=model_client,
        system_message="你是一个记忆管理专家。当被要求归档时，你的任务是调用`add_memory_sync`函数来保存对话内容。保存成功后，你必须回复'归档完成'以结束对话。",

        tools=[add_memory_sync, query_memory_sync],
    )
    
    mcp_team = RoundRobinGroupChat(
        [knowledge_searcher, web_search_agent, result_analyzer, memory_manager],
        termination_condition=TextMentionTermination("归档完成"),
        max_turns=10,  # 增加最大轮次以适应新增的智能体
    )
    
    # 显示欢迎信息
    print_welcome()
    
    # 交互式循环
    try:
        while True:
            print("\n" + "-"*60)
            user_input = input("💬 请输入您的任务 (输入 'help' 查看帮助): ").strip()
            
            # 处理特殊命令
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 感谢使用，再见！")
                break
            elif user_input.lower() == 'help':
                print_help()
                continue
            elif user_input.lower() == 'memory':
                print_memory_help()
                continue
            elif not user_input:
                print("⚠️  请输入有效的任务内容")
                continue
            
            # 执行任务
            try:
                await execute_task(user_input, mcp_team)
            except KeyboardInterrupt:
                print("\n⏸️  任务被用户中断")
                continue
            except Exception as e:
                logger.error(f"执行任务时发生错误: {e}")
                print(f"❌ 任务执行失败: {e}")
                continue
                
    except KeyboardInterrupt:
        print("\n👋 用户手动停止客户端。")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("\n👋 用户手动停止客户端。")