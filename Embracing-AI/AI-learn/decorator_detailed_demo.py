"""
装饰器注册机制深度解析 - 逐步执行演示
展示装饰器在代码执行时的每一个步骤
"""

class DetailedServer:
    """详细展示装饰器工作原理的服务器"""
    
    def __init__(self, name):
        self.name = name
        self.handlers = {}
        print(f"🏗️ [{self.name}] 服务器对象创建完成, ID: {id(self)}")
    
    def register_handler(self, event_name):
        """装饰器工厂：返回真正的装饰器"""
        print(f"📞 [{self.name}] 调用 register_handler('{event_name}')")
        print(f"   ↪️ 即将返回装饰器函数...")
        
        def actual_decorator(func):
            """真正的装饰器函数"""
            print(f"🎯 [{self.name}] 装饰器被调用！")
            print(f"   ↪️ 正在注册函数: {func.__name__}")
            print(f"   ↪️ 事件名称: {event_name}")
            print(f"   ↪️ 函数对象ID: {id(func)}")
            
            # 🔥 核心：将函数注册到处理器字典中
            self.handlers[event_name] = func
            print(f"   ✅ 注册成功！当前注册表: {list(self.handlers.keys())}")
            
            # 🔥 重要：返回原函数，不修改它
            return func
        
        print(f"📤 [{self.name}] 返回装饰器函数, ID: {id(actual_decorator)}")
        return actual_decorator
    
    async def trigger_event(self, event_name):
        """触发已注册的事件处理器"""
        print(f"\n🚨 [{self.name}] 触发事件: {event_name}")
        if event_name in self.handlers:
            handler = self.handlers[event_name]
            print(f"handler: {type(handler)} - {handler}")
            print(f"   ✅ 找到处理器: {handler.__name__}")
            print(f"   🏃‍♂️ 开始执行处理器...")
            result = await handler()
            print(f"   🎊 处理器执行完成，返回: {result}")
            return result
        else:
            print(f"   ❌ 未找到事件处理器: {event_name}")
            return None


def step_by_step_demo():
    """逐步演示装饰器的工作过程"""
    
    print("="*80)
    print("🔍 装饰器注册机制 - 逐步执行演示")
    print("="*80)
    
    print("\n📖 第一步：创建服务器对象")
    print("-" * 40)
    server = DetailedServer("演示服务器")
    
    print("\n📖 第二步：理解装饰器语法")
    print("-" * 40)
    print("当我们写：")
    print("  @server.register_handler('login')")
    print("  def handle_login():")
    print("      return 'login success'")
    print("\nPython实际执行的是：")
    print("  1. 调用 server.register_handler('login')  # 返回装饰器")
    print("  2. 用返回的装饰器装饰 handle_login 函数")
    print("  3. 等价于：handle_login = decorator(handle_login)")
    
    print("\n📖 第三步：实际执行装饰器注册")
    print("-" * 40)
    print("🎬 开始执行装饰器注册过程...")
    
    # 🔥 这里就是关键的装饰器注册语法！
    @server.register_handler('login')
    async def handle_login():
        """处理登录事件"""
        return "用户登录成功！"
    
    print("\n📖 第四步：注册更多处理器")
    print("-" * 40)
    
    @server.register_handler('logout')
    async def handle_logout():
        """处理登出事件"""
        return "用户已安全登出！"
    
    @server.register_handler('search')
    async def handle_search():
        """处理搜索事件"""
        return "搜索结果：找到100条记录"
    
    print(f"\n📖 第五步：查看最终注册结果")
    print("-" * 40)
    print(f"📊 服务器 '{server.name}' 的注册表：")
    for event, handler in server.handlers.items():
        print(f"   🎯 {event} -> {handler.__name__}() [ID: {id(handler)}]")
    
    return server


async def test_registered_handlers(server):
    """测试已注册的处理器"""
    
    print("\n" + "="*80)
    print("🧪 测试已注册的处理器")
    print("="*80)
    
    test_events = ['login', 'logout', 'search', 'unknown']
    
    for event in test_events:
        result = await server.trigger_event(event)
        print(f"   📝 事件 '{event}' 处理结果: {result}")


def compare_with_mcp_pattern():
    """对比MCP中的实际用法"""
    
    print("\n" + "="*80)
    print("🔗 对比 MCP 中的实际用法")
    print("="*80)
    
    print("""
📌 在你的MCP代码中：

```python
@self.server.list_tools()
async def handle_list_tools() -> List[types.Tool]:
    return self.tools
```

🔍 执行过程解析：

1️⃣ self.server.list_tools() 被调用
   ↪️ 返回一个装饰器函数

2️⃣ 这个装饰器函数装饰 handle_list_tools
   ↪️ 将 handle_list_tools 注册到服务器的处理器表中

3️⃣ 当客户端发送 "list_tools" 请求时
   ↪️ 服务器查找注册表，找到 handle_list_tools
   ↪️ 调用该函数并返回结果

🎯 关键点：
- 装饰器在代码**定义时**就执行了注册
- 不是在调用时才注册，而是在模块加载时就注册好了
- 这样服务器启动时就知道了所有可用的处理器
""")

    print("\n📌 Browser-use 中类似的用法：")
    print("""
```python
@browser.register_action('click')
def handle_click(element):
    element.click()

@browser.register_action('type')  
def handle_type(element, text):
    element.send_keys(text)
```

🔍 同样的原理：
- 在代码加载时注册动作处理器
- 运行时根据动作名称查找并调用对应的处理器
""")


if __name__ == "__main__":
    import asyncio
    
    # 逐步演示
    server = step_by_step_demo()
    
    # 测试处理器
    asyncio.run(test_registered_handlers(server))
    
    # 对比说明
    compare_with_mcp_pattern()