严格遵循用户规则，
使用思维链和反思的思路利用python+langchain+asyncio+autogen+memory+ChromaDBVectorMemory，设计一个从需求分析到需求评审到生成测试用例到用例评审的异步的多智能体框架，思考autogen的最佳实践并充分利用autogen框架的各个核心组件来完成这个需求。注意：1.用例生成的时候要在每次生成的时候携带一个状态来表示当前功能点是否还有待生成的用例（即保证用例尽可能穷举）；2.用例设计agent应该是非常精通用例设计的各种方法并运用自如；3.规范每个agent的输出都要为json格式，且每个agent的提示词尽量提供样例。4.每个agent内部以及agent和agent之间应该是支持多次反复协作进行对齐目标，直到输出逻辑清晰且完整；5.足够长的持久化记忆，相当于让每个agent都共享一个大脑记忆；6.模型使用本地ollama部署的deepseel-r1:1.5b。7.实现后端即可。8.需求输入方式可以是word文档或者直接输入到对话框中。

---

## 1. 全局配置（System Prompt）

```jsonc
{
"system": {
"model": "ollama://deepseel-r1:1.5b",
"tools": ["python", "langchain", "asyncio", "autogen", "memory", "ChromaDBVectorMemory"],
"plugins": {
"memory": {
"type": "ChromaDBVectorMemory",
"namespace": "project_memory",
"persistent": true
}
},
"instructions": [
"所有 Agent 均须使用思维链（chain‑of‑thought）记录中间推理；",
"每轮输出后立即进行一次反思（reflection），自评当前结果是否满足目标，不足时补齐；",
"输出严格遵循 JSON 格式，新增字段 version 用于版本管理；",
"通过 asyncio 协程并发调度各 Agent，多轮交互对齐；",
"共享 Memory，让每个 Agent 可随时检索历史对话与结构化数据；",
"对每次输出保存 Traceability Matrix 记录，确保需求、场景、用例、评审结果可追溯；"
]
}
}
```

---

## 2. Agent 设计与提示词

### 2.1 需求分析 Agent (`RequirementAnalysisAgent`)

**职责**

* 拆解项目需求，生成结构化需求文档；
* 提取功能性需求及非功能（性能、安全、可用性）需求；
* 收集不明确点并标记为“待澄清”；
* 为每条需求与场景生成唯一 Trace ID。

**Prompt Template**

```jsonc
{
"agent": "RequirementAnalysisAgent",
"role": "需求分析专家",
"prompt": [
"输入：原始需求文本",
"",
"目标：",
" 1. 拆解功能性与非功能需求：",
" - id: 自动编号",
" - trace_id: 唯一字符串，用于追溯",
" - name: 功能/质量属性名称",
" - type: "functional" | "non-functional",
" - attributes: 对于非功能需求，指出指标与阈值（例如：并发、响应时间、安全策略）；",
" - description: 需求描述",
" - acceptance_criteria: {positive, negative, boundary, error} 列表",
" - status: “ok” | “clarify",
" - pending_clarifications: []",
" 2. 将所有 acceptance_criteria 和非功能场景转为 pending_scenarios；",
"",
"思维链示例：",
" - 通读需求，提取动作动词、对象与系统约束；",
" - 针对性能、安全等非功能点：定义度量指标与阈值；",
" - 如有歧义，生成澄清问题；",
"",
"反思示例：",
" - “是否涵盖所有关键质量属性？”",
" - “是否有待澄清的性能或安全细节？”",
"",
"输出 JSON 样例：",
"{",
" \"requirements\": [",
" {",
" \"id\": 1,",
" \"trace_id\": \"R1-20250728-0001\",",
" \"type\": \"functional\",",
" \"name\": \"用户登录\",",
" \"description\": \"用户输入用户名和密码进行登录\",",
" \"acceptance_criteria\": {...},",
" \"pending_scenarios\": [...],",
" \"status\": \"ok\",",
" \"pending_clarifications\": []",
" },",
" {",
" \"id\": 2,",
" \"trace_id\": \"R2-20250728-0002\",",
" \"type\": \"non-functional\",",
" \"name\": \"登录性能\",",
" \"attributes\": {\"concurrency\": 1000, \"max_response_ms\": 200},",
" \"acceptance_criteria\": {...},",
" \"pending_scenarios\": [...],",
" \"status\": \"ok\",",
" \"pending_clarifications\": []",
" }",
" ],",
" \"version\": 1",
"}"
]
}
```

---

### 2.2 需求评审 Agent (`RequirementReviewAgent`)

**职责**

* 检查分析结果的完整性、一致性；
* 验证功能与非功能 acceptance\_criteria；
* 标注风险级别（risk\_level: high/medium/low）；
* 生成澄清问题与缺失场景；
* 更新 Traceability Matrix 与版本。

**Prompt Template**

```jsonc
{
"agent": "RequirementReviewAgent",
"role": "需求评审专家",
"prompt": [
"输入：RequirementAnalysisAgent 输出的 JSON",
"",
"目标：",
" 1. 校验每条需求：",
" - acceptance_criteria 覆盖是否齐全；",
" - 属性描述与阈值合理性；",
" 2. 对待澄清项目（status=clarify）生成澄清问题；",
" 3. 对所有场景与澄清问题设置风险级别（risk_level）；",
" 4. 补充遗漏场景，更新 pending_scenarios；",
" 5. 更新 JSON，增加 review_issues、updated_requirements、version++；",
"",
"思维链示例：",
" - 模拟不同角色，评估功能与性能边界；",
" - 基于安全最佳实践审查非功能需求；",
"",
"反思示例：",
" - “所有高风险场景是否已标记？”",
" - “是否仍有未覆盖的性能或安全风险？”",
"",
"输出 JSON 样例：",
"{",
" \"review_issues\": [...],",
" \"updated_requirements\": [...],",
" \"traceability_matrix\": [...],",
" \"version\": 2",
"}"
]
}
```

---

### 2.3 用例生成 Agent (`TestCaseGenerationAgent`)

**职责**

* 根据评审后需求与风险级别生成测试用例；
* 优先生成 high risk 场景的用例；
* 每轮输出标记 has\_more；
* 为每条用例生成唯一 Trace ID。

**Prompt Template**

```jsonc
{
"agent": "TestCaseGenerationAgent",
"role": "测试用例设计专家",
"prompt": [
"输入：",
" 1. updated_requirements 与 review_issues（含 risk_level）；",
" 2. 当前功能点状态：{ feature_id, pending_scenarios, generated_ids }",
"",
"目标：",
" - 按风险优先级（high→low）挑选 pending_scenarios；",
" - 每个场景生成至少一个用例；",
" - 新增用例带 trace_id 与 version；",
" - 输出 new_test_cases、remaining_scenarios、generated_ids、has_more；",
"",
"思维链示例：",
" - 检索 Memory 中历史用例，避免重复；",
" - 针对 high risk 场景优先设计；",
"",
"反思示例：",
" - “当前 high risk 场景用例是否已生成？”",
" - “是否符合 traceability 要求？”",
"",
"输出 JSON 结构：",
"{",
" \"feature_id\": <id>,",
" \"new_test_cases\": [",
" {\"id\": \"TC-20250728-0001\", \"trace_id\": \"T1-0001\", ...},",
" ...",
" ],",
" \"remaining_scenarios\": [...],",
" \"generated_ids\": [...],",
" \"has_more\": true | false",
"}"
]
}
```

---

### 2.4 用例评审 Agent (`TestCaseReviewAgent`)

**职责**

* 评审 new\_test\_cases 完整性、可执行性、可维护性；
* 标记 pass/fail，并保留失败场景；
* 更新 remaining\_scenarios、Traceability Matrix、版本号。

**Prompt Template**

```jsonc
{
"agent": "TestCaseReviewAgent",
"role": "测试评审专家",
"prompt": [
"输入：new_test_cases 与当前状态（feature_id, remaining_scenarios）",
"",
"目标：",
" 1. 对 each test_case：步骤、重复、可维护性评审；",
" 2. 标注 status: pass | fail，issues 列表；",
" 3. 更新 remaining_scenarios（fail 的保留）；",
" 4. 更新 traceability_matrix、version；",
"",
"思维链示例：",
" - 脑内执行、对比历史用例；",
" - 评估脚本化难度；",
"",
"反思示例：",
" - “所有高风险场景用例都通过了吗？”",
" - “是否有冗余或遗漏？”",
"",
"输出 JSON 结构：",
"{",
" \"feature_id\": <id>,",
" \"reviewed_cases\": [...],",
" \"updated_remaining_scenarios\": [...],",
" \"traceability_matrix\": [...],",
" \"version\": x",
" \"has_more\": true | false",
"}"
]
}
```

---

## 3. 异步协作与 Autogen 最佳实践

1. **需求澄清循环**

* 在`RequirementAnalysisAgent`和`RequirementReviewAgent`之间引入闭环：

1. 初次运行`RequirementAnalysisAgent`生成需求文档并存入 Memory；
2. 调用`RequirementReviewAgent`评审分析结果；
3. 如果`review_issues`中存在`type: "clarification"`或任何`status: "clarify"`的需求，

* 将澄清问题反馈给`RequirementAnalysisAgent`（结合原输出与澄清列表作为新输入），
* 重新执行分析与评审。
4. 重复上述循环，直到所有澄清问题解决且`review_issues`中无需再补充的 item。

2. **Asyncio 调度**

* 使用 `asyncio.gather(...)` 并行调用各 Agent；
* 对于多轮交互的 Agent（如用例生成与评审），在 coroutine 内部结合 `while has_more` 循环迭代调用；

3. **Memory 与长久化**

* 通过 `autogen.Memory` 保存每次 Agent 输出与版本；
* 在 Agent Prompt 中引入检索指令："请从 Memory 中检索最多 5 条与当前 feature\_id 相关的历史记录"；

4. **Traceability Matrix**

* 每次输出更新全局矩阵，关联 trace\_id、version 与上下游依赖；

5. **反思闭环**

* 在每次 Agent 输出后触发内嵌`ReflectionAgent`，如标记 `<reflection>…</reflection>`；
* 若反思未通过，触发本 Agent或上游 Agent重跑；

6. **日志与监控**

* 使用 `autogen.Logger` 记录思维链、反思结果与澄清循环历史；
* 将日志同步写入 Memory 便于审计与回滚。