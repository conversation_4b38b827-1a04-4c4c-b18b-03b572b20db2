# 跨行业多功能工具网站接口分析报告（修正版）

## 项目概述
基于用户核心功能纠正要求，重新梳理接口清单，删除不必要功能，优化核心业务逻辑。

## 核心功能模块（修正后）

### 1. 文档处理模块
#### 1.1 合同条款提取
- **接口路径**: `POST /api/contract/analyze`
- **功能**: 分析合同文本，提取关键条款和风险点
- **实现状态**: ✅ 已实现
- **请求参数**:
  ```json
  {
    "text": "合同文本内容",
    "analysis_depth": "standard|detailed",
    "focus_clauses": ["违约金", "付款条件"]
  }
  ```

#### 1.2 PDF表格提取
- **接口路径**: `POST /api/pdf-processor/extract-tables`
- **功能**: 从PDF文件中提取表格数据并转换为Excel
- **实现状态**: ❌ 需要实现
- **请求参数**:
  ```json
  {
    "file": "PDF文件",
    "extract_type": "tables|all_content",
    "output_format": "excel|csv"
  }
  ```

#### 1.3 文档格式转换
- **接口路径**: `POST /api/file-converter/convert`
- **功能**: 支持多种文档格式互转
- **实现状态**: ❌ 需要实现
- **请求参数**:
  ```json
  {
    "file": "源文件",
    "source_format": "pdf|docx|txt",
    "target_format": "docx|pdf|txt|html"
  }
  ```

#### 1.4 文本改写器
- **接口路径**: `POST /api/text-rewriter/rewrite`
- **功能**: 多风格文本改写
- **实现状态**: ✅ 已实现
- **请求参数**:
  ```json
  {
    "text": "原始文本",
    "styles": ["professional", "casual", "enthusiastic"],
    "creativity": 70,
    "language": "zh"
  }
  ```

### 2. 教育工具模块（简化）
#### 2.1 错题归档（图片识别）
- **接口路径**: `POST /api/education/error-book/upload-image`
- **功能**: 上传错题图片，OCR识别转文本并归档
- **实现状态**: ❌ 需要重新实现
- **请求参数**:
  ```json
  {
    "image": "错题图片文件",
    "subject": "数学|语文|英语|物理|化学",
    "grade": "小学|初中|高中|大学",
    "tags": ["代数", "几何"]
  }
  ```
- **响应数据**:
  ```json
  {
    "id": "错题ID",
    "recognized_text": "识别出的题目文本",
    "confidence": 0.95,
    "subject": "数学",
    "created_at": "2024-01-01T10:00:00Z"
  }
  ```

#### 2.2 错题管理
- **接口路径**: `GET /api/education/error-book/list`
- **功能**: 获取错题列表，支持筛选和搜索
- **实现状态**: ❌ 需要实现
- **查询参数**:
  ```
  ?subject=数学&grade=高中&page=1&limit=20&keyword=函数
  ```

#### 2.3 错题详情
- **接口路径**: `GET /api/education/error-book/{id}`
- **功能**: 获取单个错题详情
- **实现状态**: ❌ 需要实现

#### 2.4 错题删除
- **接口路径**: `DELETE /api/education/error-book/{id}`
- **功能**: 删除错题记录
- **实现状态**: ❌ 需要实现

### 3. 招聘工具模块（简化）
#### 3.1 简历匹配分析
- **接口路径**: `POST /api/resume-matcher/match`
- **功能**: 输入岗位JD和简历，输出多维度匹配度评分
- **实现状态**: ✅ 已实现（需要优化）
- **请求参数**:
  ```json
  {
    "job_description": "岗位JD描述",
    "resume_text": "简历内容",
    "candidate_name": "候选人姓名",
    "match_config": {
      "skills_weight": 0.4,
      "experience_weight": 0.3,
      "education_weight": 0.2,
      "other_weight": 0.1,
      "custom_keywords": ["React", "Python", "项目管理"]
    }
  }
  ```
- **响应数据**:
  ```json
  {
    "overall_score": 78,
    "detailed_scores": {
      "skills_match": 85,
      "experience_match": 72,
      "education_match": 80,
      "keyword_match": 75
    },
    "matched_keywords": ["React", "TypeScript", "团队协作"],
    "missing_keywords": ["Docker", "微服务架构"],
    "gap_analysis": [
      "缺少云计算相关经验",
      "项目管理经验不足"
    ],
    "recommendations": [
      "建议补充Docker容器化技术",
      "可考虑PMP认证提升项目管理能力"
    ]
  }
  ```

### 4. 电商工具模块
#### 4.1 商品描述转换
- **接口路径**: `POST /api/ecommerce/product-converter`
- **功能**: 将商品信息转换为不同平台的描述格式
- **实现状态**: ❌ 需要实现
- **请求参数**:
  ```json
  {
    "product_info": "商品基础信息",
    "source_platform": "淘宝|京东|拼多多",
    "target_platforms": ["亚马逊", "eBay", "速卖通"],
    "style": "professional|attractive|detailed"
  }
  ```

#### 4.2 跨境翻译润色
- **接口路径**: `POST /api/ecommerce/translation-polish`
- **功能**: 商品描述多语言翻译和本地化润色
- **实现状态**: ❌ 需要实现
- **请求参数**:
  ```json
  {
    "text": "商品描述文本",
    "source_language": "zh",
    "target_language": "en",
    "target_market": "US|UK|AU|CA",
    "polish_level": "basic|professional|marketing"
  }
  ```

### 5. 数据工具模块（简化）
#### 5.1 自然语言转SQL
- **接口路径**: `POST /api/data-tools/nl-to-sql/generate`
- **功能**: 将自然语言查询转换为SQL语句，连接MySQL查询业务库表
- **实现状态**: ✅ 已实现（需要增强MySQL连接）
- **请求参数**:
  ```json
  {
    "natural_language": "查询最近一个月销售额最高的前10个商品",
    "database_config": {
      "host": "localhost",
      "port": 3306,
      "database": "business_db",
      "username": "user",
      "password": "password"
    },
    "context_tables": ["products", "orders", "order_items"],
    "result_limit": 100
  }
  ```
- **响应数据**:
  ```json
  {
    "sql": "SELECT p.name, SUM(oi.quantity * oi.price) as total_sales FROM products p JOIN order_items oi ON p.id = oi.product_id JOIN orders o ON oi.order_id = o.id WHERE o.created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH) GROUP BY p.id ORDER BY total_sales DESC LIMIT 10;",
    "explanation": "查询最近一个月销售额最高的前10个商品",
    "tables_used": ["products", "orders", "order_items"],
    "estimated_rows": 10,
    "execution_time_estimate": "< 100ms"
  }
  ```

#### 5.2 SQL执行
- **接口路径**: `POST /api/data-tools/sql/execute`
- **功能**: 执行生成的SQL语句并返回结果
- **实现状态**: ❌ 需要实现
- **请求参数**:
  ```json
  {
    "sql": "SELECT语句",
    "database_config": "数据库连接配置",
    "safe_mode": true
  }
  ```

#### 5.3 数据库表结构查询
- **接口路径**: `GET /api/data-tools/database/schema`
- **功能**: 获取数据库表结构信息
- **实现状态**: ❌ 需要实现
- **查询参数**:
  ```
  ?database=business_db&table=products
  ```

## 系统基础接口

### 健康检查
- **接口路径**: `GET /health`
- **功能**: 系统健康状态检查
- **实现状态**: ✅ 已实现

### 文件上传
- **接口路径**: `POST /api/upload`
- **功能**: 通用文件上传接口
- **实现状态**: ❌ 需要实现

## 接口实现优先级

### 高优先级（立即实现）
1. **错题归档图片识别** - 核心功能变更
2. **PDF表格提取** - 前端页面已存在
3. **文档格式转换** - 前端页面已存在
4. **MySQL连接增强** - 自然语言转SQL功能完善

### 中优先级（近期实现）
5. **商品描述转换** - 电商工具核心功能
6. **跨境翻译润色** - 电商工具核心功能
7. **SQL执行接口** - 数据工具完善
8. **文件上传接口** - 基础功能支持

### 低优先级（后期优化）
9. **数据库表结构查询** - 辅助功能
10. **错题管理接口** - 功能完善

## 技术实现要点

### 1. 图片OCR识别
- 集成百度OCR、腾讯OCR或阿里云OCR API
- 支持数学公式识别
- 图片预处理优化识别准确率

### 2. MySQL数据库连接
- 使用SQLAlchemy ORM
- 连接池管理
- SQL注入防护
- 查询结果缓存

### 3. 文件处理
- PDF解析：使用PyPDF2、pdfplumber
- 文档转换：使用python-docx、pandoc
- 图片处理：使用Pillow

### 4. AI功能增强
- 优化Prompt工程
- 结果后处理和验证
- 错误处理和重试机制

## 删除的功能模块

以下功能已从原计划中删除：
- ❌ 在线考试系统
- ❌ 学习进度跟踪
- ❌ 职位发布管理
- ❌ 候选人管理
- ❌ 简历解析（独立功能）
- ❌ 复杂数据分析工具

## 总结

修正后的接口清单更加聚焦核心业务需求：
- **总接口数量**: 15个（vs 原来的20+个）
- **已实现**: 3个（20%）
- **需要实现**: 12个（80%）
- **核心变更**: 错题归档改为图片识别，简化招聘和数据功能

建议按照优先级顺序逐步实现，确保核心功能快速上线。