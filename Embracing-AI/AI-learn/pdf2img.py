import fitz  # PyMuPDF
import argparse
import os
import sys

def pdf_to_images(pdf_path, output_dir=".", dpi=300):
    """
    将 PDF 文件的每一页转换为 PNG 图像并保存。

    参数:
        pdf_path (str): 输入的 PDF 文件路径
        output_dir (str): 输出图片保存目录
        dpi (int): 输出图片的分辨率（默认 300）
    """
    # 打开 PDF 文件
    doc = fitz.open(pdf_path)

    # 遍历每一页
    for page_num in range(doc.page_count):
        page = doc.load_page(page_num)  # 加载页面
        # 将页面渲染为图片
        pix = page.get_pixmap(dpi=dpi)
        # 构造输出路径
        output_path = f"{output_dir}/page_{page_num + 1}.png"
        # 保存图片
        pix.save(output_path)
        print(f"已保存 {output_path}")

    print("转换完成！")

def main():
    """
    主函数，处理命令行参数并执行PDF转图片操作
    """
    parser = argparse.ArgumentParser(description='将PDF文件转换为PNG图片')
    parser.add_argument('pdf_path', help='输入的PDF文件路径')
    parser.add_argument('-o', '--output', default='.', help='输出图片保存目录（默认为当前目录）')
    parser.add_argument('-d', '--dpi', type=int, default=300, help='输出图片的分辨率（默认300）')

    args = parser.parse_args()

    # 检查PDF文件是否存在
    if not os.path.exists(args.pdf_path):
        print(f"错误：文件 '{args.pdf_path}' 不存在")
        sys.exit(1)

    # 检查输出目录是否存在，不存在则创建
    if not os.path.exists(args.output):
        os.makedirs(args.output)
        print(f"已创建输出目录：{args.output}")

    # 执行转换
    try:
        pdf_to_images(args.pdf_path, args.output, args.dpi)
    except Exception as e:
        print(f"转换过程中发生错误：{e}")
        sys.exit(1)

if __name__ == "__main__":
    main()