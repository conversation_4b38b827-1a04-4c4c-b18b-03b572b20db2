"""
Ollama连接测试脚本
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.logger import app_logger
from services.ollama_client import get_ollama_client


async def test_basic_generation():
    """测试基础生成功能"""
    app_logger.info("=== 测试基础生成功能 ===")
    
    try:
        client = await get_ollama_client()
        
        # 测试简单问答
        prompt = "请用中文简单介绍什么是软件测试。"
        response = await client.generate_response(prompt, temperature=0.3)
        
        app_logger.info(f"生成成功，响应长度: {len(response)} 字符")
        app_logger.info(f"响应内容: {response[:200]}...")
        
        return True
        
    except Exception as e:
        app_logger.error(f"基础生成测试失败: {str(e)}")
        return False


async def test_system_prompt():
    """测试系统提示词功能"""
    app_logger.info("=== 测试系统提示词功能 ===")
    
    try:
        client = await get_ollama_client()
        
        system_prompt = "你是一个专业的软件测试专家，请用专业术语回答问题。"
        prompt = "什么是边界值测试？"
        
        response = await client.generate_response(
            prompt=prompt,
            system_prompt=system_prompt,
            temperature=0.2
        )
        
        app_logger.info(f"系统提示词测试成功")
        app_logger.info(f"响应内容: {response[:200]}...")
        
        return True
        
    except Exception as e:
        app_logger.error(f"系统提示词测试失败: {str(e)}")
        return False


async def test_thinking_chain_generation():
    """测试思维链生成"""
    app_logger.info("=== 测试思维链生成 ===")
    
    try:
        client = await get_ollama_client()
        
        task = "设计一个用户注册功能的测试方案"
        context = "用户需要输入用户名、密码、邮箱进行注册，系统需要验证输入格式和唯一性"
        
        thinking_chain = await client.generate_thinking_chain(task, context, max_steps=4)
        
        if thinking_chain:
            app_logger.info(f"思维链生成成功，共 {len(thinking_chain)} 个步骤:")
            for chain in thinking_chain:
                app_logger.info(f"步骤{chain.step}: {chain.content}")
                app_logger.info(f"推理: {chain.reasoning}")
                app_logger.info(f"置信度: {chain.confidence}")
                app_logger.info("---")
            return True
        else:
            app_logger.error("思维链生成失败，返回空列表")
            return False
            
    except Exception as e:
        app_logger.error(f"思维链生成测试失败: {str(e)}")
        return False


async def test_reflection_generation():
    """测试反思生成"""
    app_logger.info("=== 测试反思生成 ===")
    
    try:
        client = await get_ollama_client()
        
        task = "为用户登录功能生成测试用例"
        result = """
        已生成以下测试用例：
        1. 正常登录测试 - 使用正确的用户名和密码
        2. 密码错误测试 - 使用正确用户名但错误密码
        3. 用户名不存在测试 - 使用不存在的用户名
        """
        
        reflection = await client.generate_reflection(task, result)
        
        app_logger.info("反思生成成功:")
        app_logger.info(f"反思问题: {reflection.question}")
        app_logger.info(f"反思答案: {reflection.answer}")
        app_logger.info(f"改进建议: {reflection.improvement or '无'}")
        app_logger.info(f"评分: {reflection.score}")
        
        return True
        
    except Exception as e:
        app_logger.error(f"反思生成测试失败: {str(e)}")
        return False


async def test_chat_completion():
    """测试聊天完成功能"""
    app_logger.info("=== 测试聊天完成功能 ===")
    
    try:
        client = await get_ollama_client()
        
        messages = [
            {"role": "system", "content": "你是一个专业的测试工程师助手。"},
            {"role": "user", "content": "我需要为一个电商网站的购物车功能设计测试用例，你能帮我分析一下需要考虑哪些测试场景吗？"},
            {"role": "assistant", "content": "好的，购物车功能需要考虑以下几个主要测试场景：1. 添加商品到购物车 2. 修改商品数量 3. 删除商品 4. 清空购物车 5. 购物车数据持久化"},
            {"role": "user", "content": "请详细说明添加商品到购物车这个场景需要考虑的测试点。"}
        ]
        
        response = await client.chat_completion(messages, temperature=0.3)
        
        app_logger.info("聊天完成测试成功")
        app_logger.info(f"响应内容: {response[:300]}...")
        
        return True
        
    except Exception as e:
        app_logger.error(f"聊天完成测试失败: {str(e)}")
        return False


async def test_stream_generation():
    """测试流式生成功能"""
    app_logger.info("=== 测试流式生成功能 ===")
    
    try:
        client = await get_ollama_client()
        
        prompt = "请详细解释什么是自动化测试，包括其优势和挑战。"
        
        app_logger.info("开始流式生成...")
        response = await client.generate_response(prompt, stream=True, temperature=0.3)
        
        app_logger.info(f"流式生成完成，总长度: {len(response)} 字符")
        app_logger.info(f"响应内容: {response[:200]}...")
        
        return True
        
    except Exception as e:
        app_logger.error(f"流式生成测试失败: {str(e)}")
        return False


async def test_json_output():
    """测试JSON格式输出"""
    app_logger.info("=== 测试JSON格式输出 ===")
    
    try:
        client = await get_ollama_client()
        
        system_prompt = """
你是一个专业的测试用例设计专家。请严格按照以下JSON格式输出测试用例：
{
  "test_case_id": "TC-001",
  "name": "测试用例名称",
  "description": "测试用例描述",
  "type": "positive/negative/boundary",
  "priority": "high/medium/low",
  "steps": [
    {"step": 1, "action": "操作描述", "expected": "预期结果"}
  ]
}
"""
        
        prompt = "为用户登录功能设计一个正向测试用例，要求输出标准JSON格式。"
        
        response = await client.generate_response(
            prompt=prompt,
            system_prompt=system_prompt,
            temperature=0.1
        )
        
        app_logger.info("JSON格式输出测试成功")
        app_logger.info(f"响应内容: {response}")
        
        # 尝试解析JSON
        import json
        try:
            json.loads(response)
            app_logger.info("JSON格式验证通过")
        except:
            app_logger.warning("JSON格式验证失败，但生成成功")
        
        return True
        
    except Exception as e:
        app_logger.error(f"JSON格式输出测试失败: {str(e)}")
        return False


async def test_performance():
    """测试性能"""
    app_logger.info("=== 测试性能 ===")
    
    try:
        client = await get_ollama_client()
        
        import time
        
        # 测试多次生成的平均时间
        prompts = [
            "什么是单元测试？",
            "什么是集成测试？",
            "什么是系统测试？",
            "什么是验收测试？",
            "什么是回归测试？"
        ]
        
        total_time = 0
        successful_requests = 0
        
        for i, prompt in enumerate(prompts):
            try:
                start_time = time.time()
                response = await client.generate_response(prompt, temperature=0.3)
                end_time = time.time()
                
                request_time = end_time - start_time
                total_time += request_time
                successful_requests += 1
                
                app_logger.info(f"请求 {i+1}: {request_time:.2f}秒, 响应长度: {len(response)} 字符")
                
            except Exception as e:
                app_logger.error(f"请求 {i+1} 失败: {str(e)}")
        
        if successful_requests > 0:
            avg_time = total_time / successful_requests
            app_logger.info(f"性能测试完成: 平均响应时间 {avg_time:.2f}秒, 成功率 {successful_requests}/{len(prompts)}")
            return True
        else:
            app_logger.error("所有性能测试请求都失败了")
            return False
        
    except Exception as e:
        app_logger.error(f"性能测试失败: {str(e)}")
        return False


async def run_all_tests():
    """运行所有测试"""
    app_logger.info("开始运行Ollama完整测试套件...")
    
    tests = [
        ("基础生成功能", test_basic_generation),
        ("系统提示词功能", test_system_prompt),
        ("思维链生成", test_thinking_chain_generation),
        ("反思生成", test_reflection_generation),
        ("聊天完成功能", test_chat_completion),
        ("流式生成功能", test_stream_generation),
        ("JSON格式输出", test_json_output),
        ("性能测试", test_performance)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            app_logger.info(f"\n开始测试: {test_name}")
            if await test_func():
                app_logger.info(f"✅ {test_name} - 通过")
                passed_tests += 1
            else:
                app_logger.error(f"❌ {test_name} - 失败")
        except Exception as e:
            app_logger.error(f"❌ {test_name} - 异常: {str(e)}")
    
    app_logger.info(f"\n测试完成: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        app_logger.info("🎉 所有测试都通过了！")
        return True
    elif passed_tests >= total_tests * 0.7:  # 70%通过率
        app_logger.warning("⚠️  大部分测试通过，系统基本可用")
        return True
    else:
        app_logger.error("💥 测试失败过多，请检查Ollama配置")
        return False


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Ollama功能测试工具")
    parser.add_argument(
        "test_type", 
        nargs="?", 
        choices=["all", "basic", "thinking", "reflection", "chat", "stream", "json", "performance"], 
        default="all",
        help="测试类型"
    )
    
    args = parser.parse_args()
    
    try:
        if args.test_type == "all":
            success = await run_all_tests()
        elif args.test_type == "basic":
            success = await test_basic_generation()
        elif args.test_type == "thinking":
            success = await test_thinking_chain_generation()
        elif args.test_type == "reflection":
            success = await test_reflection_generation()
        elif args.test_type == "chat":
            success = await test_chat_completion()
        elif args.test_type == "stream":
            success = await test_stream_generation()
        elif args.test_type == "json":
            success = await test_json_output()
        elif args.test_type == "performance":
            success = await test_performance()
        
        if success:
            app_logger.info(f"测试 '{args.test_type}' 执行成功")
            sys.exit(0)
        else:
            app_logger.error(f"测试 '{args.test_type}' 执行失败")
            sys.exit(1)
            
    except KeyboardInterrupt:
        app_logger.info("测试被用户中断")
        sys.exit(1)
    except Exception as e:
        app_logger.error(f"测试过程中发生错误: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())