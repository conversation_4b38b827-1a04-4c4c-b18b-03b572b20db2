#!/usr/bin/env python3
"""
简化的工作流程演示脚本
演示从需求分析到用例评审的完整流程（简化版）
"""

import asyncio
import sys
import json
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.logger import get_agent_logger
from core.memory import get_memory_manager
from core.traceability import get_traceability_manager
from services.ollama_client import get_ollama_client

logger = get_agent_logger("SimpleWorkflowDemo")

class SimpleWorkflowDemo:
    """简化的工作流程演示"""
    
    def __init__(self):
        self.session_id = f"demo_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.memory_manager = None
        self.traceability_manager = None
        self.ollama_client = None
    
    async def initialize(self):
        """初始化系统组件"""
        logger.info("🔧 初始化系统组件...")
        
        self.memory_manager = await get_memory_manager()
        self.traceability_manager = await get_traceability_manager()
        self.ollama_client = await get_ollama_client()
        
        logger.info("✅ 系统组件初始化完成")
    
    async def step1_requirement_analysis(self, requirement_text: str):
        """第一步：需求分析"""
        logger.info("\n" + "="*60)
        logger.info("🔍 第一步：需求分析")
        logger.info("="*60)
        logger.info(f"输入需求：\n{requirement_text}")
        
        # 模拟需求分析过程
        logger.info("📝 正在分析需求...")
        
        # 简单的需求解析（实际应该使用LLM）
        requirements = []
        lines = requirement_text.strip().split('\n')
        for i, line in enumerate(lines, 1):
            if line.strip() and (line.strip().startswith(f"{i}.") or "：" in line):
                requirements.append({
                    "id": f"REQ_{i:03d}",
                    "description": line.strip(),
                    "priority": "高" if i <= 3 else "中",
                    "type": "功能性需求",
                    "status": "待评审"
                })
        
        analysis_result = {
            "session_id": self.session_id,
            "original_text": requirement_text,
            "requirements": requirements,
            "total_count": len(requirements),
            "analysis_time": datetime.now().isoformat()
        }
        
        # 记录到日志（简化版本不存储到记忆系统）
        logger.debug(f"需求分析结果: {json.dumps(analysis_result, ensure_ascii=False, indent=2)}")
        
        logger.info(f"✅ 需求分析完成，提取到 {len(requirements)} 个需求")
        for req in requirements:
            logger.info(f"   - {req['id']}: {req['description'][:50]}...")
        
        return analysis_result
    
    async def step2_requirement_review(self, analysis_result):
        """第二步：需求评审"""
        logger.info("\n" + "="*60)
        logger.info("📋 第二步：需求评审")
        logger.info("="*60)
        
        requirements = analysis_result["requirements"]
        logger.info(f"📝 正在评审 {len(requirements)} 个需求...")
        
        # 模拟评审过程
        reviewed_requirements = []
        issues = []
        
        for req in requirements:
            # 简单的评审逻辑
            review_score = 85 if "登录" in req["description"] else 90
            
            reviewed_req = req.copy()
            reviewed_req.update({
                "review_score": review_score,
                "review_status": "通过" if review_score >= 80 else "需修改",
                "review_comments": "需求描述清晰" if review_score >= 80 else "需要更详细的描述"
            })
            
            if review_score < 80:
                issues.append(f"{req['id']}: {reviewed_req['review_comments']}")
            
            reviewed_requirements.append(reviewed_req)
        
        review_result = {
            "session_id": self.session_id,
            "reviewed_requirements": reviewed_requirements,
            "overall_score": sum(r["review_score"] for r in reviewed_requirements) / len(reviewed_requirements),
            "total_issues": len(issues),
            "issues": issues,
            "overall_status": "通过" if len(issues) == 0 else "有问题",
            "review_time": datetime.now().isoformat()
        }
        
        # 记录到日志（简化版本不存储到记忆系统）
        logger.debug(f"需求评审结果: {json.dumps(review_result, ensure_ascii=False, indent=2)}")
        
        logger.info(f"✅ 需求评审完成，总体评分: {review_result['overall_score']:.1f}")
        logger.info(f"   评审状态: {review_result['overall_status']}")
        if issues:
            logger.info(f"   发现问题: {len(issues)} 个")
            for issue in issues:
                logger.info(f"     - {issue}")
        
        return review_result
    
    async def step3_testcase_generation(self, analysis_result):
        """第三步：测试用例生成"""
        logger.info("\n" + "="*60)
        logger.info("🧪 第三步：测试用例生成")
        logger.info("="*60)
        
        requirements = analysis_result["requirements"]
        logger.info(f"📝 正在为 {len(requirements)} 个需求生成测试用例...")
        
        # 模拟测试用例生成
        testcases = []
        for i, req in enumerate(requirements, 1):
            # 为每个需求生成2-3个测试用例
            for j in range(1, 4):
                testcase = {
                    "id": f"TC_{i:03d}_{j:02d}",
                    "requirement_id": req["id"],
                    "title": f"测试{req['description'][:20]}...场景{j}",
                    "type": "正向测试" if j == 1 else ("负向测试" if j == 2 else "边界测试"),
                    "priority": req["priority"],
                    "steps": [
                        f"步骤1: 准备测试数据",
                        f"步骤2: 执行{req['description'][:10]}操作",
                        f"步骤3: 验证结果"
                    ],
                    "expected_result": f"期望结果：{req['description'][:20]}成功",
                    "status": "待执行"
                }
                testcases.append(testcase)
        
        generation_result = {
            "session_id": self.session_id,
            "testcases": testcases,
            "total_count": len(testcases),
            "coverage": {
                "requirement_coverage": "100%",
                "scenario_coverage": "85%"
            },
            "generation_time": datetime.now().isoformat()
        }
        
        # 记录到日志（简化版本不存储到记忆系统）
        logger.debug(f"测试用例生成结果: {json.dumps(generation_result, ensure_ascii=False, indent=2)}")
        
        logger.info(f"✅ 测试用例生成完成，共生成 {len(testcases)} 个测试用例")
        logger.info(f"   需求覆盖率: {generation_result['coverage']['requirement_coverage']}")
        logger.info(f"   场景覆盖率: {generation_result['coverage']['scenario_coverage']}")
        
        return generation_result
    
    async def step4_testcase_review(self, generation_result):
        """第四步：测试用例评审"""
        logger.info("\n" + "="*60)
        logger.info("✅ 第四步：测试用例评审")
        logger.info("="*60)
        
        testcases = generation_result["testcases"]
        logger.info(f"📝 正在评审 {len(testcases)} 个测试用例...")
        
        # 模拟评审过程
        reviewed_testcases = []
        review_issues = []
        
        for tc in testcases:
            # 简单的评审逻辑
            review_score = 88 if "正向" in tc["type"] else 85
            
            reviewed_tc = tc.copy()
            reviewed_tc.update({
                "review_score": review_score,
                "review_status": "通过" if review_score >= 80 else "需修改",
                "review_comments": "测试用例设计合理" if review_score >= 80 else "需要补充测试步骤"
            })
            
            if review_score < 80:
                review_issues.append(f"{tc['id']}: {reviewed_tc['review_comments']}")
            
            reviewed_testcases.append(reviewed_tc)
        
        final_review = {
            "session_id": self.session_id,
            "reviewed_testcases": reviewed_testcases,
            "overall_score": sum(tc["review_score"] for tc in reviewed_testcases) / len(reviewed_testcases),
            "total_issues": len(review_issues),
            "issues": review_issues,
            "final_status": "通过评审" if len(review_issues) == 0 else "需要修改",
            "review_time": datetime.now().isoformat()
        }
        
        # 记录到日志（简化版本不存储到记忆系统）
        logger.debug(f"测试用例评审结果: {json.dumps(final_review, ensure_ascii=False, indent=2)}")
        
        logger.info(f"✅ 测试用例评审完成，总体评分: {final_review['overall_score']:.1f}")
        logger.info(f"   最终状态: {final_review['final_status']}")
        if review_issues:
            logger.info(f"   发现问题: {len(review_issues)} 个")
        
        return final_review
    
    async def generate_summary(self, requirement_text, analysis_result, review_result, generation_result, final_review):
        """生成工作流程总结"""
        logger.info("\n" + "="*80)
        logger.info("📊 完整工作流程总结")
        logger.info("="*80)
        
        logger.info(f"🎯 会话ID: {self.session_id}")
        logger.info(f"📝 原始需求: {requirement_text[:100]}...")
        logger.info(f"🔍 需求分析: 提取 {len(analysis_result['requirements'])} 个需求")
        logger.info(f"📋 需求评审: 总体评分 {review_result['overall_score']:.1f}，状态 {review_result['overall_status']}")
        logger.info(f"🧪 用例生成: 生成 {len(generation_result['testcases'])} 个测试用例")
        logger.info(f"✅ 用例评审: 总体评分 {final_review['overall_score']:.1f}，状态 {final_review['final_status']}")
        
        # 尝试创建可追溯性链接（简化版本）
        try:
            # 先创建工件，再创建链接
            await self.traceability_manager.create_artifact(
                artifact_id=f"requirement_{self.session_id}",
                artifact_type="requirement",
                content=f"需求分析结果 - {len(analysis_result['requirements'])} 个需求",
                metadata={"session": self.session_id, "step": "analysis"}
            )

            await self.traceability_manager.create_artifact(
                artifact_id=f"testcase_{self.session_id}",
                artifact_type="testcase",
                content=f"测试用例生成结果 - {len(generation_result['testcases'])} 个用例",
                metadata={"session": self.session_id, "step": "generation"}
            )

            await self.traceability_manager.create_link(
                source_id=f"requirement_{self.session_id}",
                target_id=f"testcase_{self.session_id}",
                link_type="covers",
                metadata={"workflow": "complete", "session": self.session_id}
            )

            logger.info("🔗 可追溯性链接已建立")
        except Exception as e:
            logger.warning(f"创建可追溯性链接失败: {str(e)}，但不影响演示")
        logger.info("🎉 完整工作流程演示成功完成！")

async def main():
    """主函数"""
    # 预定义需求示例
    sample_requirement = """
用户登录功能需求：
1. 用户可以通过用户名和密码登录系统
2. 登录成功后跳转到主页面
3. 登录失败时显示错误提示
4. 支持记住密码功能
5. 连续登录失败3次后锁定账户30分钟
"""
    
    # 检查命令行参数
    requirement_text = sample_requirement
    for i, arg in enumerate(sys.argv):
        if arg == "--requirement" and i + 1 < len(sys.argv):
            requirement_text = sys.argv[i + 1]
            break
    
    demo = SimpleWorkflowDemo()
    
    try:
        # 初始化
        await demo.initialize()
        
        # 执行完整工作流程
        analysis_result = await demo.step1_requirement_analysis(requirement_text)
        review_result = await demo.step2_requirement_review(analysis_result)
        generation_result = await demo.step3_testcase_generation(analysis_result)
        final_review = await demo.step4_testcase_review(generation_result)
        
        # 生成总结
        await demo.generate_summary(requirement_text, analysis_result, review_result, generation_result, final_review)
        
        return True
        
    except Exception as e:
        logger.error(f"演示过程中发生异常: {type(e).__name__}: {str(e)}", exc_info=True)
        return False

if __name__ == "__main__":
    if "--help" in sys.argv or "-h" in sys.argv:
        print("简化的工作流程演示脚本")
        print("用法: python simple_workflow_demo.py [选项]")
        print("选项:")
        print('  --requirement "需求文本"  使用自定义需求')
        print("  --help, -h               显示帮助信息")
        print()
        print("示例:")
        print('  python simple_workflow_demo.py --requirement "用户注册功能需求"')
        sys.exit(0)
    
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("演示被用户中断")
        sys.exit(0)
    except Exception as e:
        logger.error(f"演示启动失败: {str(e)}")
        sys.exit(1)
