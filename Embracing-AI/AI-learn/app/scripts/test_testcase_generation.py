"""
测试用例生成Agent测试脚本
"""

import asyncio
import sys
from pathlib import Path
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.logger import app_logger
from agents.testcase_generation import TestCaseGenerationAgent


async def create_test_requirements():
    """创建测试需求数据"""
    return [
        {
            "id": 1,
            "trace_id": "R1-20250128-0001",
            "name": "用户登录功能",
            "type": "functional",
            "description": "用户能够通过用户名和密码登录系统，系统验证用户身份并返回相应结果",
            "acceptance_criteria": {
                "positive": ["用户输入正确的用户名和密码能够成功登录"],
                "negative": ["用户输入错误的用户名或密码无法登录"],
                "boundary": ["用户名长度在3-20字符之间", "密码长度在6-50字符之间"],
                "error": ["系统异常时显示友好错误信息", "网络异常时提供重试机制"]
            },
            "pending_scenarios": ["正常登录", "密码错误", "用户名不存在", "账户锁定", "并发登录"],
            "status": "approved",
            "risk_level": "high",
            "priority": "high"
        },
        {
            "id": 2,
            "trace_id": "R2-20250128-0002",
            "name": "数据查询功能",
            "type": "functional",
            "description": "用户能够根据条件查询数据，支持分页和排序",
            "acceptance_criteria": {
                "positive": ["查询条件正确时返回匹配的数据"],
                "negative": ["查询条件错误时返回空结果"],
                "boundary": ["单页最多显示100条记录", "查询条件最多10个"],
                "error": ["数据库异常时显示错误信息"]
            },
            "pending_scenarios": ["条件查询", "分页查询", "排序查询", "空结果查询"],
            "status": "approved",
            "risk_level": "medium",
            "priority": "medium"
        },
        {
            "id": 3,
            "trace_id": "R3-20250128-0003",
            "name": "系统性能要求",
            "type": "performance",
            "description": "系统应该支持1000个并发用户，响应时间不超过2秒",
            "acceptance_criteria": {
                "positive": ["正常负载下响应时间在2秒内"],
                "negative": ["超负载时不应该崩溃"],
                "boundary": ["1000并发用户时系统稳定"],
                "error": ["性能异常时记录日志"]
            },
            "pending_scenarios": ["负载测试", "压力测试", "并发测试", "响应时间测试"],
            "status": "approved",
            "risk_level": "high",
            "priority": "high"
        }
    ]


async def test_basic_generation():
    """测试基础生成功能"""
    app_logger.info("=== 测试基础生成功能 ===")
    
    try:
        # 创建Agent
        agent = TestCaseGenerationAgent("test_session_001")
        await agent.initialize()
        
        # 创建测试需求
        test_requirements = await create_test_requirements()
        
        # 执行生成
        result = await agent.process({
            "requirements": test_requirements,
            "feature_id": "login_feature",
            "config": {
                "batch_size": 3,
                "focus_methods": ["等价类划分", "边界值分析"]
            },
            "context": {
                "project_name": "用户管理系统",
                "test_environment": "staging"
            }
        })
        
        app_logger.info("基础生成完成")
        
        # 显示生成结果
        summary = result.get("generation_summary", {})
        app_logger.info(f"生成摘要: {summary}")
        
        new_testcases = result.get("new_test_cases", [])
        app_logger.info(f"新生成用例数: {len(new_testcases)}")
        
        # 显示前3个测试用例
        for i, tc in enumerate(new_testcases[:3]):
            app_logger.info(f"用例{i+1}: {tc.get('name', 'N/A')}")
            app_logger.info(f"  类型: {tc.get('type', 'N/A')}")
            app_logger.info(f"  优先级: {tc.get('priority', 'N/A')}")
            app_logger.info(f"  生成方法: {tc.get('generation_method', 'N/A')}")
            app_logger.info(f"  测试步骤数: {len(tc.get('test_steps', []))}")
        
        coverage_analysis = result.get("coverage_analysis", {})
        app_logger.info(f"覆盖率分析: {coverage_analysis.get('overall_coverage', 0)}%")
        
        # 清理资源
        await agent.cleanup()
        
        return True
        
    except Exception as e:
        app_logger.error(f"基础生成测试失败: {str(e)}")
        return False


async def test_design_methods():
    """测试不同设计方法"""
    app_logger.info("=== 测试不同设计方法 ===")
    
    try:
        # 创建Agent
        agent = TestCaseGenerationAgent("test_session_002")
        await agent.initialize()
        
        # 创建特定类型的需求
        method_requirements = [
            {
                "id": 1,
                "name": "输入验证功能",
                "type": "functional",
                "description": "验证用户输入的数据格式和范围，包含复杂的业务规则和条件判断",
                "acceptance_criteria": {
                    "positive": ["有效输入通过验证"],
                    "negative": ["无效输入被拒绝"],
                    "boundary": ["边界值正确处理"],
                    "error": ["异常情况正确处理"]
                },
                "pending_scenarios": ["有效输入", "无效输入", "边界值", "特殊字符"],
                "status": "approved",
                "risk_level": "medium",
                "priority": "high"
            }
        ]
        
        # 测试不同的设计方法
        methods_to_test = ["等价类划分", "边界值分析", "决策表测试", "错误推测"]
        
        for method in methods_to_test:
            app_logger.info(f"测试设计方法: {method}")
            
            result = await agent.process({
                "requirements": method_requirements,
                "config": {
                    "batch_size": 2,
                    "focus_methods": [method]
                }
            })
            
            new_testcases = result.get("new_test_cases", [])
            method_testcases = [tc for tc in new_testcases if tc.get("generation_method") == method]
            
            app_logger.info(f"  {method} 生成用例数: {len(method_testcases)}")
            
            if method_testcases:
                sample_tc = method_testcases[0]
                app_logger.info(f"  示例用例: {sample_tc.get('name', 'N/A')}")
                app_logger.info(f"  用例类型: {sample_tc.get('type', 'N/A')}")
        
        # 清理资源
        await agent.cleanup()
        
        return True
        
    except Exception as e:
        app_logger.error(f"设计方法测试失败: {str(e)}")
        return False


async def test_state_management():
    """测试状态管理功能"""
    app_logger.info("=== 测试状态管理功能 ===")
    
    try:
        # 创建Agent
        agent = TestCaseGenerationAgent("test_session_003")
        await agent.initialize()
        
        # 创建多场景需求
        multi_scenario_requirements = [
            {
                "id": 1,
                "name": "复杂业务流程",
                "type": "functional",
                "description": "包含多个步骤和状态的复杂业务流程",
                "acceptance_criteria": {
                    "positive": ["流程正常执行"],
                    "negative": ["异常情况处理"],
                    "boundary": ["边界条件处理"],
                    "error": ["错误恢复机制"]
                },
                "pending_scenarios": [
                    "场景1", "场景2", "场景3", "场景4", "场景5",
                    "场景6", "场景7", "场景8", "场景9", "场景10"
                ],
                "status": "approved",
                "risk_level": "high",
                "priority": "high"
            }
        ]
        
        # 第一轮生成
        result1 = await agent.process({
            "requirements": multi_scenario_requirements,
            "config": {"batch_size": 3}
        })
        
        app_logger.info("第一轮生成完成")
        app_logger.info(f"生成用例数: {len(result1.get('new_test_cases', []))}")
        app_logger.info(f"剩余场景数: {len(result1.get('remaining_scenarios', []))}")
        app_logger.info(f"是否还有更多: {result1.get('has_more', False)}")
        
        # 检查状态管理
        stats = await agent.get_generation_statistics()
        app_logger.info(f"生成统计: {stats}")
        
        # 第二轮生成（如果还有剩余场景）
        if result1.get('has_more', False):
            result2 = await agent.process({
                "requirements": multi_scenario_requirements,
                "config": {"batch_size": 4}
            })
            
            app_logger.info("第二轮生成完成")
            app_logger.info(f"生成用例数: {len(result2.get('new_test_cases', []))}")
            app_logger.info(f"剩余场景数: {len(result2.get('remaining_scenarios', []))}")
            app_logger.info(f"是否还有更多: {result2.get('has_more', False)}")
        
        # 清理资源
        await agent.cleanup()
        
        return True
        
    except Exception as e:
        app_logger.error(f"状态管理测试失败: {str(e)}")
        return False


async def test_coverage_analysis():
    """测试覆盖率分析"""
    app_logger.info("=== 测试覆盖率分析 ===")
    
    try:
        # 创建Agent
        agent = TestCaseGenerationAgent("test_session_004")
        await agent.initialize()
        
        # 创建测试需求
        coverage_requirements = await create_test_requirements()
        
        # 执行生成
        result = await agent.process({
            "requirements": coverage_requirements,
            "config": {"batch_size": 5}
        })
        
        # 分析覆盖率
        coverage_analysis = result.get("coverage_analysis", {})
        
        app_logger.info("覆盖率分析结果:")
        app_logger.info(f"  整体覆盖率: {coverage_analysis.get('overall_coverage', 0)}%")
        app_logger.info(f"  总场景数: {coverage_analysis.get('total_scenarios', 0)}")
        app_logger.info(f"  已覆盖场景数: {coverage_analysis.get('covered_scenarios', 0)}")
        app_logger.info(f"  剩余场景数: {coverage_analysis.get('remaining_scenarios', 0)}")
        app_logger.info(f"  总测试用例数: {coverage_analysis.get('total_testcases', 0)}")
        
        # 按需求分析覆盖率
        coverage_by_req = coverage_analysis.get("coverage_by_requirement", {})
        for req_id, req_coverage in coverage_by_req.items():
            app_logger.info(f"  需求{req_id}覆盖率: {req_coverage.get('coverage_percentage', 0)}%")
        
        # 清理资源
        await agent.cleanup()
        
        return True
        
    except Exception as e:
        app_logger.error(f"覆盖率分析测试失败: {str(e)}")
        return False


async def test_export_functionality():
    """测试导出功能"""
    app_logger.info("=== 测试导出功能 ===")
    
    try:
        # 创建Agent
        agent = TestCaseGenerationAgent("test_session_005")
        await agent.initialize()
        
        # 创建测试需求
        test_requirements = await create_test_requirements()
        
        # 执行生成
        result = await agent.process({
            "requirements": test_requirements,
            "config": {"batch_size": 3}
        })
        
        # 测试JSON导出
        json_export = await agent.export_testcases(result, "json")
        app_logger.info(f"JSON导出长度: {len(json_export)} 字符")
        
        # 测试Excel格式导出
        excel_export = await agent.export_testcases(result, "excel")
        app_logger.info(f"Excel格式导出长度: {len(excel_export)} 字符")
        
        # 测试Markdown导出
        md_export = await agent.export_testcases(result, "markdown")
        app_logger.info(f"Markdown导出长度: {len(md_export)} 字符")
        
        # 保存导出文件
        with open("test_testcases_export.json", "w", encoding="utf-8") as f:
            f.write(json_export)
        
        with open("test_testcases_export_excel.json", "w", encoding="utf-8") as f:
            f.write(excel_export)
        
        with open("test_testcases_export.md", "w", encoding="utf-8") as f:
            f.write(md_export)
        
        app_logger.info("测试用例导出文件已保存")
        
        # 清理资源
        await agent.cleanup()
        
        return True
        
    except Exception as e:
        app_logger.error(f"导出功能测试失败: {str(e)}")
        return False


async def test_iterative_improvement():
    """测试迭代改进功能"""
    app_logger.info("=== 测试迭代改进功能 ===")
    
    try:
        # 创建Agent
        agent = TestCaseGenerationAgent("test_session_006")
        await agent.initialize()
        
        # 创建简单需求用于测试迭代
        simple_requirements = [
            {
                "id": 1,
                "name": "简单功能",
                "type": "functional",
                "description": "一个简单的功能测试",
                "acceptance_criteria": {
                    "positive": ["正常工作"],
                    "negative": ["异常处理"],
                    "boundary": ["边界处理"],
                    "error": ["错误处理"]
                },
                "pending_scenarios": ["基础测试"],
                "status": "approved",
                "risk_level": "low",
                "priority": "medium"
            }
        ]
        
        # 执行生成
        result = await agent.process({
            "requirements": simple_requirements,
            "config": {"batch_size": 1}
        })
        
        app_logger.info("迭代改进测试完成")
        app_logger.info(f"最终版本: {result.get('version', 1)}")
        
        # 检查思维链和反思
        agent_state = await agent.get_state()
        thinking_steps = len(agent_state.thinking_chain)
        reflection_count = len(agent_state.reflection_results)
        
        app_logger.info(f"思维链步骤数: {thinking_steps}")
        app_logger.info(f"反思次数: {reflection_count}")
        
        # 清理资源
        await agent.cleanup()
        
        return True
        
    except Exception as e:
        app_logger.error(f"迭代改进测试失败: {str(e)}")
        return False


async def test_performance_requirements():
    """测试性能需求的用例生成"""
    app_logger.info("=== 测试性能需求的用例生成 ===")
    
    try:
        # 创建Agent
        agent = TestCaseGenerationAgent("test_session_007")
        await agent.initialize()
        
        # 创建性能需求
        performance_requirements = [
            {
                "id": 1,
                "name": "系统性能需求",
                "type": "performance",
                "description": "系统需要支持高并发和快速响应",
                "acceptance_criteria": {
                    "positive": ["正常负载下性能达标"],
                    "negative": ["超负载时不崩溃"],
                    "boundary": ["临界负载下稳定"],
                    "error": ["性能异常时恢复"]
                },
                "pending_scenarios": ["负载测试", "压力测试", "稳定性测试"],
                "status": "approved",
                "risk_level": "high",
                "priority": "critical"
            }
        ]
        
        # 执行生成
        result = await agent.process({
            "requirements": performance_requirements,
            "config": {"batch_size": 3}
        })
        
        # 分析性能测试用例
        new_testcases = result.get("new_test_cases", [])
        performance_testcases = [tc for tc in new_testcases if "性能" in tc.get("name", "") or tc.get("type") == "performance"]
        
        app_logger.info(f"性能测试用例数: {len(performance_testcases)}")
        
        for tc in performance_testcases:
            app_logger.info(f"  用例: {tc.get('name', 'N/A')}")
            app_logger.info(f"  方法: {tc.get('generation_method', 'N/A')}")
            app_logger.info(f"  标签: {tc.get('tags', [])}")
        
        # 清理资源
        await agent.cleanup()
        
        return True
        
    except Exception as e:
        app_logger.error(f"性能需求测试失败: {str(e)}")
        return False


async def run_all_tests():
    """运行所有测试"""
    app_logger.info("开始运行测试用例生成Agent测试套件")
    
    tests = [
        ("基础生成功能", test_basic_generation),
        ("不同设计方法", test_design_methods),
        ("状态管理功能", test_state_management),
        ("覆盖率分析", test_coverage_analysis),
        ("导出功能", test_export_functionality),
        ("迭代改进功能", test_iterative_improvement),
        ("性能需求处理", test_performance_requirements)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        app_logger.info(f"\n{'='*50}")
        app_logger.info(f"运行测试: {test_name}")
        app_logger.info(f"{'='*50}")
        
        try:
            success = await test_func()
            results[test_name] = "通过" if success else "失败"
            
        except Exception as e:
            app_logger.error(f"测试 {test_name} 执行异常: {str(e)}")
            results[test_name] = "异常"
    
    # 输出测试结果摘要
    app_logger.info(f"\n{'='*50}")
    app_logger.info("测试结果摘要")
    app_logger.info(f"{'='*50}")
    
    for test_name, result in results.items():
        status_icon = "✅" if result == "通过" else "❌"
        app_logger.info(f"{status_icon} {test_name}: {result}")
    
    # 统计
    passed = sum(1 for r in results.values() if r == "通过")
    total = len(results)
    
    app_logger.info(f"\n总计: {passed}/{total} 个测试通过")
    
    return results


if __name__ == "__main__":
    asyncio.run(run_all_tests())
