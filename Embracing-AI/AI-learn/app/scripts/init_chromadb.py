"""
ChromaDB初始化脚本
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.memory import get_vector_memory
from core.logger import app_logger
from config import get_settings

settings = get_settings()


async def init_chromadb():
    """初始化ChromaDB"""
    try:
        app_logger.info("开始初始化ChromaDB...")
        
        # 确保持久化目录存在
        persist_dir = Path(settings.chroma_persist_directory)
        persist_dir.mkdir(parents=True, exist_ok=True)
        app_logger.info(f"持久化目录: {persist_dir.absolute()}")
        
        # 初始化向量记忆系统
        memory = await get_vector_memory()
        
        # 获取集合统计信息
        stats = await memory.get_collection_stats()
        app_logger.info(f"ChromaDB初始化完成，统计信息: {stats}")
        
        return True
        
    except Exception as e:
        app_logger.error(f"ChromaDB初始化失败: {str(e)}")
        return False


async def test_chromadb():
    """测试ChromaDB功能"""
    try:
        app_logger.info("开始测试ChromaDB功能...")
        
        memory = await get_vector_memory()
        
        # 测试存储和搜索
        from models.schemas import RequirementModel, AcceptanceCriteria
        from models.enums import RequirementType, RequirementStatus
        
        # 创建测试需求
        test_requirement = RequirementModel(
            id=1,
            trace_id="TEST-REQ-001",
            name="用户登录功能",
            type=RequirementType.FUNCTIONAL,
            description="用户可以通过用户名和密码登录系统",
            acceptance_criteria=AcceptanceCriteria(
                positive=["用户输入正确的用户名和密码可以成功登录"],
                negative=["用户输入错误的用户名或密码无法登录"],
                boundary=["用户名长度限制在3-20个字符"],
                error=["系统应显示友好的错误提示信息"]
            ),
            pending_scenarios=["正常登录", "密码错误", "用户名不存在"],
            status=RequirementStatus.OK
        )
        
        # 存储测试需求
        doc_id = await memory.store_requirement(test_requirement, "test_session")
        app_logger.info(f"测试需求存储成功，文档ID: {doc_id}")
        
        # 搜索相似需求
        similar_reqs = await memory.search_similar_requirements("用户登录", "test_session", limit=3)
        app_logger.info(f"搜索到 {len(similar_reqs)} 个相似需求")
        
        # 获取统计信息
        stats = await memory.get_collection_stats()
        app_logger.info(f"测试完成，当前统计: {stats}")
        
        return True
        
    except Exception as e:
        app_logger.error(f"ChromaDB测试失败: {str(e)}")
        return False


async def reset_chromadb():
    """重置ChromaDB（清空所有数据）"""
    try:
        app_logger.warning("开始重置ChromaDB...")
        
        import chromadb
        from chromadb.config import Settings
        
        # 创建客户端
        client = chromadb.PersistentClient(
            path=settings.chroma_persist_directory,
            settings=Settings(allow_reset=True)
        )
        
        # 重置数据库
        client.reset()
        app_logger.warning("ChromaDB已重置，所有数据已清空")
        
        # 重新初始化
        await init_chromadb()
        
        return True
        
    except Exception as e:
        app_logger.error(f"ChromaDB重置失败: {str(e)}")
        return False


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="ChromaDB管理工具")
    parser.add_argument("action", choices=["init", "test", "reset"], help="执行的操作")
    
    args = parser.parse_args()
    
    if args.action == "init":
        success = await init_chromadb()
    elif args.action == "test":
        success = await test_chromadb()
    elif args.action == "reset":
        success = await reset_chromadb()
    
    if success:
        app_logger.info(f"操作 '{args.action}' 执行成功")
        sys.exit(0)
    else:
        app_logger.error(f"操作 '{args.action}' 执行失败")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())