"""
系统性能测试
测试系统在不同负载下的性能表现
"""

import asyncio
import time
import statistics
import sys
import json
from pathlib import Path
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
import psutil

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.logger import get_agent_logger
from core.memory import get_memory_manager
from core.traceability import get_traceability_manager
from agents import RequirementAnalysisAgent, TestCaseGenerationAgent

logger = get_agent_logger("PerformanceTest")


class PerformanceTest:
    """性能测试类"""
    
    def __init__(self):
        self.test_results = {}
        self.system_metrics = []
    
    async def test_memory_performance(self, num_operations=100):
        """测试记忆系统性能"""
        logger.info(f"测试记忆系统性能 ({num_operations} 次操作)...")
        
        memory_manager = await get_memory_manager()
        
        # 测试存储性能
        store_times = []
        for i in range(num_operations):
            start_time = time.time()
            
            await memory_manager.store_agent_result(
                agent_id=f"perf_test_agent_{i}",
                task_id=f"perf_test_task_{i}",
                result={"data": f"test_data_{i}", "index": i}
            )
            
            end_time = time.time()
            store_times.append(end_time - start_time)
        
        # 测试检索性能
        retrieve_times = []
        for i in range(num_operations):
            start_time = time.time()
            
            result = await memory_manager.get_agent_result(
                agent_id=f"perf_test_agent_{i}",
                task_id=f"perf_test_task_{i}"
            )
            
            end_time = time.time()
            retrieve_times.append(end_time - start_time)
        
        # 计算统计数据
        self.test_results["memory_performance"] = {
            "operations": num_operations,
            "store_performance": {
                "avg_time": statistics.mean(store_times),
                "min_time": min(store_times),
                "max_time": max(store_times),
                "std_dev": statistics.stdev(store_times) if len(store_times) > 1 else 0
            },
            "retrieve_performance": {
                "avg_time": statistics.mean(retrieve_times),
                "min_time": min(retrieve_times),
                "max_time": max(retrieve_times),
                "std_dev": statistics.stdev(retrieve_times) if len(retrieve_times) > 1 else 0
            }
        }
        
        logger.info(f"记忆系统性能测试完成 - 平均存储时间: {statistics.mean(store_times):.4f}s, 平均检索时间: {statistics.mean(retrieve_times):.4f}s")
    
    async def test_traceability_performance(self, num_artifacts=50):
        """测试可追溯性系统性能"""
        logger.info(f"测试可追溯性系统性能 ({num_artifacts} 个工件)...")
        
        traceability_manager = await get_traceability_manager()
        
        from core.traceability import ArtifactType
        
        # 测试工件创建性能
        create_times = []
        artifact_ids = []
        
        for i in range(num_artifacts):
            start_time = time.time()
            
            artifact_id = await traceability_manager.create_artifact(
                artifact_type=ArtifactType.REQUIREMENT,
                name=f"性能测试需求_{i}",
                description=f"用于性能测试的需求 {i}",
                content={"test_data": f"data_{i}"},
                created_by="performance_test"
            )
            
            end_time = time.time()
            create_times.append(end_time - start_time)
            artifact_ids.append(artifact_id)
        
        # 测试工件查询性能
        query_times = []
        for artifact_id in artifact_ids:
            start_time = time.time()
            
            artifact = await traceability_manager.get_artifact(artifact_id)
            
            end_time = time.time()
            query_times.append(end_time - start_time)
        
        # 计算统计数据
        self.test_results["traceability_performance"] = {
            "artifacts": num_artifacts,
            "create_performance": {
                "avg_time": statistics.mean(create_times),
                "min_time": min(create_times),
                "max_time": max(create_times),
                "std_dev": statistics.stdev(create_times) if len(create_times) > 1 else 0
            },
            "query_performance": {
                "avg_time": statistics.mean(query_times),
                "min_time": min(query_times),
                "max_time": max(query_times),
                "std_dev": statistics.stdev(query_times) if len(query_times) > 1 else 0
            }
        }
        
        logger.info(f"可追溯性系统性能测试完成 - 平均创建时间: {statistics.mean(create_times):.4f}s, 平均查询时间: {statistics.mean(query_times):.4f}s")
    
    async def test_agent_performance(self, num_requests=10):
        """测试智能体性能"""
        logger.info(f"测试智能体性能 ({num_requests} 个请求)...")
        
        # 创建需求分析智能体
        agent = RequirementAnalysisAgent("perf_test_agent")
        await agent.initialize()
        
        test_requirements = [
            f"用户需要能够执行功能{i}，包括创建、编辑、删除和查看操作" 
            for i in range(num_requests)
        ]
        
        # 测试串行处理性能
        serial_times = []
        for i, requirement in enumerate(test_requirements):
            start_time = time.time()
            
            result = await agent.analyze_requirements(requirement)
            
            end_time = time.time()
            serial_times.append(end_time - start_time)
            
            logger.info(f"串行处理 {i+1}/{num_requests} 完成")
        
        # 测试并行处理性能
        start_time = time.time()
        
        tasks = [
            agent.analyze_requirements(req) 
            for req in test_requirements
        ]
        
        parallel_results = await asyncio.gather(*tasks)
        
        end_time = time.time()
        parallel_total_time = end_time - start_time
        
        # 计算统计数据
        self.test_results["agent_performance"] = {
            "requests": num_requests,
            "serial_performance": {
                "total_time": sum(serial_times),
                "avg_time": statistics.mean(serial_times),
                "min_time": min(serial_times),
                "max_time": max(serial_times),
                "std_dev": statistics.stdev(serial_times) if len(serial_times) > 1 else 0
            },
            "parallel_performance": {
                "total_time": parallel_total_time,
                "avg_time": parallel_total_time / num_requests,
                "speedup": sum(serial_times) / parallel_total_time if parallel_total_time > 0 else 0
            }
        }
        
        logger.info(f"智能体性能测试完成 - 串行总时间: {sum(serial_times):.2f}s, 并行总时间: {parallel_total_time:.2f}s, 加速比: {sum(serial_times) / parallel_total_time:.2f}x")
    
    async def test_concurrent_load(self, concurrent_users=5, requests_per_user=10):
        """测试并发负载"""
        logger.info(f"测试并发负载 ({concurrent_users} 个并发用户, 每用户 {requests_per_user} 个请求)...")
        
        async def user_simulation(user_id):
            """模拟用户操作"""
            agent = RequirementAnalysisAgent(f"load_test_user_{user_id}")
            await agent.initialize()
            
            user_times = []
            
            for i in range(requests_per_user):
                start_time = time.time()
                
                requirement = f"用户{user_id}的需求{i}: 系统需要支持特定功能"
                result = await agent.analyze_requirements(requirement)
                
                end_time = time.time()
                user_times.append(end_time - start_time)
            
            return user_times
        
        # 启动并发用户
        start_time = time.time()
        
        tasks = [user_simulation(i) for i in range(concurrent_users)]
        all_user_times = await asyncio.gather(*tasks)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 合并所有响应时间
        all_times = []
        for user_times in all_user_times:
            all_times.extend(user_times)
        
        # 计算统计数据
        total_requests = concurrent_users * requests_per_user
        
        self.test_results["concurrent_load"] = {
            "concurrent_users": concurrent_users,
            "requests_per_user": requests_per_user,
            "total_requests": total_requests,
            "total_time": total_time,
            "throughput": total_requests / total_time,
            "response_times": {
                "avg_time": statistics.mean(all_times),
                "min_time": min(all_times),
                "max_time": max(all_times),
                "p95_time": sorted(all_times)[int(len(all_times) * 0.95)],
                "p99_time": sorted(all_times)[int(len(all_times) * 0.99)],
                "std_dev": statistics.stdev(all_times) if len(all_times) > 1 else 0
            }
        }
        
        logger.info(f"并发负载测试完成 - 吞吐量: {total_requests / total_time:.2f} req/s, 平均响应时间: {statistics.mean(all_times):.4f}s")
    
    def monitor_system_resources(self):
        """监控系统资源使用"""
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        metrics = {
            "timestamp": datetime.now().isoformat(),
            "cpu_percent": cpu_percent,
            "memory_percent": memory.percent,
            "memory_used_gb": memory.used / (1024**3),
            "memory_available_gb": memory.available / (1024**3),
            "disk_percent": disk.percent,
            "disk_used_gb": disk.used / (1024**3),
            "disk_free_gb": disk.free / (1024**3)
        }
        
        self.system_metrics.append(metrics)
        return metrics
    
    async def run_all_performance_tests(self):
        """运行所有性能测试"""
        logger.info("开始系统性能测试...")
        
        # 记录初始系统状态
        initial_metrics = self.monitor_system_resources()
        logger.info(f"初始系统状态 - CPU: {initial_metrics['cpu_percent']}%, 内存: {initial_metrics['memory_percent']}%")
        
        try:
            # 记忆系统性能测试
            await self.test_memory_performance(50)
            self.monitor_system_resources()
            
            # 可追溯性系统性能测试
            await self.test_traceability_performance(30)
            self.monitor_system_resources()
            
            # 智能体性能测试
            await self.test_agent_performance(5)
            self.monitor_system_resources()
            
            # 并发负载测试
            await self.test_concurrent_load(3, 5)
            final_metrics = self.monitor_system_resources()
            
            logger.info(f"最终系统状态 - CPU: {final_metrics['cpu_percent']}%, 内存: {final_metrics['memory_percent']}%")
            
            # 生成性能报告
            await self.generate_performance_report()
            
            return True
            
        except Exception as e:
            logger.error(f"性能测试失败: {str(e)}")
            return False
    
    async def generate_performance_report(self):
        """生成性能测试报告"""
        report = {
            "test_summary": {
                "test_time": datetime.now().isoformat(),
                "total_tests": len(self.test_results),
                "system_info": {
                    "cpu_count": psutil.cpu_count(),
                    "memory_total_gb": psutil.virtual_memory().total / (1024**3),
                    "python_version": sys.version
                }
            },
            "performance_results": self.test_results,
            "system_metrics": self.system_metrics,
            "recommendations": self._generate_performance_recommendations()
        }
        
        # 保存报告
        report_path = project_root / "app" / "logs" / f"performance_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        report_path.parent.mkdir(exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"性能测试报告已保存到: {report_path}")
        
        # 输出简要报告
        self._print_performance_summary()
    
    def _generate_performance_recommendations(self):
        """生成性能优化建议"""
        recommendations = []
        
        # 检查记忆系统性能
        if "memory_performance" in self.test_results:
            avg_store_time = self.test_results["memory_performance"]["store_performance"]["avg_time"]
            if avg_store_time > 0.1:
                recommendations.append("记忆系统存储性能较慢，建议优化ChromaDB配置或考虑批量操作")
        
        # 检查智能体性能
        if "agent_performance" in self.test_results:
            speedup = self.test_results["agent_performance"]["parallel_performance"]["speedup"]
            if speedup < 2:
                recommendations.append("智能体并行处理效果不明显，建议检查异步实现或增加并发限制")
        
        # 检查并发性能
        if "concurrent_load" in self.test_results:
            throughput = self.test_results["concurrent_load"]["throughput"]
            if throughput < 1:
                recommendations.append("系统吞吐量较低，建议优化数据库连接池和异步处理")
        
        # 检查系统资源使用
        if self.system_metrics:
            max_memory = max(m["memory_percent"] for m in self.system_metrics)
            if max_memory > 80:
                recommendations.append("内存使用率较高，建议优化内存管理或增加系统内存")
        
        if not recommendations:
            recommendations.append("系统性能表现良好，无明显性能瓶颈")
        
        return recommendations
    
    def _print_performance_summary(self):
        """打印性能测试摘要"""
        logger.info("=" * 60)
        logger.info("性能测试结果摘要")
        logger.info("=" * 60)
        
        for test_name, results in self.test_results.items():
            logger.info(f"\n{test_name.upper()}:")
            
            if test_name == "memory_performance":
                logger.info(f"  存储平均时间: {results['store_performance']['avg_time']:.4f}s")
                logger.info(f"  检索平均时间: {results['retrieve_performance']['avg_time']:.4f}s")
            
            elif test_name == "traceability_performance":
                logger.info(f"  创建平均时间: {results['create_performance']['avg_time']:.4f}s")
                logger.info(f"  查询平均时间: {results['query_performance']['avg_time']:.4f}s")
            
            elif test_name == "agent_performance":
                logger.info(f"  串行平均时间: {results['serial_performance']['avg_time']:.4f}s")
                logger.info(f"  并行加速比: {results['parallel_performance']['speedup']:.2f}x")
            
            elif test_name == "concurrent_load":
                logger.info(f"  吞吐量: {results['throughput']:.2f} req/s")
                logger.info(f"  平均响应时间: {results['response_times']['avg_time']:.4f}s")
                logger.info(f"  P95响应时间: {results['response_times']['p95_time']:.4f}s")


async def main():
    """主测试函数"""
    perf_test = PerformanceTest()
    
    try:
        success = await perf_test.run_all_performance_tests()
        return success
    except Exception as e:
        logger.error(f"性能测试执行失败: {str(e)}")
        return False


if __name__ == "__main__":
    # 运行性能测试
    success = asyncio.run(main())
    sys.exit(0 if success else 1)