"""
Ollama本地模型部署脚本
"""

import asyncio
import sys
import subprocess
import time
import platform
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.logger import app_logger
from config import get_settings
from services.ollama_client import get_ollama_client

settings = get_settings()


def check_ollama_installed() -> bool:
    """检查Ollama是否已安装"""
    try:
        result = subprocess.run(["ollama", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            app_logger.info(f"Ollama已安装: {result.stdout.strip()}")
            return True
        else:
            app_logger.warning("Ollama未安装")
            return False
    except FileNotFoundError:
        app_logger.warning("Ollama未安装")
        return False


def install_ollama():
    """安装Ollama"""
    try:
        system = platform.system().lower()
        app_logger.info(f"检测到操作系统: {system}")
        
        if system == "darwin":  # macOS
            app_logger.info("在macOS上安装Ollama...")
            # 使用curl安装
            install_cmd = "curl -fsSL https://ollama.ai/install.sh | sh"
            result = subprocess.run(install_cmd, shell=True, capture_output=True, text=True)
            
        elif system == "linux":  # Linux
            app_logger.info("在Linux上安装Ollama...")
            install_cmd = "curl -fsSL https://ollama.ai/install.sh | sh"
            result = subprocess.run(install_cmd, shell=True, capture_output=True, text=True)
            
        elif system == "windows":  # Windows
            app_logger.info("在Windows上安装Ollama...")
            app_logger.warning("请手动下载并安装Ollama: https://ollama.ai/download/windows")
            return False
            
        else:
            app_logger.error(f"不支持的操作系统: {system}")
            return False
        
        if result.returncode == 0:
            app_logger.info("Ollama安装成功")
            return True
        else:
            app_logger.error(f"Ollama安装失败: {result.stderr}")
            return False
            
    except Exception as e:
        app_logger.error(f"安装Ollama时发生错误: {str(e)}")
        return False


def start_ollama_service():
    """启动Ollama服务"""
    try:
        app_logger.info("启动Ollama服务...")
        
        # 检查服务是否已经运行
        try:
            result = subprocess.run(["pgrep", "-f", "ollama"], capture_output=True, text=True)
            if result.returncode == 0:
                app_logger.info("Ollama服务已在运行")
                return True
        except:
            pass
        
        # 启动服务
        if platform.system().lower() == "darwin":
            # macOS上启动服务
            subprocess.Popen(["ollama", "serve"], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        else:
            # Linux上启动服务
            subprocess.Popen(["ollama", "serve"], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        
        # 等待服务启动
        app_logger.info("等待Ollama服务启动...")
        time.sleep(5)
        
        return True
        
    except Exception as e:
        app_logger.error(f"启动Ollama服务失败: {str(e)}")
        return False


def pull_model(model_name: str) -> bool:
    """拉取指定模型"""
    try:
        app_logger.info(f"开始拉取模型: {model_name}")
        
        # 执行拉取命令
        result = subprocess.run(
            ["ollama", "pull", model_name], 
            capture_output=True, 
            text=True,
            timeout=1800  # 30分钟超时
        )
        
        if result.returncode == 0:
            app_logger.info(f"模型 {model_name} 拉取成功")
            return True
        else:
            app_logger.error(f"模型 {model_name} 拉取失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        app_logger.error(f"模型 {model_name} 拉取超时")
        return False
    except Exception as e:
        app_logger.error(f"拉取模型时发生错误: {str(e)}")
        return False


def list_models() -> list:
    """列出已安装的模型"""
    try:
        result = subprocess.run(["ollama", "list"], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')[1:]  # 跳过标题行
            models = []
            for line in lines:
                if line.strip():
                    parts = line.split()
                    if parts:
                        models.append(parts[0])
            
            app_logger.info(f"已安装的模型: {models}")
            return models
        else:
            app_logger.error(f"列出模型失败: {result.stderr}")
            return []
            
    except Exception as e:
        app_logger.error(f"列出模型时发生错误: {str(e)}")
        return []


async def test_model_connection():
    """测试模型连接"""
    try:
        app_logger.info("测试模型连接...")
        
        client = await get_ollama_client()
        
        # 健康检查
        if not await client.health_check():
            app_logger.error("模型健康检查失败")
            return False
        
        # 测试简单生成
        test_prompt = "请简单介绍一下你自己，用中文回答。"
        response = await client.generate_response(test_prompt, temperature=0.3)
        
        app_logger.info(f"测试生成成功，响应长度: {len(response)} 字符")
        app_logger.info(f"响应内容预览: {response[:100]}...")
        
        return True
        
    except Exception as e:
        app_logger.error(f"测试模型连接失败: {str(e)}")
        return False


async def test_thinking_chain():
    """测试思维链功能"""
    try:
        app_logger.info("测试思维链功能...")
        
        client = await get_ollama_client()
        
        task = "分析用户登录功能的测试需求"
        context = "用户可以通过用户名和密码登录系统，需要考虑正常登录、密码错误、账户锁定等场景"
        
        thinking_chain = await client.generate_thinking_chain(task, context, max_steps=3)
        
        if thinking_chain:
            app_logger.info(f"思维链生成成功，共 {len(thinking_chain)} 个步骤:")
            for i, chain in enumerate(thinking_chain):
                app_logger.info(f"  步骤{chain.step}: {chain.content[:50]}... (置信度: {chain.confidence})")
            return True
        else:
            app_logger.error("思维链生成失败")
            return False
            
    except Exception as e:
        app_logger.error(f"测试思维链功能失败: {str(e)}")
        return False


async def test_reflection():
    """测试反思功能"""
    try:
        app_logger.info("测试反思功能...")
        
        client = await get_ollama_client()
        
        task = "生成用户登录功能的测试用例"
        result = "已生成3个测试用例：正常登录、密码错误、用户名不存在"
        
        reflection = await client.generate_reflection(task, result)
        
        app_logger.info(f"反思生成成功:")
        app_logger.info(f"  问题: {reflection.question[:50]}...")
        app_logger.info(f"  答案: {reflection.answer[:50]}...")
        app_logger.info(f"  评分: {reflection.score}")
        if reflection.improvement:
            app_logger.info(f"  改进建议: {reflection.improvement[:50]}...")
        
        return True
        
    except Exception as e:
        app_logger.error(f"测试反思功能失败: {str(e)}")
        return False


async def setup_ollama():
    """完整的Ollama设置流程"""
    app_logger.info("开始Ollama设置流程...")
    
    # 1. 检查是否已安装
    if not check_ollama_installed():
        app_logger.info("Ollama未安装，开始安装...")
        if not install_ollama():
            app_logger.error("Ollama安装失败")
            return False
    
    # 2. 启动服务
    if not start_ollama_service():
        app_logger.error("Ollama服务启动失败")
        return False
    
    # 3. 检查模型是否存在
    models = list_models()
    if settings.ollama_model not in models:
        app_logger.info(f"模型 {settings.ollama_model} 不存在，开始拉取...")
        if not pull_model(settings.ollama_model):
            app_logger.error(f"模型 {settings.ollama_model} 拉取失败")
            return False
    
    # 4. 测试连接
    if not await test_model_connection():
        app_logger.error("模型连接测试失败")
        return False
    
    # 5. 测试高级功能
    if not await test_thinking_chain():
        app_logger.warning("思维链功能测试失败，但基础功能正常")
    
    if not await test_reflection():
        app_logger.warning("反思功能测试失败，但基础功能正常")
    
    app_logger.info("Ollama设置完成！")
    return True


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Ollama设置和测试工具")
    parser.add_argument("action", choices=["setup", "test", "pull", "list"], help="执行的操作")
    parser.add_argument("--model", help="指定模型名称（用于pull操作）")
    
    args = parser.parse_args()
    
    try:
        if args.action == "setup":
            success = await setup_ollama()
        elif args.action == "test":
            success = await test_model_connection()
        elif args.action == "pull":
            model_name = args.model or settings.ollama_model
            success = pull_model(model_name)
        elif args.action == "list":
            models = list_models()
            success = len(models) > 0
        
        if success:
            app_logger.info(f"操作 '{args.action}' 执行成功")
            sys.exit(0)
        else:
            app_logger.error(f"操作 '{args.action}' 执行失败")
            sys.exit(1)
            
    except KeyboardInterrupt:
        app_logger.info("操作被用户中断")
        sys.exit(1)
    except Exception as e:
        app_logger.error(f"执行过程中发生错误: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())