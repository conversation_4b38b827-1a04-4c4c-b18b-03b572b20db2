"""
测试用例评审Agent测试脚本
"""

import asyncio
import sys
from pathlib import Path
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.logger import app_logger
from agents.testcase_review import TestCaseReviewAgent


async def create_test_testcases():
    """创建测试用例数据"""
    return [
        {
            "id": "TC-001-001",
            "trace_id": "T1-001-20250128-0001",
            "name": "用户登录功能-正常登录测试",
            "description": "验证用户使用正确的用户名和密码能够成功登录系统",
            "requirement_ids": [1],
            "type": "functional",
            "priority": "high",
            "status": "draft",
            "preconditions": ["系统正常运行", "用户账户已创建"],
            "test_steps": [
                {
                    "step_number": 1,
                    "action": "打开登录页面",
                    "expected_result": "登录页面正常显示"
                },
                {
                    "step_number": 2,
                    "action": "输入正确的用户名和密码",
                    "expected_result": "用户名和密码输入完成"
                },
                {
                    "step_number": 3,
                    "action": "点击登录按钮",
                    "expected_result": "成功登录，跳转到主页面"
                }
            ],
            "expected_result": "用户成功登录系统，显示主页面",
            "tags": ["登录", "功能测试", "正常流程"],
            "generation_method": "等价类划分",
            "coverage_scenarios": ["正常登录"]
        },
        {
            "id": "TC-001-002",
            "trace_id": "T1-002-20250128-0002",
            "name": "用户登录功能-密码错误测试",
            "description": "验证用户输入错误密码时系统的处理",
            "requirement_ids": [1],
            "type": "negative",
            "priority": "medium",
            "status": "draft",
            "preconditions": ["系统正常运行", "用户账户已创建"],
            "test_steps": [
                {
                    "step_number": 1,
                    "action": "打开登录页面",
                    "expected_result": "登录页面正常显示"
                },
                {
                    "step_number": 2,
                    "action": "输入正确的用户名和错误的密码",
                    "expected_result": "用户名和密码输入完成"
                },
                {
                    "step_number": 3,
                    "action": "点击登录按钮",
                    "expected_result": "登录失败，显示错误提示信息"
                }
            ],
            "expected_result": "系统显示密码错误提示，不允许登录",
            "tags": ["登录", "异常测试", "密码错误"],
            "generation_method": "错误推测",
            "coverage_scenarios": ["密码错误"]
        },
        {
            "id": "TC-002-001",
            "name": "数据查询功能测试",  # 缺少description
            "requirement_ids": [2],
            "type": "functional",
            "priority": "medium",
            "status": "draft",
            "test_steps": [
                {
                    "step_number": 1,
                    "action": "执行查询",  # 模糊的步骤描述
                    "expected_result": "正常"  # 模糊的预期结果
                }
            ],
            "expected_result": "查询成功",
            "tags": ["查询"],  # 缺乏具体意义的标签
            "generation_method": "场景测试",
            "coverage_scenarios": ["条件查询"]
        },
        {
            "id": "TC-003-001",
            "trace_id": "T3-001-20250128-0003",
            "name": "性能测试用例",
            "description": "验证系统在高负载下的性能表现",
            "requirement_ids": [3],
            "type": "performance",
            "priority": "low",  # 性能测试优先级过低
            "status": "draft",
            "preconditions": ["测试环境准备", "性能监控工具就绪"],
            "test_steps": [
                {
                    "step_number": 1,
                    "action": "启动性能测试工具",
                    "expected_result": "工具启动成功"
                },
                {
                    "step_number": 2,
                    "action": "模拟1000个并发用户",
                    "expected_result": "并发用户模拟成功"
                },
                {
                    "step_number": 3,
                    "action": "监控系统响应时间",
                    "expected_result": "响应时间在2秒内"
                }
            ],
            "expected_result": "系统在1000并发下响应时间不超过2秒",
            "tags": ["性能测试", "并发测试", "负载测试"],
            "generation_method": "边界值分析",
            "coverage_scenarios": ["负载测试"]
        }
    ]


async def test_basic_review():
    """测试基础评审功能"""
    app_logger.info("=== 测试基础评审功能 ===")
    
    try:
        # 创建Agent
        agent = TestCaseReviewAgent("test_session_001")
        await agent.initialize()
        
        # 创建测试用例
        test_testcases = await create_test_testcases()
        
        # 执行评审
        result = await agent.process({
            "new_test_cases": test_testcases,
            "feature_id": "login_feature",
            "remaining_scenarios": ["边界值测试", "异常处理"],
            "context": {
                "project_name": "用户管理系统",
                "project_type": "web_application"
            },
            "review_config": {
                "strict_mode": True,
                "focus_areas": ["completeness", "executability"]
            }
        })
        
        app_logger.info("基础评审完成")
        
        # 显示评审结果
        summary = result.get("review_summary", {})
        app_logger.info(f"评审摘要: {summary}")
        
        quality_metrics = result.get("quality_metrics", {})
        app_logger.info(f"质量指标: 平均分数={quality_metrics.get('average_quality_score', 0):.2f}")
        app_logger.info(f"  完整性: {quality_metrics.get('completeness_score', 0):.2f}")
        app_logger.info(f"  可执行性: {quality_metrics.get('executability_score', 0):.2f}")
        app_logger.info(f"  可维护性: {quality_metrics.get('maintainability_score', 0):.2f}")
        app_logger.info(f"  业务逻辑: {quality_metrics.get('business_logic_score', 0):.2f}")
        
        # 显示问题统计
        issues = result.get("review_issues", [])
        app_logger.info(f"发现问题数: {len(issues)}")
        
        # 显示前3个问题
        for i, issue in enumerate(issues[:3]):
            app_logger.info(f"问题{i+1}: {issue.get('description', 'N/A')}")
            app_logger.info(f"  严重程度: {issue.get('severity', 'N/A')}")
            app_logger.info(f"  测试用例: {issue.get('testcase_name', 'N/A')}")
        
        # 显示改进建议
        recommendations = result.get("recommendations", [])
        app_logger.info(f"改进建议数: {len(recommendations)}")
        for i, rec in enumerate(recommendations[:3]):
            app_logger.info(f"建议{i+1}: {rec}")
        
        # 清理资源
        await agent.cleanup()
        
        return True
        
    except Exception as e:
        app_logger.error(f"基础评审测试失败: {str(e)}")
        return False


async def test_quality_metrics():
    """测试质量指标评估"""
    app_logger.info("=== 测试质量指标评估 ===")
    
    try:
        # 创建Agent
        agent = TestCaseReviewAgent("test_session_002")
        await agent.initialize()
        
        # 创建高质量测试用例
        high_quality_testcases = [
            {
                "id": "TC-HQ-001",
                "trace_id": "T-HQ-001-20250128-0001",
                "name": "高质量测试用例示例",
                "description": "这是一个完整、详细、可执行的高质量测试用例，用于验证用户登录功能的正常流程",
                "requirement_ids": [1],
                "type": "functional",
                "priority": "high",
                "status": "draft",
                "preconditions": ["系统正常运行", "测试数据已准备", "用户账户已创建并激活"],
                "test_steps": [
                    {
                        "step_number": 1,
                        "action": "在浏览器中打开登录页面 http://example.com/login",
                        "expected_result": "登录页面正常加载，显示用户名和密码输入框"
                    },
                    {
                        "step_number": 2,
                        "action": "在用户名输入框中输入有效用户名 'testuser'",
                        "expected_result": "用户名成功输入，输入框显示输入的用户名"
                    },
                    {
                        "step_number": 3,
                        "action": "在密码输入框中输入正确密码 'password123'",
                        "expected_result": "密码成功输入，输入框显示密码掩码"
                    },
                    {
                        "step_number": 4,
                        "action": "点击'登录'按钮",
                        "expected_result": "系统验证用户凭据，成功登录并跳转到用户主页"
                    },
                    {
                        "step_number": 5,
                        "action": "验证主页显示用户信息",
                        "expected_result": "主页正确显示用户名和欢迎信息"
                    }
                ],
                "expected_result": "用户成功登录系统，能够访问个人主页并看到正确的用户信息",
                "tags": ["登录功能", "正常流程", "用户认证", "功能测试"],
                "generation_method": "场景测试",
                "coverage_scenarios": ["正常登录流程"]
            }
        ]
        
        # 执行评审
        result = await agent.process({
            "new_test_cases": high_quality_testcases,
            "feature_id": "login_feature",
            "context": {"project_type": "web_application"}
        })
        
        # 分析质量指标
        quality_metrics = result.get("quality_metrics", {})
        
        app_logger.info("高质量测试用例评审结果:")
        app_logger.info(f"  平均质量分数: {quality_metrics.get('average_quality_score', 0):.2f}")
        app_logger.info(f"  通过测试用例数: {quality_metrics.get('passed_testcases', 0)}")
        app_logger.info(f"  失败测试用例数: {quality_metrics.get('failed_testcases', 0)}")
        
        # 清理资源
        await agent.cleanup()
        
        return True
        
    except Exception as e:
        app_logger.error(f"质量指标测试失败: {str(e)}")
        return False


async def test_issue_detection():
    """测试问题检测功能"""
    app_logger.info("=== 测试问题检测功能 ===")
    
    try:
        # 创建Agent
        agent = TestCaseReviewAgent("test_session_003")
        await agent.initialize()
        
        # 创建有问题的测试用例
        problematic_testcases = [
            {
                "id": "TC-BAD-001",
                "name": "测试用例",  # 命名不规范
                # 缺少description
                "requirement_ids": [],  # 缺少需求追溯
                "type": "unknown_type",  # 无效类型
                "priority": "invalid_priority",  # 无效优先级
                "status": "draft",
                # 缺少preconditions
                "test_steps": [
                    {
                        "step_number": 1,
                        "action": "检查系统",  # 模糊步骤
                        "expected_result": "正常"  # 模糊预期结果
                    }
                ],
                # 缺少expected_result
                "tags": ["测试", "用例"],  # 无意义标签
                "generation_method": "未知方法",
                "coverage_scenarios": []  # 缺少场景覆盖
            },
            {
                "id": "TC-BAD-002",
                "name": "依赖其他测试用例的测试",
                "description": "这个测试用例依赖前面的测试用例执行结果",
                "requirement_ids": [1],
                "type": "functional",
                "priority": "medium",
                "status": "draft",
                "preconditions": ["前面的测试用例已执行"],
                "test_steps": [
                    {
                        "step_number": 1,
                        "action": "基于之前测试的结果继续操作",
                        "expected_result": "操作成功"
                    }
                ],
                "expected_result": "测试通过",
                "tags": ["依赖测试"],
                "generation_method": "场景测试",
                "coverage_scenarios": ["依赖场景"]
            }
        ]
        
        # 执行评审
        result = await agent.process({
            "new_test_cases": problematic_testcases,
            "feature_id": "problem_feature"
        })
        
        # 分析检测到的问题
        issues = result.get("review_issues", [])
        app_logger.info(f"检测到问题数: {len(issues)}")
        
        # 按严重程度分类问题
        issue_by_severity = {}
        for issue in issues:
            severity = issue.get("severity", "unknown")
            if severity not in issue_by_severity:
                issue_by_severity[severity] = []
            issue_by_severity[severity].append(issue)
        
        for severity, severity_issues in issue_by_severity.items():
            app_logger.info(f"  {severity}级问题: {len(severity_issues)}个")
            for issue in severity_issues[:2]:  # 显示前2个问题
                app_logger.info(f"    - {issue.get('description', 'N/A')}")
        
        # 清理资源
        await agent.cleanup()
        
        return True
        
    except Exception as e:
        app_logger.error(f"问题检测测试失败: {str(e)}")
        return False


async def test_status_update():
    """测试状态更新功能"""
    app_logger.info("=== 测试状态更新功能 ===")
    
    try:
        # 创建Agent
        agent = TestCaseReviewAgent("test_session_004")
        await agent.initialize()
        
        # 创建混合质量的测试用例
        mixed_testcases = await create_test_testcases()
        
        # 执行评审
        result = await agent.process({
            "new_test_cases": mixed_testcases,
            "feature_id": "mixed_feature"
        })
        
        # 分析状态更新结果
        reviewed_cases = result.get("reviewed_cases", [])
        
        status_counts = {}
        for testcase in reviewed_cases:
            status = testcase.get("status", "unknown")
            status_counts[status] = status_counts.get(status, 0) + 1
        
        app_logger.info("测试用例状态分布:")
        for status, count in status_counts.items():
            app_logger.info(f"  {status}: {count}个")
        
        # 显示状态更新详情
        for testcase in reviewed_cases:
            tc_name = testcase.get("name", "未命名")
            tc_status = testcase.get("status", "未知")
            issues_count = len(testcase.get("review_issues", []))
            app_logger.info(f"  {tc_name}: {tc_status} (问题数: {issues_count})")
        
        # 清理资源
        await agent.cleanup()
        
        return True
        
    except Exception as e:
        app_logger.error(f"状态更新测试失败: {str(e)}")
        return False


async def test_export_functionality():
    """测试导出功能"""
    app_logger.info("=== 测试导出功能 ===")
    
    try:
        # 创建Agent
        agent = TestCaseReviewAgent("test_session_005")
        await agent.initialize()
        
        # 创建测试用例
        test_testcases = await create_test_testcases()
        
        # 执行评审
        result = await agent.process({
            "new_test_cases": test_testcases,
            "feature_id": "export_feature"
        })
        
        # 测试JSON导出
        json_report = await agent.export_review_report(result, "json")
        app_logger.info(f"JSON报告长度: {len(json_report)} 字符")
        
        # 测试Markdown导出
        md_report = await agent.export_review_report(result, "markdown")
        app_logger.info(f"Markdown报告长度: {len(md_report)} 字符")
        
        # 测试HTML导出
        html_report = await agent.export_review_report(result, "html")
        app_logger.info(f"HTML报告长度: {len(html_report)} 字符")
        
        # 保存导出文件
        with open("test_review_report.json", "w", encoding="utf-8") as f:
            f.write(json_report)
        
        with open("test_review_report.md", "w", encoding="utf-8") as f:
            f.write(md_report)
        
        with open("test_review_report.html", "w", encoding="utf-8") as f:
            f.write(html_report)
        
        app_logger.info("评审报告导出文件已保存")
        
        # 清理资源
        await agent.cleanup()
        
        return True
        
    except Exception as e:
        app_logger.error(f"导出功能测试失败: {str(e)}")
        return False


async def test_iterative_improvement():
    """测试迭代改进功能"""
    app_logger.info("=== 测试迭代改进功能 ===")
    
    try:
        # 创建Agent
        agent = TestCaseReviewAgent("test_session_006")
        await agent.initialize()
        
        # 创建需要改进的测试用例
        improvement_testcases = [
            {
                "id": "TC-IMP-001",
                "name": "需要改进的测试用例",
                "description": "这个测试用例需要通过多轮评审来改进质量",
                "requirement_ids": [1],
                "type": "functional",
                "priority": "medium",
                "status": "draft",
                "preconditions": ["系统运行"],
                "test_steps": [
                    {
                        "step_number": 1,
                        "action": "执行操作",
                        "expected_result": "成功"
                    }
                ],
                "expected_result": "测试通过",
                "tags": ["改进测试"],
                "generation_method": "手动创建",
                "coverage_scenarios": ["基础场景"]
            }
        ]
        
        # 执行评审
        result = await agent.process({
            "new_test_cases": improvement_testcases,
            "feature_id": "improvement_feature"
        })
        
        app_logger.info("迭代改进测试完成")
        app_logger.info(f"最终版本: {result.get('version', 1)}")
        
        # 检查思维链和反思
        stats = await agent.get_review_statistics()
        app_logger.info(f"评审统计: {stats}")
        
        # 清理资源
        await agent.cleanup()
        
        return True
        
    except Exception as e:
        app_logger.error(f"迭代改进测试失败: {str(e)}")
        return False


async def test_scenario_management():
    """测试场景管理功能"""
    app_logger.info("=== 测试场景管理功能 ===")
    
    try:
        # 创建Agent
        agent = TestCaseReviewAgent("test_session_007")
        await agent.initialize()
        
        # 创建测试用例
        test_testcases = await create_test_testcases()
        
        # 设置剩余场景
        remaining_scenarios = [
            {"scenario": "边界值测试", "priority": "high"},
            {"scenario": "异常处理", "priority": "medium"},
            {"scenario": "性能测试", "priority": "low"}
        ]
        
        # 执行评审
        result = await agent.process({
            "new_test_cases": test_testcases,
            "feature_id": "scenario_feature",
            "remaining_scenarios": remaining_scenarios
        })
        
        # 分析场景管理结果
        updated_scenarios = result.get("updated_remaining_scenarios", [])
        
        app_logger.info(f"原始剩余场景数: {len(remaining_scenarios)}")
        app_logger.info(f"更新后剩余场景数: {len(updated_scenarios)}")
        app_logger.info(f"是否还有更多工作: {result.get('has_more', False)}")
        
        # 显示剩余场景
        for scenario in updated_scenarios:
            if isinstance(scenario, dict):
                app_logger.info(f"  剩余场景: {scenario.get('scenario', 'N/A')}")
            else:
                app_logger.info(f"  剩余场景: {scenario}")
        
        # 清理资源
        await agent.cleanup()
        
        return True
        
    except Exception as e:
        app_logger.error(f"场景管理测试失败: {str(e)}")
        return False


async def run_all_tests():
    """运行所有测试"""
    app_logger.info("开始运行测试用例评审Agent测试套件")
    
    tests = [
        ("基础评审功能", test_basic_review),
        ("质量指标评估", test_quality_metrics),
        ("问题检测功能", test_issue_detection),
        ("状态更新功能", test_status_update),
        ("导出功能", test_export_functionality),
        ("迭代改进功能", test_iterative_improvement),
        ("场景管理功能", test_scenario_management)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        app_logger.info(f"\n{'='*50}")
        app_logger.info(f"运行测试: {test_name}")
        app_logger.info(f"{'='*50}")
        
        try:
            success = await test_func()
            results[test_name] = "通过" if success else "失败"
            
        except Exception as e:
            app_logger.error(f"测试 {test_name} 执行异常: {str(e)}")
            results[test_name] = "异常"
    
    # 输出测试结果摘要
    app_logger.info(f"\n{'='*50}")
    app_logger.info("测试结果摘要")
    app_logger.info(f"{'='*50}")
    
    for test_name, result in results.items():
        status_icon = "✅" if result == "通过" else "❌"
        app_logger.info(f"{status_icon} {test_name}: {result}")
    
    # 统计
    passed = sum(1 for r in results.values() if r == "通过")
    total = len(results)
    
    app_logger.info(f"\n总计: {passed}/{total} 个测试通过")
    
    return results


if __name__ == "__main__":
    asyncio.run(run_all_tests())
