"""
向量记忆系统测试脚本
"""

import asyncio
import sys
import json
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.memory import get_vector_memory
from core.logger import app_logger
from models.schemas import (
    RequirementModel, TestCaseModel, AgentMessage, CollaborationSession,
    AcceptanceCriteria, TestStep
)
from models.enums import (
    RequirementType, RequirementStatus, TestCaseType, TestCaseStatus,
    RiskLevel, AgentType, MessageType, CollaborationStatus, ProcessingStage
)


async def test_requirement_storage():
    """测试需求存储和搜索"""
    app_logger.info("=== 测试需求存储和搜索 ===")
    
    memory = await get_vector_memory()
    session_id = "test_session_req"
    
    # 创建测试需求
    requirements = [
        RequirementModel(
            id=1,
            trace_id="REQ-001",
            name="用户注册功能",
            type=RequirementType.FUNCTIONAL,
            description="新用户可以通过邮箱和手机号注册账户",
            acceptance_criteria=AcceptanceCriteria(
                positive=["用户输入有效邮箱和手机号可以成功注册"],
                negative=["已存在的邮箱或手机号无法重复注册"],
                boundary=["邮箱格式必须正确", "手机号必须是11位数字"],
                error=["注册失败时显示具体错误信息"]
            ),
            pending_scenarios=["正常注册", "邮箱重复", "手机号重复", "格式验证"],
            status=RequirementStatus.OK,
            risk_level=RiskLevel.MEDIUM
        ),
        RequirementModel(
            id=2,
            trace_id="REQ-002",
            name="用户登录功能",
            type=RequirementType.FUNCTIONAL,
            description="已注册用户可以通过用户名密码登录系统",
            acceptance_criteria=AcceptanceCriteria(
                positive=["正确的用户名密码可以成功登录"],
                negative=["错误的用户名或密码无法登录"],
                boundary=["密码长度6-20位", "连续登录失败3次锁定账户"],
                error=["登录失败显示友好提示信息"]
            ),
            pending_scenarios=["正常登录", "密码错误", "账户锁定", "记住登录状态"],
            status=RequirementStatus.OK,
            risk_level=RiskLevel.HIGH
        ),
        RequirementModel(
            id=3,
            trace_id="REQ-003",
            name="系统性能要求",
            type=RequirementType.NON_FUNCTIONAL,
            description="系统应支持1000并发用户，响应时间不超过2秒",
            attributes={"concurrency": 1000, "max_response_ms": 2000},
            acceptance_criteria=AcceptanceCriteria(
                positive=["1000并发用户下系统正常运行"],
                negative=["超过1000并发时系统可能降级"],
                boundary=["响应时间95%在2秒内"],
                error=["系统过载时返回503错误"]
            ),
            pending_scenarios=["并发测试", "压力测试", "响应时间测试"],
            status=RequirementStatus.OK,
            risk_level=RiskLevel.HIGH
        )
    ]
    
    # 存储需求
    stored_ids = []
    for req in requirements:
        doc_id = await memory.store_requirement(req, session_id)
        stored_ids.append(doc_id)
        app_logger.info(f"存储需求: {req.name} -> {doc_id}")
    
    # 测试搜索功能
    search_queries = [
        "用户登录",
        "注册功能",
        "性能要求",
        "并发用户"
    ]
    
    for query in search_queries:
        results = await memory.search_similar_requirements(query, session_id, limit=3)
        app_logger.info(f"搜索 '{query}' 找到 {len(results)} 个相似需求:")
        for i, result in enumerate(results):
            app_logger.info(f"  {i+1}. 相似度: {result['similarity']:.3f}, 需求: {result['metadata']['trace_id']}")
    
    return stored_ids


async def test_testcase_storage():
    """测试测试用例存储和搜索"""
    app_logger.info("=== 测试测试用例存储和搜索 ===")
    
    memory = await get_vector_memory()
    session_id = "test_session_tc"
    
    # 创建测试用例
    testcases = [
        TestCaseModel(
            id="TC-001",
            trace_id="TC-LOGIN-001",
            name="正常登录测试",
            description="验证用户使用正确的用户名和密码可以成功登录",
            type=TestCaseType.POSITIVE,
            priority=RiskLevel.HIGH,
            preconditions=["用户已注册", "用户未登录"],
            test_steps=[
                TestStep(step_number=1, action="打开登录页面", expected_result="显示登录表单"),
                TestStep(step_number=2, action="输入正确的用户名", expected_result="用户名输入框显示输入内容"),
                TestStep(step_number=3, action="输入正确的密码", expected_result="密码输入框显示掩码"),
                TestStep(step_number=4, action="点击登录按钮", expected_result="成功登录并跳转到主页")
            ],
            expected_result="用户成功登录系统",
            requirement_ids=[2],
            status=TestCaseStatus.APPROVED,
            tags=["登录", "正向测试", "核心功能"]
        ),
        TestCaseModel(
            id="TC-002",
            trace_id="TC-LOGIN-002",
            name="密码错误登录测试",
            description="验证用户输入错误密码时无法登录并显示错误提示",
            type=TestCaseType.NEGATIVE,
            priority=RiskLevel.MEDIUM,
            preconditions=["用户已注册", "用户未登录"],
            test_steps=[
                TestStep(step_number=1, action="打开登录页面", expected_result="显示登录表单"),
                TestStep(step_number=2, action="输入正确的用户名", expected_result="用户名输入框显示输入内容"),
                TestStep(step_number=3, action="输入错误的密码", expected_result="密码输入框显示掩码"),
                TestStep(step_number=4, action="点击登录按钮", expected_result="显示密码错误提示信息")
            ],
            expected_result="登录失败，显示密码错误提示",
            requirement_ids=[2],
            status=TestCaseStatus.APPROVED,
            tags=["登录", "负向测试", "错误处理"]
        ),
        TestCaseModel(
            id="TC-003",
            trace_id="TC-PERF-001",
            name="并发登录性能测试",
            description="验证系统在1000并发用户登录时的性能表现",
            type=TestCaseType.PERFORMANCE,
            priority=RiskLevel.HIGH,
            preconditions=["系统正常运行", "已准备1000个测试账户"],
            test_steps=[
                TestStep(step_number=1, action="启动性能测试工具", expected_result="工具正常启动"),
                TestStep(step_number=2, action="配置1000并发用户", expected_result="并发配置完成"),
                TestStep(step_number=3, action="执行并发登录测试", expected_result="所有用户开始登录"),
                TestStep(step_number=4, action="监控系统响应时间", expected_result="记录响应时间数据")
            ],
            expected_result="95%的登录请求在2秒内完成",
            requirement_ids=[3],
            status=TestCaseStatus.DRAFT,
            tags=["性能测试", "并发测试", "登录"]
        )
    ]
    
    # 存储测试用例
    stored_ids = []
    for tc in testcases:
        doc_id = await memory.store_testcase(tc, session_id)
        stored_ids.append(doc_id)
        app_logger.info(f"存储测试用例: {tc.name} -> {doc_id}")
    
    # 测试搜索功能
    search_queries = [
        "登录测试",
        "密码错误",
        "性能测试",
        "并发用户"
    ]
    
    for query in search_queries:
        results = await memory.search_similar_testcases(query, session_id, limit=3)
        app_logger.info(f"搜索 '{query}' 找到 {len(results)} 个相似测试用例:")
        for i, result in enumerate(results):
            app_logger.info(f"  {i+1}. 相似度: {result['similarity']:.3f}, 用例: {result['metadata']['trace_id']}")
    
    # 测试按需求ID搜索
    req_results = await memory.search_similar_testcases("登录", session_id, requirement_ids=[2], limit=5)
    app_logger.info(f"搜索需求ID=2相关的登录测试用例，找到 {len(req_results)} 个")
    
    return stored_ids


async def test_agent_message_storage():
    """测试Agent消息存储"""
    app_logger.info("=== 测试Agent消息存储 ===")
    
    memory = await get_vector_memory()
    session_id = "test_session_msg"
    
    # 创建测试消息
    messages = [
        AgentMessage(
            id="MSG-001",
            sender=AgentType.REQUIREMENT_ANALYSIS,
            receiver=AgentType.REQUIREMENT_REVIEW,
            message_type=MessageType.REQUEST,
            content={
                "action": "review_requirements",
                "requirements": [{"id": 1, "name": "用户登录功能"}],
                "priority": "high"
            },
            correlation_id="COLLAB-001"
        ),
        AgentMessage(
            id="MSG-002",
            sender=AgentType.REQUIREMENT_REVIEW,
            receiver=AgentType.REQUIREMENT_ANALYSIS,
            message_type=MessageType.RESPONSE,
            content={
                "action": "review_complete",
                "status": "approved",
                "issues": [],
                "suggestions": ["增加密码复杂度要求"]
            },
            correlation_id="COLLAB-001"
        ),
        AgentMessage(
            id="MSG-003",
            sender=AgentType.TESTCASE_GENERATION,
            message_type=MessageType.NOTIFICATION,
            content={
                "action": "generation_progress",
                "feature_id": 1,
                "generated_count": 5,
                "has_more": True
            }
        )
    ]
    
    # 存储消息
    stored_ids = []
    for msg in messages:
        doc_id = await memory.store_agent_message(msg, session_id)
        stored_ids.append(doc_id)
        app_logger.info(f"存储消息: {msg.sender} -> {msg.receiver or '广播'} -> {doc_id}")
    
    return stored_ids


async def test_collaboration_session():
    """测试协作会话存储"""
    app_logger.info("=== 测试协作会话存储 ===")
    
    memory = await get_vector_memory()
    
    # 创建协作会话
    session = CollaborationSession(
        session_id="COLLAB-TEST-001",
        participants=[
            AgentType.REQUIREMENT_ANALYSIS,
            AgentType.REQUIREMENT_REVIEW,
            AgentType.TESTCASE_GENERATION,
            AgentType.TESTCASE_REVIEW
        ],
        status=CollaborationStatus.IN_PROGRESS,
        current_stage=ProcessingStage.REQUIREMENT_ANALYSIS,
        shared_context={
            "project_name": "用户管理系统",
            "total_requirements": 10,
            "completed_requirements": 3,
            "current_priority": "high"
        },
        iteration_count=2,
        max_iterations=10
    )
    
    # 存储会话
    doc_id = await memory.store_collaboration_session(session)
    app_logger.info(f"存储协作会话: {session.session_id} -> {doc_id}")
    
    return doc_id


async def test_session_history():
    """测试会话历史记录获取"""
    app_logger.info("=== 测试会话历史记录获取 ===")
    
    memory = await get_vector_memory()
    
    # 获取不同会话的历史记录
    sessions = ["test_session_req", "test_session_tc", "test_session_msg"]
    
    for session_id in sessions:
        history = await memory.get_session_history(session_id, limit=10)
        app_logger.info(f"会话 {session_id} 历史记录: {len(history)} 条")
        
        for i, record in enumerate(history[:3]):  # 只显示前3条
            metadata = record["metadata"]
            app_logger.info(f"  {i+1}. 类型: {metadata.get('type')}, 时间: {record['timestamp']}")


async def test_collection_stats():
    """测试集合统计信息"""
    app_logger.info("=== 测试集合统计信息 ===")
    
    memory = await get_vector_memory()
    stats = await memory.get_collection_stats()
    
    app_logger.info("ChromaDB集合统计信息:")
    app_logger.info(f"  总文档数: {stats.get('total_documents', 0)}")
    app_logger.info(f"  类型分布: {json.dumps(stats.get('type_distribution', {}), ensure_ascii=False)}")
    app_logger.info(f"  会话分布: {json.dumps(stats.get('session_distribution', {}), ensure_ascii=False)}")
    app_logger.info(f"  集合名称: {stats.get('collection_name')}")


async def run_all_tests():
    """运行所有测试"""
    app_logger.info("开始运行向量记忆系统完整测试...")
    
    try:
        # 初始化向量记忆系统
        await get_vector_memory()
        
        # 运行各项测试
        await test_requirement_storage()
        await test_testcase_storage()
        await test_agent_message_storage()
        await test_collaboration_session()
        await test_session_history()
        await test_collection_stats()
        
        app_logger.info("所有测试完成！")
        return True
        
    except Exception as e:
        app_logger.error(f"测试过程中发生错误: {str(e)}")
        return False


async def main():
    """主函数"""
    success = await run_all_tests()
    
    if success:
        app_logger.info("向量记忆系统测试全部通过")
        sys.exit(0)
    else:
        app_logger.error("向量记忆系统测试失败")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())