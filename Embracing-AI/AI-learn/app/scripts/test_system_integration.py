"""
系统集成测试
验证各个组件的集成和基本功能
"""

import asyncio
import sys
import json
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.logger import get_agent_logger
from core.memory import get_memory_manager
from core.traceability import get_traceability_manager
from services.ollama_client import get_ollama_client
from agents import RequirementAnalysisAgent

logger = get_agent_logger("SystemIntegrationTest")


class SystemIntegrationTest:
    """系统集成测试类"""
    
    def __init__(self):
        self.test_results = {}
    
    async def test_ollama_connection(self):
        """测试Ollama连接"""
        logger.info("测试Ollama连接...")
        
        try:
            ollama_client = get_ollama_client()
            models = await ollama_client.list_models()
            
            if models:
                logger.info(f"Ollama连接成功，可用模型: {len(models)}")
                self.test_results["ollama_connection"] = {
                    "success": True,
                    "models_count": len(models)
                }
                return True
            else:
                logger.warning("Ollama连接成功但无可用模型")
                self.test_results["ollama_connection"] = {
                    "success": False,
                    "error": "无可用模型"
                }
                return False
                
        except Exception as e:
            logger.error(f"Ollama连接失败: {str(e)}")
            self.test_results["ollama_connection"] = {
                "success": False,
                "error": str(e)
            }
            return False
    
    async def test_memory_system(self):
        """测试记忆系统"""
        logger.info("测试记忆系统...")
        
        try:
            memory_manager = await get_memory_manager()
            
            # 测试存储和检索
            test_data = {
                "test_key": "test_value",
                "timestamp": datetime.now().isoformat()
            }
            
            await memory_manager.store_agent_result(
                agent_id="test_agent",
                task_id="integration_test",
                result=test_data
            )
            
            retrieved_data = await memory_manager.get_agent_result(
                agent_id="test_agent",
                task_id="integration_test"
            )
            
            if retrieved_data and retrieved_data.get("test_key") == "test_value":
                logger.info("记忆系统测试成功")
                self.test_results["memory_system"] = {
                    "success": True,
                    "operations": ["store", "retrieve"]
                }
                return True
            else:
                logger.error("记忆系统数据不匹配")
                self.test_results["memory_system"] = {
                    "success": False,
                    "error": "数据不匹配"
                }
                return False
                
        except Exception as e:
            logger.error(f"记忆系统测试失败: {str(e)}")
            self.test_results["memory_system"] = {
                "success": False,
                "error": str(e)
            }
            return False
    
    async def test_traceability_system(self):
        """测试可追溯性系统"""
        logger.info("测试可追溯性系统...")
        
        try:
            traceability_manager = await get_traceability_manager()
            
            # 创建测试工件
            from core.traceability import ArtifactType, ArtifactStatus
            
            artifact_id = await traceability_manager.create_artifact(
                artifact_type=ArtifactType.REQUIREMENT,
                name="集成测试需求",
                description="用于集成测试的需求",
                content={"test": "integration_test"},
                created_by="integration_test"
            )
            
            # 获取工件
            artifact = await traceability_manager.get_artifact(artifact_id)
            
            if artifact and artifact.name == "集成测试需求":
                logger.info("可追溯性系统测试成功")
                self.test_results["traceability_system"] = {
                    "success": True,
                    "artifact_id": artifact_id
                }
                return True
            else:
                logger.error("可追溯性系统工件创建失败")
                self.test_results["traceability_system"] = {
                    "success": False,
                    "error": "工件创建失败"
                }
                return False
                
        except Exception as e:
            logger.error(f"可追溯性系统测试失败: {str(e)}")
            self.test_results["traceability_system"] = {
                "success": False,
                "error": str(e)
            }
            return False
    
    async def test_agent_basic_function(self):
        """测试智能体基本功能"""
        logger.info("测试智能体基本功能...")
        
        try:
            # 创建需求分析智能体
            agent = RequirementAnalysisAgent("integration_test_agent")
            await agent.initialize()
            
            # 测试简单需求分析
            test_requirement = "用户需要能够登录系统"
            
            result = await agent.analyze_requirements(test_requirement)
            
            if result and "requirements" in result:
                logger.info("智能体基本功能测试成功")
                self.test_results["agent_basic_function"] = {
                    "success": True,
                    "requirements_found": len(result.get("requirements", []))
                }
                return True
            else:
                logger.error("智能体未返回预期结果")
                self.test_results["agent_basic_function"] = {
                    "success": False,
                    "error": "未返回预期结果"
                }
                return False
                
        except Exception as e:
            logger.error(f"智能体基本功能测试失败: {str(e)}")
            self.test_results["agent_basic_function"] = {
                "success": False,
                "error": str(e)
            }
            return False
    
    async def run_all_tests(self):
        """运行所有集成测试"""
        logger.info("开始系统集成测试...")
        
        tests = [
            ("Ollama连接", self.test_ollama_connection),
            ("记忆系统", self.test_memory_system),
            ("可追溯性系统", self.test_traceability_system),
            ("智能体基本功能", self.test_agent_basic_function)
        ]
        
        results = []
        
        for test_name, test_func in tests:
            logger.info(f"执行测试: {test_name}")
            try:
                success = await test_func()
                results.append(success)
                logger.info(f"测试 {test_name}: {'通过' if success else '失败'}")
            except Exception as e:
                logger.error(f"测试 {test_name} 执行异常: {str(e)}")
                results.append(False)
        
        # 生成测试报告
        await self.generate_integration_report(results)
        
        return all(results)
    
    async def generate_integration_report(self, results):
        """生成集成测试报告"""
        report = {
            "test_summary": {
                "total_tests": len(results),
                "passed_tests": sum(results),
                "failed_tests": len(results) - sum(results),
                "success_rate": sum(results) / len(results) * 100,
                "test_time": datetime.now().isoformat()
            },
            "detailed_results": self.test_results,
            "overall_status": "PASS" if all(results) else "FAIL",
            "recommendations": self._generate_recommendations()
        }
        
        # 保存报告
        report_path = project_root / "app" / "logs" / f"integration_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        report_path.parent.mkdir(exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"集成测试报告已保存到: {report_path}")
        
        # 输出简要报告
        logger.info("=" * 50)
        logger.info("集成测试结果总结")
        logger.info("=" * 50)
        logger.info(f"总测试数: {report['test_summary']['total_tests']}")
        logger.info(f"通过测试: {report['test_summary']['passed_tests']}")
        logger.info(f"失败测试: {report['test_summary']['failed_tests']}")
        logger.info(f"成功率: {report['test_summary']['success_rate']:.1f}%")
        logger.info(f"总体状态: {report['overall_status']}")
    
    def _generate_recommendations(self):
        """生成建议"""
        recommendations = []
        
        if not self.test_results.get("ollama_connection", {}).get("success"):
            recommendations.append("请检查Ollama服务是否正常运行，并确保模型已正确加载")
        
        if not self.test_results.get("memory_system", {}).get("success"):
            recommendations.append("请检查ChromaDB配置和连接")
        
        if not self.test_results.get("traceability_system", {}).get("success"):
            recommendations.append("请检查可追溯性系统的数据库配置")
        
        if not self.test_results.get("agent_basic_function", {}).get("success"):
            recommendations.append("请检查智能体配置和依赖项")
        
        if not recommendations:
            recommendations.append("所有基础组件测试通过，系统集成正常")
        
        return recommendations


async def main():
    """主测试函数"""
    test = SystemIntegrationTest()
    
    try:
        success = await test.run_all_tests()
        return success
    except Exception as e:
        logger.error(f"集成测试执行失败: {str(e)}")
        return False


if __name__ == "__main__":
    # 运行集成测试
    success = asyncio.run(main())
    sys.exit(0 if success else 1)