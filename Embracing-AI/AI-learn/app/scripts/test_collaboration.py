"""
异步协作框架测试脚本
"""

import asyncio
import sys
from pathlib import Path
import json
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.logger import app_logger
from core.collaboration import (
    MessageBus, CollaborationOrchestrator, Message, MessageType, MessagePriority,
    CollaborationMode, AgentCollaborationMixin, get_message_bus, get_orchestrator,
    create_workflow_from_template, cleanup_collaboration
)
from models.enums import AgentType


class MockAgent(AgentCollaborationMixin):
    """模拟Agent用于测试"""
    
    def __init__(self, agent_id: str, agent_type: AgentType):
        super().__init__()
        self.agent_id = agent_id
        self.agent_type = agent_type
        self.status = "idle"
        self.processed_count = 0
    
    async def process(self, data: any) -> dict:
        """模拟处理数据"""
        await asyncio.sleep(0.1)  # 模拟处理时间
        self.processed_count += 1
        
        return {
            "agent_id": self.agent_id,
            "agent_type": self.agent_type.value,
            "processed_count": self.processed_count,
            "input_data": data,
            "result": f"处理结果来自 {self.agent_id}",
            "timestamp": datetime.now().isoformat(),
            "has_more": self.processed_count < 3  # 模拟迭代条件
        }


async def test_message_bus():
    """测试消息总线"""
    app_logger.info("=== 测试消息总线 ===")
    
    try:
        # 创建消息总线
        message_bus = MessageBus()
        await message_bus.start()
        
        # 测试消息处理
        received_messages = []
        
        async def message_handler(message: Message):
            received_messages.append(message)
            app_logger.info(f"收到消息: {message.id} (类型: {message.type.value})")
            return {"handled": True, "message_id": message.id}
        
        # 订阅消息
        message_bus.subscribe(MessageType.NOTIFICATION, message_handler)
        
        # 发送测试消息
        test_message = Message(
            id="test_msg_001",
            type=MessageType.NOTIFICATION,
            priority=MessagePriority.MEDIUM,
            sender_id="test_sender",
            receiver_id="test_receiver",
            content={"test": "data"},
            metadata={"test": True},
            timestamp=datetime.now()
        )
        
        await message_bus.publish(test_message)
        
        # 等待消息处理
        await asyncio.sleep(0.5)
        
        # 验证结果
        assert len(received_messages) == 1
        assert received_messages[0].id == "test_msg_001"
        
        # 测试队列状态
        queue_status = message_bus.get_queue_status()
        app_logger.info(f"队列状态: {queue_status}")
        
        # 测试消息历史
        history = message_bus.get_message_history(10)
        app_logger.info(f"消息历史数量: {len(history)}")
        
        await message_bus.stop()
        
        app_logger.info("消息总线测试通过")
        return True
        
    except Exception as e:
        app_logger.error(f"消息总线测试失败: {str(e)}")
        return False


async def test_orchestrator():
    """测试协作编排器"""
    app_logger.info("=== 测试协作编排器 ===")
    
    try:
        # 获取消息总线和编排器
        message_bus = await get_message_bus()
        orchestrator = await get_orchestrator()
        
        # 创建模拟Agent
        agent1 = MockAgent("agent_001", AgentType.REQUIREMENT_ANALYSIS)
        agent2 = MockAgent("agent_002", AgentType.REQUIREMENT_REVIEW)
        
        # 设置协作环境
        await agent1.setup_collaboration(message_bus, orchestrator)
        await agent2.setup_collaboration(message_bus, orchestrator)
        
        # 注册Agent
        await orchestrator.register_agent(
            agent_id="agent_001",
            agent_type=AgentType.REQUIREMENT_ANALYSIS,
            capabilities=["需求分析", "文档解析"]
        )
        
        await orchestrator.register_agent(
            agent_id="agent_002",
            agent_type=AgentType.REQUIREMENT_REVIEW,
            capabilities=["需求评审", "质量检查"]
        )
        
        # 检查Agent注册状态
        registry = orchestrator.get_agent_registry()
        app_logger.info(f"已注册Agent数量: {len(registry)}")
        
        # 创建简单的协作任务
        task_id = await orchestrator.create_collaboration_task(
            name="测试协作任务",
            description="测试Agent之间的协作",
            mode=CollaborationMode.SEQUENTIAL,
            workflow=[
                {
                    "agent_id": "agent_001",
                    "action": "process",
                    "params": {"test": True}
                },
                {
                    "agent_id": "agent_002",
                    "action": "process",
                    "params": {"test": True}
                }
            ]
        )
        
        app_logger.info(f"创建协作任务: {task_id}")
        
        # 执行任务
        result = await orchestrator.execute_task(task_id, {"test_data": "hello"})
        
        app_logger.info(f"任务执行结果: {result}")
        
        # 验证结果
        assert "agent_001" in result
        assert "agent_002" in result
        assert result["agent_001"]["status"] == "success"
        assert result["agent_002"]["status"] == "success"
        
        # 检查任务历史
        history = orchestrator.get_task_history()
        app_logger.info(f"任务历史数量: {len(history)}")
        
        app_logger.info("协作编排器测试通过")
        return True
        
    except Exception as e:
        app_logger.error(f"协作编排器测试失败: {str(e)}")
        return False


async def test_collaboration_modes():
    """测试不同协作模式"""
    app_logger.info("=== 测试协作模式 ===")
    
    try:
        orchestrator = await get_orchestrator()
        
        # 创建多个模拟Agent
        agents = []
        for i in range(4):
            agent = MockAgent(f"agent_{i:03d}", AgentType.TESTCASE_GENERATION)
            message_bus = await get_message_bus()
            await agent.setup_collaboration(message_bus, orchestrator)
            await orchestrator.register_agent(
                agent_id=f"agent_{i:03d}",
                agent_type=AgentType.TESTCASE_GENERATION,
                capabilities=["测试用例生成"]
            )
            agents.append(agent)
        
        # 测试顺序执行模式
        app_logger.info("测试顺序执行模式")
        sequential_task_id = await orchestrator.create_collaboration_task(
            name="顺序执行测试",
            description="测试顺序执行模式",
            mode=CollaborationMode.SEQUENTIAL,
            workflow=[
                {"agent_id": "agent_000", "action": "process", "params": {}},
                {"agent_id": "agent_001", "action": "process", "params": {}}
            ]
        )
        
        start_time = datetime.now()
        sequential_result = await orchestrator.execute_task(sequential_task_id, {"mode": "sequential"})
        sequential_duration = (datetime.now() - start_time).total_seconds()
        
        app_logger.info(f"顺序执行耗时: {sequential_duration:.2f}秒")
        
        # 测试并行执行模式
        app_logger.info("测试并行执行模式")
        parallel_task_id = await orchestrator.create_collaboration_task(
            name="并行执行测试",
            description="测试并行执行模式",
            mode=CollaborationMode.PARALLEL,
            workflow=[
                {"agent_id": "agent_002", "action": "process", "params": {}},
                {"agent_id": "agent_003", "action": "process", "params": {}}
            ]
        )
        
        start_time = datetime.now()
        parallel_result = await orchestrator.execute_task(parallel_task_id, {"mode": "parallel"})
        parallel_duration = (datetime.now() - start_time).total_seconds()
        
        app_logger.info(f"并行执行耗时: {parallel_duration:.2f}秒")
        
        # 验证并行执行更快
        assert parallel_duration < sequential_duration
        
        # 测试迭代执行模式
        app_logger.info("测试迭代执行模式")
        iterative_task_id = await orchestrator.create_collaboration_task(
            name="迭代执行测试",
            description="测试迭代执行模式",
            mode=CollaborationMode.ITERATIVE,
            workflow=[
                {"agent_id": "agent_000", "action": "process", "params": {}}
            ]
        )
        
        iterative_result = await orchestrator.execute_task(iterative_task_id, {"mode": "iterative"})
        
        # 验证迭代结果
        assert "iteration_1" in iterative_result
        assert "iteration_2" in iterative_result
        
        app_logger.info("协作模式测试通过")
        return True
        
    except Exception as e:
        app_logger.error(f"协作模式测试失败: {str(e)}")
        return False


async def test_workflow_templates():
    """测试工作流模板"""
    app_logger.info("=== 测试工作流模板 ===")
    
    try:
        orchestrator = await get_orchestrator()
        
        # 创建需要的Agent类型
        agent_types = [
            AgentType.REQUIREMENT_ANALYSIS,
            AgentType.REQUIREMENT_REVIEW,
            AgentType.TESTCASE_GENERATION,
            AgentType.TESTCASE_REVIEW
        ]
        
        for i, agent_type in enumerate(agent_types):
            agent = MockAgent(f"template_agent_{i}", agent_type)
            message_bus = await get_message_bus()
            await agent.setup_collaboration(message_bus, orchestrator)
            await orchestrator.register_agent(
                agent_id=f"template_agent_{i}",
                agent_type=agent_type,
                capabilities=[f"能力_{i}"]
            )
        
        # 测试需求到测试用例模板
        app_logger.info("测试需求到测试用例工作流模板")
        task_id = await create_workflow_from_template(
            "requirement_to_testcase",
            orchestrator,
            {"timeout": timedelta(minutes=5)}
        )
        
        result = await orchestrator.execute_task(task_id, {"requirements": "测试需求"})
        
        # 验证结果包含所有步骤
        expected_agents = ["template_agent_0", "template_agent_1", "template_agent_2", "template_agent_3"]
        for agent_id in expected_agents:
            assert agent_id in result
        
        app_logger.info("工作流模板测试通过")
        return True
        
    except Exception as e:
        app_logger.error(f"工作流模板测试失败: {str(e)}")
        return False


async def test_message_passing():
    """测试消息传递"""
    app_logger.info("=== 测试消息传递 ===")
    
    try:
        message_bus = await get_message_bus()
        orchestrator = await get_orchestrator()
        
        # 创建两个Agent
        agent1 = MockAgent("msg_agent_1", AgentType.REQUIREMENT_ANALYSIS)
        agent2 = MockAgent("msg_agent_2", AgentType.REQUIREMENT_REVIEW)
        
        await agent1.setup_collaboration(message_bus, orchestrator)
        await agent2.setup_collaboration(message_bus, orchestrator)
        
        # 测试直接消息传递
        response = await agent1.request_collaboration(
            target_agent_id="msg_agent_2",
            action="process",
            data={"test": "collaboration"},
            timeout=5.0
        )
        
        app_logger.info(f"协作响应: {response}")
        
        # 验证响应
        assert response["status"] == "success"
        assert response["agent_id"] == "msg_agent_2"
        
        # 测试广播消息
        await agent1.broadcast_message(
            MessageType.NOTIFICATION,
            {"type": "test_broadcast", "message": "Hello everyone!"}
        )
        
        # 测试心跳
        await agent1.send_heartbeat()
        await agent2.send_heartbeat()
        
        # 等待消息处理
        await asyncio.sleep(0.5)
        
        app_logger.info("消息传递测试通过")
        return True
        
    except Exception as e:
        app_logger.error(f"消息传递测试失败: {str(e)}")
        return False


async def test_error_handling():
    """测试错误处理"""
    app_logger.info("=== 测试错误处理 ===")
    
    try:
        orchestrator = await get_orchestrator()
        
        # 创建会出错的Agent
        class ErrorAgent(MockAgent):
            async def process(self, data):
                if data.get("should_error"):
                    raise ValueError("模拟处理错误")
                return await super().process(data)
        
        error_agent = ErrorAgent("error_agent", AgentType.TESTCASE_GENERATION)
        message_bus = await get_message_bus()
        await error_agent.setup_collaboration(message_bus, orchestrator)
        await orchestrator.register_agent(
            agent_id="error_agent",
            agent_type=AgentType.TESTCASE_GENERATION,
            capabilities=["错误测试"]
        )
        
        # 创建会出错的任务
        task_id = await orchestrator.create_collaboration_task(
            name="错误处理测试",
            description="测试错误处理机制",
            mode=CollaborationMode.SEQUENTIAL,
            workflow=[
                {"agent_id": "error_agent", "action": "process", "params": {}}
            ]
        )
        
        # 执行会出错的任务
        try:
            result = await orchestrator.execute_task(task_id, {"should_error": True})
            app_logger.error("预期应该出错但没有出错")
            return False
        except Exception as e:
            app_logger.info(f"正确捕获到错误: {str(e)}")
        
        # 测试任务取消
        cancel_task_id = await orchestrator.create_collaboration_task(
            name="取消测试任务",
            description="测试任务取消",
            mode=CollaborationMode.SEQUENTIAL,
            workflow=[
                {"agent_id": "error_agent", "action": "process", "params": {}}
            ]
        )
        
        # 取消任务
        cancelled = await orchestrator.cancel_task(cancel_task_id)
        assert cancelled is True
        
        app_logger.info("错误处理测试通过")
        return True
        
    except Exception as e:
        app_logger.error(f"错误处理测试失败: {str(e)}")
        return False


async def test_performance():
    """测试性能"""
    app_logger.info("=== 测试性能 ===")
    
    try:
        orchestrator = await get_orchestrator()
        
        # 创建多个Agent
        num_agents = 10
        agents = []
        
        for i in range(num_agents):
            agent = MockAgent(f"perf_agent_{i}", AgentType.TESTCASE_GENERATION)
            message_bus = await get_message_bus()
            await agent.setup_collaboration(message_bus, orchestrator)
            await orchestrator.register_agent(
                agent_id=f"perf_agent_{i}",
                agent_type=AgentType.TESTCASE_GENERATION,
                capabilities=["性能测试"]
            )
            agents.append(agent)
        
        # 测试大量并发任务
        app_logger.info(f"创建 {num_agents} 个并发任务")
        
        tasks = []
        start_time = datetime.now()
        
        for i in range(num_agents):
            task_id = await orchestrator.create_collaboration_task(
                name=f"性能测试任务_{i}",
                description=f"性能测试任务 {i}",
                mode=CollaborationMode.SEQUENTIAL,
                workflow=[
                    {"agent_id": f"perf_agent_{i}", "action": "process", "params": {}}
                ]
            )
            
            # 创建异步任务
            task_coroutine = orchestrator.execute_task(task_id, {"task_id": i})
            tasks.append(task_coroutine)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # 统计结果
        success_count = sum(1 for r in results if not isinstance(r, Exception))
        error_count = len(results) - success_count
        
        app_logger.info(f"性能测试结果:")
        app_logger.info(f"  总任务数: {num_agents}")
        app_logger.info(f"  成功任务数: {success_count}")
        app_logger.info(f"  失败任务数: {error_count}")
        app_logger.info(f"  总耗时: {duration:.2f}秒")
        app_logger.info(f"  平均每任务耗时: {duration/num_agents:.2f}秒")
        app_logger.info(f"  吞吐量: {num_agents/duration:.2f}任务/秒")
        
        # 验证性能指标
        assert success_count >= num_agents * 0.8  # 至少80%成功率
        assert duration < num_agents * 2  # 平均每任务不超过2秒
        
        app_logger.info("性能测试通过")
        return True
        
    except Exception as e:
        app_logger.error(f"性能测试失败: {str(e)}")
        return False


async def test_memory_integration():
    """测试内存集成"""
    app_logger.info("=== 测试内存集成 ===")
    
    try:
        orchestrator = await get_orchestrator()
        
        # 创建Agent
        agent = MockAgent("memory_agent", AgentType.REQUIREMENT_ANALYSIS)
        message_bus = await get_message_bus()
        await agent.setup_collaboration(message_bus, orchestrator)
        await orchestrator.register_agent(
            agent_id="memory_agent",
            agent_type=AgentType.REQUIREMENT_ANALYSIS,
            capabilities=["内存测试"]
        )
        
        # 创建任务并执行
        task_id = await orchestrator.create_collaboration_task(
            name="内存集成测试",
            description="测试与内存系统的集成",
            mode=CollaborationMode.SEQUENTIAL,
            workflow=[
                {"agent_id": "memory_agent", "action": "process", "params": {"save_to_memory": True}}
            ]
        )
        
        result = await orchestrator.execute_task(task_id, {"test_data": "memory_test"})
        
        # 验证结果
        assert "memory_agent" in result
        assert result["memory_agent"]["status"] == "success"
        
        app_logger.info("内存集成测试通过")
        return True
        
    except Exception as e:
        app_logger.error(f"内存集成测试失败: {str(e)}")
        return False


async def test_cleanup():
    """测试资源清理"""
    app_logger.info("=== 测试资源清理 ===")
    
    try:
        # 执行清理
        await cleanup_collaboration()
        
        app_logger.info("资源清理测试通过")
        return True
        
    except Exception as e:
        app_logger.error(f"资源清理测试失败: {str(e)}")
        return False


async def run_all_tests():
    """运行所有测试"""
    app_logger.info("开始运行异步协作框架测试套件")
    
    tests = [
        ("消息总线", test_message_bus),
        ("协作编排器", test_orchestrator),
        ("协作模式", test_collaboration_modes),
        ("工作流模板", test_workflow_templates),
        ("消息传递", test_message_passing),
        ("错误处理", test_error_handling),
        ("性能测试", test_performance),
        ("内存集成", test_memory_integration),
        ("资源清理", test_cleanup)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        app_logger.info(f"\n{'='*50}")
        app_logger.info(f"运行测试: {test_name}")
        app_logger.info(f"{'='*50}")
        
        try:
            success = await test_func()
            results[test_name] = "通过" if success else "失败"
            
        except Exception as e:
            app_logger.error(f"测试 {test_name} 执行异常: {str(e)}")
            results[test_name] = "异常"
    
    # 输出测试结果摘要
    app_logger.info(f"\n{'='*50}")
    app_logger.info("测试结果摘要")
    app_logger.info(f"{'='*50}")
    
    for test_name, result in results.items():
        status_icon = "✅" if result == "通过" else "❌"
        app_logger.info(f"{status_icon} {test_name}: {result}")
    
    # 统计
    passed = sum(1 for r in results.values() if r == "通过")
    total = len(results)
    
    app_logger.info(f"\n总计: {passed}/{total} 个测试通过")
    
    return results


if __name__ == "__main__":
    asyncio.run(run_all_tests())