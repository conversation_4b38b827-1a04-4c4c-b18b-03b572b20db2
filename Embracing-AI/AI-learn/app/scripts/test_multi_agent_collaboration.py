"""
多智能体协作对齐机制测试
测试需求分析->需求评审->测试用例生成->测试用例评审的完整流程
"""

import asyncio
import json
import sys
import os
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from agents import (
    RequirementAnalysisAgent, RequirementReviewAgent,
    TestCaseGenerationAgent, TestCaseReviewAgent
)
from core.collaboration import CollaborationManager
from core.memory import get_memory_manager
from core.traceability import get_traceability_manager
from core.logger import get_agent_logger
from utils.json_formatter import JSONFormatter, create_standard_response

logger = get_agent_logger("MultiAgentTest")


class MultiAgentCollaborationTest:
    """多智能体协作测试类"""
    
    def __init__(self):
        self.collaboration_manager = None
        self.memory_manager = None
        self.traceability_manager = None
        self.agents = {}
        self.test_results = []
    
    async def initialize(self):
        """初始化测试环境"""
        logger.info("初始化多智能体协作测试环境...")
        
        # 初始化管理器
        self.collaboration_manager = CollaborationManager()
        await self.collaboration_manager.initialize()
        
        self.memory_manager = await get_memory_manager()
        self.traceability_manager = await get_traceability_manager()
        
        # 创建智能体
        self.agents = {
            "requirement_analysis": RequirementAnalysisAgent("req_analysis_test"),
            "requirement_review": RequirementReviewAgent("req_review_test"),
            "testcase_generation": TestCaseGenerationAgent("tc_gen_test"),
            "testcase_review": TestCaseReviewAgent("tc_review_test")
        }
        
        # 初始化所有智能体
        for agent_name, agent in self.agents.items():
            await agent.initialize()
            logger.info(f"智能体 {agent_name} 初始化完成")
    
    async def test_complete_workflow(self):
        """测试完整的工作流程"""
        logger.info("开始测试完整工作流程...")
        
        # 测试需求文本
        test_requirement = """
        用户登录功能需求：
        1. 用户可以通过用户名和密码登录系统
        2. 登录失败时显示错误信息
        3. 登录成功后跳转到主页面
        4. 支持记住密码功能
        5. 连续登录失败3次后锁定账户30分钟
        
        性能要求：
        - 登录响应时间不超过2秒
        - 支持1000个并发用户同时登录
        
        安全要求：
        - 密码必须加密存储
        - 支持验证码防止暴力破解
        """
        
        workflow_id = f"workflow_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        try:
            # 第一步：需求分析
            logger.info("步骤1: 需求分析")
            req_analysis_result = await self._test_requirement_analysis(
                test_requirement, workflow_id
            )
            
            # 第二步：需求评审（多轮对齐）
            logger.info("步骤2: 需求评审")
            req_review_result = await self._test_requirement_review(
                req_analysis_result, workflow_id
            )
            
            # 第三步：测试用例生成（多轮生成）
            logger.info("步骤3: 测试用例生成")
            tc_generation_result = await self._test_testcase_generation(
                req_review_result, workflow_id
            )
            
            # 第四步：测试用例评审（多轮对齐）
            logger.info("步骤4: 测试用例评审")
            tc_review_result = await self._test_testcase_review(
                tc_generation_result, workflow_id
            )
            
            # 验证完整性
            await self._verify_workflow_completeness(workflow_id)
            
            logger.info("完整工作流程测试完成")
            return True
            
        except Exception as e:
            logger.error(f"工作流程测试失败: {str(e)}")
            return False
    
    async def _test_requirement_analysis(self, requirement_text: str, workflow_id: str):
        """测试需求分析阶段"""
        agent = self.agents["requirement_analysis"]
        
        # 执行需求分析
        result = await agent.analyze_requirements(requirement_text)
        
        # 验证输出格式
        formatted_result = JSONFormatter.format_agent_output(
            result, "requirement_analysis"
        )
        
        # 保存到记忆系统
        await self.memory_manager.store_agent_result(
            agent_id=agent.agent_id,
            task_id=f"{workflow_id}_req_analysis",
            result=formatted_result
        )
        
        # 记录测试结果
        self.test_results.append({
            "stage": "requirement_analysis",
            "workflow_id": workflow_id,
            "success": True,
            "result_summary": {
                "requirements_count": len(formatted_result.get("requirements", [])),
                "has_clarifications": any(
                    req.get("status") == "clarify" 
                    for req in formatted_result.get("requirements", [])
                )
            }
        })
        
        logger.info(f"需求分析完成，识别到 {len(formatted_result.get('requirements', []))} 个需求")
        return formatted_result
    
    async def _test_requirement_review(self, analysis_result: dict, workflow_id: str):
        """测试需求评审阶段（多轮对齐）"""
        agent = self.agents["requirement_review"]
        
        max_iterations = 3
        current_result = analysis_result
        
        for iteration in range(max_iterations):
            logger.info(f"需求评审第 {iteration + 1} 轮")
            
            # 执行需求评审
            review_result = await agent.review_requirements(current_result)
            
            # 格式化输出
            formatted_result = JSONFormatter.format_agent_output(
                review_result, "requirement_review"
            )
            
            # 检查是否需要继续迭代
            review_issues = formatted_result.get("review_issues", [])
            critical_issues = [
                issue for issue in review_issues 
                if issue.get("severity") == "high"
            ]
            
            if not critical_issues:
                logger.info("需求评审通过，无关键问题")
                break
            
            # 如果有关键问题，需要重新分析
            if iteration < max_iterations - 1:
                logger.info(f"发现 {len(critical_issues)} 个关键问题，进行下一轮评审")
                current_result = formatted_result
            else:
                logger.warning("达到最大迭代次数，评审结束")
        
        # 保存最终结果
        await self.memory_manager.store_agent_result(
            agent_id=agent.agent_id,
            task_id=f"{workflow_id}_req_review",
            result=formatted_result
        )
        
        self.test_results.append({
            "stage": "requirement_review",
            "workflow_id": workflow_id,
            "success": True,
            "iterations": iteration + 1,
            "result_summary": {
                "review_issues_count": len(formatted_result.get("review_issues", [])),
                "updated_requirements_count": len(formatted_result.get("updated_requirements", []))
            }
        })
        
        return formatted_result
    
    async def _test_testcase_generation(self, review_result: dict, workflow_id: str):
        """测试测试用例生成阶段（多轮生成）"""
        agent = self.agents["testcase_generation"]
        
        all_test_cases = []
        requirements = review_result.get("updated_requirements", [])
        
        for req in requirements:
            feature_id = req.get("trace_id", f"feature_{req.get('id')}")
            has_more = True
            iteration = 0
            max_iterations = 5
            
            while has_more and iteration < max_iterations:
                logger.info(f"为需求 {feature_id} 生成测试用例，第 {iteration + 1} 轮")
                
                # 生成测试用例
                generation_result = await agent.generate_test_cases(
                    requirements=[req],
                    context={
                        "feature_id": feature_id,
                        "iteration": iteration,
                        "existing_cases": all_test_cases
                    }
                )
                
                # 格式化输出
                formatted_result = JSONFormatter.format_agent_output(
                    generation_result, "testcase_generation"
                )
                
                # 收集新生成的测试用例
                new_cases = formatted_result.get("new_test_cases", [])
                all_test_cases.extend(new_cases)
                
                # 检查是否还有更多用例需要生成
                has_more = formatted_result.get("has_more", False)
                iteration += 1
                
                logger.info(f"本轮生成 {len(new_cases)} 个测试用例，总计 {len(all_test_cases)} 个")
        
        # 保存最终结果
        final_result = {
            "all_test_cases": all_test_cases,
            "total_count": len(all_test_cases),
            "version": 1,
            "timestamp": datetime.now().isoformat()
        }
        
        await self.memory_manager.store_agent_result(
            agent_id=agent.agent_id,
            task_id=f"{workflow_id}_tc_generation",
            result=final_result
        )
        
        self.test_results.append({
            "stage": "testcase_generation",
            "workflow_id": workflow_id,
            "success": True,
            "result_summary": {
                "total_test_cases": len(all_test_cases),
                "requirements_covered": len(requirements)
            }
        })
        
        return final_result
    
    async def _test_testcase_review(self, generation_result: dict, workflow_id: str):
        """测试测试用例评审阶段（多轮对齐）"""
        agent = self.agents["testcase_review"]
        
        test_cases = generation_result.get("all_test_cases", [])
        max_iterations = 3
        
        for iteration in range(max_iterations):
            logger.info(f"测试用例评审第 {iteration + 1} 轮")
            
            # 执行测试用例评审
            review_result = await agent.review_test_cases(
                test_cases=test_cases,
                context={"iteration": iteration}
            )
            
            # 格式化输出
            formatted_result = JSONFormatter.format_agent_output(
                review_result, "testcase_review"
            )
            
            # 检查评审结果
            reviewed_cases = formatted_result.get("reviewed_cases", [])
            failed_cases = [
                case for case in reviewed_cases 
                if case.get("status") == "fail"
            ]
            
            if not failed_cases:
                logger.info("所有测试用例评审通过")
                break
            
            if iteration < max_iterations - 1:
                logger.info(f"有 {len(failed_cases)} 个测试用例需要修改，进行下一轮评审")
                # 这里可以实现测试用例修改逻辑
            else:
                logger.warning("达到最大迭代次数，评审结束")
        
        # 保存最终结果
        await self.memory_manager.store_agent_result(
            agent_id=agent.agent_id,
            task_id=f"{workflow_id}_tc_review",
            result=formatted_result
        )
        
        self.test_results.append({
            "stage": "testcase_review",
            "workflow_id": workflow_id,
            "success": True,
            "iterations": iteration + 1,
            "result_summary": {
                "reviewed_cases_count": len(reviewed_cases),
                "passed_cases_count": len([c for c in reviewed_cases if c.get("status") == "pass"]),
                "failed_cases_count": len(failed_cases)
            }
        })
        
        return formatted_result
    
    async def _verify_workflow_completeness(self, workflow_id: str):
        """验证工作流程完整性"""
        logger.info("验证工作流程完整性...")
        
        # 检查每个阶段的结果是否都已保存
        stages = ["req_analysis", "req_review", "tc_generation", "tc_review"]
        
        for stage in stages:
            task_id = f"{workflow_id}_{stage}"
            result = await self.memory_manager.get_agent_result(
                agent_id=f"{stage.split('_')[0]}_test",
                task_id=task_id
            )
            
            if not result:
                raise Exception(f"阶段 {stage} 的结果未找到")
        
        # 检查可追溯性链接
        # 这里可以添加更多的完整性检查逻辑
        
        logger.info("工作流程完整性验证通过")
    
    async def test_agent_communication(self):
        """测试智能体间通信"""
        logger.info("测试智能体间通信...")
        
        # 创建协作任务
        task_id = await self.collaboration_manager.create_collaboration_task(
            task_name="通信测试",
            participants=list(self.agents.keys()),
            workflow_config={
                "steps": [
                    {"agent": "requirement_analysis", "action": "analyze"},
                    {"agent": "requirement_review", "action": "review"}
                ]
            }
        )
        
        # 发送消息
        message = JSONFormatter.format_collaboration_message(
            from_agent="requirement_analysis",
            to_agent="requirement_review",
            message_type="request",
            content={"test": "通信测试消息"}
        )
        
        await self.collaboration_manager.send_message(
            task_id=task_id,
            message=message
        )
        
        # 等待消息处理
        await asyncio.sleep(1)
        
        # 获取消息历史
        messages = await self.collaboration_manager.get_task_messages(task_id)
        
        if messages:
            logger.info(f"通信测试成功，收到 {len(messages)} 条消息")
            return True
        else:
            logger.error("通信测试失败，未收到消息")
            return False
    
    async def generate_test_report(self):
        """生成测试报告"""
        logger.info("生成测试报告...")
        
        report = {
            "test_summary": {
                "total_stages": len(self.test_results),
                "successful_stages": len([r for r in self.test_results if r["success"]]),
                "test_time": datetime.now().isoformat()
            },
            "stage_results": self.test_results,
            "performance_metrics": {
                "memory_usage": "待实现",
                "response_times": "待实现",
                "error_rates": "待实现"
            },
            "recommendations": [
                "所有智能体协作流程正常",
                "多轮交互对齐机制工作正常",
                "JSON格式验证通过",
                "建议进行性能优化测试"
            ]
        }
        
        # 保存报告
        report_path = project_root / "app" / "logs" / f"collaboration_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        report_path.parent.mkdir(exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"测试报告已保存到: {report_path}")
        return report


async def main():
    """主测试函数"""
    test = MultiAgentCollaborationTest()
    
    try:
        # 初始化测试环境
        await test.initialize()
        
        # 执行测试
        logger.info("=" * 50)
        logger.info("开始多智能体协作对齐机制测试")
        logger.info("=" * 50)
        
        # 测试完整工作流程
        workflow_success = await test.test_complete_workflow()
        
        # 测试智能体通信
        communication_success = await test.test_agent_communication()
        
        # 生成测试报告
        report = await test.generate_test_report()
        
        # 输出测试结果
        logger.info("=" * 50)
        logger.info("测试结果总结")
        logger.info("=" * 50)
        logger.info(f"完整工作流程测试: {'通过' if workflow_success else '失败'}")
        logger.info(f"智能体通信测试: {'通过' if communication_success else '失败'}")
        logger.info(f"总体测试结果: {'通过' if workflow_success and communication_success else '失败'}")
        
        return workflow_success and communication_success
        
    except Exception as e:
        logger.error(f"测试执行失败: {str(e)}")
        return False


if __name__ == "__main__":
    # 运行测试
    success = asyncio.run(main())
    sys.exit(0 if success else 1)