"""
系统启动脚本
启动异步多智能体测试用例生成框架
"""

import asyncio
import sys
import os
import uvicorn
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.logger import get_agent_logger
from core.memory import get_memory_manager
from core.traceability import get_traceability_manager
from services.ollama_client import get_ollama_client
from config import settings

# 导入智能体
from agents.requirement_analysis import RequirementAnalysisAgent
from agents.requirement_review import RequirementReviewAgent
from agents.testcase_generation import TestCaseGenerationAgent
from agents.testcase_review import TestCaseReviewAgent
from models.enums import AgentType

logger = get_agent_logger("SystemStartup")


class SystemManager:
    """系统管理器"""
    
    def __init__(self):
        self.components_status = {}
    
    async def initialize_components(self):
        """初始化系统组件"""
        logger.info("开始初始化系统组件...")
        
        # 初始化记忆系统
        try:
            logger.info("初始化记忆系统...")
            memory_manager = await get_memory_manager()
            await memory_manager.health_check()
            self.components_status["memory"] = "healthy"
            logger.info("记忆系统初始化成功")
        except Exception as e:
            logger.error(f"记忆系统初始化失败: {str(e)}")
            self.components_status["memory"] = f"error: {str(e)}"
        
        # 初始化可追溯性系统
        # try:
        logger.info("初始化可追溯性系统...")
        traceability_manager = await get_traceability_manager()
        stats = await traceability_manager.get_statistics()
        self.components_status["traceability"] = "healthy"
        logger.info(f"可追溯性系统初始化成功，当前统计: {stats}")
        # except Exception as e:
        #     logger.error(f"可追溯性系统初始化失败: {str(e)}")
        #     self.components_status["traceability"] = f"error: {str(e)}"
        
        # 检查Ollama连接
        try:
            logger.info("检查Ollama连接...")
            ollama_client = await get_ollama_client()
            models = await ollama_client.list_models()
            if models:
                self.components_status["ollama"] = f"healthy ({len(models)} models)"
                logger.info(f"Ollama连接成功，可用模型数: {len(models)}")
            else:
                self.components_status["ollama"] = "no models available"
                logger.warning("Ollama连接成功但无可用模型")
        except Exception as e:
            logger.error(f"Ollama连接失败: {str(e)}")
            self.components_status["ollama"] = f"error: {str(e)}"
        
        # 输出组件状态
        logger.info("系统组件状态:")
        for component, status in self.components_status.items():
            logger.info(f"  {component}: {status}")
    
    def check_system_health(self):
        """检查系统健康状态"""
        healthy_components = [
            comp for comp, status in self.components_status.items() 
            if status.startswith("healthy")
        ]
        
        total_components = len(self.components_status)
        health_ratio = len(healthy_components) / total_components if total_components > 0 else 0
        
        if health_ratio >= 0.8:
            return "healthy"
        elif health_ratio >= 0.5:
            return "degraded"
        else:
            return "unhealthy"
    
    # async def start_api_server(self):
    #     """启动API服务器"""
    #     logger.info("启动FastAPI服务器...")
        
    #     try:
    #         # 导入FastAPI应用
    #         from api.main import app
            
    #         # 配置服务器参数
    #         config = uvicorn.Config(
    #             app=app,
    #             host=settings.host,
    #             port=settings.port,
    #             log_level="info",
    #             reload=settings.debug,
    #             access_log=True
    #         )
            
    #         server = uvicorn.Server(config)
            
    #         logger.info(f"API服务器将在 http://{settings.host}:{settings.port} 启动")
    #         logger.info(f"API文档地址: http://{settings.host}:{settings.port}/docs")
            
    #         await server.serve()
            
    #     except Exception as e:
    #         logger.error(f"API服务器启动失败: {str(e)}")
    #         raise
    
    async def start_api_server(self):
        """启动API服务器"""
        logger.info("启动FastAPI服务器...")
    
        # 导入FastAPI应用
        from api.main import app
        
        # 配置服务器参数
        config = uvicorn.Config(
            app=app,
            host=settings.host,
            port=settings.port,
            log_level="info",
            reload=settings.debug,
            access_log=True
        )
        
        server = uvicorn.Server(config)
        
        logger.info(f"API服务器将在 http://{settings.host}:{settings.port} 启动")
        logger.info(f"API文档地址: http://{settings.host}:{settings.port}/docs")
        
        await server.serve()
    
    async def run_startup_tests(self):
        """运行启动测试"""
        logger.info("运行启动测试...")
        
        # try:
        # 运行系统集成测试
        from scripts.test_system_integration import SystemIntegrationTest
        
        integration_test = SystemIntegrationTest()
        success = await integration_test.run_all_tests()
        
        if success:
            logger.info("启动测试通过")
            return True
        else:
            logger.warning("启动测试部分失败，但系统仍可启动")
            return False
                
        # except Exception as e:
        #     logger.error(f"启动测试失败: {str(e)}")
        #     return False

    async def run_complete_workflow(self, requirement_text: str):
        """运行完整的需求处理工作流程"""
        logger.info("=" * 80)
        logger.info("开始完整的需求处理工作流程演示")
        logger.info("=" * 80)
        logger.info(f"输入需求: {requirement_text}")

        try:
            # 第一步：需求分析
            logger.info("\n🔍 第一步：需求分析")
            logger.info("-" * 50)

            req_analysis_agent = RequirementAnalysisAgent(AgentType.REQUIREMENT_ANALYSIS)
            await req_analysis_agent.initialize()

            analysis_result = await req_analysis_agent.process(requirement_text)
            logger.info(f"需求分析完成，提取到 {len(analysis_result.get('requirements', []))} 个需求")

            # 第二步：需求评审
            logger.info("\n📋 第二步：需求评审")
            logger.info("-" * 50)

            req_review_agent = RequirementReviewAgent(AgentType.REQUIREMENT_REVIEW)
            await req_review_agent.initialize()

            review_result = await req_review_agent.process(analysis_result)
            logger.info(f"需求评审完成，评审状态: {review_result.get('overall_status', 'unknown')}")

            # 第三步：测试用例生成
            logger.info("\n🧪 第三步：测试用例生成")
            logger.info("-" * 50)

            testcase_gen_agent = TestCaseGenerationAgent(AgentType.TESTCASE_GENERATION)
            await testcase_gen_agent.initialize()

            testcases_result = await testcase_gen_agent.process(analysis_result)
            logger.info(f"测试用例生成完成，生成了 {len(testcases_result.get('testcases', []))} 个测试用例")

            # 第四步：测试用例评审
            logger.info("\n✅ 第四步：测试用例评审")
            logger.info("-" * 50)

            testcase_review_agent = TestCaseReviewAgent(AgentType.TESTCASE_REVIEW)
            await testcase_review_agent.initialize()

            final_review = await testcase_review_agent.process(testcases_result)
            logger.info(f"测试用例评审完成，最终评分: {final_review.get('overall_score', 'N/A')}")

            # 输出完整流程总结
            logger.info("\n" + "=" * 80)
            logger.info("完整工作流程总结")
            logger.info("=" * 80)
            logger.info(f"📝 原始需求: {requirement_text[:100]}...")
            logger.info(f"🔍 分析结果: 提取 {len(analysis_result.get('requirements', []))} 个需求")
            logger.info(f"📋 评审状态: {review_result.get('overall_status', 'unknown')}")
            logger.info(f"🧪 生成用例: {len(testcases_result.get('testcases', []))} 个")
            logger.info(f"✅ 最终评分: {final_review.get('overall_score', 'N/A')}")

            return {
                "requirement_text": requirement_text,
                "analysis_result": analysis_result,
                "review_result": review_result,
                "testcases_result": testcases_result,
                "final_review": final_review,
                "success": True
            }

        except Exception as e:
            logger.error(f"完整工作流程执行失败: {type(e).__name__}: {str(e)}", exc_info=True)
            return {
                "requirement_text": requirement_text,
                "success": False,
                "error": str(e)
            }


async def main():
    """主启动函数"""
    logger.info("=" * 60)
    logger.info("异步多智能体测试用例生成框架")
    logger.info("=" * 60)
    logger.info(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    system_manager = SystemManager()

    # try:
        # 初始化系统组件
    await system_manager.initialize_components()

    # 检查系统健康状态
    health_status = system_manager.check_system_health()
    logger.info(f"系统健康状态: {health_status}")

    if health_status == "unhealthy":
        logger.error("系统健康状态不佳，请检查组件配置")
        return False

    # 检查是否有需求输入参数
    requirement_text = None
    for i, arg in enumerate(sys.argv):
        if arg == "--requirement" and i + 1 < len(sys.argv):
            requirement_text = sys.argv[i + 1]
            break

    if requirement_text:
        # 运行完整的需求处理工作流程演示
        logger.info("检测到需求输入参数，将运行完整工作流程演示")
        workflow_result = await system_manager.run_complete_workflow(requirement_text)

        if workflow_result["success"]:
            logger.info("✅ 完整工作流程演示成功完成！")
        else:
            logger.error(f"❌ 完整工作流程演示失败: {workflow_result.get('error', '未知错误')}")

        return workflow_result["success"]

    # 运行启动测试（可选）
    if "--skip-tests" not in sys.argv:
        test_success = await system_manager.run_startup_tests()
        if not test_success:
            logger.warning("启动测试未完全通过，但继续启动系统")

    # 启动API服务器
    logger.info("系统初始化完成，启动API服务器...")
    await system_manager.start_api_server()
        
    # except KeyboardInterrupt:
    #     logger.info("收到中断信号，正在关闭系统...")
    #     return True
    # except Exception as e:
    #     logger.error(f"系统启动失败: {str(e)}")
    #     return False


if __name__ == "__main__":
    # 检查命令行参数
    if "--help" in sys.argv or "-h" in sys.argv:
        print("异步多智能体测试用例生成框架启动脚本")
        print("用法: python start_system.py [选项]")
        print("选项:")
        print("  --skip-tests              跳过启动测试")
        print("  --requirement <需求文本>   运行完整工作流程演示")
        print("  --help, -h                显示帮助信息")
        print()
        print("示例:")
        print('  python start_system.py --requirement "用户登录功能：用户可以通过用户名和密码登录系统"')
        print("  python start_system.py --skip-tests")
        sys.exit(0)
    
    # 运行系统
    # try:
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
    # except Exception as e:
    #     print(f"系统启动异常: {str(e)}")
    #     sys.exit(1)