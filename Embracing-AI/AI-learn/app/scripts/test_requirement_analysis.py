"""
需求分析Agent测试脚本
"""

import asyncio
import sys
from pathlib import Path
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.logger import app_logger
from agents.requirement_analysis import RequirementAnalysisAgent


async def create_test_document(doc_path: str):
    """创建测试Word文档"""
    try:
        from docx import Document
        
        doc = Document()
        doc.add_heading('电商系统需求文档', 0)
        
        doc.add_heading('1. 用户管理模块', level=1)
        doc.add_paragraph('用户能够注册新账户，需要提供用户名、密码、邮箱和手机号。')
        doc.add_paragraph('用户能够通过用户名或邮箱登录系统。')
        doc.add_paragraph('系统应该在登录失败3次后锁定账户30分钟。')
        
        doc.add_heading('2. 商品管理模块', level=1)
        doc.add_paragraph('管理员能够添加、修改、删除商品信息。')
        doc.add_paragraph('商品信息包括名称、价格、库存、描述、图片等。')
        doc.add_paragraph('系统应该支持商品分类和标签管理。')
        
        doc.add_heading('3. 订单管理模块', level=1)
        doc.add_paragraph('用户能够将商品添加到购物车。')
        doc.add_paragraph('用户能够提交订单并选择支付方式。')
        doc.add_paragraph('系统应该在订单提交后5分钟内处理完成。')
        
        doc.add_heading('4. 性能要求', level=1)
        doc.add_paragraph('系统应该支持10000个并发用户。')
        doc.add_paragraph('页面响应时间不应超过2秒。')
        doc.add_paragraph('系统可用性应达到99.9%。')
        
        doc.save(doc_path)
        app_logger.info(f"测试Word文档创建完成: {doc_path}")
        
    except Exception as e:
        app_logger.error(f"创建测试文档失败: {str(e)}")
        raise


async def test_text_analysis():
    """测试文本需求分析"""
    app_logger.info("=== 测试文本需求分析 ===")
    
    try:
        # 创建Agent
        agent = RequirementAnalysisAgent("test_session_001")
        await agent.initialize()
        
        # 测试需求文本
        requirement_text = """
        用户管理系统需求：
        1. 用户能够通过用户名和密码登录系统
        2. 系统应该在2秒内响应用户登录请求
        3. 管理员能够添加、修改、删除用户信息
        4. 系统需要记录所有用户操作日志
        5. 用户密码必须加密存储
        6. 系统应该支持1000个并发用户同时在线
        7. 用户忘记密码时能够通过邮箱重置密码
        """
        
        # 执行分析
        result = await agent.process({
            "requirement_text": requirement_text,
            "context": {
                "project_name": "用户管理系统",
                "version": "1.0",
                "stakeholders": ["产品经理", "开发团队", "测试团队"]
            }
        })
        
        app_logger.info("需求分析完成")
        app_logger.info(f"识别需求数量: {len(result.get('requirements', []))}")
        
        # 显示分析结果摘要
        summary = result.get("analysis_summary", {})
        app_logger.info(f"分析摘要: {summary}")
        
        # 显示前3个需求的详细信息
        requirements = result.get("requirements", [])
        for i, req in enumerate(requirements[:3]):
            app_logger.info(f"需求{i+1}: {req.get('name', 'N/A')}")
            app_logger.info(f"  类型: {req.get('type', 'N/A')}")
            app_logger.info(f"  风险级别: {req.get('risk_level', 'N/A')}")
            app_logger.info(f"  测试场景数: {len(req.get('pending_scenarios', []))}")
        
        # 清理资源
        await agent.cleanup()
        
        return True
        
    except Exception as e:
        app_logger.error(f"文本需求分析测试失败: {str(e)}")
        return False


async def test_document_analysis():
    """测试Word文档需求分析"""
    app_logger.info("=== 测试Word文档需求分析 ===")
    
    try:
        # 创建测试Word文档
        test_doc_path = "test_requirements.docx"
        await create_test_document(test_doc_path)
        
        # 创建Agent
        agent = RequirementAnalysisAgent("test_session_002")
        await agent.initialize()
        
        # 执行文档分析
        result = await agent.process({
            "document_path": test_doc_path,
            "context": {
                "project_name": "电商系统",
                "version": "2.0"
            }
        })
        
        app_logger.info("Word文档分析完成")
        app_logger.info(f"识别需求数量: {len(result.get('requirements', []))}")
        
        # 清理资源
        await agent.cleanup()
        
        # 删除测试文档
        Path(test_doc_path).unlink(missing_ok=True)
        
        return True
        
    except Exception as e:
        app_logger.error(f"Word文档分析测试失败: {str(e)}")
        return False


async def test_iterative_improvement():
    """测试迭代改进功能"""
    app_logger.info("=== 测试迭代改进功能 ===")
    
    try:
        # 创建Agent
        agent = RequirementAnalysisAgent("test_session_003")
        await agent.initialize()
        
        # 使用简单的需求文本测试迭代
        requirement_text = "用户登录功能"
        
        # 执行分析
        result = await agent.process({
            "requirement_text": requirement_text,
            "context": {"project_name": "测试系统"}
        })
        
        app_logger.info("迭代改进测试完成")
        app_logger.info(f"最终版本: {result.get('version', 1)}")
        
        # 检查思维链
        agent_state = await agent.get_state()
        thinking_steps = len(agent_state.thinking_chain)
        reflection_count = len(agent_state.reflection_results)
        
        app_logger.info(f"思维链步骤数: {thinking_steps}")
        app_logger.info(f"反思次数: {reflection_count}")
        
        # 清理资源
        await agent.cleanup()
        
        return True
        
    except Exception as e:
        app_logger.error(f"迭代改进测试失败: {str(e)}")
        return False


async def test_export_functionality():
    """测试导出功能"""
    app_logger.info("=== 测试导出功能 ===")
    
    try:
        # 创建Agent
        agent = RequirementAnalysisAgent("test_session_004")
        await agent.initialize()
        
        # 执行分析
        requirement_text = """
        在线购物系统需求：
        1. 用户能够浏览商品目录
        2. 用户能够将商品添加到购物车
        3. 用户能够提交订单并支付
        4. 系统应该在1秒内加载商品页面
        5. 支付信息必须加密传输
        """
        
        result = await agent.process({
            "requirement_text": requirement_text,
            "context": {"project_name": "购物系统"}
        })
        
        # 测试JSON导出
        json_export = await agent.export_requirements(result, "json")
        app_logger.info(f"JSON导出长度: {len(json_export)} 字符")
        
        # 测试Markdown导出
        md_export = await agent.export_requirements(result, "markdown")
        app_logger.info(f"Markdown导出长度: {len(md_export)} 字符")
        
        # 保存导出文件
        with open("test_requirements_export.json", "w", encoding="utf-8") as f:
            f.write(json_export)
        
        with open("test_requirements_export.md", "w", encoding="utf-8") as f:
            f.write(md_export)
        
        app_logger.info("导出文件已保存")
        
        # 清理资源
        await agent.cleanup()
        
        return True
        
    except Exception as e:
        app_logger.error(f"导出功能测试失败: {str(e)}")
        return False


async def test_requirement_classification():
    """测试需求分类功能"""
    app_logger.info("=== 测试需求分类功能 ===")
    
    try:
        # 创建Agent
        agent = RequirementAnalysisAgent("test_session_005")
        await agent.initialize()
        
        # 包含各种类型需求的文本
        mixed_requirements = """
        系统需求规格说明：
        1. 用户能够注册和登录账户（功能性）
        2. 系统响应时间不超过2秒（性能）
        3. 用户数据必须加密存储（安全）
        4. 界面应该简洁易用（可用性）
        5. 系统应该支持10000并发用户（性能）
        6. 管理员能够管理用户权限（安全）
        7. 用户能够修改个人信息（功能性）
        8. 系统应该提供友好的错误提示（可用性）
        """
        
        result = await agent.process({
            "requirement_text": mixed_requirements,
            "context": {"project_name": "综合系统"}
        })
        
        # 统计各类型需求数量
        requirements = result.get("requirements", [])
        type_counts = {}
        risk_counts = {}
        
        for req in requirements:
            req_type = req.get("type", "unknown")
            risk_level = req.get("risk_level", "unknown")
            
            type_counts[req_type] = type_counts.get(req_type, 0) + 1
            risk_counts[risk_level] = risk_counts.get(risk_level, 0) + 1
        
        app_logger.info("需求分类统计:")
        for req_type, count in type_counts.items():
            app_logger.info(f"  {req_type}: {count}")
        
        app_logger.info("风险级别统计:")
        for risk_level, count in risk_counts.items():
            app_logger.info(f"  {risk_level}: {count}")
        
        # 清理资源
        await agent.cleanup()
        
        return True
        
    except Exception as e:
        app_logger.error(f"需求分类测试失败: {str(e)}")
        return False


async def run_all_tests():
    """运行所有测试"""
    app_logger.info("开始运行需求分析Agent测试套件")
    
    tests = [
        ("文本需求分析", test_text_analysis),
        ("Word文档分析", test_document_analysis),
        ("迭代改进功能", test_iterative_improvement),
        ("导出功能", test_export_functionality),
        ("需求分类功能", test_requirement_classification)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        app_logger.info(f"\n{'='*50}")
        app_logger.info(f"运行测试: {test_name}")
        app_logger.info(f"{'='*50}")
        
        try:
            success = await test_func()
            results[test_name] = "通过" if success else "失败"
            
        except Exception as e:
            app_logger.error(f"测试 {test_name} 执行异常: {str(e)}")
            results[test_name] = "异常"
    
    # 输出测试结果摘要
    app_logger.info(f"\n{'='*50}")
    app_logger.info("测试结果摘要")
    app_logger.info(f"{'='*50}")
    
    for test_name, result in results.items():
        status_icon = "✅" if result == "通过" else "❌"
        app_logger.info(f"{status_icon} {test_name}: {result}")
    
    # 统计
    passed = sum(1 for r in results.values() if r == "通过")
    total = len(results)
    
    app_logger.info(f"\n总计: {passed}/{total} 个测试通过")
    
    return results


if __name__ == "__main__":
    asyncio.run(run_all_tests())