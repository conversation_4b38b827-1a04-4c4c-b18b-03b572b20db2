"""
需求评审Agent测试脚本
"""

import asyncio
import sys
from pathlib import Path
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.logger import app_logger
from agents.requirement_review import RequirementReviewAgent


async def create_test_requirements():
    """创建测试需求数据"""
    return [
        {
            "id": 1,
            "trace_id": "R1-20250128-0001",
            "name": "用户登录功能",
            "type": "functional",
            "description": "用户能够通过用户名和密码登录系统，系统应该验证用户身份并返回相应结果",
            "acceptance_criteria": {
                "positive": ["用户输入正确的用户名和密码能够成功登录"],
                "negative": ["用户输入错误的用户名或密码无法登录"],
                "boundary": ["用户名长度在3-20字符之间", "密码长度在6-50字符之间"],
                "error": ["系统异常时显示友好错误信息", "网络异常时提供重试机制"]
            },
            "pending_scenarios": ["正常登录", "密码错误", "用户名不存在", "账户锁定"],
            "status": "draft",
            "risk_level": "medium",
            "priority": "high",
            "pending_clarifications": []
        },
        {
            "id": 2,
            "trace_id": "R2-20250128-0002",
            "name": "系统性能要求",
            "type": "performance",
            "description": "系统应该支持高并发访问",
            "acceptance_criteria": {
                "positive": ["待定"],
                "negative": [],
                "boundary": [],
                "error": []
            },
            "pending_scenarios": ["负载测试"],
            "status": "draft",
            "risk_level": "low",
            "priority": "medium",
            "pending_clarifications": []
        },
        {
            "id": 3,
            "name": "",  # 缺少名称
            "type": "security",
            "description": "用户数据需要加密存储，确保数据安全性和隐私保护",
            "acceptance_criteria": {
                "positive": ["敏感数据使用AES-256加密", "密码使用bcrypt哈希"],
                "negative": ["不允许明文存储密码", "不允许未加密传输敏感数据"],
                "boundary": ["加密密钥长度至少256位"],
                "error": ["加密失败时记录日志并报警", "解密失败时安全处理"]
            },
            "pending_scenarios": ["数据加密测试", "密钥管理测试", "安全传输测试"],
            "status": "draft",
            "risk_level": "high",
            "priority": "critical",
            "pending_clarifications": []
        }
    ]


async def test_basic_review():
    """测试基础评审功能"""
    app_logger.info("=== 测试基础评审功能 ===")
    
    try:
        # 创建Agent
        agent = RequirementReviewAgent("test_session_001")
        await agent.initialize()
        
        # 创建测试需求
        test_requirements = await create_test_requirements()
        
        # 执行评审
        result = await agent.process({
            "requirements": test_requirements,
            "context": {
                "project_name": "用户管理系统",
                "project_type": "web_application",
                "review_focus": "all"
            }
        })
        
        app_logger.info("基础评审完成")
        
        # 显示评审结果
        summary = result.get("review_summary", {})
        app_logger.info(f"评审摘要: {summary}")
        
        issues = result.get("review_issues", [])
        app_logger.info(f"发现问题数: {len(issues)}")
        
        # 显示前3个问题
        for i, issue in enumerate(issues[:3]):
            app_logger.info(f"问题{i+1}: {issue.get('description', 'N/A')}")
            app_logger.info(f"  严重程度: {issue.get('severity', 'N/A')}")
            app_logger.info(f"  建议: {issue.get('suggestion', 'N/A')}")
        
        recommendations = result.get("recommendations", [])
        app_logger.info(f"改进建议数: {len(recommendations)}")
        
        # 清理资源
        await agent.cleanup()
        
        return True
        
    except Exception as e:
        app_logger.error(f"基础评审测试失败: {str(e)}")
        return False


async def test_completeness_check():
    """测试完整性检查"""
    app_logger.info("=== 测试完整性检查 ===")
    
    try:
        # 创建Agent
        agent = RequirementReviewAgent("test_session_002")
        await agent.initialize()
        
        # 创建不完整的需求
        incomplete_requirements = [
            {
                "id": 1,
                "name": "测试需求",
                # 缺少description
                "type": "functional",
                "acceptance_criteria": {
                    "positive": [],  # 空的验收标准
                    "negative": [],
                    "boundary": [],
                    "error": []
                },
                "pending_scenarios": [],  # 空的测试场景
                "status": "draft",
                "risk_level": "medium",
                "priority": "medium"
            }
        ]
        
        # 执行评审
        result = await agent.process({
            "requirements": incomplete_requirements,
            "context": {"review_focus": "completeness"}
        })
        
        app_logger.info("完整性检查完成")
        
        issues = result.get("review_issues", [])
        completeness_issues = [i for i in issues if "缺少" in i.get("description", "")]
        
        app_logger.info(f"完整性问题数: {len(completeness_issues)}")
        for issue in completeness_issues:
            app_logger.info(f"  - {issue.get('description', 'N/A')}")
        
        # 清理资源
        await agent.cleanup()
        
        return True
        
    except Exception as e:
        app_logger.error(f"完整性检查测试失败: {str(e)}")
        return False


async def test_consistency_check():
    """测试一致性检查"""
    app_logger.info("=== 测试一致性检查 ===")
    
    try:
        # 创建Agent
        agent = RequirementReviewAgent("test_session_003")
        await agent.initialize()
        
        # 创建不一致的需求
        inconsistent_requirements = [
            {
                "id": 1,
                "trace_id": "INVALID_FORMAT",  # 错误的追溯ID格式
                "name": "需求@#$%",  # 不符合命名规范
                "type": "functional",
                "description": "这是一个测试需求的描述",
                "acceptance_criteria": {
                    "positive": ["正向测试"],
                    "negative": ["负向测试"],
                    "boundary": ["边界测试"],
                    "error": ["异常测试"]
                },
                "pending_scenarios": ["测试场景"],
                "status": "invalid_status",  # 无效状态
                "risk_level": "invalid_risk",  # 无效风险级别
                "priority": "invalid_priority"  # 无效优先级
            }
        ]
        
        # 执行评审
        result = await agent.process({
            "requirements": inconsistent_requirements,
            "context": {"review_focus": "consistency"}
        })
        
        app_logger.info("一致性检查完成")
        
        issues = result.get("review_issues", [])
        consistency_issues = [i for i in issues if "格式" in i.get("description", "") or "无效" in i.get("description", "")]
        
        app_logger.info(f"一致性问题数: {len(consistency_issues)}")
        for issue in consistency_issues:
            app_logger.info(f"  - {issue.get('description', 'N/A')}")
        
        # 清理资源
        await agent.cleanup()
        
        return True
        
    except Exception as e:
        app_logger.error(f"一致性检查测试失败: {str(e)}")
        return False


async def test_quality_assessment():
    """测试质量评估"""
    app_logger.info("=== 测试质量评估 ===")
    
    try:
        # 创建Agent
        agent = RequirementReviewAgent("test_session_004")
        await agent.initialize()
        
        # 创建质量问题的需求
        quality_requirements = [
            {
                "id": 1,
                "trace_id": "R1-20250128-0001",
                "name": "模糊需求",
                "type": "functional",
                "description": "系统应该比较快，用户体验要好，性能要合理，界面要美观",  # 模糊描述
                "acceptance_criteria": {
                    "positive": ["待定", "TBD"],  # 不具体的验收标准
                    "negative": ["不应该慢"],
                    "boundary": ["合适的范围"],
                    "error": ["错误处理"]
                },
                "pending_scenarios": ["测试"],
                "status": "draft",
                "risk_level": "medium",
                "priority": "medium"
            }
        ]
        
        # 执行评审
        result = await agent.process({
            "requirements": quality_requirements,
            "context": {"review_focus": "quality"}
        })
        
        app_logger.info("质量评估完成")
        
        issues = result.get("review_issues", [])
        quality_issues = [i for i in issues if "模糊" in i.get("description", "") or "不够具体" in i.get("description", "")]
        
        app_logger.info(f"质量问题数: {len(quality_issues)}")
        for issue in quality_issues:
            app_logger.info(f"  - {issue.get('description', 'N/A')}")
        
        # 清理资源
        await agent.cleanup()
        
        return True
        
    except Exception as e:
        app_logger.error(f"质量评估测试失败: {str(e)}")
        return False


async def test_risk_assessment():
    """测试风险评估"""
    app_logger.info("=== 测试风险评估 ===")
    
    try:
        # 创建Agent
        agent = RequirementReviewAgent("test_session_005")
        await agent.initialize()
        
        # 创建风险评估不当的需求
        risk_requirements = [
            {
                "id": 1,
                "trace_id": "R1-20250128-0001",
                "name": "支付安全需求",
                "type": "security",
                "description": "系统需要处理用户支付信息，包括信用卡数据、银行账户信息等核心金融数据的安全存储和传输",
                "acceptance_criteria": {
                    "positive": ["支付数据加密存储"],
                    "negative": ["不允许明文存储"],
                    "boundary": ["支持多种支付方式"],
                    "error": ["支付失败时回滚"]
                },
                "pending_scenarios": ["支付测试"],
                "status": "draft",
                "risk_level": "low",  # 风险评估过低
                "priority": "medium"
            }
        ]
        
        # 执行评审
        result = await agent.process({
            "requirements": risk_requirements,
            "context": {"review_focus": "risk"}
        })
        
        app_logger.info("风险评估完成")
        
        issues = result.get("review_issues", [])
        risk_issues = [i for i in issues if "风险" in i.get("description", "")]
        
        app_logger.info(f"风险问题数: {len(risk_issues)}")
        for issue in risk_issues:
            app_logger.info(f"  - {issue.get('description', 'N/A')}")
        
        # 清理资源
        await agent.cleanup()
        
        return True
        
    except Exception as e:
        app_logger.error(f"风险评估测试失败: {str(e)}")
        return False


async def test_export_functionality():
    """测试导出功能"""
    app_logger.info("=== 测试导出功能 ===")
    
    try:
        # 创建Agent
        agent = RequirementReviewAgent("test_session_006")
        await agent.initialize()
        
        # 创建测试需求
        test_requirements = await create_test_requirements()
        
        # 执行评审
        result = await agent.process({
            "requirements": test_requirements,
            "context": {"project_name": "测试项目"}
        })
        
        # 测试JSON导出
        json_export = await agent.export_review_report(result, "json")
        app_logger.info(f"JSON导出长度: {len(json_export)} 字符")
        
        # 测试Markdown导出
        md_export = await agent.export_review_report(result, "markdown")
        app_logger.info(f"Markdown导出长度: {len(md_export)} 字符")
        
        # 保存导出文件
        with open("test_review_export.json", "w", encoding="utf-8") as f:
            f.write(json_export)
        
        with open("test_review_export.md", "w", encoding="utf-8") as f:
            f.write(md_export)
        
        app_logger.info("评审报告导出文件已保存")
        
        # 清理资源
        await agent.cleanup()
        
        return True
        
    except Exception as e:
        app_logger.error(f"导出功能测试失败: {str(e)}")
        return False


async def run_all_tests():
    """运行所有测试"""
    app_logger.info("开始运行需求评审Agent测试套件")
    
    tests = [
        ("基础评审功能", test_basic_review),
        ("完整性检查", test_completeness_check),
        ("一致性检查", test_consistency_check),
        ("质量评估", test_quality_assessment),
        ("风险评估", test_risk_assessment),
        ("导出功能", test_export_functionality)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        app_logger.info(f"\n{'='*50}")
        app_logger.info(f"运行测试: {test_name}")
        app_logger.info(f"{'='*50}")
        
        try:
            success = await test_func()
            results[test_name] = "通过" if success else "失败"
            
        except Exception as e:
            app_logger.error(f"测试 {test_name} 执行异常: {str(e)}")
            results[test_name] = "异常"
    
    # 输出测试结果摘要
    app_logger.info(f"\n{'='*50}")
    app_logger.info("测试结果摘要")
    app_logger.info(f"{'='*50}")
    
    for test_name, result in results.items():
        status_icon = "✅" if result == "通过" else "❌"
        app_logger.info(f"{status_icon} {test_name}: {result}")
    
    # 统计
    passed = sum(1 for r in results.values() if r == "通过")
    total = len(results)
    
    app_logger.info(f"\n总计: {passed}/{total} 个测试通过")
    
    return results


if __name__ == "__main__":
    asyncio.run(run_all_tests())