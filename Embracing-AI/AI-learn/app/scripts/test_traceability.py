"""
可追溯性矩阵和状态管理系统测试脚本
"""

import asyncio
import sys
from pathlib import Path
import json
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.logger import app_logger
from core.traceability import (
    TraceabilityMatrix, StateManager, TraceabilityArtifact, TraceabilityLink,
    ArtifactType, ArtifactStatus, TraceabilityType, ChangeType,
    AgentTraceabilityMixin, get_traceability_manager, get_state_manager,
    cleanup_traceability
)
from models.enums import AgentType


class TestAgent(AgentTraceabilityMixin):
    """测试Agent"""
    
    def __init__(self, agent_id: str, agent_type: AgentType):
        super().__init__()
        self.agent_id = agent_id
        self.agent_type = agent_type


async def test_traceability_matrix():
    """测试可追溯性矩阵"""
    app_logger.info("=== 测试可追溯性矩阵 ===")
    
    try:
        # 获取追溯矩阵管理器
        matrix = await get_traceability_manager()
        
        # 测试创建工件
        req_id = await matrix.create_artifact(
            artifact_type=ArtifactType.REQUIREMENT,
            name="用户登录需求",
            description="用户能够通过用户名和密码登录系统",
            content={
                "priority": "high",
                "type": "functional",
                "acceptance_criteria": ["正确凭据登录成功", "错误凭据登录失败"]
            },
            created_by="test_user",
            tags=["登录", "功能需求"]
        )
        
        app_logger.info(f"创建需求工件成功: {req_id}")
        
        # 创建测试用例工件
        tc_id = await matrix.create_artifact(
            artifact_type=ArtifactType.TESTCASE,
            name="用户登录测试用例",
            description="验证用户登录功能的测试用例",
            content={
                "steps": ["输入用户名", "输入密码", "点击登录"],
                "expected": "成功登录系统"
            },
            created_by="test_user",
            tags=["登录", "测试用例"]
        )
        
        app_logger.info(f"创建测试用例工件成功: {tc_id}")
        
        # 创建追溯链接
        link_id = await matrix.create_link(
            source_id=req_id,
            target_id=tc_id,
            link_type=TraceabilityType.TESTS,
            strength=0.9,
            created_by="test_user"
        )
        
        app_logger.info(f"创建追溯链接成功: {link_id}")
        
        # 测试获取工件
        requirement = await matrix.get_artifact(req_id)
        assert requirement is not None
        assert requirement.name == "用户登录需求"
        
        # 测试获取相关工件
        related = await matrix.get_related_artifacts(req_id)
        assert len(related) > 0
        
        app_logger.info(f"获取到 {len(related)} 个相关工件")
        
        # 测试按类型获取工件
        requirements = await matrix.get_artifacts_by_type(ArtifactType.REQUIREMENT)
        testcases = await matrix.get_artifacts_by_type(ArtifactType.TESTCASE)
        
        app_logger.info(f"需求工件数: {len(requirements)}, 测试用例工件数: {len(testcases)}")
        
        # 测试生成覆盖率报告
        coverage_report = await matrix.generate_coverage_report(
            source_type=ArtifactType.REQUIREMENT,
            target_type=ArtifactType.TESTCASE,
            link_types=[TraceabilityType.TESTS]
        )
        
        app_logger.info(f"覆盖率报告: {coverage_report.coverage_percentage:.1f}%")
        app_logger.info(f"总需求数: {coverage_report.total_requirements}")
        app_logger.info(f"已覆盖需求数: {coverage_report.covered_requirements}")
        
        # 测试统计信息
        stats = await matrix.get_statistics()
        app_logger.info(f"统计信息: {stats}")
        
        return True
        
    except Exception as e:
        app_logger.error(f"可追溯性矩阵测试失败: {str(e)}")
        return False


async def test_state_manager():
    """测试状态管理器"""
    app_logger.info("=== 测试状态管理器 ===")
    
    try:
        # 获取状态管理器
        state_manager = await get_state_manager()
        
        # 测试设置状态
        await state_manager.set_state("agent_001", "status", "processing")
        await state_manager.set_state("agent_001", "progress", 0.5)
        await state_manager.set_state("agent_001", "current_task", "需求分析")
        
        # 测试获取状态
        status = await state_manager.get_state("agent_001", "status")
        assert status == "processing"
        
        progress = await state_manager.get_state("agent_001", "progress")
        assert progress == 0.5
        
        # 测试获取所有状态
        all_states = await state_manager.get_state("agent_001")
        assert "status" in all_states
        assert "progress" in all_states
        assert "current_task" in all_states
        
        app_logger.info(f"Agent状态: {all_states}")
        
        # 测试状态历史
        history = await state_manager.get_state_history("agent_001")
        app_logger.info(f"状态历史记录数: {len(history)}")
        
        # 测试状态监听
        state_changes = []
        
        def state_watcher(entity_id, state_key, old_value, new_value):
            state_changes.append({
                "entity_id": entity_id,
                "key": state_key,
                "old": old_value,
                "new": new_value
            })
        
        state_manager.watch_state("agent_001", state_watcher)
        
        # 触发状态变化
        await state_manager.set_state("agent_001", "status", "completed")
        
        # 等待通知
        await asyncio.sleep(0.1)
        
        assert len(state_changes) > 0
        app_logger.info(f"捕获到 {len(state_changes)} 个状态变化")
        
        return True
        
    except Exception as e:
        app_logger.error(f"状态管理器测试失败: {str(e)}")
        return False


async def test_agent_traceability_mixin():
    """测试Agent追溯混入"""
    app_logger.info("=== 测试Agent追溯混入 ===")
    
    try:
        # 创建测试Agent
        agent = TestAgent("trace_agent_001", AgentType.REQUIREMENT_ANALYSIS)
        
        # 设置追溯系统
        await agent.setup_traceability()
        
        # 测试创建工件
        artifact_id = await agent.create_artifact(
            artifact_type=ArtifactType.REQUIREMENT,
            name="Agent创建的需求",
            description="通过Agent混入创建的需求工件",
            content={"test": "data"},
            tags=["agent", "测试"]
        )
        
        app_logger.info(f"Agent创建工件成功: {artifact_id}")
        
        # 创建另一个工件用于链接
        testcase_id = await agent.create_artifact(
            artifact_type=ArtifactType.TESTCASE,
            name="Agent创建的测试用例",
            description="通过Agent混入创建的测试用例工件",
            content={"test": "testcase"},
            tags=["agent", "测试用例"]
        )
        
        # 测试创建链接
        link_id = await agent.create_link(
            source_id=artifact_id,
            target_id=testcase_id,
            link_type=TraceabilityType.TESTS
        )
        
        app_logger.info(f"Agent创建链接成功: {link_id}")
        
        # 测试获取相关工件
        related = await agent.get_related_artifacts(artifact_id)
        app_logger.info(f"Agent获取到 {len(related)} 个相关工件")
        
        # 测试状态管理
        await agent.set_state("current_task", "测试追溯功能")
        await agent.set_state("progress", 0.8)
        
        current_task = await agent.get_state("current_task")
        progress = await agent.get_state("progress")
        
        app_logger.info(f"Agent状态 - 当前任务: {current_task}, 进度: {progress}")
        
        # 测试生成覆盖率报告
        coverage = await agent.generate_coverage_report(
            source_type=ArtifactType.REQUIREMENT,
            target_type=ArtifactType.TESTCASE
        )
        
        app_logger.info(f"Agent生成覆盖率报告: {coverage.coverage_percentage:.1f}%")
        
        return True
        
    except Exception as e:
        app_logger.error(f"Agent追溯混入测试失败: {str(e)}")
        return False


async def test_traceability_paths():
    """测试追溯路径"""
    app_logger.info("=== 测试追溯路径 ===")
    
    try:
        matrix = await get_traceability_manager()
        
        # 创建一系列相关工件
        req1_id = await matrix.create_artifact(
            ArtifactType.REQUIREMENT, "需求1", "第一个需求", {"id": 1}
        )
        
        req2_id = await matrix.create_artifact(
            ArtifactType.REQUIREMENT, "需求2", "第二个需求", {"id": 2}
        )
        
        tc1_id = await matrix.create_artifact(
            ArtifactType.TESTCASE, "测试用例1", "第一个测试用例", {"id": 1}
        )
        
        tc2_id = await matrix.create_artifact(
            ArtifactType.TESTCASE, "测试用例2", "第二个测试用例", {"id": 2}
        )
        
        review_id = await matrix.create_artifact(
            ArtifactType.REVIEW, "评审记录", "测试用例评审记录", {"id": 1}
        )
        
        # 创建链接形成路径
        await matrix.create_link(req1_id, tc1_id, TraceabilityType.TESTS)
        await matrix.create_link(req2_id, tc2_id, TraceabilityType.TESTS)
        await matrix.create_link(tc1_id, review_id, TraceabilityType.REVIEWS)
        await matrix.create_link(tc2_id, review_id, TraceabilityType.REVIEWS)
        await matrix.create_link(req1_id, req2_id, TraceabilityType.RELATES_TO)
        
        # 测试获取追溯路径
        paths = await matrix.get_traceability_path(req1_id, review_id)
        app_logger.info(f"从需求1到评审记录的路径数: {len(paths)}")
        
        for i, path in enumerate(paths):
            app_logger.info(f"路径{i+1}: {' -> '.join(path)}")
        
        # 测试影响分析
        impact = await matrix.get_impact_analysis(req1_id)
        app_logger.info(f"需求1的影响分析: 直接影响{len(impact['directly_impacted'])}个, 间接影响{len(impact['indirectly_impacted'])}个")
        
        return True
        
    except Exception as e:
        app_logger.error(f"追溯路径测试失败: {str(e)}")
        return False


async def test_change_tracking():
    """测试变更跟踪"""
    app_logger.info("=== 测试变更跟踪 ===")
    
    try:
        matrix = await get_traceability_manager()
        
        # 创建工件
        artifact_id = await matrix.create_artifact(
            ArtifactType.REQUIREMENT,
            "变更测试需求",
            "用于测试变更跟踪的需求",
            {"version": "1.0", "status": "draft"},
            created_by="test_user"
        )
        
        # 更新工件
        await matrix.update_artifact(
            artifact_id,
            description="更新后的需求描述",
            content={"version": "1.1", "status": "under_review"},
            status=ArtifactStatus.UNDER_REVIEW,
            updated_by="test_user"
        )
        
        # 再次更新
        await matrix.update_artifact(
            artifact_id,
            status=ArtifactStatus.APPROVED,
            content={"version": "1.2", "status": "approved"},
            updated_by="reviewer"
        )
        
        # 获取变更历史
        changes = await matrix.get_change_history(artifact_id)
        app_logger.info(f"工件 {artifact_id} 的变更历史记录数: {len(changes)}")
        
        for change in changes:
            app_logger.info(f"变更: {change.change_type.value} by {change.changed_by} at {change.changed_at}")
        
        # 获取所有变更历史
        all_changes = await matrix.get_change_history()
        app_logger.info(f"系统总变更记录数: {len(all_changes)}")
        
        return True
        
    except Exception as e:
        app_logger.error(f"变更跟踪测试失败: {str(e)}")
        return False


async def test_coverage_analysis():
    """测试覆盖率分析"""
    app_logger.info("=== 测试覆盖率分析 ===")
    
    try:
        matrix = await get_traceability_manager()
        
        # 创建多个需求
        req_ids = []
        for i in range(5):
            req_id = await matrix.create_artifact(
                ArtifactType.REQUIREMENT,
                f"需求{i+1}",
                f"第{i+1}个需求",
                {"priority": "high" if i < 3 else "medium"}
            )
            req_ids.append(req_id)
        
        # 创建测试用例，但只覆盖部分需求
        tc_ids = []
        for i in range(3):  # 只创建3个测试用例
            tc_id = await matrix.create_artifact(
                ArtifactType.TESTCASE,
                f"测试用例{i+1}",
                f"第{i+1}个测试用例",
                {"type": "functional"}
            )
            tc_ids.append(tc_id)
            
            # 链接到对应的需求
            await matrix.create_link(req_ids[i], tc_id, TraceabilityType.TESTS)
        
        # 生成覆盖率报告
        coverage = await matrix.generate_coverage_report(
            ArtifactType.REQUIREMENT,
            ArtifactType.TESTCASE,
            [TraceabilityType.TESTS]
        )
        
        app_logger.info(f"覆盖率分析结果:")
        app_logger.info(f"  总需求数: {coverage.total_requirements}")
        app_logger.info(f"  已覆盖需求数: {coverage.covered_requirements}")
        app_logger.info(f"  未覆盖需求数: {len(coverage.uncovered_requirements)}")
        app_logger.info(f"  覆盖率: {coverage.coverage_percentage:.1f}%")
        app_logger.info(f"  缺口数: {len(coverage.gaps)}")
        app_logger.info(f"  建议数: {len(coverage.recommendations)}")
        
        for gap in coverage.gaps:
            app_logger.info(f"  缺口: {gap['artifact_name']} - {gap['description']}")
        
        for rec in coverage.recommendations:
            app_logger.info(f"  建议: {rec}")
        
        return True
        
    except Exception as e:
        app_logger.error(f"覆盖率分析测试失败: {str(e)}")
        return False


async def test_cleanup():
    """测试资源清理"""
    app_logger.info("=== 测试资源清理 ===")
    
    try:
        # 执行清理
        await cleanup_traceability()
        
        app_logger.info("追溯资源清理测试完成")
        return True
        
    except Exception as e:
        app_logger.error(f"资源清理测试失败: {str(e)}")
        return False


async def run_all_tests():
    """运行所有测试"""
    app_logger.info("开始运行可追溯性矩阵和状态管理系统测试套件")
    
    tests = [
        ("可追溯性矩阵", test_traceability_matrix),
        ("状态管理器", test_state_manager),
        ("Agent追溯混入", test_agent_traceability_mixin),
        ("追溯路径", test_traceability_paths),
        ("变更跟踪", test_change_tracking),
        ("覆盖率分析", test_coverage_analysis),
        ("资源清理", test_cleanup)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        app_logger.info(f"\n{'='*50}")
        app_logger.info(f"运行测试: {test_name}")
        app_logger.info(f"{'='*50}")
        
        try:
            success = await test_func()
            results[test_name] = "通过" if success else "失败"
            
        except Exception as e:
            app_logger.error(f"测试 {test_name} 执行异常: {str(e)}")
            results[test_name] = "异常"
    
    # 输出测试结果摘要
    app_logger.info(f"\n{'='*50}")
    app_logger.info("测试结果摘要")
    app_logger.info(f"{'='*50}")
    
    for test_name, result in results.items():
        status_icon = "✅" if result == "通过" else "❌"
        app_logger.info(f"{status_icon} {test_name}: {result}")
    
    # 统计
    passed = sum(1 for r in results.values() if r == "通过")
    total = len(results)
    
    app_logger.info(f"\n总计: {passed}/{total} 个测试通过")
    
    return results


if __name__ == "__main__":
    asyncio.run(run_all_tests())