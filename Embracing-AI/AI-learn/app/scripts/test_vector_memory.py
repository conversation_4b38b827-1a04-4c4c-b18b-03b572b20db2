"""
向量记忆系统测试脚本
"""

import asyncio
import sys
from pathlib import Path
import json
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.logger import app_logger
from core.vector_memory import (
    VectorMemoryManager, SharedKnowledgeBase, MemoryEntry, MemoryType, 
    MemoryScope, SearchQuery, AgentMemoryMixin, get_vector_memory_manager,
    get_shared_knowledge_base, cleanup_vector_memory
)
from models.enums import AgentType


class TestAgent(AgentMemoryMixin):
    """测试Agent"""
    
    def __init__(self, agent_id: str, agent_type: AgentType, session_id: str):
        super().__init__()
        self.agent_id = agent_id
        self.agent_type = agent_type
        self.session_id = session_id


async def test_vector_memory_manager():
    """测试向量记忆管理器"""
    app_logger.info("=== 测试向量记忆管理器 ===")
    
    try:
        # 获取向量记忆管理器
        memory_manager = await get_vector_memory_manager()
        
        # 测试存储记忆
        test_content = {
            "requirement_id": 1,
            "name": "用户登录功能",
            "description": "用户能够通过用户名和密码登录系统",
            "type": "functional",
            "priority": "high"
        }
        
        memory_id = await memory_manager.store_memory(
            content=test_content,
            memory_type=MemoryType.REQUIREMENT,
            scope=MemoryScope.PROJECT,
            tags=["登录", "功能需求", "高优先级"],
            source_agent="test_agent",
            session_id="test_session_001"
        )
        
        app_logger.info(f"存储记忆成功: {memory_id}")
        
        # 测试获取记忆
        retrieved_memory = await memory_manager.get_memory(memory_id)
        assert retrieved_memory is not None
        assert retrieved_memory.content["name"] == "用户登录功能"
        
        app_logger.info("获取记忆测试通过")
        
        # 测试搜索记忆
        search_query = SearchQuery(
            text="用户登录功能",
            memory_types=[MemoryType.REQUIREMENT],
            limit=5
        )
        
        search_results = await memory_manager.search_memories(search_query)
        assert len(search_results) > 0
        assert search_results[0].id == memory_id
        
        app_logger.info("搜索记忆测试通过")
        
        # 测试更新记忆
        updated_content = test_content.copy()
        updated_content["description"] = "用户能够通过用户名和密码安全登录系统"
        
        update_success = await memory_manager.update_memory(
            memory_id=memory_id,
            content=updated_content,
            tags=["登录", "功能需求", "高优先级", "安全"]
        )
        
        assert update_success is True
        
        # 验证更新
        updated_memory = await memory_manager.get_memory(memory_id)
        assert "安全登录" in updated_memory.content["description"]
        assert "安全" in updated_memory.tags
        
        app_logger.info("更新记忆测试通过")
        
        # 测试按类型获取记忆
        requirement_memories = await memory_manager.get_memories_by_type(
            MemoryType.REQUIREMENT, limit=10
        )
        assert len(requirement_memories) > 0
        
        app_logger.info("按类型获取记忆测试通过")
        
        # 测试记忆统计
        stats = await memory_manager.get_memory_statistics()
        assert stats["total_memories"] > 0
        assert MemoryType.REQUIREMENT.value in stats["by_type"]
        
        app_logger.info(f"记忆统计: {stats}")
        
        return True
        
    except Exception as e:
        app_logger.error(f"向量记忆管理器测试失败: {str(e)}")
        return False


async def test_shared_knowledge_base():
    """测试共享知识库"""
    app_logger.info("=== 测试共享知识库 ===")
    
    try:
        # 获取共享知识库
        knowledge_base = await get_shared_knowledge_base()
        
        # 测试存储经验
        experience_content = {
            "task": "需求分析",
            "approach": "使用思维链方法分析需求",
            "result": "成功识别了5个功能需求和3个非功能需求",
            "lessons_learned": ["思维链方法有效", "需要更多时间分析非功能需求"],
            "success_factors": ["详细的需求文档", "与用户的充分沟通"]
        }
        
        experience_id = await knowledge_base.store_experience(
            agent_id="requirement_agent_001",
            session_id="test_session_001",
            experience_type="requirement_analysis",
            content=experience_content,
            tags=["需求分析", "思维链", "成功案例"]
        )
        
        app_logger.info(f"存储经验成功: {experience_id}")
        
        # 测试获取相关经验
        relevant_experiences = await knowledge_base.get_relevant_experiences(
            query="需求分析方法",
            agent_type=AgentType.REQUIREMENT_ANALYSIS,
            limit=5
        )
        
        assert len(relevant_experiences) > 0
        app_logger.info(f"获取到 {len(relevant_experiences)} 个相关经验")
        
        # 测试存储最佳实践
        best_practice_id = await knowledge_base.store_best_practice(
            title="高效需求分析流程",
            description="通过结构化方法进行需求分析的最佳实践",
            context={"domain": "软件开发", "complexity": "medium"},
            steps=[
                "收集原始需求文档",
                "使用思维链方法分解需求",
                "识别功能性和非功能性需求",
                "与利益相关者确认需求",
                "生成结构化需求文档"
            ],
            tags=["需求分析", "最佳实践", "流程"],
            source_agent="requirement_agent_001"
        )
        
        app_logger.info(f"存储最佳实践成功: {best_practice_id}")
        
        # 测试获取最佳实践
        best_practices = await knowledge_base.get_best_practices(
            query="需求分析流程",
            limit=5
        )
        
        assert len(best_practices) > 0
        app_logger.info(f"获取到 {len(best_practices)} 个最佳实践")
        
        # 测试从反馈中学习
        feedback = {
            "task_id": "req_analysis_001",
            "feedback_type": "quality_review",
            "rating": 4,
            "comments": "分析质量很好，但需要更多关注性能需求",
            "suggestions": ["加强性能需求分析", "提供更详细的验收标准"],
            "positive_aspects": ["结构清晰", "覆盖全面"],
            "improvement_areas": ["性能需求", "验收标准"]
        }
        
        await knowledge_base.learn_from_feedback(
            agent_id="requirement_agent_001",
            task_type="requirement_analysis",
            feedback=feedback,
            session_id="test_session_001"
        )
        
        app_logger.info("从反馈学习测试完成")
        
        # 测试获取学习洞察
        insights = await knowledge_base.get_learning_insights(
            agent_id="requirement_agent_001",
            task_type="requirement_analysis",
            limit=10
        )
        
        app_logger.info(f"获取到 {len(insights)} 个学习洞察")
        for insight in insights[:3]:
            app_logger.info(f"  洞察: {insight['insight']}")
        
        return True
        
    except Exception as e:
        app_logger.error(f"共享知识库测试失败: {str(e)}")
        return False


async def test_agent_memory_mixin():
    """测试Agent记忆混入"""
    app_logger.info("=== 测试Agent记忆混入 ===")
    
    try:
        # 创建测试Agent
        agent = TestAgent(
            agent_id="test_agent_001",
            agent_type=AgentType.REQUIREMENT_ANALYSIS,
            session_id="test_session_002"
        )
        
        # 设置记忆系统
        await agent.setup_memory()
        
        # 测试存储记忆
        memory_content = {
            "task": "分析用户注册需求",
            "input": "用户需要能够注册账户",
            "output": {
                "functional_requirements": [
                    "用户信息输入",
                    "邮箱验证",
                    "密码设置"
                ],
                "non_functional_requirements": [
                    "响应时间 < 2秒",
                    "数据加密存储"
                ]
            },
            "quality_score": 0.85
        }
        
        memory_id = await agent.store_memory(
            content=memory_content,
            memory_type=MemoryType.REQUIREMENT,
            tags=["用户注册", "需求分析"]
        )
        
        app_logger.info(f"Agent存储记忆成功: {memory_id}")
        
        # 测试搜索记忆
        memories = await agent.search_memories(
            query="用户注册需求分析",
            memory_types=[MemoryType.REQUIREMENT],
            limit=5
        )
        
        assert len(memories) > 0
        app_logger.info(f"Agent搜索到 {len(memories)} 个记忆")
        
        # 测试获取相关经验
        experiences = await agent.get_relevant_experiences(
            query="需求分析经验",
            limit=3
        )
        
        app_logger.info(f"Agent获取到 {len(experiences)} 个相关经验")
        
        # 测试存储经验
        experience_id = await agent.store_experience(
            experience_type="requirement_analysis",
            content={
                "task": "用户注册需求分析",
                "method": "结构化分析",
                "result": "成功识别3个功能需求和2个非功能需求",
                "duration": "15分钟",
                "challenges": ["需求描述不够详细"],
                "solutions": ["主动询问澄清问题"]
            },
            tags=["需求分析", "用户注册", "经验总结"]
        )
        
        app_logger.info(f"Agent存储经验成功: {experience_id}")
        
        # 测试从反馈学习
        feedback = {
            "task_performance": "良好",
            "accuracy": 0.9,
            "completeness": 0.8,
            "feedback_text": "分析准确，但可以更全面地考虑边界情况",
            "improvement_suggestions": ["考虑更多边界情况", "增加异常处理需求"]
        }
        
        await agent.learn_from_feedback(feedback)
        app_logger.info("Agent从反馈学习完成")
        
        # 测试获取记忆上下文
        context = await agent.get_memory_context("用户注册", limit=3)
        assert len(context) > 0
        app_logger.info(f"获取记忆上下文长度: {len(context)} 字符")
        
        return True
        
    except Exception as e:
        app_logger.error(f"Agent记忆混入测试失败: {str(e)}")
        return False


async def test_memory_search_performance():
    """测试记忆搜索性能"""
    app_logger.info("=== 测试记忆搜索性能 ===")
    
    try:
        memory_manager = await get_vector_memory_manager()
        
        # 批量创建测试记忆
        memory_ids = []
        test_data = [
            {"name": "用户登录", "type": "功能需求", "priority": "高"},
            {"name": "数据查询", "type": "功能需求", "priority": "中"},
            {"name": "系统性能", "type": "非功能需求", "priority": "高"},
            {"name": "安全认证", "type": "功能需求", "priority": "高"},
            {"name": "用户界面", "type": "功能需求", "priority": "低"},
            {"name": "数据备份", "type": "非功能需求", "priority": "中"},
            {"name": "错误处理", "type": "功能需求", "priority": "中"},
            {"name": "日志记录", "type": "非功能需求", "priority": "低"},
            {"name": "用户权限", "type": "功能需求", "priority": "高"},
            {"name": "系统监控", "type": "非功能需求", "priority": "中"}
        ]
        
        # 存储测试数据
        start_time = datetime.now()
        for i, data in enumerate(test_data):
            memory_id = await memory_manager.store_memory(
                content=data,
                memory_type=MemoryType.REQUIREMENT,
                scope=MemoryScope.PROJECT,
                tags=[data["type"], data["priority"]],
                source_agent=f"test_agent_{i % 3}",
                session_id="performance_test"
            )
            memory_ids.append(memory_id)
        
        store_duration = (datetime.now() - start_time).total_seconds()
        app_logger.info(f"存储 {len(test_data)} 条记忆耗时: {store_duration:.2f}秒")
        
        # 测试搜索性能
        search_queries = [
            "用户登录功能",
            "系统性能要求",
            "数据相关需求",
            "高优先级需求",
            "功能需求列表"
        ]
        
        total_search_time = 0
        for query in search_queries:
            start_time = datetime.now()
            
            search_query = SearchQuery(
                text=query,
                memory_types=[MemoryType.REQUIREMENT],
                limit=5
            )
            
            results = await memory_manager.search_memories(search_query)
            search_duration = (datetime.now() - start_time).total_seconds()
            total_search_time += search_duration
            
            app_logger.info(f"搜索 '{query}' 耗时: {search_duration:.3f}秒, 结果数: {len(results)}")
        
        avg_search_time = total_search_time / len(search_queries)
        app_logger.info(f"平均搜索耗时: {avg_search_time:.3f}秒")
        
        # 清理测试数据
        for memory_id in memory_ids:
            await memory_manager.delete_memory(memory_id)
        
        return True
        
    except Exception as e:
        app_logger.error(f"记忆搜索性能测试失败: {str(e)}")
        return False


async def test_memory_relationships():
    """测试记忆关系"""
    app_logger.info("=== 测试记忆关系 ===")
    
    try:
        memory_manager = await get_vector_memory_manager()
        knowledge_base = await get_shared_knowledge_base()
        
        # 创建相关的记忆
        requirement_content = {
            "id": 1,
            "name": "用户登录功能",
            "description": "用户通过用户名和密码登录系统"
        }
        
        testcase_content = {
            "id": "TC001",
            "name": "用户登录测试",
            "requirement_id": 1,
            "steps": ["输入用户名", "输入密码", "点击登录"],
            "expected": "成功登录系统"
        }
        
        review_content = {
            "testcase_id": "TC001",
            "reviewer": "test_reviewer",
            "status": "通过",
            "comments": "测试用例设计合理"
        }
        
        # 存储记忆
        req_memory_id = await memory_manager.store_memory(
            content=requirement_content,
            memory_type=MemoryType.REQUIREMENT,
            tags=["登录", "功能需求"]
        )
        
        tc_memory_id = await memory_manager.store_memory(
            content=testcase_content,
            memory_type=MemoryType.TESTCASE,
            tags=["登录", "测试用例"]
        )
        
        review_memory_id = await memory_manager.store_memory(
            content=review_content,
            memory_type=MemoryType.REVIEW,
            tags=["登录", "评审"]
        )
        
        # 建立关系
        await knowledge_base.knowledge_graph.add_relationship(
            source_memory_id=req_memory_id,
            target_memory_id=tc_memory_id,
            relationship_type="generates",
            strength=0.9
        )
        
        await knowledge_base.knowledge_graph.add_relationship(
            source_memory_id=tc_memory_id,
            target_memory_id=review_memory_id,
            relationship_type="reviewed_by",
            strength=0.8
        )
        
        # 测试获取相关记忆
        related_memories = await memory_manager.get_related_memories(
            memory_id=req_memory_id,
            limit=5
        )
        
        app_logger.info(f"需求记忆的相关记忆数: {len(related_memories)}")
        
        # 测试通过关系图获取相关记忆
        graph_related = await knowledge_base.knowledge_graph.get_related_memories(
            memory_id=req_memory_id,
            max_depth=2
        )
        
        app_logger.info(f"通过关系图获取的相关记忆数: {len(graph_related)}")
        
        return True
        
    except Exception as e:
        app_logger.error(f"记忆关系测试失败: {str(e)}")
        return False


async def test_memory_expiration():
    """测试记忆过期机制"""
    app_logger.info("=== 测试记忆过期机制 ===")
    
    try:
        memory_manager = await get_vector_memory_manager()
        
        # 创建即将过期的记忆
        expires_soon = datetime.now() + timedelta(seconds=1)
        
        memory_id = await memory_manager.store_memory(
            content={"test": "expiring_memory"},
            memory_type=MemoryType.EXPERIENCE,
            tags=["临时", "测试"],
            expires_at=expires_soon
        )
        
        # 验证记忆存在
        memory = await memory_manager.get_memory(memory_id)
        assert memory is not None
        app_logger.info("过期前记忆存在")
        
        # 等待过期
        await asyncio.sleep(2)
        
        # 清理过期记忆
        expired_count = await memory_manager.cleanup_expired_memories()
        app_logger.info(f"清理了 {expired_count} 条过期记忆")
        
        # 验证记忆已被清理
        memory = await memory_manager.get_memory(memory_id)
        # 注意：由于我们的实现中删除是物理删除，所以这里应该是None
        app_logger.info(f"过期后记忆状态: {'存在' if memory else '已清理'}")
        
        return True
        
    except Exception as e:
        app_logger.error(f"记忆过期机制测试失败: {str(e)}")
        return False


async def test_knowledge_patterns():
    """测试知识模式发现"""
    app_logger.info("=== 测试知识模式发现 ===")
    
    try:
        memory_manager = await get_vector_memory_manager()
        knowledge_base = await get_shared_knowledge_base()
        
        # 创建具有模式的测试数据
        test_memories = [
            {"content": {"type": "登录"}, "tags": ["功能需求", "高优先级"]},
            {"content": {"type": "注册"}, "tags": ["功能需求", "高优先级"]},
            {"content": {"type": "查询"}, "tags": ["功能需求", "中优先级"]},
            {"content": {"type": "性能"}, "tags": ["非功能需求", "高优先级"]},
            {"content": {"type": "安全"}, "tags": ["非功能需求", "高优先级"]},
            {"content": {"type": "界面"}, "tags": ["功能需求", "低优先级"]}
        ]
        
        # 存储测试记忆
        for memory_data in test_memories:
            await memory_manager.store_memory(
                content=memory_data["content"],
                memory_type=MemoryType.REQUIREMENT,
                tags=memory_data["tags"]
            )
        
        # 发现模式
        patterns = await knowledge_base.knowledge_graph.find_patterns(MemoryType.REQUIREMENT)
        
        app_logger.info(f"发现了 {len(patterns)} 个模式")
        for pattern in patterns:
            app_logger.info(f"  模式: {pattern['pattern']}, 频率: {pattern['frequency']}")
        
        return True
        
    except Exception as e:
        app_logger.error(f"知识模式发现测试失败: {str(e)}")
        return False


async def test_concurrent_access():
    """测试并发访问"""
    app_logger.info("=== 测试并发访问 ===")
    
    try:
        memory_manager = await get_vector_memory_manager()
        
        # 并发存储测试
        async def store_memory_task(task_id: int):
            content = {
                "task_id": task_id,
                "name": f"并发测试任务_{task_id}",
                "timestamp": datetime.now().isoformat()
            }
            
            memory_id = await memory_manager.store_memory(
                content=content,
                memory_type=MemoryType.EXPERIENCE,
                tags=[f"并发测试", f"任务_{task_id}"],
                source_agent=f"concurrent_agent_{task_id % 3}"
            )
            
            return memory_id
        
        # 创建并发任务
        num_tasks = 10
        tasks = [store_memory_task(i) for i in range(num_tasks)]
        
        start_time = datetime.now()
        memory_ids = await asyncio.gather(*tasks)
        duration = (datetime.now() - start_time).total_seconds()
        
        app_logger.info(f"并发存储 {num_tasks} 条记忆耗时: {duration:.2f}秒")
        app_logger.info(f"成功存储 {len(memory_ids)} 条记忆")
        
        # 并发搜索测试
        async def search_memory_task(query: str):
            search_query = SearchQuery(
                text=query,
                memory_types=[MemoryType.EXPERIENCE],
                limit=5
            )
            
            results = await memory_manager.search_memories(search_query)
            return len(results)
        
        search_queries = [f"并发测试任务_{i}" for i in range(5)]
        search_tasks = [search_memory_task(query) for query in search_queries]
        
        start_time = datetime.now()
        search_results = await asyncio.gather(*search_tasks)
        search_duration = (datetime.now() - start_time).total_seconds()
        
        app_logger.info(f"并发搜索 {len(search_queries)} 次耗时: {search_duration:.2f}秒")
        app_logger.info(f"搜索结果: {search_results}")
        
        return True
        
    except Exception as e:
        app_logger.error(f"并发访问测试失败: {str(e)}")
        return False


async def test_memory_cleanup():
    """测试记忆清理"""
    app_logger.info("=== 测试记忆清理 ===")
    
    try:
        # 执行清理
        await cleanup_vector_memory()
        
        app_logger.info("记忆清理测试完成")
        return True
        
    except Exception as e:
        app_logger.error(f"记忆清理测试失败: {str(e)}")
        return False


async def run_all_tests():
    """运行所有测试"""
    app_logger.info("开始运行向量记忆系统测试套件")
    
    tests = [
        ("向量记忆管理器", test_vector_memory_manager),
        ("共享知识库", test_shared_knowledge_base),
        ("Agent记忆混入", test_agent_memory_mixin),
        ("记忆搜索性能", test_memory_search_performance),
        ("记忆关系", test_memory_relationships),
        ("记忆过期机制", test_memory_expiration),
        ("知识模式发现", test_knowledge_patterns),
        ("并发访问", test_concurrent_access),
        ("记忆清理", test_memory_cleanup)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        app_logger.info(f"\n{'='*50}")
        app_logger.info(f"运行测试: {test_name}")
        app_logger.info(f"{'='*50}")
        
        try:
            success = await test_func()
            results[test_name] = "通过" if success else "失败"
            
        except Exception as e:
            app_logger.error(f"测试 {test_name} 执行异常: {str(e)}")
            results[test_name] = "异常"
    
    # 输出测试结果摘要
    app_logger.info(f"\n{'='*50}")
    app_logger.info("测试结果摘要")
    app_logger.info(f"{'='*50}")
    
    for test_name, result in results.items():
        status_icon = "✅" if result == "通过" else "❌"
        app_logger.info(f"{status_icon} {test_name}: {result}")
    
    # 统计
    passed = sum(1 for r in results.values() if r == "通过")
    total = len(results)
    
    app_logger.info(f"\n总计: {passed}/{total} 个测试通过")
    
    return results


if __name__ == "__main__":
    asyncio.run(run_all_tests())
