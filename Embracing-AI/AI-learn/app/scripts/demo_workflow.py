#!/usr/bin/env python3
"""
完整工作流程演示脚本
演示从需求分析到用例评审的完整流程
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from scripts.start_system import SystemManager
from core.logger import get_agent_logger

logger = get_agent_logger("WorkflowDemo")

# 预定义的示例需求
SAMPLE_REQUIREMENTS = {
    "用户登录": """
用户登录功能需求：
1. 用户可以通过用户名和密码登录系统
2. 登录成功后跳转到主页面
3. 登录失败时显示错误提示
4. 支持记住密码功能
5. 连续登录失败3次后锁定账户30分钟
""",
    
    "购物车": """
购物车功能需求：
1. 用户可以将商品添加到购物车
2. 用户可以修改购物车中商品的数量
3. 用户可以删除购物车中的商品
4. 购物车显示商品总价和数量
5. 购物车数据在用户会话期间保持
""",
    
    "文件上传": """
文件上传功能需求：
1. 支持上传图片、文档、视频等多种格式文件
2. 单个文件大小不超过100MB
3. 支持批量上传，最多10个文件
4. 上传过程显示进度条
5. 上传完成后返回文件访问链接
6. 支持拖拽上传
"""
}

async def run_demo(requirement_key: str = None, custom_requirement: str = None):
    """运行演示"""
    logger.info("🚀 启动完整工作流程演示")
    
    # 确定要使用的需求
    if custom_requirement:
        requirement_text = custom_requirement
        logger.info(f"使用自定义需求")
    elif requirement_key and requirement_key in SAMPLE_REQUIREMENTS:
        requirement_text = SAMPLE_REQUIREMENTS[requirement_key]
        logger.info(f"使用预定义需求: {requirement_key}")
    else:
        # 默认使用用户登录需求
        requirement_text = SAMPLE_REQUIREMENTS["用户登录"]
        logger.info("使用默认需求: 用户登录")
    
    # 创建系统管理器并初始化
    system_manager = SystemManager()
    
    try:
        # 初始化系统组件
        logger.info("初始化系统组件...")
        await system_manager.initialize_components()
        
        # 检查系统健康状态
        health_status = system_manager.check_system_health()
        logger.info(f"系统健康状态: {health_status}")
        
        if health_status == "unhealthy":
            logger.error("系统健康状态不佳，无法运行演示")
            return False
        
        # 运行完整工作流程
        result = await system_manager.run_complete_workflow(requirement_text)
        
        if result["success"]:
            logger.info("🎉 演示成功完成！")
            return True
        else:
            logger.error(f"演示失败: {result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        logger.error(f"演示过程中发生异常: {type(e).__name__}: {str(e)}", exc_info=True)
        return False

def main():
    """主函数"""
    if "--help" in sys.argv or "-h" in sys.argv:
        print("完整工作流程演示脚本")
        print("用法: python demo_workflow.py [选项]")
        print()
        print("选项:")
        print("  --requirement <需求文本>  使用自定义需求")
        print("  --sample <类型>          使用预定义需求样例")
        print("  --list-samples           列出所有预定义需求样例")
        print("  --help, -h               显示帮助信息")
        print()
        print("预定义需求样例:")
        for key in SAMPLE_REQUIREMENTS.keys():
            print(f"  - {key}")
        print()
        print("示例:")
        print("  python demo_workflow.py --sample 用户登录")
        print("  python demo_workflow.py --sample 购物车")
        print('  python demo_workflow.py --requirement "自定义需求文本"')
        return
    
    if "--list-samples" in sys.argv:
        print("可用的预定义需求样例:")
        for key, value in SAMPLE_REQUIREMENTS.items():
            print(f"\n📋 {key}:")
            print("-" * 40)
            print(value.strip())
        return
    
    # 解析命令行参数
    custom_requirement = None
    sample_key = None
    
    for i, arg in enumerate(sys.argv):
        if arg == "--requirement" and i + 1 < len(sys.argv):
            custom_requirement = sys.argv[i + 1]
        elif arg == "--sample" and i + 1 < len(sys.argv):
            sample_key = sys.argv[i + 1]
    
    # 运行演示
    try:
        success = asyncio.run(run_demo(sample_key, custom_requirement))
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("演示被用户中断")
        sys.exit(0)
    except Exception as e:
        logger.error(f"演示启动失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
