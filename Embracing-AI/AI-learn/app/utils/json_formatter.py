"""
JSON格式化工具
"""

import json
import jsonschema
from typing import Dict, Any, Optional
from datetime import datetime

from models.json_schemas import get_agent_output_schema
from models.validators import ValidationError


class JSONFormatter:
    """JSON格式化器"""
    
    @staticmethod
    def format_agent_output(data: Dict[str, Any], agent_type: str) -> Dict[str, Any]:
        """格式化Agent输出"""
        # 添加基础字段
        if "timestamp" not in data:
            data["timestamp"] = datetime.now().isoformat()
        
        if "version" not in data:
            data["version"] = 1
        
        # 验证格式
        schema = get_agent_output_schema(agent_type)
        if schema:
            try:
                jsonschema.validate(data, schema)
            except jsonschema.ValidationError as e:
                raise ValidationError(f"JSON格式验证失败: {e.message}")
        
        return data
    
    @staticmethod
    def format_collaboration_message(
        from_agent: str,
        to_agent: str,
        message_type: str,
        content: Dict[str, Any],
        correlation_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """格式化协作消息"""
        import uuid
        
        message = {
            "message_id": str(uuid.uuid4()),
            "from_agent": from_agent,
            "to_agent": to_agent,
            "message_type": message_type,
            "content": content,
            "timestamp": datetime.now().isoformat(),
            "correlation_id": correlation_id or str(uuid.uuid4())
        }
        
        return message
    
    @staticmethod
    def pretty_print_json(data: Dict[str, Any]) -> str:
        """美化打印JSON"""
        return json.dumps(data, ensure_ascii=False, indent=2, sort_keys=True)
    
    @staticmethod
    def validate_and_format(data: Dict[str, Any], schema: Dict[str, Any]) -> Dict[str, Any]:
        """验证并格式化数据"""
        try:
            jsonschema.validate(data, schema)
            return data
        except jsonschema.ValidationError as e:
            raise ValidationError(f"数据格式验证失败: {e.message}")


def create_standard_response(
    success: bool = True,
    data: Any = None,
    message: str = "",
    error_code: Optional[str] = None
) -> Dict[str, Any]:
    """创建标准API响应格式"""
    response = {
        "success": success,
        "timestamp": datetime.now().isoformat(),
        "message": message
    }
    
    if success:
        response["data"] = data
    else:
        response["error"] = {
            "code": error_code or "UNKNOWN_ERROR",
            "message": message
        }
    
    return response