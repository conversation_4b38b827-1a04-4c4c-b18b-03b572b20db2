# 应用配置
APP_NAME=异步多智能体测试用例生成框架
APP_VERSION=1.0.0
DEBUG=true

# 服务器配置
HOST=0.0.0.0
PORT=8000

# Ollama配置
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=deepseel-r1:1.5b
OLLAMA_TIMEOUT=300

# ChromaDB配置
CHROMA_HOST=localhost
CHROMA_PORT=8001
CHROMA_COLLECTION_NAME=agent_memory
CHROMA_PERSIST_DIRECTORY=./chroma_db

# Redis配置
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=

# Celery配置
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
LOG_ROTATION=1 day
LOG_RETENTION=30 days

# Agent配置
MAX_ITERATIONS=10
REFLECTION_THRESHOLD=0.8
COLLABORATION_TIMEOUT=600

# 文档处理配置
MAX_FILE_SIZE=10485760
SUPPORTED_FORMATS=["docx", "doc", "txt"]

# 测试用例配置
MAX_TEST_CASES_PER_FEATURE=50
TEST_CASE_PRIORITY_LEVELS=["high", "medium", "low"]