# 异步多智能体测试用例生成框架

## 项目概述

这是一个基于Python的智能化测试管理系统，通过4个专业化Agent协作完成从需求分析到测试用例生成的完整流程。系统采用异步架构，支持多轮交互和持久化记忆，确保测试用例的完整性和可追溯性。

## 核心特性

- **需求分析Agent**: 解析Word文档或对话输入，提取功能点和业务逻辑
- **需求评审Agent**: 对需求分析结果进行评审，确保需求完整性和准确性
- **用例生成Agent**: 基于多种测试设计方法生成全面的测试用例，维护生成状态
- **用例评审Agent**: 对生成的测试用例进行质量评审和完整性检查
- **异步协作机制**: 支持Agent间多轮交互和状态同步
- **向量记忆系统**: 使用ChromaDB实现长期记忆和知识共享
- **可追溯性矩阵**: 维护需求到测试用例的完整追溯关系

## 技术架构

- **核心框架**: Python 3.9+ + FastAPI + AsyncIO
- **智能体框架**: LangChain + AutoGen
- **向量数据库**: ChromaDB
- **本地模型**: Ollama + DeepSeek-R1:1.5b
- **任务队列**: Celery + Redis
- **文档处理**: python-docx
- **数据验证**: Pydantic
- **日志系统**: Loguru

## 快速开始

### 1. 环境准备

```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置Ollama模型

```bash
# 安装Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# 拉取DeepSeek-R1模型
ollama pull deepseel-r1:1.5b
```

### 3. 启动服务

```bash
# 启动Redis
redis-server

# 启动Celery Worker
celery -A app.celery worker --loglevel=info

# 启动FastAPI服务
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

## 项目结构

```
async-multi-agent-test-framework/
├── app/
│   ├── __init__.py
│   ├── main.py                 # FastAPI应用入口
│   ├── config.py              # 配置管理
│   ├── agents/                # Agent实现
│   │   ├── __init__.py
│   │   ├── base.py           # 基础Agent类
│   │   ├── requirement_analysis.py
│   │   ├── requirement_review.py
│   │   ├── testcase_generation.py
│   │   └── testcase_review.py
│   ├── core/                  # 核心组件
│   │   ├── __init__.py
│   │   ├── memory.py         # 向量记忆系统
│   │   ├── collaboration.py  # 协作框架
│   │   └── traceability.py   # 可追溯性矩阵
│   ├── models/               # 数据模型
│   │   ├── __init__.py
│   │   ├── schemas.py        # Pydantic模型
│   │   └── enums.py          # 枚举定义
│   ├── services/             # 业务服务
│   │   ├── __init__.py
│   │   ├── document_parser.py
│   │   └── ollama_client.py
│   └── api/                  # API路由
│       ├── __init__.py
│       └── routes.py
├── tests/                    # 测试文件
├── docs/                     # 文档
├── requirements.txt
├── pyproject.toml
└── README.md
```

## API文档

启动服务后访问 http://localhost:8000/docs 查看完整的API文档。

## 开发指南

### 代码规范

- 使用Black进行代码格式化
- 使用Flake8进行代码检查
- 遵循PEP 8编码规范
- 所有函数和类都需要类型注解

### 测试

```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_agents.py

# 生成覆盖率报告
pytest --cov=app tests/
```

## 许可证

MIT License