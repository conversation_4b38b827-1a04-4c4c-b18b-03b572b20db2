"""
JSON格式规范定义
"""

from typing import Dict, Any

# 需求分析Agent输出格式
REQUIREMENT_ANALYSIS_SCHEMA = {
    "type": "object",
    "properties": {
        "requirements": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "id": {"type": "integer"},
                    "trace_id": {"type": "string"},
                    "name": {"type": "string"},
                    "type": {"type": "string", "enum": ["functional", "non-functional"]},
                    "description": {"type": "string"},
                    "acceptance_criteria": {"type": "object"},
                    "status": {"type": "string", "enum": ["ok", "clarify"]},
                    "pending_clarifications": {"type": "array"}
                },
                "required": ["id", "trace_id", "name", "type", "description"]
            }
        },
        "version": {"type": "integer"},
        "timestamp": {"type": "string"}
    },
    "required": ["requirements", "version", "timestamp"]
}

# 需求评审Agent输出格式
REQUIREMENT_REVIEW_SCHEMA = {
    "type": "object",
    "properties": {
        "review_issues": {"type": "array"},
        "updated_requirements": {"type": "array"},
        "traceability_matrix": {"type": "array"},
        "version": {"type": "integer"},
        "timestamp": {"type": "string"}
    },
    "required": ["review_issues", "updated_requirements", "version", "timestamp"]
}

# 测试用例生成Agent输出格式
TESTCASE_GENERATION_SCHEMA = {
    "type": "object",
    "properties": {
        "feature_id": {"type": "string"},
        "new_test_cases": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "id": {"type": "string"},
                    "trace_id": {"type": "string"},
                    "name": {"type": "string"},
                    "description": {"type": "string"},
                    "steps": {"type": "array"},
                    "expected_result": {"type": "string"},
                    "priority": {"type": "string", "enum": ["high", "medium", "low"]}
                },
                "required": ["id", "trace_id", "name", "description", "steps"]
            }
        },
        "remaining_scenarios": {"type": "array"},
        "generated_ids": {"type": "array"},
        "has_more": {"type": "boolean"},
        "version": {"type": "integer"},
        "timestamp": {"type": "string"}
    },
    "required": ["feature_id", "new_test_cases", "has_more", "version", "timestamp"]
}

# 测试用例评审Agent输出格式
TESTCASE_REVIEW_SCHEMA = {
    "type": "object",
    "properties": {
        "feature_id": {"type": "string"},
        "reviewed_cases": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "id": {"type": "string"},
                    "status": {"type": "string", "enum": ["pass", "fail"]},
                    "issues": {"type": "array"},
                    "suggestions": {"type": "array"}
                },
                "required": ["id", "status"]
            }
        },
        "updated_remaining_scenarios": {"type": "array"},
        "traceability_matrix": {"type": "array"},
        "version": {"type": "integer"},
        "has_more": {"type": "boolean"},
        "timestamp": {"type": "string"}
    },
    "required": ["feature_id", "reviewed_cases", "version", "has_more", "timestamp"]
}

# Agent协作消息格式
COLLABORATION_MESSAGE_SCHEMA = {
    "type": "object",
    "properties": {
        "message_id": {"type": "string"},
        "from_agent": {"type": "string"},
        "to_agent": {"type": "string"},
        "message_type": {"type": "string", "enum": ["request", "response", "notification"]},
        "content": {"type": "object"},
        "timestamp": {"type": "string"},
        "correlation_id": {"type": "string"}
    },
    "required": ["message_id", "from_agent", "to_agent", "message_type", "content", "timestamp"]
}

# 获取指定Agent类型的输出格式规范
def get_agent_output_schema(agent_type: str) -> Dict[str, Any]:
    """获取Agent输出格式规范"""
    schemas = {
        "requirement_analysis": REQUIREMENT_ANALYSIS_SCHEMA,
        "requirement_review": REQUIREMENT_REVIEW_SCHEMA,
        "testcase_generation": TESTCASE_GENERATION_SCHEMA,
        "testcase_review": TESTCASE_REVIEW_SCHEMA
    }
    return schemas.get(agent_type, {})