"""
数据验证器模块
"""

import re
import json
from typing import Any, Dict, List, Optional, Union
from datetime import datetime
from pydantic import BaseModel, validator
from enum import Enum

class ValidationError(Exception):
    """验证错误异常"""
    def __init__(self, message: str, field: str = None):
        self.message = message
        self.field = field
        super().__init__(self.message)

class BaseValidator:
    """基础验证器类"""
    
    @staticmethod
    def validate_not_empty(value: str, field_name: str = "字段") -> str:
        """验证字符串不为空"""
        if not value or not value.strip():
            raise ValidationError(f"{field_name}不能为空", field_name)
        return value.strip()
    
    @staticmethod
    def validate_json_format(value: Union[str, dict], field_name: str = "JSON字段") -> dict:
        """验证JSON格式"""
        if isinstance(value, str):
            try:
                return json.loads(value)
            except json.JSONDecodeError as e:
                raise ValidationError(f"{field_name}格式不正确: {str(e)}", field_name)
        elif isinstance(value, dict):
            return value
        else:
            raise ValidationError(f"{field_name}必须是JSON格式", field_name)

class RequirementValidator(BaseValidator):
    """需求验证器"""
    
    @staticmethod
    def validate_requirement_data(data: dict) -> dict:
        """验证需求数据"""
        required_fields = ["name", "description", "type"]
        for field in required_fields:
            if field not in data:
                raise ValidationError(f"缺少必需字段: {field}")
        
        # 验证需求类型
        valid_types = ["functional", "non-functional"]
        if data["type"] not in valid_types:
            raise ValidationError(f"需求类型必须是: {', '.join(valid_types)}")
        
        return data

class TestCaseValidator(BaseValidator):
    """测试用例验证器"""
    
    @staticmethod
    def validate_testcase_data(data: dict) -> dict:
        """验证测试用例数据"""
        required_fields = ["name", "description", "steps"]
        for field in required_fields:
            if field not in data:
                raise ValidationError(f"缺少必需字段: {field}")
        
        # 验证测试步骤
        if not isinstance(data["steps"], list) or len(data["steps"]) == 0:
            raise ValidationError("测试步骤不能为空")
        
        return data

def validate_agent_output(output: dict, agent_type: str) -> dict:
    """验证Agent输出格式"""
    required_base_fields = ["version", "timestamp"]
    
    for field in required_base_fields:
        if field not in output:
            raise ValidationError(f"Agent输出缺少必需字段: {field}")
    
    # 根据Agent类型验证特定字段
    if agent_type == "requirement_analysis":
        if "requirements" not in output:
            raise ValidationError("需求分析Agent输出必须包含requirements字段")
    elif agent_type == "testcase_generation":
        if "new_test_cases" not in output:
            raise ValidationError("测试用例生成Agent输出必须包含new_test_cases字段")
    
    return output