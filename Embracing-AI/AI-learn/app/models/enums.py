"""
枚举定义
"""

from enum import Enum


class AgentType(str, Enum):
    """Agent类型枚举"""
    REQUIREMENT_ANALYSIS = "requirement_analysis"
    REQUIREMENT_REVIEW = "requirement_review"
    TESTCASE_GENERATION = "testcase_generation"
    TESTCASE_REVIEW = "testcase_review"


class AgentStatus(str, Enum):
    """Agent状态枚举"""
    IDLE = "idle"
    THINKING = "thinking"
    PROCESSING = "processing"
    COLLABORATING = "collaborating"
    REFLECTING = "reflecting"
    COMPLETED = "completed"
    ERROR = "error"


class RequirementType(str, Enum):
    """需求类型枚举"""
    FUNCTIONAL = "functional"
    NON_FUNCTIONAL = "non_functional"


class RequirementStatus(str, Enum):
    """需求状态枚举"""
    OK = "ok"
    CLARIFY = "clarify"
    REVIEW = "review"
    APPROVED = "approved"
    REJECTED = "rejected"


class RiskLevel(str, Enum):
    """风险级别枚举"""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class TestCaseType(str, Enum):
    """测试用例类型枚举"""
    POSITIVE = "positive"
    NEGATIVE = "negative"
    BOUNDARY = "boundary"
    ERROR = "error"
    PERFORMANCE = "performance"
    SECURITY = "security"
    USABILITY = "usability"


class TestCaseStatus(str, Enum):
    """测试用例状态枚举"""
    DRAFT = "draft"
    REVIEW = "review"
    APPROVED = "approved"
    REJECTED = "rejected"
    EXECUTED = "executed"


class TestPriority(str, Enum):
    """测试优先级枚举"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class CollaborationStatus(str, Enum):
    """协作状态枚举"""
    INITIATED = "initiated"
    IN_PROGRESS = "in_progress"
    WAITING_FEEDBACK = "waiting_feedback"
    COMPLETED = "completed"
    FAILED = "failed"


class MessageType(str, Enum):
    """消息类型枚举"""
    REQUEST = "request"
    RESPONSE = "response"
    NOTIFICATION = "notification"
    ERROR = "error"


class DocumentFormat(str, Enum):
    """文档格式枚举"""
    DOCX = "docx"
    DOC = "doc"
    TXT = "txt"
    PDF = "pdf"


class ProcessingStage(str, Enum):
    """处理阶段枚举"""
    REQUIREMENT_ANALYSIS = "requirement_analysis"
    REQUIREMENT_REVIEW = "requirement_review"
    TESTCASE_GENERATION = "testcase_generation"
    TESTCASE_REVIEW = "testcase_review"
    COMPLETED = "completed"


class IssueType(str, Enum):
    """问题类型枚举"""
    LOGIC = "logic"
    COMPLETENESS = "completeness"
    CONSISTENCY = "consistency"
    CLARITY = "clarity"
    FEASIBILITY = "feasibility"
    TESTABILITY = "testability"
    PERFORMANCE = "performance"
    SECURITY = "security"
    USABILITY = "usability"
    OTHER = "other"


class IssueSeverity(str, Enum):
    """问题严重程度枚举"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"