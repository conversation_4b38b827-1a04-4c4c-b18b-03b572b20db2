"""
Pydantic数据模型定义
"""

from datetime import datetime
from typing import List, Dict, Optional, Any, Union
from pydantic import BaseModel, Field, validator
from models.enums import (
    AgentType, AgentStatus, RequirementType, RequirementStatus,
    RiskLevel, TestCaseType, TestCaseStatus, CollaborationStatus,
    MessageType, ProcessingStage
)


class BaseSchema(BaseModel):
    """基础模型"""
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")
    version: int = Field(default=1, description="版本号")
    
    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ThinkingChain(BaseModel):
    """思维链模型"""
    step: int = Field(description="思考步骤")
    content: str = Field(description="思考内容")
    reasoning: str = Field(description="推理过程")
    confidence: float = Field(ge=0.0, le=1.0, description="置信度")


class ReflectionResult(BaseModel):
    """反思结果模型"""
    question: str = Field(description="反思问题")
    answer: str = Field(description="反思答案")
    improvement: Optional[str] = Field(default=None, description="改进建议")
    score: float = Field(ge=0.0, le=1.0, description="评分")


class AcceptanceCriteria(BaseModel):
    """验收标准模型"""
    positive: List[str] = Field(default_factory=list, description="正向标准")
    negative: List[str] = Field(default_factory=list, description="负向标准")
    boundary: List[str] = Field(default_factory=list, description="边界标准")
    error: List[str] = Field(default_factory=list, description="错误处理标准")


class RequirementModel(BaseSchema):
    """需求模型"""
    id: int = Field(description="需求ID")
    trace_id: str = Field(description="追溯ID")
    name: str = Field(description="需求名称")
    type: RequirementType = Field(description="需求类型")
    description: str = Field(description="需求描述")
    attributes: Optional[Dict[str, Any]] = Field(default_factory=dict, description="需求属性")
    acceptance_criteria: AcceptanceCriteria = Field(description="验收标准")
    pending_scenarios: List[str] = Field(default_factory=list, description="待处理场景")
    status: RequirementStatus = Field(default=RequirementStatus.OK, description="需求状态")
    pending_clarifications: List[str] = Field(default_factory=list, description="待澄清问题")
    risk_level: Optional[RiskLevel] = Field(default=None, description="风险级别")


class ReviewIssue(BaseModel):
    """评审问题模型"""
    id: str = Field(description="问题ID")
    type: str = Field(description="问题类型")
    description: str = Field(description="问题描述")
    severity: RiskLevel = Field(description="严重程度")
    suggestion: Optional[str] = Field(default=None, description="改进建议")
    requirement_id: Optional[int] = Field(default=None, description="关联需求ID")


class ReviewResult(BaseModel):
    """评审结果模型"""
    requirement_id: int = Field(description="需求ID")
    status: str = Field(description="评审状态")
    issues: List[ReviewIssue] = Field(default_factory=list, description="发现的问题")
    suggestions: List[str] = Field(default_factory=list, description="改进建议")
    score: Optional[float] = Field(default=None, description="评审得分")
    reviewer: str = Field(description="评审者")
    review_time: datetime = Field(default_factory=datetime.now, description="评审时间")


class TestStep(BaseModel):
    """测试步骤模型"""
    step_number: int = Field(description="步骤编号")
    action: str = Field(description="操作描述")
    expected_result: str = Field(description="预期结果")
    test_data: Optional[str] = Field(default=None, description="测试数据")


class TestCaseModel(BaseSchema):
    """测试用例模型"""
    id: str = Field(description="用例ID")
    trace_id: str = Field(description="追溯ID")
    name: str = Field(description="用例名称")
    description: str = Field(description="用例描述")
    type: TestCaseType = Field(description="用例类型")
    priority: RiskLevel = Field(description="优先级")
    preconditions: List[str] = Field(default_factory=list, description="前置条件")
    test_steps: List[TestStep] = Field(description="测试步骤")
    expected_result: str = Field(description="预期结果")
    requirement_ids: List[int] = Field(default_factory=list, description="关联需求ID")
    status: TestCaseStatus = Field(default=TestCaseStatus.DRAFT, description="用例状态")
    tags: List[str] = Field(default_factory=list, description="标签")


class TraceabilityMatrix(BaseModel):
    """可追溯性矩阵模型"""
    requirement_id: int = Field(description="需求ID")
    requirement_trace_id: str = Field(description="需求追溯ID")
    testcase_ids: List[str] = Field(default_factory=list, description="测试用例ID列表")
    coverage_percentage: float = Field(ge=0.0, le=100.0, description="覆盖率百分比")
    last_updated: datetime = Field(default_factory=datetime.now, description="最后更新时间")


class AgentMessage(BaseModel):
    """Agent消息模型"""
    id: str = Field(description="消息ID")
    sender: AgentType = Field(description="发送者")
    receiver: Optional[AgentType] = Field(default=None, description="接收者")
    message_type: MessageType = Field(description="消息类型")
    content: Dict[str, Any] = Field(description="消息内容")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")
    correlation_id: Optional[str] = Field(default=None, description="关联ID")


class AgentState(BaseModel):
    """Agent状态模型"""
    agent_type: AgentType = Field(description="Agent类型")
    status: AgentStatus = Field(description="当前状态")
    current_task: Optional[str] = Field(default=None, description="当前任务")
    progress: float = Field(ge=0.0, le=1.0, default=0.0, description="进度")
    thinking_chain: List[ThinkingChain] = Field(default_factory=list, description="思维链")
    reflection_results: List[ReflectionResult] = Field(default_factory=list, description="反思结果")
    last_activity: datetime = Field(default_factory=datetime.now, description="最后活动时间")


class CollaborationSession(BaseSchema):
    """协作会话模型"""
    session_id: str = Field(description="会话ID")
    participants: List[AgentType] = Field(description="参与者")
    status: CollaborationStatus = Field(description="协作状态")
    current_stage: ProcessingStage = Field(description="当前阶段")
    messages: List[AgentMessage] = Field(default_factory=list, description="消息列表")
    shared_context: Dict[str, Any] = Field(default_factory=dict, description="共享上下文")
    iteration_count: int = Field(default=0, description="迭代次数")
    max_iterations: int = Field(default=10, description="最大迭代次数")


class DocumentInput(BaseModel):
    """文档输入模型"""
    content: Optional[str] = Field(default=None, description="文本内容")
    file_path: Optional[str] = Field(default=None, description="文件路径")
    file_content: Optional[bytes] = Field(default=None, description="文件内容")
    format: Optional[str] = Field(default=None, description="文档格式")
    
    @validator('content', 'file_path', 'file_content')
    def validate_input(cls, v, values):
        """验证至少有一种输入方式"""
        if not any([values.get('content'), values.get('file_path'), v]):
            raise ValueError("必须提供content、file_path或file_content中的至少一个")
        return v


class ProcessingRequest(BaseModel):
    """处理请求模型"""
    session_id: str = Field(description="会话ID")
    document_input: DocumentInput = Field(description="文档输入")
    requirements: Optional[List[RequirementModel]] = Field(default=None, description="已有需求")
    options: Dict[str, Any] = Field(default_factory=dict, description="处理选项")


class ProcessingResponse(BaseSchema):
    """处理响应模型"""
    session_id: str = Field(description="会话ID")
    stage: ProcessingStage = Field(description="处理阶段")
    success: bool = Field(description="是否成功")
    data: Dict[str, Any] = Field(description="响应数据")
    errors: List[str] = Field(default_factory=list, description="错误信息")
    has_more: bool = Field(default=False, description="是否还有更多内容")


class FeatureGenerationState(BaseModel):
    """功能点生成状态模型"""
    feature_id: int = Field(description="功能点ID")
    pending_scenarios: List[str] = Field(description="待处理场景")
    generated_ids: List[str] = Field(default_factory=list, description="已生成用例ID")
    has_more: bool = Field(default=True, description="是否还有更多用例待生成")
    current_priority: RiskLevel = Field(description="当前处理优先级")
    iteration_count: int = Field(default=0, description="迭代次数")


# API相关模型
class AgentCreateRequest(BaseModel):
    """Agent创建请求模型"""
    agent_type: AgentType = Field(description="Agent类型")
    config: Optional[Dict[str, Any]] = Field(default_factory=dict, description="配置参数")


class AgentResponse(BaseModel):
    """Agent响应模型"""
    agent_id: str = Field(description="Agent ID")
    agent_type: AgentType = Field(description="Agent类型")
    status: AgentStatus = Field(description="Agent状态")
    created_at: datetime = Field(description="创建时间")


class AgentStatusResponse(BaseModel):
    """Agent状态响应模型"""
    agent_id: str = Field(description="Agent ID")
    status: AgentStatus = Field(description="Agent状态")
    current_task: Optional[str] = Field(default=None, description="当前任务")
    progress: Optional[float] = Field(default=None, description="进度")


class AgentExecuteRequest(BaseModel):
    """Agent执行请求模型"""
    task_type: str = Field(description="任务类型")
    input_data: Dict[str, Any] = Field(description="输入数据")
    config: Optional[Dict[str, Any]] = Field(default_factory=dict, description="执行配置")


class AgentExecuteResponse(BaseModel):
    """Agent执行响应模型"""
    task_id: str = Field(description="任务ID")
    status: str = Field(description="执行状态")
    result: Optional[Dict[str, Any]] = Field(default=None, description="执行结果")
    error: Optional[str] = Field(default=None, description="错误信息")


class AgentListResponse(BaseModel):
    """Agent列表响应模型"""
    agents: List[AgentResponse] = Field(description="Agent列表")
    total: int = Field(description="总数量")


# 需求分析相关模型
class RequirementAnalysisRequest(BaseModel):
    """需求分析请求模型"""
    requirement_text: str = Field(description="需求文本")
    file_path: Optional[str] = Field(default=None, description="文件路径")
    analysis_type: str = Field(default="comprehensive", description="分析类型")
    config: Optional[Dict[str, Any]] = Field(default_factory=dict, description="分析配置")


class RequirementAnalysisResponse(BaseModel):
    """需求分析响应模型"""
    task_id: str = Field(description="任务ID")
    status: str = Field(description="分析状态")
    requirements: Optional[List[Dict[str, Any]]] = Field(default=None, description="分析结果")
    summary: Optional[Dict[str, Any]] = Field(default=None, description="分析摘要")


class RequirementReviewRequest(BaseModel):
    """需求评审请求模型"""
    requirement_id: str = Field(description="需求ID")
    review_criteria: Optional[List[str]] = Field(default_factory=list, description="评审标准")
    config: Optional[Dict[str, Any]] = Field(default_factory=dict, description="评审配置")


class RequirementReviewResponse(BaseModel):
    """需求评审响应模型"""
    task_id: str = Field(description="任务ID")
    status: str = Field(description="评审状态")
    review_result: Optional[Dict[str, Any]] = Field(default=None, description="评审结果")
    issues: Optional[List[Dict[str, Any]]] = Field(default=None, description="发现的问题")


class RequirementListResponse(BaseModel):
    """需求列表响应模型"""
    requirements: List[Dict[str, Any]] = Field(description="需求列表")
    total: int = Field(description="总数量")
    page: int = Field(default=1, description="页码")
    page_size: int = Field(default=20, description="每页大小")


class RequirementResponse(BaseModel):
    """需求响应模型"""
    requirement_id: str = Field(description="需求ID")
    name: str = Field(description="需求名称")
    type: RequirementType = Field(description="需求类型")
    status: RequirementStatus = Field(description="需求状态")
    description: str = Field(description="需求描述")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")


# 测试用例生成相关模型
class TestCaseGenerationRequest(BaseModel):
    """测试用例生成请求模型"""
    requirement_id: str = Field(description="需求ID")
    generation_type: str = Field(default="comprehensive", description="生成类型")
    test_methods: Optional[List[str]] = Field(default_factory=list, description="测试方法")
    config: Optional[Dict[str, Any]] = Field(default_factory=dict, description="生成配置")


class TestCaseGenerationResponse(BaseModel):
    """测试用例生成响应模型"""
    task_id: str = Field(description="任务ID")
    status: str = Field(description="生成状态")
    test_cases: Optional[List[Dict[str, Any]]] = Field(default=None, description="生成的测试用例")
    summary: Optional[Dict[str, Any]] = Field(default=None, description="生成摘要")


class TestCaseReviewRequest(BaseModel):
    """测试用例评审请求模型"""
    test_case_id: str = Field(description="测试用例ID")
    review_criteria: Optional[List[str]] = Field(default_factory=list, description="评审标准")
    config: Optional[Dict[str, Any]] = Field(default_factory=dict, description="评审配置")


class TestCaseReviewResponse(BaseModel):
    """测试用例评审响应模型"""
    task_id: str = Field(description="任务ID")
    status: str = Field(description="评审状态")
    review_result: Optional[Dict[str, Any]] = Field(default=None, description="评审结果")
    issues: Optional[List[Dict[str, Any]]] = Field(default=None, description="发现的问题")


class TestCaseListResponse(BaseModel):
    """测试用例列表响应模型"""
    test_cases: List[Dict[str, Any]] = Field(description="测试用例列表")
    total: int = Field(description="总数量")
    page: int = Field(default=1, description="页码")
    page_size: int = Field(default=20, description="每页大小")


class TestCaseResponse(BaseModel):
    """测试用例响应模型"""
    test_case_id: str = Field(description="测试用例ID")
    name: str = Field(description="测试用例名称")
    type: TestCaseType = Field(description="测试用例类型")
    status: TestCaseStatus = Field(description="测试用例状态")
    description: str = Field(description="测试用例描述")
    steps: List[Dict[str, Any]] = Field(description="测试步骤")
    expected_result: str = Field(description="预期结果")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")


# 协作相关模型
class CollaborationRequest(BaseModel):
    """协作请求模型"""
    task_type: str = Field(description="任务类型")
    participants: List[str] = Field(description="参与者列表")
    task_data: Dict[str, Any] = Field(description="任务数据")
    config: Optional[Dict[str, Any]] = Field(default_factory=dict, description="协作配置")


class CollaborationResponse(BaseModel):
    """协作响应模型"""
    collaboration_id: str = Field(description="协作ID")
    status: str = Field(description="协作状态")
    participants: List[str] = Field(description="参与者列表")
    created_at: datetime = Field(description="创建时间")


class CollaborationStatusResponse(BaseModel):
    """协作状态响应模型"""
    collaboration_id: str = Field(description="协作ID")
    status: CollaborationStatus = Field(description="协作状态")
    progress: float = Field(description="进度百分比")
    current_task: Optional[str] = Field(default=None, description="当前任务")
    participants_status: Dict[str, str] = Field(description="参与者状态")


class CollaborationTaskResponse(BaseModel):
    """协作任务响应模型"""
    task_id: str = Field(description="任务ID")
    collaboration_id: str = Field(description="协作ID")
    task_type: str = Field(description="任务类型")
    status: str = Field(description="任务状态")
    result: Optional[Dict[str, Any]] = Field(default=None, description="任务结果")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")


# 可追溯性相关模型
class TraceabilityArtifactRequest(BaseModel):
    """可追溯性工件请求模型"""
    artifact_type: str = Field(description="工件类型")
    name: str = Field(description="工件名称")
    content: str = Field(description="工件内容")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="元数据")


class TraceabilityArtifactResponse(BaseModel):
    """可追溯性工件响应模型"""
    artifact_id: str = Field(description="工件ID")
    artifact_type: str = Field(description="工件类型")
    name: str = Field(description="工件名称")
    status: str = Field(description="工件状态")
    created_at: datetime = Field(description="创建时间")


class TraceabilityLinkRequest(BaseModel):
    """可追溯性链接请求模型"""
    source_id: str = Field(description="源工件ID")
    target_id: str = Field(description="目标工件ID")
    link_type: str = Field(description="链接类型")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="链接元数据")


class TraceabilityLinkResponse(BaseModel):
    """可追溯性链接响应模型"""
    link_id: str = Field(description="链接ID")
    source_id: str = Field(description="源工件ID")
    target_id: str = Field(description="目标工件ID")
    link_type: str = Field(description="链接类型")
    created_at: datetime = Field(description="创建时间")


class CoverageReportResponse(BaseModel):
    """覆盖率报告响应模型"""
    total_requirements: int = Field(description="总需求数")
    covered_requirements: int = Field(description="已覆盖需求数")
    coverage_percentage: float = Field(description="覆盖率百分比")
    uncovered_requirements: List[str] = Field(description="未覆盖需求列表")
    report_data: Dict[str, Any] = Field(description="详细报告数据")


class TraceabilityStatsResponse(BaseModel):
    """可追溯性统计响应模型"""
    total_artifacts: int = Field(description="总工件数")
    total_links: int = Field(description="总链接数")
    artifact_types: Dict[str, int] = Field(description="工件类型统计")
    link_types: Dict[str, int] = Field(description="链接类型统计")
    orphaned_artifacts: int = Field(description="孤立工件数")


# 内存相关模型
class MemoryStoreRequest(BaseModel):
    """内存存储请求模型"""
    content: str = Field(description="内容")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="元数据")
    collection_name: Optional[str] = Field(default=None, description="集合名称")
    tags: Optional[List[str]] = Field(default_factory=list, description="标签")


class MemorySearchRequest(BaseModel):
    """内存搜索请求模型"""
    query: str = Field(description="搜索查询")
    collection_name: Optional[str] = Field(default=None, description="集合名称")
    limit: Optional[int] = Field(default=10, description="结果数量限制")
    threshold: Optional[float] = Field(default=0.7, description="相似度阈值")
    filters: Optional[Dict[str, Any]] = Field(default_factory=dict, description="过滤条件")


class MemorySearchResponse(BaseModel):
    """内存搜索响应模型"""
    results: List[Dict[str, Any]] = Field(description="搜索结果")
    total: int = Field(description="总结果数")
    query_time: float = Field(description="查询时间(秒)")


class MemoryStatsResponse(BaseModel):
    """内存统计响应模型"""
    total_documents: int = Field(description="总文档数")
    total_collections: int = Field(description="总集合数")
    collection_stats: Dict[str, int] = Field(description="集合统计")
    memory_usage: Dict[str, Any] = Field(description="内存使用情况")