# 异步多智能体测试用例生成框架 - 完整工作流程演示

## 概述

本框架提供了从需求分析到用例评审的完整工作流程演示，展示了多智能体协作生成测试用例的全过程。

## 🚀 快速开始

### 1. 环境准备

确保已安装并配置好以下组件：
- Python 3.8+
- Conda环境 `autogen`
- Ollama服务（本地LLM）
- ChromaDB（向量数据库）

### 2. 激活环境

```bash
cd /path/to/Embracing-AI/AI-learn/app
source ~/.bash_profile && conda activate autogen
```

### 3. 运行演示

#### 方式一：使用简化演示脚本（推荐）

```bash
# 使用默认需求（用户登录功能）
python scripts/simple_workflow_demo.py

# 使用自定义需求
python scripts/simple_workflow_demo.py --requirement "你的需求描述"

# 查看帮助
python scripts/simple_workflow_demo.py --help
```

#### 方式二：使用完整演示脚本

```bash
# 查看预定义需求样例
python scripts/demo_workflow.py --list-samples

# 使用预定义需求
python scripts/demo_workflow.py --sample 用户登录
python scripts/demo_workflow.py --sample 购物车
python scripts/demo_workflow.py --sample 文件上传

# 使用自定义需求
python scripts/demo_workflow.py --requirement "你的需求描述"
```

#### 方式三：通过系统启动脚本

```bash
# 运行完整工作流程演示
python scripts/start_system.py --requirement "你的需求描述"

# 查看帮助
python scripts/start_system.py --help
```

## 📋 工作流程说明

### 第一步：需求分析 🔍
- **输入**：原始需求文本
- **处理**：解析需求，提取功能点
- **输出**：结构化需求列表，包含ID、描述、优先级等

### 第二步：需求评审 📋
- **输入**：需求分析结果
- **处理**：评审需求质量，识别问题
- **输出**：评审报告，包含评分、状态、改进建议

### 第三步：测试用例生成 🧪
- **输入**：评审后的需求
- **处理**：为每个需求生成多种测试场景
- **输出**：完整的测试用例集合，包含步骤、期望结果

### 第四步：测试用例评审 ✅
- **输入**：生成的测试用例
- **处理**：评审用例质量，检查覆盖率
- **输出**：最终评审报告和优化建议

## 📊 演示输出示例

```
============================================================
🔍 第一步：需求分析
============================================================
输入需求：
用户登录功能需求：
1. 用户可以通过用户名和密码登录系统
2. 登录成功后跳转到主页面
3. 登录失败时显示错误提示
4. 支持记住密码功能
5. 连续登录失败3次后锁定账户30分钟

📝 正在分析需求...
✅ 需求分析完成，提取到 1 个需求
   - REQ_001: 用户登录功能需求：...

============================================================
📋 第二步：需求评审
============================================================
📝 正在评审 1 个需求...
✅ 需求评审完成，总体评分: 85.0
   评审状态: 通过

============================================================
🧪 第三步：测试用例生成
============================================================
📝 正在为 1 个需求生成测试用例...
✅ 测试用例生成完成，共生成 3 个测试用例
   需求覆盖率: 100%
   场景覆盖率: 85%

============================================================
✅ 第四步：测试用例评审
============================================================
📝 正在评审 3 个测试用例...
✅ 测试用例评审完成，总体评分: 86.0
   最终状态: 通过评审

================================================================================
📊 完整工作流程总结
================================================================================
🎯 会话ID: demo_20250729_154904
📝 原始需求: 用户登录功能需求：...
🔍 需求分析: 提取 1 个需求
📋 需求评审: 总体评分 85.0，状态 通过
🧪 用例生成: 生成 3 个测试用例
✅ 用例评审: 总体评分 86.0，状态 通过评审
🎉 完整工作流程演示成功完成！
```

## 🛠️ 系统架构

### 核心组件
- **记忆系统**：ChromaDB向量数据库，存储历史数据
- **可追溯性系统**：管理需求与测试用例的追溯关系
- **LLM服务**：Ollama本地大语言模型
- **多智能体系统**：需求分析、需求评审、用例生成、用例评审

### 数据流
```
原始需求 → 需求分析Agent → 需求评审Agent → 用例生成Agent → 用例评审Agent → 最终报告
     ↓           ↓              ↓              ↓              ↓
   记忆系统 ← 可追溯性系统 ← 可追溯性系统 ← 可追溯性系统 ← 可追溯性系统
```

## 🔧 故障排除

### 常见问题

1. **Ollama连接失败**
   ```bash
   # 检查Ollama服务状态
   ollama list
   
   # 启动Ollama服务
   ollama serve
   ```

2. **ChromaDB初始化失败**
   ```bash
   # 检查数据目录权限
   ls -la data/chroma/
   
   # 清理数据重新初始化
   rm -rf data/chroma/
   ```

3. **智能体处理超时**
   - 检查网络连接
   - 确认模型可用性
   - 调整超时设置

### 日志查看

系统日志保存在 `logs/` 目录下，按日期和组件分类：
- `logs/agent_*.log`：智能体日志
- `logs/system_*.log`：系统日志
- `logs/error_*.log`：错误日志

## 📈 性能优化

### 建议配置
- **内存**：建议8GB以上
- **CPU**：建议4核以上
- **存储**：SSD硬盘，至少10GB可用空间

### 优化选项
- 调整批处理大小
- 配置并发处理数量
- 优化向量数据库索引

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个框架！

### 开发环境设置
```bash
git clone <repository>
cd Embracing-AI/AI-learn/app
conda create -n autogen python=3.8
conda activate autogen
pip install -r requirements.txt
```

### 测试
```bash
# 运行基本组件测试
python scripts/quick_test.py

# 运行系统集成测试
python scripts/test_system_integration.py
```

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

---

**注意**：这是一个演示版本，实际生产环境中需要根据具体需求进行配置和优化。
