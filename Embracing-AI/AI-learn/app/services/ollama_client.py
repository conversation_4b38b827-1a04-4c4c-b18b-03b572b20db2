"""
Ollama本地模型客户端
"""

import asyncio
import json
import httpx
from typing import Dict, List, Optional, Any, AsyncGenerator
from datetime import datetime
import time

from config import get_settings
from core.logger import get_agent_logger
from models.schemas import ThinkingChain, ReflectionResult

settings = get_settings()
logger = get_agent_logger("OllamaClient")


class OllamaClient:
    """Ollama本地模型客户端"""
    
    def __init__(self):
        self.base_url = settings.ollama_base_url
        self.model = settings.ollama_model
        self.timeout = settings.ollama_timeout
        self.client = None
        self._initialized = False
    
    async def initialize(self):
        """初始化HTTP客户端"""
        try:
            self.client = httpx.AsyncClient(
                base_url=self.base_url,
                timeout=httpx.Timeout(self.timeout)
            )
            
            # 测试连接
            await self.health_check()
            self._initialized = True
            logger.info(f"Ollama客户端初始化成功，模型: {self.model}")
            
        except Exception as e:
            logger.error(f"Ollama客户端初始化失败: {str(e)}")
            raise
    
    def _ensure_initialized(self):
        """确保客户端已初始化"""
        if not self._initialized:
            raise RuntimeError("Ollama客户端未初始化，请先调用initialize()")
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            response = await self.client.get("/api/tags")
            if response.status_code == 200:
                data = response.json()
                models = [model["name"] for model in data.get("models", [])]
                
                if self.model in models:
                    logger.info(f"模型 {self.model} 可用")
                    return True
                else:
                    logger.warning(f"模型 {self.model} 不可用，可用模型: {models}")
                    return False
            else:
                logger.error(f"Ollama服务不可用，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"健康检查失败: {str(e)}")
            return False

    async def list_models(self) -> List[str]:
        """获取可用模型列表"""
        try:
            response = await self.client.get("/api/tags")
            if response.status_code == 200:
                data = response.json()
                models = [model["name"] for model in data.get("models", [])]
                return models
            else:
                logger.error(f"获取模型列表失败，状态码: {response.status_code}")
                return []

        except Exception as e:
            logger.error(f"获取模型列表失败: {str(e)}")
            return []

    async def generate_response(
        self, 
        prompt: str, 
        system_prompt: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stream: bool = False
    ) -> str:
        """生成响应"""
        self._ensure_initialized()
        
        try:
            # 构建请求数据
            request_data = {
                "model": self.model,
                "prompt": prompt,
                "stream": stream,
                "options": {
                    "temperature": temperature,
                }
            }
            
            if system_prompt:
                request_data["system"] = system_prompt
            
            if max_tokens:
                request_data["options"]["num_predict"] = max_tokens
            
            start_time = time.time()
            
            if stream:
                # 流式响应
                response_text = ""
                async for chunk in self._stream_generate(request_data):
                    response_text += chunk
                
                end_time = time.time()
                logger.info(f"流式生成完成，耗时: {end_time - start_time:.2f}秒")
                return response_text
            else:
                # 非流式响应 - stream=false时返回单个JSON对象
                response = await self.client.post("/api/generate", json=request_data)
                response.raise_for_status()

                data = response.json()
                end_time = time.time()

                logger.info(f"生成完成，耗时: {end_time - start_time:.2f}秒")
                return data.get("response", "")
                
        except Exception as e:
            logger.error(f"生成响应失败: {str(e)}")
            raise
    
    async def _stream_generate(self, request_data: Dict[str, Any]) -> AsyncGenerator[str, None]:
        """流式生成响应"""
        try:
            async with self.client.stream("POST", "/api/generate", json=request_data) as response:
                response.raise_for_status()
                
                async for line in response.aiter_lines():
                    if line.strip():
                        try:
                            data = json.loads(line)
                            if "response" in data:
                                yield data["response"]
                        except json.JSONDecodeError:
                            continue
                            
        except Exception as e:
            logger.error(f"流式生成失败: {str(e)}")
            raise
    
    async def generate_thinking_chain(
        self, 
        task_description: str, 
        context: Optional[str] = None,
        max_steps: int = 5
    ) -> List[ThinkingChain]:
        """生成思维链"""
        self._ensure_initialized()
        
        system_prompt = """
你是一个专业的AI助手，需要使用思维链方法来分析和解决问题。
请按照以下格式输出你的思考过程：

步骤1: [具体的思考内容]
推理: [详细的推理过程]
置信度: [0.0-1.0之间的数值]

步骤2: [具体的思考内容]
推理: [详细的推理过程]
置信度: [0.0-1.0之间的数值]

...

请确保每个步骤都有清晰的思考内容、详细的推理过程和合理的置信度评估。
"""
        
        prompt = f"""
任务描述: {task_description}

{f"上下文信息: {context}" if context else ""}

请使用思维链方法分析这个任务，最多{max_steps}个步骤。
"""
        
        try:
            response = await self.generate_response(
                prompt=prompt,
                system_prompt=system_prompt,
                temperature=0.3
            )
            
            # 解析思维链
            thinking_chain = self._parse_thinking_chain(response)
            logger.info(f"生成思维链，共{len(thinking_chain)}个步骤")
            
            return thinking_chain
            
        except Exception as e:
            logger.error(f"生成思维链失败: {str(e)}")
            return []
    
    def _parse_thinking_chain(self, response: str) -> List[ThinkingChain]:
        """解析思维链响应"""
        thinking_chain = []
        lines = response.strip().split('\n')
        
        current_step = None
        current_content = ""
        current_reasoning = ""
        current_confidence = 0.0
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            if line.startswith('步骤'):
                # 保存前一个步骤
                if current_step is not None:
                    thinking_chain.append(ThinkingChain(
                        step=current_step,
                        content=current_content,
                        reasoning=current_reasoning,
                        confidence=current_confidence
                    ))
                
                # 开始新步骤
                try:
                    step_num = int(line.split(':')[0].replace('步骤', ''))
                    current_step = step_num
                    current_content = line.split(':', 1)[1].strip()
                except:
                    current_step = len(thinking_chain) + 1
                    current_content = line
                    
            elif line.startswith('推理:'):
                current_reasoning = line.split(':', 1)[1].strip()
                
            elif line.startswith('置信度:'):
                try:
                    confidence_str = line.split(':', 1)[1].strip()
                    current_confidence = float(confidence_str)
                except:
                    current_confidence = 0.5
        
        # 保存最后一个步骤
        if current_step is not None:
            thinking_chain.append(ThinkingChain(
                step=current_step,
                content=current_content,
                reasoning=current_reasoning,
                confidence=current_confidence
            ))
        
        return thinking_chain
    
    async def generate_reflection(
        self, 
        task_description: str,
        current_result: str,
        thinking_chain: Optional[List[ThinkingChain]] = None
    ) -> ReflectionResult:
        """生成反思结果"""
        self._ensure_initialized()
        
        system_prompt = """
你是一个专业的AI助手，需要对当前的工作结果进行反思和评估。
请按照以下格式输出你的反思：

反思问题: [针对当前结果提出的关键问题]
反思答案: [对问题的详细分析和回答]
改进建议: [具体的改进建议，如果没有可以为空]
评分: [0.0-1.0之间的数值，表示当前结果的质量]

请确保反思客观、具体，评分合理。
"""
        
        thinking_context = ""
        if thinking_chain:
            thinking_context = "\n思维链过程:\n"
            for chain in thinking_chain:
                thinking_context += f"步骤{chain.step}: {chain.content}\n推理: {chain.reasoning}\n"
        
        prompt = f"""
任务描述: {task_description}

当前结果: {current_result}

{thinking_context}

请对当前结果进行深入反思，评估其质量和完整性。
"""
        
        try:
            response = await self.generate_response(
                prompt=prompt,
                system_prompt=system_prompt,
                temperature=0.2
            )
            
            # 解析反思结果
            reflection = self._parse_reflection(response)
            logger.info(f"生成反思结果，评分: {reflection.score}")
            
            return reflection
            
        except Exception as e:
            logger.error(f"生成反思失败: {str(e)}")
            return ReflectionResult(
                question="反思生成失败",
                answer=f"错误: {str(e)}",
                score=0.0
            )
    
    def _parse_reflection(self, response: str) -> ReflectionResult:
        """解析反思响应"""
        lines = response.strip().split('\n')
        
        question = ""
        answer = ""
        improvement = None
        score = 0.0
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            if line.startswith('反思问题:'):
                question = line.split(':', 1)[1].strip()
            elif line.startswith('反思答案:'):
                answer = line.split(':', 1)[1].strip()
            elif line.startswith('改进建议:'):
                improvement_text = line.split(':', 1)[1].strip()
                if improvement_text and improvement_text != "无" and improvement_text != "空":
                    improvement = improvement_text
            elif line.startswith('评分:'):
                try:
                    score_str = line.split(':', 1)[1].strip()
                    score = float(score_str)
                except:
                    score = 0.5
        
        return ReflectionResult(
            question=question or "未识别到反思问题",
            answer=answer or "未识别到反思答案",
            improvement=improvement,
            score=max(0.0, min(1.0, score))  # 确保评分在0-1之间
        )
    
    async def chat_completion(
        self,
        messages: List[Dict[str, str]],
        temperature: float = 0.7,
        max_tokens: Optional[int] = None
    ) -> str:
        """聊天完成接口"""
        self._ensure_initialized()
        
        try:
            # 构建对话历史
            conversation = ""
            for msg in messages:
                role = msg.get("role", "user")
                content = msg.get("content", "")
                
                if role == "system":
                    conversation = f"系统: {content}\n\n" + conversation
                elif role == "user":
                    conversation += f"用户: {content}\n"
                elif role == "assistant":
                    conversation += f"助手: {content}\n"
            
            conversation += "助手: "
            
            response = await self.generate_response(
                prompt=conversation,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            return response
            
        except Exception as e:
            logger.error(f"聊天完成失败: {str(e)}")
            raise
    
    async def close(self):
        """关闭客户端"""
        if self.client:
            await self.client.aclose()
            logger.info("Ollama客户端已关闭")


# 全局Ollama客户端实例
ollama_client = OllamaClient()


async def get_ollama_client() -> OllamaClient:
    """获取Ollama客户端实例"""
    if not ollama_client._initialized:
        await ollama_client.initialize()
    return ollama_client