"""
日志配置模块
"""

import sys
import os
from pathlib import Path
from loguru import logger
from config import get_settings

settings = get_settings()


def setup_logger():
    """配置日志系统"""
    
    # 移除默认处理器
    logger.remove()
    
    # 确保日志目录存在
    log_file_path = Path(settings.log_file)
    log_file_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 控制台输出配置
    logger.add(
        sys.stdout,
        level=settings.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
               "<level>{level: <8}</level> | "
               "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
               "<level>{message}</level>",
        colorize=True,
        backtrace=True,
        diagnose=True
    )
    
    # 文件输出配置
    logger.add(
        settings.log_file,
        level=settings.log_level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
        rotation=settings.log_rotation,
        retention=settings.log_retention,
        compression="zip",
        backtrace=True,
        diagnose=True,
        encoding="utf-8"
    )
    
    # Agent专用日志
    logger.add(
        "logs/agents.log",
        level="DEBUG",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {extra[agent_name]} | {message}",
        filter=lambda record: "agent_name" in record["extra"],
        rotation="100 MB",
        retention="7 days",
        encoding="utf-8"
    )
    
    # 协作日志
    logger.add(
        "logs/collaboration.log",
        level="INFO",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {extra[collaboration_id]} | {message}",
        filter=lambda record: "collaboration_id" in record["extra"],
        rotation="50 MB",
        retention="7 days",
        encoding="utf-8"
    )
    
    logger.info("日志系统初始化完成")
    return logger


# 获取配置好的logger实例
app_logger = setup_logger()


def get_agent_logger(agent_name: str):
    """获取Agent专用logger"""
    return logger.bind(agent_name=agent_name)


def get_collaboration_logger(collaboration_id: str):
    """获取协作专用logger"""
    return logger.bind(collaboration_id=collaboration_id)