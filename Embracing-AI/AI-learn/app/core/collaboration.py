"""
异步协作框架和消息传递机制
"""

import asyncio
import uuid
import json
from typing import Dict, List, Any, Optional, Union, Callable, Tuple
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass, asdict
import weakref
from collections import defaultdict, deque

from core.logger import get_agent_logger
from core.memory import get_memory_manager
from models.enums import AgentType, AgentStatus
from models.schemas import ThinkingChain, ReflectionResult


class MessageType(Enum):
    """消息类型枚举"""
    REQUEST = "request"
    RESPONSE = "response"
    NOTIFICATION = "notification"
    BROADCAST = "broadcast"
    HEARTBEAT = "heartbeat"
    ERROR = "error"
    SYNC = "sync"
    FEEDBACK = "feedback"


class MessagePriority(Enum):
    """消息优先级枚举"""
    CRITICAL = 1
    HIGH = 2
    MEDIUM = 3
    LOW = 4


class CollaborationMode(Enum):
    """协作模式枚举"""
    SEQUENTIAL = "sequential"  # 顺序执行
    PARALLEL = "parallel"     # 并行执行
    PIPELINE = "pipeline"     # 流水线执行
    ITERATIVE = "iterative"   # 迭代执行
    FEEDBACK_LOOP = "feedback_loop"  # 反馈循环


@dataclass
class Message:
    """消息数据结构"""
    id: str
    type: MessageType
    priority: MessagePriority
    sender_id: str
    receiver_id: Optional[str]  # None表示广播消息
    content: Dict[str, Any]
    metadata: Dict[str, Any]
    timestamp: datetime
    expires_at: Optional[datetime] = None
    correlation_id: Optional[str] = None  # 用于关联请求和响应
    retry_count: int = 0
    max_retries: int = 3
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "type": self.type.value,
            "priority": self.priority.value,
            "sender_id": self.sender_id,
            "receiver_id": self.receiver_id,
            "content": self.content,
            "metadata": self.metadata,
            "timestamp": self.timestamp.isoformat(),
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "correlation_id": self.correlation_id,
            "retry_count": self.retry_count,
            "max_retries": self.max_retries
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Message':
        """从字典创建消息"""
        return cls(
            id=data["id"],
            type=MessageType(data["type"]),
            priority=MessagePriority(data["priority"]),
            sender_id=data["sender_id"],
            receiver_id=data.get("receiver_id"),
            content=data["content"],
            metadata=data["metadata"],
            timestamp=datetime.fromisoformat(data["timestamp"]),
            expires_at=datetime.fromisoformat(data["expires_at"]) if data.get("expires_at") else None,
            correlation_id=data.get("correlation_id"),
            retry_count=data.get("retry_count", 0),
            max_retries=data.get("max_retries", 3)
        )


@dataclass
class CollaborationTask:
    """协作任务数据结构"""
    id: str
    name: str
    description: str
    mode: CollaborationMode
    participants: List[str]  # Agent IDs
    workflow: List[Dict[str, Any]]  # 工作流定义
    dependencies: Dict[str, List[str]]  # 依赖关系
    timeout: Optional[timedelta] = None
    max_iterations: int = 10
    current_iteration: int = 0
    status: str = "pending"
    results: Dict[str, Any] = None
    created_at: datetime = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.results is None:
            self.results = {}


class MessageBus:
    """消息总线"""
    
    def __init__(self):
        self.logger = get_agent_logger("MessageBus")
        self._subscribers: Dict[str, List[Callable]] = defaultdict(list)
        self._message_queue: Dict[MessagePriority, deque] = {
            priority: deque() for priority in MessagePriority
        }
        self._pending_responses: Dict[str, asyncio.Future] = {}
        self._message_history: deque = deque(maxlen=1000)
        self._running = False
        self._processor_task: Optional[asyncio.Task] = None
        
    async def start(self):
        """启动消息总线"""
        if self._running:
            return
        
        self._running = True
        self._processor_task = asyncio.create_task(self._process_messages())
        self.logger.info("消息总线已启动")
    
    async def stop(self):
        """停止消息总线"""
        if not self._running:
            return
        
        self._running = False
        if self._processor_task:
            self._processor_task.cancel()
            try:
                await self._processor_task
            except asyncio.CancelledError:
                pass
        
        # 清理待处理的响应
        for future in self._pending_responses.values():
            if not future.done():
                future.cancel()
        
        self.logger.info("消息总线已停止")
    
    def subscribe(self, message_type: MessageType, handler: Callable):
        """订阅消息类型"""
        self._subscribers[message_type.value].append(handler)
        self.logger.debug(f"订阅消息类型: {message_type.value}")
    
    def unsubscribe(self, message_type: MessageType, handler: Callable):
        """取消订阅消息类型"""
        if handler in self._subscribers[message_type.value]:
            self._subscribers[message_type.value].remove(handler)
            self.logger.debug(f"取消订阅消息类型: {message_type.value}")
    
    async def publish(self, message: Message) -> Optional[Any]:
        """发布消息"""
        if not self._running:
            raise RuntimeError("消息总线未启动")
        
        # 检查消息是否过期
        if message.expires_at and datetime.now() > message.expires_at:
            self.logger.warning(f"消息已过期: {message.id}")
            return None
        
        # 添加到消息队列
        self._message_queue[message.priority].append(message)
        self._message_history.append(message)
        
        # 如果是请求消息，等待响应
        if message.type == MessageType.REQUEST and message.correlation_id:
            future = asyncio.Future()
            self._pending_responses[message.correlation_id] = future
            
            try:
                # 设置超时
                timeout = 30.0  # 默认30秒超时
                if message.metadata.get("timeout"):
                    timeout = message.metadata["timeout"]
                
                response = await asyncio.wait_for(future, timeout=timeout)
                return response
                
            except asyncio.TimeoutError:
                self.logger.warning(f"消息响应超时: {message.id}")
                self._pending_responses.pop(message.correlation_id, None)
                return None
            except Exception as e:
                self.logger.error(f"等待消息响应时发生错误: {str(e)}")
                self._pending_responses.pop(message.correlation_id, None)
                return None
        
        return None
    
    async def _process_messages(self):
        """处理消息队列"""
        while self._running:
            try:
                # 按优先级处理消息
                message = None
                for priority in MessagePriority:
                    if self._message_queue[priority]:
                        message = self._message_queue[priority].popleft()
                        break
                
                if message is None:
                    await asyncio.sleep(0.1)  # 没有消息时短暂休眠
                    continue
                
                # 处理消息
                await self._handle_message(message)
                
            except Exception as e:
                self.logger.error(f"处理消息时发生错误: {str(e)}")
                await asyncio.sleep(0.1)
    
    async def _handle_message(self, message: Message):
        """处理单个消息"""
        try:
            self.logger.debug(f"处理消息: {message.id} (类型: {message.type.value})")
            
            # 获取订阅者
            handlers = self._subscribers.get(message.type.value, [])
            
            if not handlers:
                self.logger.warning(f"没有找到消息类型 {message.type.value} 的处理器")
                return
            
            # 并发调用所有处理器
            tasks = []
            for handler in handlers:
                task = asyncio.create_task(self._call_handler(handler, message))
                tasks.append(task)
            
            if tasks:
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # 处理响应消息
                for result in results:
                    if isinstance(result, Exception):
                        self.logger.error(f"消息处理器执行失败: {str(result)}")
                    elif result and message.type == MessageType.REQUEST:
                        # 发送响应
                        await self._send_response(message, result)
            
        except Exception as e:
            self.logger.error(f"处理消息 {message.id} 时发生错误: {str(e)}")
    
    async def _call_handler(self, handler: Callable, message: Message) -> Any:
        """调用消息处理器"""
        try:
            if asyncio.iscoroutinefunction(handler):
                return await handler(message)
            else:
                return handler(message)
        except Exception as e:
            self.logger.error(f"消息处理器调用失败: {str(e)}")
            raise
    
    async def _send_response(self, request_message: Message, response_data: Any):
        """发送响应消息"""
        if not request_message.correlation_id:
            return
        
        # 检查是否有等待的Future
        future = self._pending_responses.get(request_message.correlation_id)
        if future and not future.done():
            future.set_result(response_data)
            self._pending_responses.pop(request_message.correlation_id, None)
    
    def get_message_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取消息历史"""
        history = list(self._message_history)[-limit:]
        return [msg.to_dict() for msg in history]
    
    def get_queue_status(self) -> Dict[str, int]:
        """获取队列状态"""
        return {
            priority.name: len(queue) 
            for priority, queue in self._message_queue.items()
        }


class CollaborationOrchestrator:
    """协作编排器"""
    
    def __init__(self, message_bus: MessageBus):
        self.logger = get_agent_logger("CollaborationOrchestrator")
        self.message_bus = message_bus
        self.memory_manager = None
        self._active_tasks: Dict[str, CollaborationTask] = {}
        self._agent_registry: Dict[str, Dict[str, Any]] = {}
        self._task_history: deque = deque(maxlen=100)
        
    async def initialize(self):
        """初始化协作编排器"""
        self.memory_manager = await get_memory_manager()
        
        # 订阅相关消息类型
        self.message_bus.subscribe(MessageType.HEARTBEAT, self._handle_heartbeat)
        self.message_bus.subscribe(MessageType.SYNC, self._handle_sync)
        self.message_bus.subscribe(MessageType.FEEDBACK, self._handle_feedback)
        
        self.logger.info("协作编排器初始化完成")
    
    async def register_agent(
        self,
        agent_id: str,
        agent_type: AgentType,
        capabilities: List[str],
        metadata: Dict[str, Any] = None
    ):
        """注册Agent"""
        self._agent_registry[agent_id] = {
            "id": agent_id,
            "type": agent_type.value,
            "capabilities": capabilities,
            "metadata": metadata or {},
            "status": AgentStatus.IDLE.value,
            "last_heartbeat": datetime.now(),
            "registered_at": datetime.now()
        }
        
        self.logger.info(f"Agent已注册: {agent_id} (类型: {agent_type.value})")
    
    async def unregister_agent(self, agent_id: str):
        """注销Agent"""
        if agent_id in self._agent_registry:
            del self._agent_registry[agent_id]
            self.logger.info(f"Agent已注销: {agent_id}")
    
    async def create_collaboration_task(
        self,
        name: str,
        description: str,
        mode: CollaborationMode,
        workflow: List[Dict[str, Any]],
        participants: Optional[List[str]] = None,
        dependencies: Optional[Dict[str, List[str]]] = None,
        timeout: Optional[timedelta] = None
    ) -> str:
        """创建协作任务"""
        task_id = f"task_{uuid.uuid4().hex[:8]}"
        
        # 如果没有指定参与者，根据工作流自动选择
        if participants is None:
            participants = await self._select_participants(workflow)
        
        task = CollaborationTask(
            id=task_id,
            name=name,
            description=description,
            mode=mode,
            participants=participants,
            workflow=workflow,
            dependencies=dependencies or {},
            timeout=timeout
        )
        
        self._active_tasks[task_id] = task
        self.logger.info(f"协作任务已创建: {task_id} (模式: {mode.value})")
        
        return task_id
    
    async def execute_task(self, task_id: str, initial_data: Any = None) -> Dict[str, Any]:
        """执行协作任务"""
        if task_id not in self._active_tasks:
            raise ValueError(f"任务不存在: {task_id}")
        
        task = self._active_tasks[task_id]
        task.status = "running"
        task.started_at = datetime.now()
        
        try:
            self.logger.info(f"开始执行协作任务: {task_id}")
            
            # 根据协作模式执行任务
            if task.mode == CollaborationMode.SEQUENTIAL:
                result = await self._execute_sequential(task, initial_data)
            elif task.mode == CollaborationMode.PARALLEL:
                result = await self._execute_parallel(task, initial_data)
            elif task.mode == CollaborationMode.PIPELINE:
                result = await self._execute_pipeline(task, initial_data)
            elif task.mode == CollaborationMode.ITERATIVE:
                result = await self._execute_iterative(task, initial_data)
            elif task.mode == CollaborationMode.FEEDBACK_LOOP:
                result = await self._execute_feedback_loop(task, initial_data)
            else:
                raise ValueError(f"不支持的协作模式: {task.mode}")
            
            task.status = "completed"
            task.completed_at = datetime.now()
            task.results = result
            
            # 移动到历史记录
            self._task_history.append(task)
            del self._active_tasks[task_id]
            
            self.logger.info(f"协作任务执行完成: {task_id}")
            return result
            
        except Exception as e:
            task.status = "failed"
            task.completed_at = datetime.now()
            self.logger.error(f"协作任务执行失败: {task_id}, 错误: {str(e)}")
            raise
    
    async def _execute_sequential(self, task: CollaborationTask, initial_data: Any) -> Dict[str, Any]:
        """顺序执行模式"""
        results = {}
        current_data = initial_data
        
        for step in task.workflow:
            agent_id = step.get("agent_id")
            action = step.get("action")
            params = step.get("params", {})
            
            if not agent_id or agent_id not in self._agent_registry:
                raise ValueError(f"无效的Agent ID: {agent_id}")
            
            # 发送请求消息
            message = Message(
                id=f"msg_{uuid.uuid4().hex[:8]}",
                type=MessageType.REQUEST,
                priority=MessagePriority.MEDIUM,
                sender_id="orchestrator",
                receiver_id=agent_id,
                content={
                    "action": action,
                    "params": params,
                    "data": current_data
                },
                metadata={
                    "task_id": task.id,
                    "step": step,
                    "timeout": 60.0
                },
                correlation_id=f"corr_{uuid.uuid4().hex[:8]}"
            )
            
            # 等待响应
            response = await self.message_bus.publish(message)
            if response is None:
                raise RuntimeError(f"Agent {agent_id} 未响应")
            
            results[agent_id] = response
            current_data = response  # 将结果传递给下一步
        
        return results
    
    async def _execute_parallel(self, task: CollaborationTask, initial_data: Any) -> Dict[str, Any]:
        """并行执行模式"""
        tasks = []
        
        for step in task.workflow:
            agent_id = step.get("agent_id")
            action = step.get("action")
            params = step.get("params", {})
            
            if not agent_id or agent_id not in self._agent_registry:
                continue
            
            # 创建并行任务
            task_coroutine = self._send_agent_request(
                agent_id, action, params, initial_data, task.id
            )
            tasks.append((agent_id, task_coroutine))
        
        # 等待所有任务完成
        results = {}
        for agent_id, task_coroutine in tasks:
            try:
                response = await task_coroutine
                results[agent_id] = response
            except Exception as e:
                self.logger.error(f"Agent {agent_id} 执行失败: {str(e)}")
                results[agent_id] = {"error": str(e)}
        
        return results
    
    async def _execute_pipeline(self, task: CollaborationTask, initial_data: Any) -> Dict[str, Any]:
        """流水线执行模式"""
        results = {}
        data_pipeline = [initial_data]
        
        # 创建数据流水线
        for i, step in enumerate(task.workflow):
            agent_id = step.get("agent_id")
            action = step.get("action")
            params = step.get("params", {})
            
            if not agent_id or agent_id not in self._agent_registry:
                continue
            
            # 从流水线获取输入数据
            input_data = data_pipeline[i] if i < len(data_pipeline) else None
            
            # 异步处理
            response_future = asyncio.create_task(
                self._send_agent_request(agent_id, action, params, input_data, task.id)
            )
            
            # 将Future添加到流水线
            data_pipeline.append(response_future)
        
        # 等待流水线完成
        for i, step in enumerate(task.workflow):
            agent_id = step.get("agent_id")
            if i + 1 < len(data_pipeline):
                future = data_pipeline[i + 1]
                if asyncio.isfuture(future) or asyncio.iscoroutine(future):
                    try:
                        response = await future
                        results[agent_id] = response
                        data_pipeline[i + 1] = response  # 替换Future为实际结果
                    except Exception as e:
                        self.logger.error(f"流水线步骤 {i} 失败: {str(e)}")
                        results[agent_id] = {"error": str(e)}
        
        return results
    
    async def _execute_iterative(self, task: CollaborationTask, initial_data: Any) -> Dict[str, Any]:
        """迭代执行模式"""
        results = {}
        current_data = initial_data
        
        for iteration in range(task.max_iterations):
            task.current_iteration = iteration + 1
            iteration_results = {}
            
            # 执行一轮迭代
            for step in task.workflow:
                agent_id = step.get("agent_id")
                action = step.get("action")
                params = step.get("params", {})
                
                if not agent_id or agent_id not in self._agent_registry:
                    continue
                
                response = await self._send_agent_request(
                    agent_id, action, params, current_data, task.id
                )
                iteration_results[agent_id] = response
            
            results[f"iteration_{iteration + 1}"] = iteration_results
            
            # 检查收敛条件
            if await self._check_convergence(iteration_results, task):
                self.logger.info(f"任务 {task.id} 在第 {iteration + 1} 轮迭代后收敛")
                break
            
            # 准备下一轮迭代的数据
            current_data = await self._prepare_next_iteration_data(
                iteration_results, current_data
            )
        
        return results
    
    async def _execute_feedback_loop(self, task: CollaborationTask, initial_data: Any) -> Dict[str, Any]:
        """反馈循环执行模式"""
        results = {}
        current_data = initial_data
        feedback_history = []
        
        for iteration in range(task.max_iterations):
            task.current_iteration = iteration + 1
            iteration_results = {}
            
            # 执行主要工作流
            for step in task.workflow:
                agent_id = step.get("agent_id")
                action = step.get("action")
                params = step.get("params", {})
                
                if not agent_id or agent_id not in self._agent_registry:
                    continue
                
                # 添加反馈历史到参数中
                enhanced_params = {**params, "feedback_history": feedback_history}
                
                response = await self._send_agent_request(
                    agent_id, action, enhanced_params, current_data, task.id
                )
                iteration_results[agent_id] = response
            
            # 生成反馈
            feedback = await self._generate_feedback(iteration_results, task)
            feedback_history.append({
                "iteration": iteration + 1,
                "results": iteration_results,
                "feedback": feedback,
                "timestamp": datetime.now().isoformat()
            })
            
            results[f"iteration_{iteration + 1}"] = {
                "results": iteration_results,
                "feedback": feedback
            }
            
            # 检查是否满足停止条件
            if feedback.get("should_stop", False):
                self.logger.info(f"反馈循环任务 {task.id} 在第 {iteration + 1} 轮后停止")
                break
            
            # 根据反馈调整下一轮的数据
            current_data = feedback.get("next_data", current_data)
        
        return results
    
    async def _send_agent_request(
        self,
        agent_id: str,
        action: str,
        params: Dict[str, Any],
        data: Any,
        task_id: str
    ) -> Any:
        """发送Agent请求"""
        message = Message(
            id=f"msg_{uuid.uuid4().hex[:8]}",
            type=MessageType.REQUEST,
            priority=MessagePriority.MEDIUM,
            sender_id="orchestrator",
            receiver_id=agent_id,
            content={
                "action": action,
                "params": params,
                "data": data
            },
            metadata={
                "task_id": task_id,
                "timeout": 60.0
            },
            correlation_id=f"corr_{uuid.uuid4().hex[:8]}"
        )
        
        response = await self.message_bus.publish(message)
        if response is None:
            raise RuntimeError(f"Agent {agent_id} 未响应请求")
        
        return response
    
    async def _select_participants(self, workflow: List[Dict[str, Any]]) -> List[str]:
        """根据工作流自动选择参与者"""
        participants = set()
        
        for step in workflow:
            agent_type = step.get("agent_type")
            if agent_type:
                # 根据Agent类型查找可用的Agent
                for agent_id, agent_info in self._agent_registry.items():
                    if agent_info["type"] == agent_type:
                        participants.add(agent_id)
                        break
        
        return list(participants)
    
    async def _check_convergence(
        self,
        iteration_results: Dict[str, Any],
        task: CollaborationTask
    ) -> bool:
        """检查迭代收敛条件"""
        # 简单的收敛检查逻辑
        # 实际应用中可以根据具体需求实现更复杂的收敛判断
        
        for agent_id, result in iteration_results.items():
            if isinstance(result, dict):
                # 检查是否有明确的收敛标志
                if result.get("converged", False):
                    return True
                
                # 检查是否没有更多工作要做
                if result.get("has_more", True) is False:
                    return True
        
        return False
    
    async def _prepare_next_iteration_data(
        self,
        iteration_results: Dict[str, Any],
        current_data: Any
    ) -> Any:
        """准备下一轮迭代的数据"""
        # 合并当前迭代的结果
        next_data = {
            "previous_results": iteration_results,
            "original_data": current_data
        }
        
        # 提取关键信息用于下一轮
        for agent_id, result in iteration_results.items():
            if isinstance(result, dict):
                # 提取需要传递给下一轮的数据
                if "next_iteration_data" in result:
                    next_data[f"{agent_id}_data"] = result["next_iteration_data"]
        
        return next_data
    
    async def _generate_feedback(
        self,
        iteration_results: Dict[str, Any],
        task: CollaborationTask
    ) -> Dict[str, Any]:
        """生成反馈信息"""
        feedback = {
            "iteration": task.current_iteration,
            "timestamp": datetime.now().isoformat(),
            "results_summary": {},
            "issues": [],
            "suggestions": [],
            "should_stop": False,
            "next_data": None
        }
        
        # 分析每个Agent的结果
        for agent_id, result in iteration_results.items():
            if isinstance(result, dict):
                # 提取结果摘要
                feedback["results_summary"][agent_id] = {
                    "status": result.get("status", "unknown"),
                    "quality_score": result.get("quality_score", 0),
                    "issues_count": len(result.get("issues", []))
                }
                
                # 收集问题和建议
                if "issues" in result:
                    feedback["issues"].extend(result["issues"])
                
                if "suggestions" in result:
                    feedback["suggestions"].extend(result["suggestions"])
                
                # 检查是否应该停止
                if result.get("should_stop", False):
                    feedback["should_stop"] = True
        
        # 生成整体评估
        total_quality = sum(
            summary.get("quality_score", 0) 
            for summary in feedback["results_summary"].values()
        )
        avg_quality = total_quality / len(feedback["results_summary"]) if feedback["results_summary"] else 0
        
        # 如果质量足够高，建议停止
        if avg_quality >= 0.8:
            feedback["should_stop"] = True
            feedback["suggestions"].append("整体质量已达到要求，建议停止迭代")
        
        return feedback
    
    async def _handle_heartbeat(self, message: Message):
        """处理心跳消息"""
        agent_id = message.sender_id
        if agent_id in self._agent_registry:
            self._agent_registry[agent_id]["last_heartbeat"] = datetime.now()
            self._agent_registry[agent_id]["status"] = message.content.get("status", "unknown")
    
    async def _handle_sync(self, message: Message):
        """处理同步消息"""
        # 处理Agent状态同步
        agent_id = message.sender_id
        if agent_id in self._agent_registry:
            sync_data = message.content
            self._agent_registry[agent_id].update(sync_data)
    
    async def _handle_feedback(self, message: Message):
        """处理反馈消息"""
        # 处理Agent反馈信息
        feedback_data = message.content
        self.logger.info(f"收到来自 {message.sender_id} 的反馈: {feedback_data}")
        
        # 可以根据反馈调整协作策略
        # 这里可以实现更复杂的反馈处理逻辑
    
    def get_active_tasks(self) -> List[Dict[str, Any]]:
        """获取活跃任务列表"""
        return [
            {
                "id": task.id,
                "name": task.name,
                "description": task.description,
                "mode": task.mode.value,
                "participants": task.participants,
                "status": task.status,
                "current_iteration": task.current_iteration,
                "max_iterations": task.max_iterations,
                "created_at": task.created_at.isoformat(),
                "started_at": task.started_at.isoformat() if task.started_at else None
            }
            for task in self._active_tasks.values()
        ]
    
    def get_task_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取任务历史"""
        history = list(self._task_history)[-limit:]
        return [
            {
                "id": task.id,
                "name": task.name,
                "mode": task.mode.value,
                "status": task.status,
                "participants": task.participants,
                "created_at": task.created_at.isoformat(),
                "started_at": task.started_at.isoformat() if task.started_at else None,
                "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                "duration": (task.completed_at - task.started_at).total_seconds() if task.completed_at and task.started_at else None
            }
            for task in history
        ]
    
    def get_agent_registry(self) -> Dict[str, Dict[str, Any]]:
        """获取Agent注册表"""
        return dict(self._agent_registry)
    
    def get_agent_status(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """获取Agent状态"""
        return self._agent_registry.get(agent_id)
    
    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        if task_id not in self._active_tasks:
            return False
        
        task = self._active_tasks[task_id]
        task.status = "cancelled"
        task.completed_at = datetime.now()
        
        # 移动到历史记录
        self._task_history.append(task)
        del self._active_tasks[task_id]
        
        self.logger.info(f"任务已取消: {task_id}")
        return True


class AgentCollaborationMixin:
    """Agent协作混入类"""
    
    def __init__(self):
        self.collaboration_bus: Optional[MessageBus] = None
        self.orchestrator: Optional[CollaborationOrchestrator] = None
        self._message_handlers: Dict[MessageType, Callable] = {}
        self._collaboration_logger = get_agent_logger(f"Collaboration_{self.__class__.__name__}")
    
    async def setup_collaboration(
        self,
        message_bus: MessageBus,
        orchestrator: CollaborationOrchestrator
    ):
        """设置协作环境"""
        self.collaboration_bus = message_bus
        self.orchestrator = orchestrator
        
        # 注册默认消息处理器
        self._setup_default_handlers()
        
        # 订阅消息
        for msg_type, handler in self._message_handlers.items():
            self.collaboration_bus.subscribe(msg_type, handler)
        
        self._collaboration_logger.info("协作环境设置完成")
    
    def _setup_default_handlers(self):
        """设置默认消息处理器"""
        self._message_handlers[MessageType.REQUEST] = self._handle_collaboration_request
        self._message_handlers[MessageType.NOTIFICATION] = self._handle_notification
        self._message_handlers[MessageType.BROADCAST] = self._handle_broadcast
    
    async def _handle_collaboration_request(self, message: Message) -> Dict[str, Any]:
        """处理协作请求"""
        try:
            action = message.content.get("action")
            params = message.content.get("params", {})
            data = message.content.get("data")
            
            if action == "process":
                # 处理数据
                result = await self.process(data)
                return {
                    "status": "success",
                    "result": result,
                    "agent_id": getattr(self, 'agent_id', 'unknown')
                }
            elif action == "status":
                # 返回状态信息
                return {
                    "status": "success",
                    "agent_status": getattr(self, 'status', 'unknown'),
                    "agent_id": getattr(self, 'agent_id', 'unknown')
                }
            else:
                return {
                    "status": "error",
                    "error": f"不支持的操作: {action}",
                    "agent_id": getattr(self, 'agent_id', 'unknown')
                }
                
        except Exception as e:
            self._collaboration_logger.error(f"处理协作请求失败: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "agent_id": getattr(self, 'agent_id', 'unknown')
            }
    
    async def _handle_notification(self, message: Message):
        """处理通知消息"""
        notification_type = message.content.get("type")
        self._collaboration_logger.info(f"收到通知: {notification_type}")
    
    async def _handle_broadcast(self, message: Message):
        """处理广播消息"""
        broadcast_type = message.content.get("type")
        self._collaboration_logger.info(f"收到广播: {broadcast_type}")
    
    async def send_message(
        self,
        receiver_id: str,
        message_type: MessageType,
        content: Dict[str, Any],
        priority: MessagePriority = MessagePriority.MEDIUM,
        wait_response: bool = False
    ) -> Optional[Any]:
        """发送消息"""
        if not self.collaboration_bus:
            raise RuntimeError("协作环境未设置")
        
        correlation_id = f"corr_{uuid.uuid4().hex[:8]}" if wait_response else None
        
        message = Message(
            id=f"msg_{uuid.uuid4().hex[:8]}",
            type=message_type,
            priority=priority,
            sender_id=getattr(self, 'agent_id', 'unknown'),
            receiver_id=receiver_id,
            content=content,
            metadata={
                "sender_type": self.__class__.__name__,
                "timestamp": datetime.now().isoformat()
            },
            correlation_id=correlation_id
        )
        
        if wait_response:
            return await self.collaboration_bus.publish(message)
        else:
            await self.collaboration_bus.publish(message)
            return None
    
    async def broadcast_message(
        self,
        message_type: MessageType,
        content: Dict[str, Any],
        priority: MessagePriority = MessagePriority.LOW
    ):
        """广播消息"""
        if not self.collaboration_bus:
            raise RuntimeError("协作环境未设置")
        
        message = Message(
            id=f"msg_{uuid.uuid4().hex[:8]}",
            type=message_type,
            priority=priority,
            sender_id=getattr(self, 'agent_id', 'unknown'),
            receiver_id=None,  # 广播消息
            content=content,
            metadata={
                "sender_type": self.__class__.__name__,
                "timestamp": datetime.now().isoformat()
            }
        )
        
        await self.collaboration_bus.publish(message)
    
    async def send_heartbeat(self):
        """发送心跳"""
        if not self.collaboration_bus:
            return
        
        await self.send_message(
            receiver_id="orchestrator",
            message_type=MessageType.HEARTBEAT,
            content={
                "status": getattr(self, 'status', 'unknown'),
                "timestamp": datetime.now().isoformat()
            },
            priority=MessagePriority.LOW
        )
    
    async def request_collaboration(
        self,
        target_agent_id: str,
        action: str,
        data: Any,
        params: Dict[str, Any] = None,
        timeout: float = 30.0
    ) -> Any:
        """请求其他Agent协作"""
        content = {
            "action": action,
            "data": data,
            "params": params or {}
        }
        
        response = await self.send_message(
            receiver_id=target_agent_id,
            message_type=MessageType.REQUEST,
            content=content,
            priority=MessagePriority.MEDIUM,
            wait_response=True
        )
        
        return response


# 全局实例
_message_bus: Optional[MessageBus] = None
_orchestrator: Optional[CollaborationOrchestrator] = None


async def get_message_bus() -> MessageBus:
    """获取消息总线实例"""
    global _message_bus
    if _message_bus is None:
        _message_bus = MessageBus()
        await _message_bus.start()
    return _message_bus


async def get_orchestrator() -> CollaborationOrchestrator:
    """获取协作编排器实例"""
    global _orchestrator
    if _orchestrator is None:
        message_bus = await get_message_bus()
        _orchestrator = CollaborationOrchestrator(message_bus)
        await _orchestrator.initialize()
    return _orchestrator


async def cleanup_collaboration():
    """清理协作资源"""
    global _message_bus, _orchestrator
    
    if _message_bus:
        await _message_bus.stop()
        _message_bus = None
    
    _orchestrator = None


# 工作流模板
WORKFLOW_TEMPLATES = {
    "requirement_to_testcase": {
        "name": "需求到测试用例生成流程",
        "description": "从需求分析到测试用例生成的完整流程",
        "mode": CollaborationMode.SEQUENTIAL,
        "workflow": [
            {
                "agent_type": "requirement_analysis",
                "action": "process",
                "params": {"analysis_depth": "detailed"}
            },
            {
                "agent_type": "requirement_review",
                "action": "process",
                "params": {"review_mode": "comprehensive"}
            },
            {
                "agent_type": "testcase_generation",
                "action": "process",
                "params": {"generation_mode": "exhaustive"}
            },
            {
                "agent_type": "testcase_review",
                "action": "process",
                "params": {"review_mode": "quality_focused"}
            }
        ]
    },
    "iterative_improvement": {
        "name": "迭代改进流程",
        "description": "通过多轮迭代不断改进测试用例质量",
        "mode": CollaborationMode.ITERATIVE,
        "workflow": [
            {
                "agent_type": "testcase_generation",
                "action": "process",
                "params": {"batch_size": 5}
            },
            {
                "agent_type": "testcase_review",
                "action": "process",
                "params": {"strict_mode": True}
            }
        ]
    },
    "parallel_analysis": {
        "name": "并行分析流程",
        "description": "并行执行需求分析和评审",
        "mode": CollaborationMode.PARALLEL,
        "workflow": [
            {
                "agent_type": "requirement_analysis",
                "action": "process",
                "params": {"focus": "functional"}
            },
            {
                "agent_type": "requirement_analysis",
                "action": "process",
                "params": {"focus": "non_functional"}
            }
        ]
    },
    "feedback_loop": {
        "name": "反馈循环流程",
        "description": "基于反馈持续改进的流程",
        "mode": CollaborationMode.FEEDBACK_LOOP,
        "workflow": [
            {
                "agent_type": "testcase_generation",
                "action": "process",
                "params": {}
            },
            {
                "agent_type": "testcase_review",
                "action": "process",
                "params": {}
            }
        ]
    }
}


async def create_workflow_from_template(
    template_name: str,
    orchestrator: CollaborationOrchestrator,
    custom_params: Dict[str, Any] = None
) -> str:
    """从模板创建工作流"""
    if template_name not in WORKFLOW_TEMPLATES:
        raise ValueError(f"未知的工作流模板: {template_name}")
    
    template = WORKFLOW_TEMPLATES[template_name].copy()
    
    # 应用自定义参数
    if custom_params:
        for step in template["workflow"]:
            step["params"].update(custom_params.get("step_params", {}))
    
    # 创建协作任务
    task_id = await orchestrator.create_collaboration_task(
        name=template["name"],
        description=template["description"],
        mode=CollaborationMode(template["mode"]),
        workflow=template["workflow"],
        timeout=custom_params.get("timeout") if custom_params else None
    )
    
    return task_id


# 全局协作管理器实例
_collaboration_manager = None


def get_collaboration_manager() -> CollaborationOrchestrator:
    """获取协作管理器实例"""
    global _collaboration_manager
    if _collaboration_manager is None:
        message_bus = MessageBus()
        _collaboration_manager = CollaborationOrchestrator(message_bus)
    return _collaboration_manager
