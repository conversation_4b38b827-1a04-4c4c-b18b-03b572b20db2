"""
可追溯性矩阵和状态管理系统
"""

import asyncio
import uuid
import json
from typing import Dict, List, Any, Optional, Union, Tuple, Set
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict, field
from enum import Enum
import networkx as nx
from collections import defaultdict, deque

from core.logger import get_agent_logger
from core.memory import get_memory_manager
from models.enums import AgentType, AgentStatus


class TraceabilityType(Enum):
    """追溯关系类型"""
    DERIVES_FROM = "derives_from"        # 派生自
    IMPLEMENTS = "implements"            # 实现
    TESTS = "tests"                     # 测试
    REVIEWS = "reviews"                 # 评审
    DEPENDS_ON = "depends_on"           # 依赖于
    CONFLICTS_WITH = "conflicts_with"    # 冲突
    RELATES_TO = "relates_to"           # 相关
    VALIDATES = "validates"             # 验证
    COVERS = "covers"                   # 覆盖


class ArtifactType(Enum):
    """工件类型"""
    REQUIREMENT = "requirement"
    SPECIFICATION = "specification"
    DESIGN = "design"
    TESTCASE = "testcase"
    TEST_RESULT = "test_result"
    REVIEW = "review"
    DEFECT = "defect"
    CHANGE_REQUEST = "change_request"


class ArtifactStatus(Enum):
    """工件状态"""
    DRAFT = "draft"
    UNDER_REVIEW = "under_review"
    APPROVED = "approved"
    IMPLEMENTED = "implemented"
    TESTED = "tested"
    DEPLOYED = "deployed"
    REJECTED = "rejected"
    OBSOLETE = "obsolete"


class ChangeType(Enum):
    """变更类型"""
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"
    STATUS_CHANGE = "status_change"
    RELATIONSHIP_ADD = "relationship_add"
    RELATIONSHIP_REMOVE = "relationship_remove"


@dataclass
class TraceabilityArtifact:
    """可追溯工件"""
    id: str
    type: ArtifactType
    name: str
    description: str
    content: Dict[str, Any]
    status: ArtifactStatus
    version: str = "1.0"
    created_by: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "type": self.type.value,
            "name": self.name,
            "description": self.description,
            "content": self.content,
            "status": self.status.value,
            "version": self.version,
            "created_by": self.created_by,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "tags": self.tags,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TraceabilityArtifact':
        """从字典创建工件"""
        return cls(
            id=data["id"],
            type=ArtifactType(data["type"]),
            name=data["name"],
            description=data["description"],
            content=data["content"],
            status=ArtifactStatus(data["status"]),
            version=data.get("version", "1.0"),
            created_by=data.get("created_by"),
            created_at=datetime.fromisoformat(data["created_at"]),
            updated_at=datetime.fromisoformat(data["updated_at"]),
            tags=data.get("tags", []),
            metadata=data.get("metadata", {})
        )


@dataclass
class TraceabilityLink:
    """追溯链接"""
    id: str
    source_id: str
    target_id: str
    type: TraceabilityType
    strength: float = 1.0
    bidirectional: bool = False
    created_by: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "source_id": self.source_id,
            "target_id": self.target_id,
            "type": self.type.value,
            "strength": self.strength,
            "bidirectional": self.bidirectional,
            "created_by": self.created_by,
            "created_at": self.created_at.isoformat(),
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TraceabilityLink':
        """从字典创建链接"""
        return cls(
            id=data["id"],
            source_id=data["source_id"],
            target_id=data["target_id"],
            type=TraceabilityType(data["type"]),
            strength=data.get("strength", 1.0),
            bidirectional=data.get("bidirectional", False),
            created_by=data.get("created_by"),
            created_at=datetime.fromisoformat(data["created_at"]),
            metadata=data.get("metadata", {})
        )


@dataclass
class ChangeRecord:
    """变更记录"""
    id: str
    artifact_id: str
    change_type: ChangeType
    old_value: Any
    new_value: Any
    changed_by: str
    changed_at: datetime = field(default_factory=datetime.now)
    reason: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "artifact_id": self.artifact_id,
            "change_type": self.change_type.value,
            "old_value": self.old_value,
            "new_value": self.new_value,
            "changed_by": self.changed_by,
            "changed_at": self.changed_at.isoformat(),
            "reason": self.reason,
            "metadata": self.metadata
        }


@dataclass
class CoverageReport:
    """覆盖率报告"""
    total_requirements: int
    covered_requirements: int
    uncovered_requirements: List[str]
    coverage_percentage: float
    coverage_by_type: Dict[str, Dict[str, int]]
    gaps: List[Dict[str, Any]]
    recommendations: List[str]
    generated_at: datetime = field(default_factory=datetime.now)


class TraceabilityMatrix:
    """可追溯性矩阵"""
    
    def __init__(self):
        self.logger = get_agent_logger("TraceabilityMatrix")
        self.memory_manager = None
        
        # 工件存储
        self._artifacts: Dict[str, TraceabilityArtifact] = {}
        self._links: Dict[str, TraceabilityLink] = {}
        self._change_history: List[ChangeRecord] = []
        
        # 索引
        self._type_index: Dict[ArtifactType, Set[str]] = defaultdict(set)
        self._status_index: Dict[ArtifactStatus, Set[str]] = defaultdict(set)
        self._tag_index: Dict[str, Set[str]] = defaultdict(set)
        self._creator_index: Dict[str, Set[str]] = defaultdict(set)
        
        # 关系图
        self._graph = nx.DiGraph()
        
        # 缓存
        self._coverage_cache: Dict[str, CoverageReport] = {}
        self._path_cache: Dict[Tuple[str, str], List[List[str]]] = {}
    
    async def initialize(self):
        """初始化追溯矩阵"""
        self.memory_manager = await get_memory_manager()
        await self._load_existing_data()
        self.logger.info("可追溯性矩阵初始化完成")
    
    async def _load_existing_data(self):
        """加载现有数据"""
        # try:
        # 从内存管理器加载数据
        collection = await self.memory_manager.get_collection("traceability_matrix")
        if collection:
            results = collection.get()
            if results and results['ids']:
                for i, item_id in enumerate(results['ids']):
                    metadata = results['metadatas'][i] if results['metadatas'] else {}
                    
                    if metadata.get('type') == 'artifact':
                        artifact_data = json.loads(metadata['data'])
                        artifact = TraceabilityArtifact.from_dict(artifact_data)
                        self._artifacts[artifact.id] = artifact
                        await self._update_artifact_indexes(artifact)
                        
                    elif metadata.get('type') == 'link':
                        link_data = json.loads(metadata['data'])
                        link = TraceabilityLink.from_dict(link_data)
                        self._links[link.id] = link
                        self._graph.add_edge(link.source_id, link.target_id, 
                                            link_type=link.type, link_id=link.id)
        
        self.logger.info(f"加载了 {len(self._artifacts)} 个工件和 {len(self._links)} 个链接")
            
        # except Exception as e:
        #     self.logger.error(f"加载现有数据失败: {str(e)}")
    
    async def create_artifact(
        self,
        artifact_type: ArtifactType,
        name: str,
        description: str,
        content: Dict[str, Any],
        created_by: str = None,
        tags: List[str] = None,
        metadata: Dict[str, Any] = None
    ) -> str:
        """创建工件"""
        try:
            artifact_id = f"{artifact_type.value}_{uuid.uuid4().hex[:8]}"
            
            artifact = TraceabilityArtifact(
                id=artifact_id,
                type=artifact_type,
                name=name,
                description=description,
                content=content,
                status=ArtifactStatus.DRAFT,
                created_by=created_by,
                tags=tags or [],
                metadata=metadata or {}
            )
            
            # 存储工件
            self._artifacts[artifact_id] = artifact
            await self._update_artifact_indexes(artifact)
            
            # 添加到图
            self._graph.add_node(artifact_id, artifact_type=artifact_type, name=name)
            
            # 持久化
            await self._persist_artifact(artifact)
            
            # 记录变更
            await self._record_change(
                artifact_id=artifact_id,
                change_type=ChangeType.CREATE,
                old_value=None,
                new_value=artifact.to_dict(),
                changed_by=created_by or "system"
            )
            
            self.logger.info(f"创建工件成功: {artifact_id} ({name})")
            return artifact_id
            
        except Exception as e:
            self.logger.error(f"创建工件失败: {str(e)}")
            raise
    
    async def create_link(
        self,
        source_id: str,
        target_id: str,
        link_type: TraceabilityType,
        strength: float = 1.0,
        bidirectional: bool = False,
        created_by: str = None,
        metadata: Dict[str, Any] = None
    ) -> str:
        """创建追溯链接"""
        try:
            # 验证工件存在
            if source_id not in self._artifacts or target_id not in self._artifacts:
                raise ValueError("源工件或目标工件不存在")
            
            link_id = f"link_{uuid.uuid4().hex[:8]}"
            
            link = TraceabilityLink(
                id=link_id,
                source_id=source_id,
                target_id=target_id,
                type=link_type,
                strength=strength,
                bidirectional=bidirectional,
                created_by=created_by,
                metadata=metadata or {}
            )
            
            # 存储链接
            self._links[link_id] = link
            
            # 添加到图
            self._graph.add_edge(source_id, target_id, 
                               link_type=link_type, link_id=link_id, strength=strength)
            
            if bidirectional:
                self._graph.add_edge(target_id, source_id, 
                                   link_type=link_type, link_id=link_id, strength=strength)
            
            # 持久化
            await self._persist_link(link)
            
            # 记录变更
            await self._record_change(
                artifact_id=source_id,
                change_type=ChangeType.RELATIONSHIP_ADD,
                old_value=None,
                new_value=link.to_dict(),
                changed_by=created_by or "system"
            )
            
            # 清除缓存
            self._clear_cache()
            
            self.logger.info(f"创建链接成功: {source_id} -> {target_id} ({link_type.value})")
            return link_id
            
        except Exception as e:
            self.logger.error(f"创建链接失败: {str(e)}")
            raise
    
    async def get_artifact(self, artifact_id: str) -> Optional[TraceabilityArtifact]:
        """获取工件"""
        return self._artifacts.get(artifact_id)
    
    async def get_artifacts_by_type(self, artifact_type: ArtifactType) -> List[TraceabilityArtifact]:
        """按类型获取工件"""
        artifact_ids = self._type_index.get(artifact_type, set())
        return [self._artifacts[aid] for aid in artifact_ids if aid in self._artifacts]
    
    async def get_related_artifacts(
        self,
        artifact_id: str,
        link_types: List[TraceabilityType] = None,
        direction: str = "both"  # "forward", "backward", "both"
    ) -> List[Tuple[TraceabilityArtifact, TraceabilityLink]]:
        """获取相关工件"""
        try:
            related = []
            
            for link_id, link in self._links.items():
                include_link = False
                target_artifact_id = None
                
                # 检查方向和链接类型
                if direction in ["forward", "both"] and link.source_id == artifact_id:
                    if not link_types or link.type in link_types:
                        include_link = True
                        target_artifact_id = link.target_id
                
                if direction in ["backward", "both"] and link.target_id == artifact_id:
                    if not link_types or link.type in link_types:
                        include_link = True
                        target_artifact_id = link.source_id
                
                if include_link and target_artifact_id in self._artifacts:
                    related.append((self._artifacts[target_artifact_id], link))
            
            return related
            
        except Exception as e:
            self.logger.error(f"获取相关工件失败: {str(e)}")
            return []
    
    async def generate_coverage_report(
        self,
        source_type: ArtifactType,
        target_type: ArtifactType,
        link_types: List[TraceabilityType] = None
    ) -> CoverageReport:
        """生成覆盖率报告"""
        try:
            # 检查缓存
            cache_key = f"{source_type.value}_{target_type.value}_{hash(tuple(link_types or []))}"
            if cache_key in self._coverage_cache:
                return self._coverage_cache[cache_key]
            
            # 获取源工件和目标工件
            source_artifacts = await self.get_artifacts_by_type(source_type)
            target_artifacts = await self.get_artifacts_by_type(target_type)
            
            total_requirements = len(source_artifacts)
            covered_requirements = 0
            uncovered_requirements = []
            coverage_by_type = defaultdict(lambda: {"covered": 0, "total": 0})
            gaps = []
            
            # 分析覆盖情况
            for source_artifact in source_artifacts:
                is_covered = False
                
                # 检查是否有到目标类型的链接
                for link_id, link in self._links.items():
                    if link.source_id == source_artifact.id:
                        if link_types is None or link.type in link_types:
                            target_artifact = self._artifacts.get(link.target_id)
                            if target_artifact and target_artifact.type == target_type:
                                is_covered = True
                                break
                
                if is_covered:
                    covered_requirements += 1
                else:
                    uncovered_requirements.append(source_artifact.id)
                    gaps.append({
                        "artifact_id": source_artifact.id,
                        "artifact_name": source_artifact.name,
                        "gap_type": "missing_coverage",
                        "description": f"缺少到{target_type.value}的追溯链接"
                    })
                
                # 按状态统计
                status_key = source_artifact.status.value
                coverage_by_type[status_key]["total"] += 1
                if is_covered:
                    coverage_by_type[status_key]["covered"] += 1
            
            # 计算覆盖率
            coverage_percentage = (covered_requirements / total_requirements * 100) if total_requirements > 0 else 0
            
            # 生成建议
            recommendations = []
            if coverage_percentage < 80:
                recommendations.append("覆盖率低于80%，建议增加测试用例")
            if len(gaps) > 0:
                recommendations.append(f"发现{len(gaps)}个覆盖缺口，需要补充相应的追溯链接")
            
            # 创建报告
            report = CoverageReport(
                total_requirements=total_requirements,
                covered_requirements=covered_requirements,
                uncovered_requirements=uncovered_requirements,
                coverage_percentage=coverage_percentage,
                coverage_by_type=dict(coverage_by_type),
                gaps=gaps,
                recommendations=recommendations
            )
            
            # 缓存报告
            self._coverage_cache[cache_key] = report
            
            return report
            
        except Exception as e:
            self.logger.error(f"生成覆盖率报告失败: {str(e)}")
            return CoverageReport(0, 0, [], 0, {}, [], [])
    
    async def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        try:
            stats = {
                "artifacts": {
                    "total": len(self._artifacts),
                    "by_type": {},
                    "by_status": {}
                },
                "links": {
                    "total": len(self._links),
                    "by_type": {}
                },
                "coverage": {},
                "graph": {
                    "nodes": self._graph.number_of_nodes(),
                    "edges": self._graph.number_of_edges(),
                    "density": nx.density(self._graph) if self._graph.number_of_nodes() > 0 else 0
                }
            }
            
            # 按类型统计工件
            for artifact_type, artifact_ids in self._type_index.items():
                stats["artifacts"]["by_type"][artifact_type.value] = len(artifact_ids)
            
            # 按状态统计工件
            for status, artifact_ids in self._status_index.items():
                stats["artifacts"]["by_status"][status.value] = len(artifact_ids)
            
            # 按类型统计链接
            link_type_counts = defaultdict(int)
            for link in self._links.values():
                link_type_counts[link.type.value] += 1
            stats["links"]["by_type"] = dict(link_type_counts)
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取统计信息失败: {str(e)}")
            return {}
    
    async def _update_artifact_indexes(self, artifact: TraceabilityArtifact):
        """更新工件索引"""
        try:
            artifact_id = artifact.id
            
            # 类型索引
            self._type_index[artifact.type].add(artifact_id)
            
            # 状态索引
            self._status_index[artifact.status].add(artifact_id)
            
            # 标签索引
            for tag in artifact.tags:
                self._tag_index[tag].add(artifact_id)
            
            # 创建者索引
            if artifact.created_by:
                self._creator_index[artifact.created_by].add(artifact_id)
            
        except Exception as e:
            self.logger.error(f"更新工件索引失败: {str(e)}")
    
    async def _persist_artifact(self, artifact: TraceabilityArtifact):
        """持久化工件"""
        try:
            collection = await self.memory_manager.get_collection("traceability_matrix")
            if not collection:
                collection = await self.memory_manager.create_collection("traceability_matrix")
            
            # 准备数据
            metadata = {
                "type": "artifact",
                "data": json.dumps(artifact.to_dict()),
                "artifact_type": artifact.type.value,
                "status": artifact.status.value,
                "created_at": artifact.created_at.isoformat()
            }
            
            # 检查是否已存在
            existing = collection.get(ids=[artifact.id])
            if existing and existing['ids']:
                # 更新
                collection.update(
                    ids=[artifact.id],
                    metadatas=[metadata],
                    documents=[artifact.description]
                )
            else:
                # 添加
                collection.add(
                    ids=[artifact.id],
                    metadatas=[metadata],
                    documents=[artifact.description]
                )
            
        except Exception as e:
            self.logger.error(f"持久化工件失败: {str(e)}")
    
    async def _persist_link(self, link: TraceabilityLink):
        """持久化链接"""
        try:
            collection = await self.memory_manager.get_collection("traceability_matrix")
            if not collection:
                collection = await self.memory_manager.create_collection("traceability_matrix")
            
            # 准备数据
            metadata = {
                "type": "link",
                "data": json.dumps(link.to_dict()),
                "link_type": link.type.value,
                "source_id": link.source_id,
                "target_id": link.target_id,
                "created_at": link.created_at.isoformat()
            }
            
            # 添加
            collection.add(
                ids=[link.id],
                metadatas=[metadata],
                documents=[f"{link.source_id} -> {link.target_id}"]
            )
            
        except Exception as e:
            self.logger.error(f"持久化链接失败: {str(e)}")
    
    async def _record_change(
        self,
        artifact_id: str,
        change_type: ChangeType,
        old_value: Any,
        new_value: Any,
        changed_by: str,
        reason: str = None
    ):
        """记录变更"""
        try:
            change_record = ChangeRecord(
                id=f"change_{uuid.uuid4().hex[:8]}",
                artifact_id=artifact_id,
                change_type=change_type,
                old_value=old_value,
                new_value=new_value,
                changed_by=changed_by,
                reason=reason
            )
            
            self._change_history.append(change_record)
            
            # 限制历史记录数量
            if len(self._change_history) > 1000:
                self._change_history = self._change_history[-800:]
            
        except Exception as e:
            self.logger.error(f"记录变更失败: {str(e)}")
    
    def _clear_cache(self):
        """清除缓存"""
        self._coverage_cache.clear()
        self._path_cache.clear()


class StateManager:
    """状态管理器"""
    
    def __init__(self):
        self.logger = get_agent_logger("StateManager")
        self._states: Dict[str, Dict[str, Any]] = {}
        self._state_history: Dict[str, List[Dict[str, Any]]] = defaultdict(list)
        self._watchers: Dict[str, List[callable]] = defaultdict(list)
    
    async def set_state(
        self,
        entity_id: str,
        state_key: str,
        state_value: Any,
        metadata: Dict[str, Any] = None
    ):
        """设置状态"""
        try:
            if entity_id not in self._states:
                self._states[entity_id] = {}
            
            old_value = self._states[entity_id].get(state_key)
            self._states[entity_id][state_key] = state_value
            
            # 记录状态历史
            history_entry = {
                "timestamp": datetime.now().isoformat(),
                "key": state_key,
                "old_value": old_value,
                "new_value": state_value,
                "metadata": metadata or {}
            }
            
            self._state_history[entity_id].append(history_entry)
            
            # 限制历史记录数量
            if len(self._state_history[entity_id]) > 100:
                self._state_history[entity_id] = self._state_history[entity_id][-80:]
            
            # 通知观察者
            await self._notify_watchers(entity_id, state_key, old_value, state_value)
            
            self.logger.debug(f"设置状态: {entity_id}.{state_key} = {state_value}")
            
        except Exception as e:
            self.logger.error(f"设置状态失败: {str(e)}")
    
    async def get_state(self, entity_id: str, state_key: str = None) -> Any:
        """获取状态"""
        try:
            if entity_id not in self._states:
                return None
            
            if state_key is None:
                return self._states[entity_id]
            else:
                return self._states[entity_id].get(state_key)
                
        except Exception as e:
            self.logger.error(f"获取状态失败: {str(e)}")
            return None
    
    async def _notify_watchers(
        self,
        entity_id: str,
        state_key: str,
        old_value: Any,
        new_value: Any
    ):
        """通知观察者"""
        try:
            watchers = self._watchers.get(entity_id, [])
            for watcher in watchers:
                try:
                    if asyncio.iscoroutinefunction(watcher):
                        await watcher(entity_id, state_key, old_value, new_value)
                    else:
                        watcher(entity_id, state_key, old_value, new_value)
                except Exception as e:
                    self.logger.error(f"通知观察者失败: {str(e)}")
        except Exception as e:
            self.logger.error(f"通知观察者过程失败: {str(e)}")


# 全局实例
_traceability_matrix: Optional[TraceabilityMatrix] = None
_state_manager: Optional[StateManager] = None


async def get_traceability_manager() -> TraceabilityMatrix:
    """获取追溯矩阵管理器实例"""
    global _traceability_matrix
    if _traceability_matrix is None:
        _traceability_matrix = TraceabilityMatrix()
        await _traceability_matrix.initialize()
    return _traceability_matrix


async def get_state_manager() -> StateManager:
    """获取状态管理器实例"""
    global _state_manager
    if _state_manager is None:
        _state_manager = StateManager()
    return _state_manager


async def cleanup_traceability():
    """清理追溯资源"""
    global _traceability_matrix, _state_manager
    _traceability_matrix = None
    _state_manager = None


# Agent追溯混入类
class AgentTraceabilityMixin:
    """Agent追溯混入类"""
    
    def __init__(self):
        self.traceability_manager: Optional[TraceabilityMatrix] = None
        self.state_manager: Optional[StateManager] = None
        self._traceability_logger = get_agent_logger(f"Traceability_{self.__class__.__name__}")
    
    async def setup_traceability(self):
        """设置追溯系统"""
        self.traceability_manager = await get_traceability_manager()
        self.state_manager = await get_state_manager()
        self._traceability_logger.info("追溯系统设置完成")
    
    async def create_artifact(
        self,
        artifact_type: ArtifactType,
        name: str,
        description: str,
        content: Dict[str, Any],
        tags: List[str] = None
    ) -> str:
        """创建工件"""
        if not self.traceability_manager:
            await self.setup_traceability()
        
        return await self.traceability_manager.create_artifact(
            artifact_type=artifact_type,
            name=name,
            description=description,
            content=content,
            created_by=getattr(self, 'agent_id', 'unknown'),
            tags=tags
        )
    
    async def create_link(
        self,
        source_id: str,
        target_id: str,
        link_type: TraceabilityType,
        strength: float = 1.0
    ) -> str:
        """创建追溯链接"""
        if not self.traceability_manager:
            await self.setup_traceability()
        
        return await self.traceability_manager.create_link(
            source_id=source_id,
            target_id=target_id,
            link_type=link_type,
            strength=strength,
            created_by=getattr(self, 'agent_id', 'unknown')
        )
    
    async def get_related_artifacts(
        self,
        artifact_id: str,
        link_types: List[TraceabilityType] = None
    ) -> List[Tuple[TraceabilityArtifact, TraceabilityLink]]:
        """获取相关工件"""
        if not self.traceability_manager:
            await self.setup_traceability()
        
        return await self.traceability_manager.get_related_artifacts(
            artifact_id=artifact_id,
            link_types=link_types
        )
    
    async def set_state(self, state_key: str, state_value: Any):
        """设置状态"""
        if not self.state_manager:
            await self.setup_traceability()
        
        entity_id = getattr(self, 'agent_id', 'unknown')
        await self.state_manager.set_state(entity_id, state_key, state_value)
    
    async def get_state(self, state_key: str = None) -> Any:
        """获取状态"""
        if not self.state_manager:
            await self.setup_traceability()
        
        entity_id = getattr(self, 'agent_id', 'unknown')
        return await self.state_manager.get_state(entity_id, state_key)
    
    async def generate_coverage_report(
        self,
        source_type: ArtifactType,
        target_type: ArtifactType
    ) -> CoverageReport:
        """生成覆盖率报告"""
        if not self.traceability_manager:
            await self.setup_traceability()
        
        return await self.traceability_manager.generate_coverage_report(
            source_type=source_type,
            target_type=target_type
        )
