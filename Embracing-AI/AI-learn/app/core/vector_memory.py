"""
向量记忆系统和知识共享机制
"""

import asyncio
import uuid
import json
import numpy as np
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import hashlib

from core.logger import get_agent_logger
from core.memory import get_memory_manager
from models.schemas import Thinking<PERSON><PERSON><PERSON>, ReflectionResult
from models.enums import AgentType


class MemoryType(Enum):
    """记忆类型枚举"""
    REQUIREMENT = "requirement"
    TESTCASE = "testcase"
    REVIEW = "review"
    COLLABORATION = "collaboration"
    EXPERIENCE = "experience"
    KNOWLEDGE = "knowledge"
    PATTERN = "pattern"
    FEEDBACK = "feedback"


class MemoryScope(Enum):
    """记忆范围枚举"""
    GLOBAL = "global"        # 全局共享
    PROJECT = "project"      # 项目级别
    SESSION = "session"      # 会话级别
    AGENT = "agent"          # Agent私有
    TEAM = "team"           # 团队共享


@dataclass
class MemoryEntry:
    """记忆条目数据结构"""
    id: str
    type: MemoryType
    scope: MemoryScope
    content: Dict[str, Any]
    metadata: Dict[str, Any]
    embedding: Optional[List[float]] = None
    tags: List[str] = None
    source_agent: Optional[str] = None
    session_id: Optional[str] = None
    project_id: Optional[str] = None
    created_at: datetime = None
    updated_at: datetime = None
    access_count: int = 0
    relevance_score: float = 0.0
    expires_at: Optional[datetime] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = self.created_at
        if self.tags is None:
            self.tags = []
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "type": self.type.value,
            "scope": self.scope.value,
            "content": self.content,
            "metadata": self.metadata,
            "embedding": self.embedding,
            "tags": self.tags,
            "source_agent": self.source_agent,
            "session_id": self.session_id,
            "project_id": self.project_id,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "access_count": self.access_count,
            "relevance_score": self.relevance_score,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MemoryEntry':
        """从字典创建记忆条目"""
        return cls(
            id=data["id"],
            type=MemoryType(data["type"]),
            scope=MemoryScope(data["scope"]),
            content=data["content"],
            metadata=data["metadata"],
            embedding=data.get("embedding"),
            tags=data.get("tags", []),
            source_agent=data.get("source_agent"),
            session_id=data.get("session_id"),
            project_id=data.get("project_id"),
            created_at=datetime.fromisoformat(data["created_at"]),
            updated_at=datetime.fromisoformat(data["updated_at"]),
            access_count=data.get("access_count", 0),
            relevance_score=data.get("relevance_score", 0.0),
            expires_at=datetime.fromisoformat(data["expires_at"]) if data.get("expires_at") else None
        )


@dataclass
class SearchQuery:
    """搜索查询数据结构"""
    text: str
    memory_types: List[MemoryType] = None
    scopes: List[MemoryScope] = None
    tags: List[str] = None
    agent_id: Optional[str] = None
    session_id: Optional[str] = None
    project_id: Optional[str] = None
    time_range: Optional[Tuple[datetime, datetime]] = None
    limit: int = 10
    similarity_threshold: float = 0.7
    include_expired: bool = False
    
    def __post_init__(self):
        if self.memory_types is None:
            self.memory_types = []
        if self.scopes is None:
            self.scopes = []
        if self.tags is None:
            self.tags = []


class VectorMemoryManager:
    """向量记忆管理器"""
    
    def __init__(self):
        self.logger = get_agent_logger("VectorMemoryManager")
        self.memory_manager = None
        self._embedding_cache: Dict[str, List[float]] = {}
        self._memory_cache: Dict[str, MemoryEntry] = {}
        self._access_stats: Dict[str, Dict[str, int]] = {}
        
        # 记忆索引
        self._type_index: Dict[MemoryType, List[str]] = {}
        self._scope_index: Dict[MemoryScope, List[str]] = {}
        self._tag_index: Dict[str, List[str]] = {}
        self._agent_index: Dict[str, List[str]] = {}
        self._session_index: Dict[str, List[str]] = {}
        
        # 初始化索引
        for memory_type in MemoryType:
            self._type_index[memory_type] = []
        for scope in MemoryScope:
            self._scope_index[scope] = []
    
    async def initialize(self):
        """初始化向量记忆管理器"""
        self.memory_manager = await get_memory_manager()
        await self._load_existing_memories()
        self.logger.info("向量记忆管理器初始化完成")
    
    async def _load_existing_memories(self):
        """加载现有记忆"""
        try:
            # 从ChromaDB加载现有记忆
            collection = await self.memory_manager.get_collection("vector_memories")
            if collection:
                results = collection.get()
                if results and results['ids']:
                    for i, memory_id in enumerate(results['ids']):
                        metadata = results['metadatas'][i] if results['metadatas'] else {}
                        embedding = results['embeddings'][i] if results['embeddings'] else None
                        
                        # 重建记忆条目
                        if 'memory_data' in metadata:
                            memory_data = json.loads(metadata['memory_data'])
                            memory_entry = MemoryEntry.from_dict(memory_data)
                            memory_entry.embedding = embedding
                            
                            # 添加到缓存和索引
                            self._memory_cache[memory_id] = memory_entry
                            await self._update_indexes(memory_entry)
            
            self.logger.info(f"加载了 {len(self._memory_cache)} 条现有记忆")
            
        except Exception as e:
            self.logger.error(f"加载现有记忆失败: {str(e)}")
    
    async def store_memory(
        self,
        content: Dict[str, Any],
        memory_type: MemoryType,
        scope: MemoryScope = MemoryScope.SESSION,
        tags: List[str] = None,
        source_agent: str = None,
        session_id: str = None,
        project_id: str = None,
        metadata: Dict[str, Any] = None,
        expires_at: datetime = None
    ) -> str:
        """存储记忆"""
        try:
            # 生成记忆ID
            memory_id = f"mem_{uuid.uuid4().hex[:12]}"
            
            # 创建记忆条目
            memory_entry = MemoryEntry(
                id=memory_id,
                type=memory_type,
                scope=scope,
                content=content,
                metadata=metadata or {},
                tags=tags or [],
                source_agent=source_agent,
                session_id=session_id,
                project_id=project_id,
                expires_at=expires_at
            )
            
            # 生成文本表示用于向量化
            text_content = await self._extract_text_content(content)
            
            # 生成向量嵌入
            embedding = await self._generate_embedding(text_content)
            memory_entry.embedding = embedding
            
            # 存储到ChromaDB
            await self._store_to_chromadb(memory_entry, text_content)
            
            # 更新缓存和索引
            self._memory_cache[memory_id] = memory_entry
            await self._update_indexes(memory_entry)
            
            self.logger.info(f"存储记忆成功: {memory_id} (类型: {memory_type.value})")
            return memory_id
            
        except Exception as e:
            self.logger.error(f"存储记忆失败: {str(e)}")
            raise
    
    async def search_memories(self, query: SearchQuery) -> List[MemoryEntry]:
        """搜索记忆"""
        try:
            # 生成查询向量
            query_embedding = await self._generate_embedding(query.text)
            
            # 从ChromaDB搜索
            results = await self._search_from_chromadb(query, query_embedding)
            
            # 过滤和排序结果
            filtered_results = await self._filter_search_results(results, query)
            
            # 更新访问统计
            for memory_entry in filtered_results:
                memory_entry.access_count += 1
                memory_entry.updated_at = datetime.now()
            
            self.logger.info(f"搜索记忆完成: 查询='{query.text}', 结果数={len(filtered_results)}")
            return filtered_results
            
        except Exception as e:
            self.logger.error(f"搜索记忆失败: {str(e)}")
            return []
    
    async def get_memory(self, memory_id: str) -> Optional[MemoryEntry]:
        """获取特定记忆"""
        try:
            # 先从缓存查找
            if memory_id in self._memory_cache:
                memory_entry = self._memory_cache[memory_id]
                memory_entry.access_count += 1
                memory_entry.updated_at = datetime.now()
                return memory_entry
            
            # 从ChromaDB查找
            collection = await self.memory_manager.get_collection("vector_memories")
            if collection:
                results = collection.get(ids=[memory_id])
                if results and results['ids']:
                    metadata = results['metadatas'][0]
                    embedding = results['embeddings'][0] if results['embeddings'] else None
                    
                    if 'memory_data' in metadata:
                        memory_data = json.loads(metadata['memory_data'])
                        memory_entry = MemoryEntry.from_dict(memory_data)
                        memory_entry.embedding = embedding
                        
                        # 添加到缓存
                        self._memory_cache[memory_id] = memory_entry
                        return memory_entry
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取记忆失败: {str(e)}")
            return None
    
    async def update_memory(
        self,
        memory_id: str,
        content: Dict[str, Any] = None,
        tags: List[str] = None,
        metadata: Dict[str, Any] = None
    ) -> bool:
        """更新记忆"""
        try:
            memory_entry = await self.get_memory(memory_id)
            if not memory_entry:
                return False
            
            # 更新内容
            if content is not None:
                memory_entry.content = content
                # 重新生成嵌入
                text_content = await self._extract_text_content(content)
                memory_entry.embedding = await self._generate_embedding(text_content)
            
            if tags is not None:
                memory_entry.tags = tags
            
            if metadata is not None:
                memory_entry.metadata.update(metadata)
            
            memory_entry.updated_at = datetime.now()
            
            # 更新ChromaDB
            await self._update_in_chromadb(memory_entry)
            
            # 更新缓存
            self._memory_cache[memory_id] = memory_entry
            
            self.logger.info(f"更新记忆成功: {memory_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"更新记忆失败: {str(e)}")
            return False
    
    async def delete_memory(self, memory_id: str) -> bool:
        """删除记忆"""
        try:
            # 从ChromaDB删除
            collection = await self.memory_manager.get_collection("vector_memories")
            if collection:
                collection.delete(ids=[memory_id])
            
            # 从缓存删除
            if memory_id in self._memory_cache:
                memory_entry = self._memory_cache[memory_id]
                await self._remove_from_indexes(memory_entry)
                del self._memory_cache[memory_id]
            
            self.logger.info(f"删除记忆成功: {memory_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"删除记忆失败: {str(e)}")
            return False
    
    async def get_memories_by_type(
        self,
        memory_type: MemoryType,
        limit: int = 50
    ) -> List[MemoryEntry]:
        """按类型获取记忆"""
        memory_ids = self._type_index.get(memory_type, [])[:limit]
        memories = []
        
        for memory_id in memory_ids:
            memory = await self.get_memory(memory_id)
            if memory:
                memories.append(memory)
        
        return memories
    
    async def get_memories_by_agent(
        self,
        agent_id: str,
        limit: int = 50
    ) -> List[MemoryEntry]:
        """按Agent获取记忆"""
        memory_ids = self._agent_index.get(agent_id, [])[:limit]
        memories = []
        
        for memory_id in memory_ids:
            memory = await self.get_memory(memory_id)
            if memory:
                memories.append(memory)
        
        return memories
    
    async def get_memories_by_session(
        self,
        session_id: str,
        limit: int = 50
    ) -> List[MemoryEntry]:
        """按会话获取记忆"""
        memory_ids = self._session_index.get(session_id, [])[:limit]
        memories = []
        
        for memory_id in memory_ids:
            memory = await self.get_memory(memory_id)
            if memory:
                memories.append(memory)
        
        return memories
    
    async def get_related_memories(
        self,
        memory_id: str,
        limit: int = 10,
        similarity_threshold: float = 0.7
    ) -> List[MemoryEntry]:
        """获取相关记忆"""
        try:
            base_memory = await self.get_memory(memory_id)
            if not base_memory or not base_memory.embedding:
                return []
            
            # 搜索相似记忆
            query = SearchQuery(
                text="",  # 使用嵌入向量搜索
                limit=limit + 1,  # +1 因为会包含自己
                similarity_threshold=similarity_threshold
            )
            
            results = await self._search_by_embedding(base_memory.embedding, query)
            
            # 排除自己
            related_memories = [mem for mem in results if mem.id != memory_id]
            
            return related_memories[:limit]
            
        except Exception as e:
            self.logger.error(f"获取相关记忆失败: {str(e)}")
            return []
    
    async def cleanup_expired_memories(self) -> int:
        """清理过期记忆"""
        try:
            current_time = datetime.now()
            expired_count = 0
            
            expired_ids = []
            for memory_id, memory_entry in self._memory_cache.items():
                if memory_entry.expires_at and memory_entry.expires_at < current_time:
                    expired_ids.append(memory_id)
            
            for memory_id in expired_ids:
                await self.delete_memory(memory_id)
                expired_count += 1
            
            self.logger.info(f"清理过期记忆完成: {expired_count} 条")
            return expired_count
            
        except Exception as e:
            self.logger.error(f"清理过期记忆失败: {str(e)}")
            return 0
    
    async def get_memory_statistics(self) -> Dict[str, Any]:
        """获取记忆统计信息"""
        try:
            stats = {
                "total_memories": len(self._memory_cache),
                "by_type": {},
                "by_scope": {},
                "by_agent": {},
                "by_session": {},
                "cache_size": len(self._memory_cache),
                "embedding_cache_size": len(self._embedding_cache)
            }
            
            # 按类型统计
            for memory_type, memory_ids in self._type_index.items():
                stats["by_type"][memory_type.value] = len(memory_ids)
            
            # 按范围统计
            for scope, memory_ids in self._scope_index.items():
                stats["by_scope"][scope.value] = len(memory_ids)
            
            # 按Agent统计
            for agent_id, memory_ids in self._agent_index.items():
                stats["by_agent"][agent_id] = len(memory_ids)
            
            # 按会话统计
            for session_id, memory_ids in self._session_index.items():
                stats["by_session"][session_id] = len(memory_ids)
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取记忆统计失败: {str(e)}")
            return {}
    
    async def _extract_text_content(self, content: Dict[str, Any]) -> str:
        """提取文本内容用于向量化"""
        try:
            text_parts = []
            
            def extract_text_recursive(obj, prefix=""):
                if isinstance(obj, dict):
                    for key, value in obj.items():
                        if isinstance(value, str):
                            text_parts.append(f"{prefix}{key}: {value}")
                        elif isinstance(value, (dict, list)):
                            extract_text_recursive(value, f"{prefix}{key}.")
                        else:
                            text_parts.append(f"{prefix}{key}: {str(value)}")
                elif isinstance(obj, list):
                    for i, item in enumerate(obj):
                        extract_text_recursive(item, f"{prefix}[{i}].")
                else:
                    text_parts.append(f"{prefix}: {str(obj)}")
            
            extract_text_recursive(content)
            return " ".join(text_parts)
            
        except Exception as e:
            self.logger.error(f"提取文本内容失败: {str(e)}")
            return str(content)
    
    async def _generate_embedding(self, text: str) -> List[float]:
        """生成文本嵌入向量"""
        try:
            # 检查缓存
            text_hash = hashlib.md5(text.encode()).hexdigest()
            if text_hash in self._embedding_cache:
                return self._embedding_cache[text_hash]
            
            # 使用简单的TF-IDF向量化（实际应用中应该使用更好的嵌入模型）
            # 这里为了演示，使用简化的向量化方法
            words = text.lower().split()
            word_freq = {}
            for word in words:
                word_freq[word] = word_freq.get(word, 0) + 1
            
            # 生成固定长度的向量（实际应该使用预训练模型）
            vector_size = 384  # 模拟sentence-transformers的输出维度
            embedding = [0.0] * vector_size
            
            # 简单的哈希映射到向量空间
            for word, freq in word_freq.items():
                word_hash = hash(word) % vector_size
                embedding[word_hash] += freq / len(words)
            
            # 归一化
            norm = sum(x * x for x in embedding) ** 0.5
            if norm > 0:
                embedding = [x / norm for x in embedding]
            
            # 缓存结果
            self._embedding_cache[text_hash] = embedding
            
            return embedding
            
        except Exception as e:
            self.logger.error(f"生成嵌入向量失败: {str(e)}")
            return [0.0] * 384  # 返回零向量
    
    async def _store_to_chromadb(self, memory_entry: MemoryEntry, text_content: str):
        """存储到ChromaDB"""
        try:
            collection = await self.memory_manager.get_collection("vector_memories")
            if not collection:
                collection = await self.memory_manager.create_collection("vector_memories")
            
            # 准备元数据
            metadata = {
                "memory_data": json.dumps(memory_entry.to_dict()),
                "type": memory_entry.type.value,
                "scope": memory_entry.scope.value,
                "source_agent": memory_entry.source_agent or "",
                "session_id": memory_entry.session_id or "",
                "project_id": memory_entry.project_id or "",
                "created_at": memory_entry.created_at.isoformat(),
                "tags": ",".join(memory_entry.tags)
            }
            
            # 添加到集合
            collection.add(
                ids=[memory_entry.id],
                embeddings=[memory_entry.embedding],
                documents=[text_content],
                metadatas=[metadata]
            )
            
        except Exception as e:
            self.logger.error(f"存储到ChromaDB失败: {str(e)}")
            raise
    
    async def _search_from_chromadb(
        self,
        query: SearchQuery,
        query_embedding: List[float]
    ) -> List[MemoryEntry]:
        """从ChromaDB搜索"""
        try:
            collection = await self.memory_manager.get_collection("vector_memories")
            if not collection:
                return []
            
            # 构建过滤条件
            where_conditions = {}
            if query.memory_types:
                where_conditions["type"] = {"$in": [t.value for t in query.memory_types]}
            if query.scopes:
                where_conditions["scope"] = {"$in": [s.value for s in query.scopes]}
            if query.agent_id:
                where_conditions["source_agent"] = query.agent_id
            if query.session_id:
                where_conditions["session_id"] = query.session_id
            if query.project_id:
                where_conditions["project_id"] = query.project_id
            
            # 执行搜索
            results = collection.query(
                query_embeddings=[query_embedding],
                n_results=query.limit,
                where=where_conditions if where_conditions else None
            )
            
            # 转换结果
            memories = []
            if results and results['ids']:
                for i, memory_id in enumerate(results['ids'][0]):
                    metadata = results['metadatas'][0][i]
                    distance = results['distances'][0][i] if results['distances'] else 0
                    
                    # 计算相似度分数
                    similarity = 1 - distance  # 距离越小，相似度越高
                    
                    if similarity >= query.similarity_threshold:
                        if 'memory_data' in metadata:
                            memory_data = json.loads(metadata['memory_data'])
                            memory_entry = MemoryEntry.from_dict(memory_data)
                            memory_entry.relevance_score = similarity
                            memories.append(memory_entry)
            
            return memories
            
        except Exception as e:
            self.logger.error(f"从ChromaDB搜索失败: {str(e)}")
            return []
    
    async def _search_by_embedding(
        self,
        embedding: List[float],
        query: SearchQuery
    ) -> List[MemoryEntry]:
        """通过嵌入向量搜索"""
        try:
            collection = await self.memory_manager.get_collection("vector_memories")
            if not collection:
                return []
            
            results = collection.query(
                query_embeddings=[embedding],
                n_results=query.limit
            )
            
            memories = []
            if results and results['ids']:
                for i, memory_id in enumerate(results['ids'][0]):
                    metadata = results['metadatas'][0][i]
                    distance = results['distances'][0][i] if results['distances'] else 0
                    similarity = 1 - distance
                    
                    if similarity >= query.similarity_threshold:
                        if 'memory_data' in metadata:
                            memory_data = json.loads(metadata['memory_data'])
                            memory_entry = MemoryEntry.from_dict(memory_data)
                            memory_entry.relevance_score = similarity
                            memories.append(memory_entry)
            
            return memories
            
        except Exception as e:
            self.logger.error(f"通过嵌入向量搜索失败: {str(e)}")
            return []
    
    async def _filter_search_results(
        self,
        results: List[MemoryEntry],
        query: SearchQuery
    ) -> List[MemoryEntry]:
        """过滤搜索结果"""
        filtered_results = []
        current_time = datetime.now()
        
        for memory_entry in results:
            # 检查是否过期
            if not query.include_expired and memory_entry.expires_at:
                if memory_entry.expires_at < current_time:
                    continue
            
            # 检查标签
            if query.tags:
                if not any(tag in memory_entry.tags for tag in query.tags):
                    continue
            
            # 检查时间范围
            if query.time_range:
                start_time, end_time = query.time_range
                if not (start_time <= memory_entry.created_at <= end_time):
                    continue
            
            filtered_results.append(memory_entry)
        
        # 按相关性分数排序
        filtered_results.sort(key=lambda x: x.relevance_score, reverse=True)
        
        return filtered_results
    
    async def _update_in_chromadb(self, memory_entry: MemoryEntry):
        """更新ChromaDB中的记忆"""
        try:
            # 先删除旧记录
            collection = await self.memory_manager.get_collection("vector_memories")
            if collection:
                collection.delete(ids=[memory_entry.id])
            
            # 重新添加
            text_content = await self._extract_text_content(memory_entry.content)
            await self._store_to_chromadb(memory_entry, text_content)
            
        except Exception as e:
            self.logger.error(f"更新ChromaDB失败: {str(e)}")
            raise
    
    async def _update_indexes(self, memory_entry: MemoryEntry):
        """更新索引"""
        try:
            memory_id = memory_entry.id
            
            # 更新类型索引
            if memory_id not in self._type_index[memory_entry.type]:
                self._type_index[memory_entry.type].append(memory_id)
            
            # 更新范围索引
            if memory_id not in self._scope_index[memory_entry.scope]:
                self._scope_index[memory_entry.scope].append(memory_id)
            
            # 更新标签索引
            for tag in memory_entry.tags:
                if tag not in self._tag_index:
                    self._tag_index[tag] = []
                if memory_id not in self._tag_index[tag]:
                    self._tag_index[tag].append(memory_id)
            
            # 更新Agent索引
            if memory_entry.source_agent:
                if memory_entry.source_agent not in self._agent_index:
                    self._agent_index[memory_entry.source_agent] = []
                if memory_id not in self._agent_index[memory_entry.source_agent]:
                    self._agent_index[memory_entry.source_agent].append(memory_id)
            
            # 更新会话索引
            if memory_entry.session_id:
                if memory_entry.session_id not in self._session_index:
                    self._session_index[memory_entry.session_id] = []
                if memory_id not in self._session_index[memory_entry.session_id]:
                    self._session_index[memory_entry.session_id].append(memory_id)
            
        except Exception as e:
            self.logger.error(f"更新索引失败: {str(e)}")
    
    async def _remove_from_indexes(self, memory_entry: MemoryEntry):
        """从索引中移除"""
        try:
            memory_id = memory_entry.id
            
            # 从类型索引移除
            if memory_id in self._type_index[memory_entry.type]:
                self._type_index[memory_entry.type].remove(memory_id)
            
            # 从范围索引移除
            if memory_id in self._scope_index[memory_entry.scope]:
                self._scope_index[memory_entry.scope].remove(memory_id)
            
            # 从标签索引移除
            for tag in memory_entry.tags:
                if tag in self._tag_index and memory_id in self._tag_index[tag]:
                    self._tag_index[tag].remove(memory_id)
                    if not self._tag_index[tag]:  # 如果标签下没有记忆了，删除标签
                        del self._tag_index[tag]
            
            # 从Agent索引移除
            if memory_entry.source_agent and memory_entry.source_agent in self._agent_index:
                if memory_id in self._agent_index[memory_entry.source_agent]:
                    self._agent_index[memory_entry.source_agent].remove(memory_id)
                    if not self._agent_index[memory_entry.source_agent]:
                        del self._agent_index[memory_entry.source_agent]
            
            # 从会话索引移除
            if memory_entry.session_id and memory_entry.session_id in self._session_index:
                if memory_id in self._session_index[memory_entry.session_id]:
                    self._session_index[memory_entry.session_id].remove(memory_id)
                    if not self._session_index[memory_entry.session_id]:
                        del self._session_index[memory_entry.session_id]
            
        except Exception as e:
            self.logger.error(f"从索引移除失败: {str(e)}")


class KnowledgeGraph:
    """知识图谱"""
    
    def __init__(self, vector_memory: VectorMemoryManager):
        self.logger = get_agent_logger("KnowledgeGraph")
        self.vector_memory = vector_memory
        self._relationships: Dict[str, List[Dict[str, Any]]] = {}
        self._entity_types: Dict[str, str] = {}
    
    async def add_relationship(
        self,
        source_memory_id: str,
        target_memory_id: str,
        relationship_type: str,
        strength: float = 1.0,
        metadata: Dict[str, Any] = None
    ):
        """添加记忆间的关系"""
        try:
            if source_memory_id not in self._relationships:
                self._relationships[source_memory_id] = []
            
            relationship = {
                "target": target_memory_id,
                "type": relationship_type,
                "strength": strength,
                "metadata": metadata or {},
                "created_at": datetime.now().isoformat()
            }
            
            self._relationships[source_memory_id].append(relationship)
            
            self.logger.info(f"添加关系: {source_memory_id} -> {target_memory_id} ({relationship_type})")
            
        except Exception as e:
            self.logger.error(f"添加关系失败: {str(e)}")
    
    async def get_related_memories(
        self,
        memory_id: str,
        relationship_types: List[str] = None,
        max_depth: int = 2
    ) -> List[MemoryEntry]:
        """获取相关记忆（通过关系图）"""
        try:
            related_ids = set()
            
            def traverse_relationships(current_id: str, depth: int):
                if depth > max_depth or current_id in related_ids:
                    return
                
                if current_id in self._relationships:
                    for rel in self._relationships[current_id]:
                        if not relationship_types or rel["type"] in relationship_types:
                            target_id = rel["target"]
                            related_ids.add(target_id)
                            if depth < max_depth:
                                traverse_relationships(target_id, depth + 1)
            
            traverse_relationships(memory_id, 0)
            
            # 获取相关记忆
            related_memories = []
            for related_id in related_ids:
                memory = await self.vector_memory.get_memory(related_id)
                if memory:
                    related_memories.append(memory)
            
            return related_memories
            
        except Exception as e:
            self.logger.error(f"获取相关记忆失败: {str(e)}")
            return []
    
    async def find_patterns(self, memory_type: MemoryType) -> List[Dict[str, Any]]:
        """发现模式"""
        try:
            memories = await self.vector_memory.get_memories_by_type(memory_type)
            patterns = []
            
            # 简单的模式发现：查找频繁出现的标签组合
            tag_combinations = {}
            for memory in memories:
                if len(memory.tags) >= 2:
                    for i in range(len(memory.tags)):
                        for j in range(i + 1, len(memory.tags)):
                            combo = tuple(sorted([memory.tags[i], memory.tags[j]]))
                            tag_combinations[combo] = tag_combinations.get(combo, 0) + 1
            
            # 找出频繁模式
            min_frequency = max(2, len(memories) // 10)  # 至少出现2次或10%的记忆中
            for combo, frequency in tag_combinations.items():
                if frequency >= min_frequency:
                    patterns.append({
                        "type": "tag_combination",
                        "pattern": combo,
                        "frequency": frequency,
                        "support": frequency / len(memories)
                    })
            
            return patterns
            
        except Exception as e:
            self.logger.error(f"发现模式失败: {str(e)}")
            return []


class SharedKnowledgeBase:
    """共享知识库"""
    
    def __init__(self, vector_memory: VectorMemoryManager):
        self.logger = get_agent_logger("SharedKnowledgeBase")
        self.vector_memory = vector_memory
        self.knowledge_graph = KnowledgeGraph(vector_memory)
    
    async def store_experience(
        self,
        agent_id: str,
        session_id: str,
        experience_type: str,
        content: Dict[str, Any],
        tags: List[str] = None,
        project_id: str = None
    ) -> str:
        """存储经验"""
        try:
            experience_content = {
                "type": experience_type,
                "agent_id": agent_id,
                "content": content,
                "timestamp": datetime.now().isoformat()
            }
            
            memory_id = await self.vector_memory.store_memory(
                content=experience_content,
                memory_type=MemoryType.EXPERIENCE,
                scope=MemoryScope.GLOBAL,
                tags=tags or [],
                source_agent=agent_id,
                session_id=session_id,
                project_id=project_id,
                metadata={"experience_type": experience_type}
            )
            
            self.logger.info(f"存储经验成功: {memory_id} (类型: {experience_type})")
            return memory_id
            
        except Exception as e:
            self.logger.error(f"存储经验失败: {str(e)}")
            raise
    
    async def get_relevant_experiences(
        self,
        query: str,
        agent_type: AgentType = None,
        experience_type: str = None,
        limit: int = 5
    ) -> List[MemoryEntry]:
        """获取相关经验"""
        try:
            search_query = SearchQuery(
                text=query,
                memory_types=[MemoryType.EXPERIENCE],
                scopes=[MemoryScope.GLOBAL, MemoryScope.PROJECT],
                limit=limit,
                similarity_threshold=0.6
            )
            
            # 如果指定了Agent类型，添加到标签过滤
            if agent_type:
                search_query.tags = [agent_type.value]
            
            experiences = await self.vector_memory.search_memories(search_query)
            
            # 进一步过滤经验类型
            if experience_type:
                experiences = [
                    exp for exp in experiences 
                    if exp.metadata.get("experience_type") == experience_type
                ]
            
            return experiences
            
        except Exception as e:
            self.logger.error(f"获取相关经验失败: {str(e)}")
            return []
    
    async def store_best_practice(
        self,
        title: str,
        description: str,
        context: Dict[str, Any],
        steps: List[str],
        tags: List[str] = None,
        source_agent: str = None
    ) -> str:
        """存储最佳实践"""
        try:
            practice_content = {
                "title": title,
                "description": description,
                "context": context,
                "steps": steps,
                "created_by": source_agent,
                "usage_count": 0
            }
            
            memory_id = await self.vector_memory.store_memory(
                content=practice_content,
                memory_type=MemoryType.KNOWLEDGE,
                scope=MemoryScope.GLOBAL,
                tags=tags or [],
                source_agent=source_agent,
                metadata={"knowledge_type": "best_practice"}
            )
            
            self.logger.info(f"存储最佳实践成功: {memory_id}")
            return memory_id
            
        except Exception as e:
            self.logger.error(f"存储最佳实践失败: {str(e)}")
            raise
    
    async def get_best_practices(
        self,
        query: str,
        limit: int = 10
    ) -> List[MemoryEntry]:
        """获取最佳实践"""
        try:
            search_query = SearchQuery(
                text=query,
                memory_types=[MemoryType.KNOWLEDGE],
                scopes=[MemoryScope.GLOBAL],
                limit=limit,
                similarity_threshold=0.5
            )
            
            practices = await self.vector_memory.search_memories(search_query)
            
            # 过滤最佳实践
            practices = [
                p for p in practices 
                if p.metadata.get("knowledge_type") == "best_practice"
            ]
            
            return practices
            
        except Exception as e:
            self.logger.error(f"获取最佳实践失败: {str(e)}")
            return []
    
    async def learn_from_feedback(
        self,
        agent_id: str,
        task_type: str,
        feedback: Dict[str, Any],
        session_id: str = None
    ):
        """从反馈中学习"""
        try:
            # 分析反馈内容
            feedback_analysis = await self._analyze_feedback(feedback)
            
            # 存储学习内容
            learning_content = {
                "agent_id": agent_id,
                "task_type": task_type,
                "original_feedback": feedback,
                "analysis": feedback_analysis,
                "learning_points": feedback_analysis.get("learning_points", []),
                "improvement_suggestions": feedback_analysis.get("improvements", [])
            }
            
            memory_id = await self.vector_memory.store_memory(
                content=learning_content,
                memory_type=MemoryType.FEEDBACK,
                scope=MemoryScope.AGENT,
                tags=[task_type, "learning", "feedback"],
                source_agent=agent_id,
                session_id=session_id,
                metadata={"learning_type": "feedback_analysis"}
            )
            
            # 建立与相关经验的关系
            await self._link_feedback_to_experiences(memory_id, agent_id, task_type)
            
            self.logger.info(f"从反馈学习完成: {memory_id}")
            
        except Exception as e:
            self.logger.error(f"从反馈学习失败: {str(e)}")
    
    async def get_learning_insights(
        self,
        agent_id: str,
        task_type: str = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """获取学习洞察"""
        try:
            # 搜索相关的反馈学习
            search_query = SearchQuery(
                text=f"agent:{agent_id} task:{task_type}" if task_type else f"agent:{agent_id}",
                memory_types=[MemoryType.FEEDBACK],
                scopes=[MemoryScope.AGENT, MemoryScope.GLOBAL],
                limit=limit
            )
            
            feedback_memories = await self.vector_memory.search_memories(search_query)
            
            # 提取学习洞察
            insights = []
            for memory in feedback_memories:
                content = memory.content
                if "learning_points" in content:
                    insights.extend(content["learning_points"])
                if "improvement_suggestions" in content:
                    insights.extend(content["improvement_suggestions"])
            
            # 去重和排序
            unique_insights = list(set(insights))
            
            return [{"insight": insight, "relevance": 1.0} for insight in unique_insights]
            
        except Exception as e:
            self.logger.error(f"获取学习洞察失败: {str(e)}")
            return []
    
    async def _analyze_feedback(self, feedback: Dict[str, Any]) -> Dict[str, Any]:
        """分析反馈内容"""
        try:
            analysis = {
                "sentiment": "neutral",
                "learning_points": [],
                "improvements": [],
                "patterns": []
            }
            
            # 简单的反馈分析
            feedback_text = str(feedback).lower()
            
            # 情感分析
            positive_words = ["好", "优秀", "正确", "成功", "满意"]
            negative_words = ["差", "错误", "失败", "问题", "不满意"]
            
            positive_count = sum(1 for word in positive_words if word in feedback_text)
            negative_count = sum(1 for word in negative_words if word in feedback_text)
            
            if positive_count > negative_count:
                analysis["sentiment"] = "positive"
            elif negative_count > positive_count:
                analysis["sentiment"] = "negative"
            
            # 提取学习点
            if "学习" in feedback_text or "改进" in feedback_text:
                analysis["learning_points"].append("需要持续学习和改进")
            
            if "质量" in feedback_text:
                analysis["learning_points"].append("关注输出质量")
            
            if "效率" in feedback_text:
                analysis["learning_points"].append("提高工作效率")
            
            # 提取改进建议
            if analysis["sentiment"] == "negative":
                analysis["improvements"].append("分析失败原因并制定改进计划")
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"分析反馈失败: {str(e)}")
            return {"sentiment": "neutral", "learning_points": [], "improvements": []}
    
    async def _link_feedback_to_experiences(
        self,
        feedback_memory_id: str,
        agent_id: str,
        task_type: str
    ):
        """将反馈链接到相关经验"""
        try:
            # 查找相关经验
            experiences = await self.get_relevant_experiences(
                query=f"agent:{agent_id} task:{task_type}",
                limit=5
            )
            
            # 建立关系
            for experience in experiences:
                await self.knowledge_graph.add_relationship(
                    source_memory_id=feedback_memory_id,
                    target_memory_id=experience.id,
                    relationship_type="learns_from",
                    strength=0.8,
                    metadata={"link_type": "feedback_to_experience"}
                )
            
        except Exception as e:
            self.logger.error(f"链接反馈到经验失败: {str(e)}")


# 全局实例
_vector_memory_manager: Optional[VectorMemoryManager] = None
_shared_knowledge_base: Optional[SharedKnowledgeBase] = None


async def get_vector_memory_manager() -> VectorMemoryManager:
    """获取向量记忆管理器实例"""
    global _vector_memory_manager
    if _vector_memory_manager is None:
        _vector_memory_manager = VectorMemoryManager()
        await _vector_memory_manager.initialize()
    return _vector_memory_manager


async def get_shared_knowledge_base() -> SharedKnowledgeBase:
    """获取共享知识库实例"""
    global _shared_knowledge_base
    if _shared_knowledge_base is None:
        vector_memory = await get_vector_memory_manager()
        _shared_knowledge_base = SharedKnowledgeBase(vector_memory)
    return _shared_knowledge_base


async def cleanup_vector_memory():
    """清理向量记忆资源"""
    global _vector_memory_manager, _shared_knowledge_base
    
    if _vector_memory_manager:
        await _vector_memory_manager.cleanup_expired_memories()
    
    _vector_memory_manager = None
    _shared_knowledge_base = None


# Agent记忆混入类
class AgentMemoryMixin:
    """Agent记忆混入类"""
    
    def __init__(self):
        self.vector_memory: Optional[VectorMemoryManager] = None
        self.knowledge_base: Optional[SharedKnowledgeBase] = None
        self._memory_logger = get_agent_logger(f"Memory_{self.__class__.__name__}")
    
    async def setup_memory(self):
        """设置记忆系统"""
        self.vector_memory = await get_vector_memory_manager()
        self.knowledge_base = await get_shared_knowledge_base()
        self._memory_logger.info("记忆系统设置完成")
    
    async def store_memory(
        self,
        content: Dict[str, Any],
        memory_type: MemoryType,
        tags: List[str] = None,
        scope: MemoryScope = MemoryScope.SESSION,
        expires_at: datetime = None
    ) -> str:
        """存储记忆"""
        if not self.vector_memory:
            await self.setup_memory()
        
        return await self.vector_memory.store_memory(
            content=content,
            memory_type=memory_type,
            scope=scope,
            tags=tags or [],
            source_agent=getattr(self, 'agent_id', 'unknown'),
            session_id=getattr(self, 'session_id', None),
            expires_at=expires_at
        )
    
    async def search_memories(
        self,
        query: str,
        memory_types: List[MemoryType] = None,
        limit: int = 10
    ) -> List[MemoryEntry]:
        """搜索记忆"""
        if not self.vector_memory:
            await self.setup_memory()
        
        search_query = SearchQuery(
            text=query,
            memory_types=memory_types,
            agent_id=getattr(self, 'agent_id', None),
            session_id=getattr(self, 'session_id', None),
            limit=limit
        )
        
        return await self.vector_memory.search_memories(search_query)
    
    async def get_relevant_experiences(
        self,
        query: str,
        limit: int = 5
    ) -> List[MemoryEntry]:
        """获取相关经验"""
        if not self.knowledge_base:
            await self.setup_memory()
        
        agent_type = getattr(self, 'agent_type', None)
        return await self.knowledge_base.get_relevant_experiences(
            query=query,
            agent_type=agent_type,
            limit=limit
        )
    
    async def store_experience(
        self,
        experience_type: str,
        content: Dict[str, Any],
        tags: List[str] = None
    ) -> str:
        """存储经验"""
        if not self.knowledge_base:
            await self.setup_memory()
        
        return await self.knowledge_base.store_experience(
            agent_id=getattr(self, 'agent_id', 'unknown'),
            session_id=getattr(self, 'session_id', 'unknown'),
            experience_type=experience_type,
            content=content,
            tags=tags
        )
    
    async def learn_from_feedback(self, feedback: Dict[str, Any]):
        """从反馈中学习"""
        if not self.knowledge_base:
            await self.setup_memory()
        
        await self.knowledge_base.learn_from_feedback(
            agent_id=getattr(self, 'agent_id', 'unknown'),
            task_type=getattr(self, 'agent_type', 'unknown').value if hasattr(getattr(self, 'agent_type', None), 'value') else 'unknown',
            feedback=feedback,
            session_id=getattr(self, 'session_id', None)
        )
    
    async def get_memory_context(self, query: str, limit: int = 5) -> str:
        """获取记忆上下文"""
        try:
            memories = await self.search_memories(query, limit=limit)
            
            if not memories:
                return ""
            
            context_parts = []
            for memory in memories:
                context_parts.append(f"记忆类型: {memory.type.value}")
                context_parts.append(f"内容: {json.dumps(memory.content, ensure_ascii=False)}")
                context_parts.append(f"标签: {', '.join(memory.tags)}")
                context_parts.append("---")
            
            return "\n".join(context_parts)
            
        except Exception as e:
            self._memory_logger.error(f"获取记忆上下文失败: {str(e)}")
            return ""
"""
向量记忆系统和知识共享机制
"""

import asyncio
import uuid
import json
import numpy as np
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import hashlib

from core.logger import get_agent_logger
from core.memory import get_memory_manager
from models.schemas import ThinkingChain, ReflectionResult
from models.enums import AgentType


class MemoryType(Enum):
    """记忆类型枚举"""
    REQUIREMENT = "requirement"
    TESTCASE = "testcase"
    REVIEW = "review"
    COLLABORATION = "collaboration"
    EXPERIENCE = "experience"
    KNOWLEDGE = "knowledge"
    PATTERN = "pattern"
    FEEDBACK = "feedback"


class MemoryScope(Enum):
    """记忆范围枚举"""
    GLOBAL = "global"        # 全局共享
    PROJECT = "project"      # 项目级别
    SESSION = "session"      # 会话级别
    AGENT = "agent"          # Agent私有
    TEAM = "team"           # 团队共享


@dataclass
class MemoryEntry:
    """记忆条目数据结构"""
    id: str
    type: MemoryType
    scope: MemoryScope
    content: Dict[str, Any]
    metadata: Dict[str, Any]
    embedding: Optional[List[float]] = None
    tags: List[str] = None
    source_agent: Optional[str] = None
    session_id: Optional[str] = None
    project_id: Optional[str] = None
    created_at: datetime = None
    updated_at: datetime = None
    access_count: int = 0
    relevance_score: float = 0.0
    expires_at: Optional[datetime] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = self.created_at
        if self.tags is None:
            self.tags = []
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "type": self.type.value,
            "scope": self.scope.value,
            "content": self.content,
            "metadata": self.metadata,
            "embedding": self.embedding,
            "tags": self.tags,
            "source_agent": self.source_agent,
            "session_id": self.session_id,
            "project_id": self.project_id,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "access_count": self.access_count,
            "relevance_score": self.relevance_score,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MemoryEntry':
        """从字典创建记忆条目"""
        return cls(
            id=data["id"],
            type=MemoryType(data["type"]),
            scope=MemoryScope(data["scope"]),
            content=data["content"],
            metadata=data["metadata"],
            embedding=data.get("embedding"),
            tags=data.get("tags", []),
            source_agent=data.get("source_agent"),
            session_id=data.get("session_id"),
            project_id=data.get("project_id"),
            created_at=datetime.fromisoformat(data["created_at"]),
            updated_at=datetime.fromisoformat(data["updated_at"]),
            access_count=data.get("access_count", 0),
            relevance_score=data.get("relevance_score", 0.0),
            expires_at=datetime.fromisoformat(data["expires_at"]) if data.get("expires_at") else None
        )


@dataclass
class SearchQuery:
    """搜索查询数据结构"""
    text: str
    memory_types: List[MemoryType] = None
    scopes: List[MemoryScope] = None
    tags: List[str] = None
    agent_id: Optional[str] = None
    session_id: Optional[str] = None
    project_id: Optional[str] = None
    time_range: Optional[Tuple[datetime, datetime]] = None
    limit: Optional[int] = None