"""
ChromaDB向量记忆系统
"""

import asyncio
import uuid
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
import chromadb
from chromadb.config import Settings
from chromadb.utils import embedding_functions
from sentence_transformers import SentenceTransformer
import json
import numpy as np
from config import get_settings
from core.logger import get_agent_logger
from models.schemas import (
    RequirementModel, TestCaseModel, AgentMessage, 
    TraceabilityMatrix, CollaborationSession
)

settings = get_settings()
logger = get_agent_logger("VectorMemory")


class ChromaDBVectorMemory:
    """ChromaDB向量记忆系统"""
    
    def __init__(self):
        self.client = None
        self.collection = None
        self.embedding_function = None
        self._initialized = False
        self.collection_name = None
        
    async def initialize(self):
        """初始化ChromaDB客户端和集合"""
        try:
            # 创建ChromaDB客户端
            self.client = chromadb.PersistentClient(
                path=settings.chroma_persist_directory,
                settings=Settings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )
            
            # 初始化嵌入函数
            self.embedding_function = embedding_functions.SentenceTransformerEmbeddingFunction(
                model_name="all-MiniLM-L6-v2"
            )
            
            # 设置集合名称
            self.collection_name = settings.chroma_collection_name

            # 创建或获取集合
            try:
                self.collection = self.client.get_collection(
                    name=settings.chroma_collection_name,
                    embedding_function=self.embedding_function
                )
                logger.info(f"已连接到现有集合: {settings.chroma_collection_name}")
            except Exception:
                self.collection = self.client.create_collection(
                    name=settings.chroma_collection_name,
                    embedding_function=self.embedding_function,
                    metadata={"description": "Agent共享记忆存储"}
                )
                logger.info(f"已创建新集合: {settings.chroma_collection_name}")
            
            self._initialized = True
            logger.info("ChromaDB向量记忆系统初始化完成")
            
        except Exception as e:
            logger.error(f"ChromaDB初始化失败: {str(e)}")
            raise
    
    def _ensure_initialized(self):
        """确保系统已初始化"""
        if not self._initialized:
            raise RuntimeError("ChromaDB向量记忆系统未初始化，请先调用initialize()")

    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            self._ensure_initialized()

            # 检查客户端连接
            if not self.client:
                return {"status": "unhealthy", "error": "ChromaDB客户端未初始化"}

            # 检查集合
            if not self.collection:
                return {"status": "unhealthy", "error": "ChromaDB集合未初始化"}

            # 获取集合统计信息
            stats = await self.get_statistics()

            return {
                "status": "healthy",
                "collection_name": settings.chroma_collection_name,
                "total_documents": stats.get("total_documents", 0),
                "initialized": self._initialized
            }

        except Exception as e:
            logger.error(f"健康检查失败: {str(e)}")
            return {"status": "unhealthy", "error": str(e)}
    
    async def store_requirement(self, requirement: RequirementModel, session_id: str) -> str:
        """存储需求信息"""
        self._ensure_initialized()
        
        try:
            doc_id = f"req_{requirement.trace_id}_{uuid.uuid4().hex[:8]}"
            
            # 构建文档内容
            content = f"""
            需求名称: {requirement.name}
            需求类型: {requirement.type}
            需求描述: {requirement.description}
            验收标准: {json.dumps(requirement.acceptance_criteria.dict(), ensure_ascii=False)}
            待处理场景: {', '.join(requirement.pending_scenarios)}
            状态: {requirement.status}
            风险级别: {requirement.risk_level or '未设置'}
            """
            
            # 构建元数据
            metadata = {
                "type": "requirement",
                "requirement_id": requirement.id,
                "trace_id": requirement.trace_id,
                "requirement_type": requirement.type,
                "status": requirement.status,
                "risk_level": requirement.risk_level or "unknown",
                "session_id": session_id,
                "created_at": requirement.created_at.isoformat(),
                "version": requirement.version
            }
            
            # 存储到ChromaDB
            self.collection.add(
                documents=[content.strip()],
                metadatas=[metadata],
                ids=[doc_id]
            )
            
            logger.info(f"已存储需求: {requirement.name} (ID: {doc_id})")
            return doc_id
            
        except Exception as e:
            logger.error(f"存储需求失败: {str(e)}")
            raise
    
    async def store_testcase(self, testcase: TestCaseModel, session_id: str) -> str:
        """存储测试用例信息"""
        self._ensure_initialized()
        
        try:
            doc_id = f"tc_{testcase.trace_id}_{uuid.uuid4().hex[:8]}"
            
            # 构建测试步骤文本
            steps_text = "\n".join([
                f"步骤{step.step_number}: {step.action} -> {step.expected_result}"
                for step in testcase.test_steps
            ])
            
            # 构建文档内容
            content = f"""
            用例名称: {testcase.name}
            用例描述: {testcase.description}
            用例类型: {testcase.type}
            优先级: {testcase.priority}
            前置条件: {', '.join(testcase.preconditions)}
            测试步骤:
            {steps_text}
            预期结果: {testcase.expected_result}
            关联需求: {', '.join(map(str, testcase.requirement_ids))}
            标签: {', '.join(testcase.tags)}
            """
            
            # 构建元数据
            metadata = {
                "type": "testcase",
                "testcase_id": testcase.id,
                "trace_id": testcase.trace_id,
                "testcase_type": testcase.type,
                "priority": testcase.priority,
                "status": testcase.status,
                "session_id": session_id,
                "requirement_ids": json.dumps(testcase.requirement_ids),
                "created_at": testcase.created_at.isoformat(),
                "version": testcase.version
            }
            
            # 存储到ChromaDB
            self.collection.add(
                documents=[content.strip()],
                metadatas=[metadata],
                ids=[doc_id]
            )
            
            logger.info(f"已存储测试用例: {testcase.name} (ID: {doc_id})")
            return doc_id
            
        except Exception as e:
            logger.error(f"存储测试用例失败: {str(e)}")
            raise
    
    async def store_agent_message(self, message: AgentMessage, session_id: str) -> str:
        """存储Agent消息"""
        self._ensure_initialized()
        
        try:
            doc_id = f"msg_{message.id}_{uuid.uuid4().hex[:8]}"
            
            # 构建文档内容
            content = f"""
            发送者: {message.sender}
            接收者: {message.receiver or '广播'}
            消息类型: {message.message_type}
            消息内容: {json.dumps(message.content, ensure_ascii=False)}
            时间戳: {message.timestamp.isoformat()}
            """
            
            # 构建元数据
            metadata = {
                "type": "agent_message",
                "message_id": message.id,
                "sender": message.sender,
                "receiver": message.receiver or "broadcast",
                "message_type": message.message_type,
                "session_id": session_id,
                "correlation_id": message.correlation_id or "",
                "timestamp": message.timestamp.isoformat()
            }
            
            # 存储到ChromaDB
            self.collection.add(
                documents=[content.strip()],
                metadatas=[metadata],
                ids=[doc_id]
            )
            
            logger.info(f"已存储Agent消息: {message.sender} -> {message.receiver} (ID: {doc_id})")
            return doc_id
            
        except Exception as e:
            logger.error(f"存储Agent消息失败: {str(e)}")
            raise
    
    async def store_collaboration_session(self, session: CollaborationSession) -> str:
        """存储协作会话信息"""
        self._ensure_initialized()
        
        try:
            doc_id = f"session_{session.session_id}_{uuid.uuid4().hex[:8]}"
            
            # 构建文档内容
            content = f"""
            会话ID: {session.session_id}
            参与者: {', '.join(session.participants)}
            协作状态: {session.status}
            当前阶段: {session.current_stage}
            迭代次数: {session.iteration_count}/{session.max_iterations}
            共享上下文: {json.dumps(session.shared_context, ensure_ascii=False)}
            创建时间: {session.created_at.isoformat()}
            """
            
            # 构建元数据
            metadata = {
                "type": "collaboration_session",
                "session_id": session.session_id,
                "status": session.status,
                "current_stage": session.current_stage,
                "participants": json.dumps(session.participants),
                "iteration_count": session.iteration_count,
                "created_at": session.created_at.isoformat(),
                "version": session.version
            }
            
            # 存储到ChromaDB
            self.collection.add(
                documents=[content.strip()],
                metadatas=[metadata],
                ids=[doc_id]
            )
            
            logger.info(f"已存储协作会话: {session.session_id} (ID: {doc_id})")
            return doc_id
            
        except Exception as e:
            logger.error(f"存储协作会话失败: {str(e)}")
            raise
    
    async def search_similar_requirements(
        self, 
        query: str, 
        session_id: Optional[str] = None,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """搜索相似需求"""
        self._ensure_initialized()
        
        try:
            # 构建过滤条件
            where_filter = {"type": "requirement"}
            if session_id:
                where_filter["session_id"] = session_id
            
            # 执行相似性搜索
            results = self.collection.query(
                query_texts=[query],
                n_results=limit,
                where=where_filter,
                include=["documents", "metadatas", "distances"]
            )
            
            # 格式化结果
            formatted_results = []
            if results["documents"] and results["documents"][0]:
                for i, doc in enumerate(results["documents"][0]):
                    formatted_results.append({
                        "document": doc,
                        "metadata": results["metadatas"][0][i],
                        "similarity": 1 - results["distances"][0][i]  # 转换为相似度
                    })
            
            logger.info(f"搜索到 {len(formatted_results)} 个相似需求")
            return formatted_results
            
        except Exception as e:
            logger.error(f"搜索相似需求失败: {str(e)}")
            return []
    
    async def search_similar_testcases(
        self, 
        query: str, 
        session_id: Optional[str] = None,
        requirement_ids: Optional[List[int]] = None,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """搜索相似测试用例"""
        self._ensure_initialized()
        
        try:
            # 构建过滤条件
            where_filter = {"type": "testcase"}
            if session_id:
                where_filter["session_id"] = session_id
            
            # 执行相似性搜索
            results = self.collection.query(
                query_texts=[query],
                n_results=limit,
                where=where_filter,
                include=["documents", "metadatas", "distances"]
            )
            
            # 格式化结果并过滤需求ID
            formatted_results = []
            if results["documents"] and results["documents"][0]:
                for i, doc in enumerate(results["documents"][0]):
                    metadata = results["metadatas"][0][i]
                    
                    # 如果指定了需求ID，进行过滤
                    if requirement_ids:
                        stored_req_ids = json.loads(metadata.get("requirement_ids", "[]"))
                        if not any(req_id in stored_req_ids for req_id in requirement_ids):
                            continue
                    
                    formatted_results.append({
                        "document": doc,
                        "metadata": metadata,
                        "similarity": 1 - results["distances"][0][i]
                    })
            
            logger.info(f"搜索到 {len(formatted_results)} 个相似测试用例")
            return formatted_results
            
        except Exception as e:
            logger.error(f"搜索相似测试用例失败: {str(e)}")
            return []
    
    async def get_session_history(
        self, 
        session_id: str, 
        message_type: Optional[str] = None,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """获取会话历史记录"""
        self._ensure_initialized()
        
        try:
            # 构建过滤条件
            where_filter = {"session_id": session_id}
            if message_type:
                where_filter["type"] = message_type
            
            # 获取历史记录
            results = self.collection.get(
                where=where_filter,
                limit=limit,
                include=["documents", "metadatas"]
            )
            
            # 格式化结果并按时间排序
            formatted_results = []
            if results["documents"]:
                for i, doc in enumerate(results["documents"]):
                    metadata = results["metadatas"][i]
                    formatted_results.append({
                        "document": doc,
                        "metadata": metadata,
                        "timestamp": metadata.get("timestamp") or metadata.get("created_at")
                    })
                
                # 按时间戳排序
                formatted_results.sort(key=lambda x: x["timestamp"])
            
            logger.info(f"获取到 {len(formatted_results)} 条会话历史记录")
            return formatted_results
            
        except Exception as e:
            logger.error(f"获取会话历史失败: {str(e)}")
            return []
    
    async def update_document(self, doc_id: str, content: str, metadata: Dict[str, Any]):
        """更新文档"""
        self._ensure_initialized()
        
        try:
            # ChromaDB不支持直接更新，需要先删除再添加
            self.collection.delete(ids=[doc_id])
            self.collection.add(
                documents=[content],
                metadatas=[metadata],
                ids=[doc_id]
            )
            
            logger.info(f"已更新文档: {doc_id}")
            
        except Exception as e:
            logger.error(f"更新文档失败: {str(e)}")
            raise
    
    async def delete_session_data(self, session_id: str):
        """删除会话相关数据"""
        self._ensure_initialized()
        
        try:
            # 获取会话相关的所有文档ID
            results = self.collection.get(
                where={"session_id": session_id},
                include=["metadatas"]
            )
            
            if results["ids"]:
                # 删除所有相关文档
                self.collection.delete(ids=results["ids"])
                logger.info(f"已删除会话 {session_id} 的 {len(results['ids'])} 条记录")
            
        except Exception as e:
            logger.error(f"删除会话数据失败: {str(e)}")
            raise
    
    async def get_collection_stats(self) -> Dict[str, Any]:
        """获取集合统计信息"""
        self._ensure_initialized()
        
        try:
            # 获取集合信息
            collection_info = self.collection.get(include=["metadatas"])
            
            # 统计各类型文档数量
            type_counts = {}
            session_counts = {}
            
            if collection_info["metadatas"]:
                for metadata in collection_info["metadatas"]:
                    doc_type = metadata.get("type", "unknown")
                    session_id = metadata.get("session_id", "unknown")
                    
                    type_counts[doc_type] = type_counts.get(doc_type, 0) + 1
                    session_counts[session_id] = session_counts.get(session_id, 0) + 1
            
            stats = {
                "total_documents": len(collection_info["ids"]) if collection_info["ids"] else 0,
                "type_distribution": type_counts,
                "session_distribution": session_counts,
                "collection_name": settings.chroma_collection_name
            }
            
            logger.info(f"集合统计: {stats['total_documents']} 个文档")
            return stats
            
        except Exception as e:
            logger.error(f"获取集合统计失败: {str(e)}")
            return {}

    async def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息（兼容性别名）"""
        return await self.get_collection_stats()

    def get_collection(self, collection_name: Optional[str] = None):
        """获取ChromaDB集合对象"""
        self._ensure_initialized()
        if collection_name and collection_name != self.collection_name:
            # 如果请求的是不同的集合，尝试获取或创建
            try:
                return self.client.get_collection(collection_name)
            except Exception:
                # 如果集合不存在，返回None
                return None
        return self.collection

    async def create_collection(self, collection_name: str):
        """创建新的集合"""
        self._ensure_initialized()
        try:
            return self.client.create_collection(collection_name)
        except Exception as e:
            self.logger.error(f"创建集合失败: {str(e)}")
            return None


# 全局向量记忆实例
vector_memory = ChromaDBVectorMemory()


async def get_vector_memory() -> ChromaDBVectorMemory:
    """获取向量记忆实例"""
    if not vector_memory._initialized:
        await vector_memory.initialize()
    return vector_memory


async def get_memory_manager() -> ChromaDBVectorMemory:
    """获取记忆管理器实例（兼容性别名）"""
    return await get_vector_memory()


async def cleanup_memory():
    """清理记忆系统资源"""
    try:
        global vector_memory
        if vector_memory and vector_memory._initialized:
            # 这里可以添加具体的清理逻辑
            logger.info("记忆系统资源清理完成")
    except Exception as e:
        logger.error(f"记忆系统清理失败: {str(e)}")