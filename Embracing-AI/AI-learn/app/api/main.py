"""
FastAPI主应用程序
"""

import asyncio
import uvicorn
from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>, HTTPException, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi.openapi.utils import get_openapi
import time
from typing import Dict, Any

from config import settings
from core.logger import get_agent_logger
from core.memory import cleanup_memory
from core.traceability import cleanup_traceability
from api.routes import (
    agents_router,
    requirements_router,
    testcases_router,
    collaboration_router,
    traceability_router,
    memory_router,
    system_router
)


# 应用生命周期管理
@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    logger = get_agent_logger("FastAPI")
    
    # 启动时初始化
    logger.info("正在启动异步多智能体测试用例生成框架...")
    
    try:
        # 初始化各个组件
        from core.memory import get_memory_manager
        from core.traceability import get_traceability_manager
        
        # 预热内存管理器
        memory_manager = await get_memory_manager()
        logger.info("内存管理器初始化完成")
        
        # 预热追溯矩阵
        traceability_manager = await get_traceability_manager()
        logger.info("可追溯性矩阵初始化完成")
        
        logger.info("系统启动完成")
        
    except Exception as e:
        logger.error(f"系统启动失败: {str(e)}")
        raise
    
    yield
    
    # 关闭时清理
    logger.info("正在关闭系统...")
    
    try:
        await cleanup_memory()
        await cleanup_traceability()
        logger.info("系统关闭完成")
        
    except Exception as e:
        logger.error(f"系统关闭时出错: {str(e)}")


# 创建FastAPI应用
app = FastAPI(
    title="异步多智能体测试用例生成框架",
    description="""
    基于Python + LangChain + AutoGen + AsyncIO的智能化测试用例生成系统
    
    ## 主要功能
    
    * **需求分析**: 智能解析需求文档，提取功能点和验收标准
    * **需求评审**: 自动化需求完整性和一致性检查
    * **测试用例生成**: 基于多种设计方法的智能用例生成
    * **测试用例评审**: 用例质量检查和完整性验证
    * **异步协作**: 多Agent协同工作和状态同步
    * **向量记忆**: 基于ChromaDB的长期记忆和知识共享
    * **可追溯性**: 完整的需求到测试用例追溯链
    
    ## 技术栈
    
    * **后端**: Python + FastAPI + LangChain + AutoGen
    * **数据库**: ChromaDB (向量数据库)
    * **AI模型**: Ollama + DeepSeek-R1:1.5b
    * **异步处理**: AsyncIO + Celery + Redis
    * **文档处理**: python-docx
    """,
    version="1.0.0",
    contact={
        "name": "AI测试框架团队",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT",
        "url": "https://opensource.org/licenses/MIT",
    },
    lifespan=lifespan,
    docs_url=None,  # 禁用默认文档
    redoc_url=None,  # 禁用ReDoc
)

# 添加中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)


# 请求处理中间件
@app.middleware("http")
async def process_time_middleware(request: Request, call_next):
    """请求处理时间中间件"""
    start_time = time.time()
    
    # 添加请求ID
    import uuid
    request_id = str(uuid.uuid4())[:8]
    request.state.request_id = request_id
    
    response = await call_next(request)
    
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    response.headers["X-Request-ID"] = request_id
    
    return response


# 全局异常处理
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP异常处理器"""
    logger = get_agent_logger("FastAPI")
    request_id = getattr(request.state, "request_id", "unknown")
    
    logger.error(f"HTTP异常 [{request_id}]: {exc.status_code} - {exc.detail}")
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": {
                "code": exc.status_code,
                "message": exc.detail,
                "request_id": request_id,
                "timestamp": time.time()
            }
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理器"""
    logger = get_agent_logger("FastAPI")
    request_id = getattr(request.state, "request_id", "unknown")
    
    logger.error(f"未处理异常 [{request_id}]: {str(exc)}", exc_info=True)
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "error": {
                "code": 500,
                "message": "内部服务器错误",
                "request_id": request_id,
                "timestamp": time.time()
            }
        }
    )


# 自定义OpenAPI文档
def custom_openapi():
    """自定义OpenAPI规范"""
    if app.openapi_schema:
        return app.openapi_schema
    
    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )
    
    # 添加自定义标签
    openapi_schema["tags"] = [
        {
            "name": "agents",
            "description": "智能体管理和操作"
        },
        {
            "name": "requirements",
            "description": "需求分析和管理"
        },
        {
            "name": "testcases",
            "description": "测试用例生成和管理"
        },
        {
            "name": "collaboration",
            "description": "多智能体协作"
        },
        {
            "name": "traceability",
            "description": "可追溯性管理"
        },
        {
            "name": "memory",
            "description": "向量记忆管理"
        },
        {
            "name": "system",
            "description": "系统管理和监控"
        }
    ]
    
    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi


# 自定义文档页面
@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    """自定义Swagger UI"""
    return get_swagger_ui_html(
        openapi_url=app.openapi_url,
        title=f"{app.title} - API文档",
        swagger_js_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui-bundle.js",
        swagger_css_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui.css",
        swagger_favicon_url="https://fastapi.tiangolo.com/img/favicon.png",
    )


# 健康检查
@app.get("/health", tags=["system"])
async def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "version": app.version,
        "service": "异步多智能体测试用例生成框架"
    }


# 根路径
@app.get("/", tags=["system"])
async def root():
    """根路径信息"""
    return {
        "message": "欢迎使用异步多智能体测试用例生成框架",
        "version": app.version,
        "docs_url": "/docs",
        "health_url": "/health",
        "features": [
            "需求分析Agent",
            "需求评审Agent", 
            "测试用例生成Agent",
            "测试用例评审Agent",
            "异步协作机制",
            "向量记忆系统",
            "可追溯性矩阵",
            "多轮交互对齐"
        ]
    }


# 注册路由
app.include_router(agents_router, prefix="/api/v1/agents", tags=["agents"])
app.include_router(requirements_router, prefix="/api/v1/requirements", tags=["requirements"])
app.include_router(testcases_router, prefix="/api/v1/testcases", tags=["testcases"])
app.include_router(collaboration_router, prefix="/api/v1/collaboration", tags=["collaboration"])
app.include_router(traceability_router, prefix="/api/v1/traceability", tags=["traceability"])
app.include_router(memory_router, prefix="/api/v1/memory", tags=["memory"])
app.include_router(system_router, prefix="/api/v1/system", tags=["system"])


# 静态文件服务（如果需要）
# app.mount("/static", StaticFiles(directory="static"), name="static")


if __name__ == "__main__":
    uvicorn.run(
        "app.api.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info" if not settings.DEBUG else "debug",
        access_log=True
    )