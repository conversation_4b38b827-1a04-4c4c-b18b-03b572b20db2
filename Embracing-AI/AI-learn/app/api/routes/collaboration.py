"""
多智能体协作API路由
"""

import asyncio
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks, WebSocket, WebSocketDisconnect
from fastapi.responses import StreamingResponse
import json
import uuid
from datetime import datetime

from models.schemas import (
    CollaborationRequest, CollaborationResponse,
    CollaborationStatusResponse, CollaborationTaskResponse
)
from models.enums import AgentType, AgentStatus
from core.logger import get_agent_logger
from core.collaboration import get_collaboration_manager
from core.traceability import get_traceability_manager, ArtifactType
from core.memory import get_memory_manager

router = APIRouter()
logger = get_agent_logger("CollaborationAPI")

# WebSocket连接管理
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
    
    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
    
    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)
    
    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                # 连接已断开，移除
                self.active_connections.remove(connection)

manager = ConnectionManager()


@router.post("/start", response_model=CollaborationResponse)
async def start_collaboration(
    request: CollaborationRequest,
    background_tasks: BackgroundTasks
):
    """启动多智能体协作任务"""
    try:
        collaboration_manager = await get_collaboration_manager()
        
        # 创建协作任务
        task_id = str(uuid.uuid4())
        
        # 异步执行协作任务
        async def execute_collaboration():
            try:
                result = await collaboration_manager.execute_workflow(
                    workflow_config={
                        "task_id": task_id,
                        "agents": request.agents,
                        "workflow_type": request.workflow_type,
                        "input_data": request.input_data,
                        "context": request.context or {}
                    }
                )
                
                # 保存结果到内存
                memory_manager = await get_memory_manager()
                await memory_manager.store_collaboration_result(
                    task_id=task_id,
                    result=result
                )
                
                # 广播完成消息
                await manager.broadcast(json.dumps({
                    "type": "collaboration_completed",
                    "task_id": task_id,
                    "status": "completed",
                    "timestamp": datetime.now().isoformat()
                }))
                
                logger.info(f"协作任务完成: {task_id}")
                
            except Exception as e:
                logger.error(f"协作任务执行失败: {str(e)}")
                
                # 广播错误消息
                await manager.broadcast(json.dumps({
                    "type": "collaboration_error",
                    "task_id": task_id,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }))
        
        # 添加到后台任务
        background_tasks.add_task(execute_collaboration)
        
        # 记录到追溯矩阵
        traceability_manager = await get_traceability_manager()
        await traceability_manager.create_artifact(
            artifact_type=ArtifactType.DESIGN,
            name=f"协作任务_{task_id}",
            description=f"多智能体协作任务: {request.workflow_type}",
            content={
                "task_id": task_id,
                "workflow_type": request.workflow_type,
                "agents": [agent.dict() for agent in request.agents],
                "input_data": request.input_data
            },
            created_by="collaboration_api",
            tags=["协作", request.workflow_type]
        )
        
        return CollaborationResponse(
            task_id=task_id,
            status="started",
            agents=request.agents,
            workflow_type=request.workflow_type,
            created_at=datetime.now(),
            message="协作任务已启动"
        )
        
    except Exception as e:
        logger.error(f"启动协作任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"启动协作任务失败: {str(e)}")


@router.get("/status/{task_id}", response_model=CollaborationStatusResponse)
async def get_collaboration_status(task_id: str):
    """获取协作任务状态"""
    try:
        collaboration_manager = await get_collaboration_manager()
        status = await collaboration_manager.get_task_status(task_id)
        
        if not status:
            raise HTTPException(status_code=404, detail="协作任务不存在")
        
        return CollaborationStatusResponse(
            task_id=task_id,
            status=status.get("status", "unknown"),
            current_agent=status.get("current_agent"),
            progress=status.get("progress", 0.0),
            agents_status=status.get("agents_status", {}),
            last_activity=status.get("last_activity"),
            error_message=status.get("error_message")
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取协作任务状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取协作任务状态失败: {str(e)}")


@router.get("/result/{task_id}")
async def get_collaboration_result(task_id: str):
    """获取协作任务结果"""
    try:
        memory_manager = await get_memory_manager()
        result = await memory_manager.get_collaboration_result(task_id)
        
        if result is None:
            raise HTTPException(status_code=404, detail="协作任务结果不存在")
        
        return {
            "task_id": task_id,
            "result": result,
            "status": "completed"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取协作任务结果失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取协作任务结果失败: {str(e)}")


@router.get("/tasks", response_model=List[CollaborationTaskResponse])
async def list_collaboration_tasks(
    status: Optional[str] = None,
    limit: int = 50,
    offset: int = 0
):
    """获取协作任务列表"""
    try:
        collaboration_manager = await get_collaboration_manager()
        tasks = await collaboration_manager.list_tasks(
            status=status,
            limit=limit,
            offset=offset
        )
        
        task_responses = []
        for task in tasks:
            task_responses.append(CollaborationTaskResponse(
                task_id=task["task_id"],
                workflow_type=task["workflow_type"],
                status=task["status"],
                agents_count=len(task.get("agents", [])),
                created_at=datetime.fromisoformat(task["created_at"]),
                updated_at=datetime.fromisoformat(task["updated_at"]) if task.get("updated_at") else None,
                progress=task.get("progress", 0.0)
            ))
        
        return task_responses
        
    except Exception as e:
        logger.error(f"获取协作任务列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取协作任务列表失败: {str(e)}")


@router.post("/stop/{task_id}")
async def stop_collaboration(task_id: str):
    """停止协作任务"""
    try:
        collaboration_manager = await get_collaboration_manager()
        success = await collaboration_manager.stop_task(task_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="协作任务不存在或无法停止")
        
        # 广播停止消息
        await manager.broadcast(json.dumps({
            "type": "collaboration_stopped",
            "task_id": task_id,
            "timestamp": datetime.now().isoformat()
        }))
        
        return {
            "task_id": task_id,
            "status": "stopped",
            "message": "协作任务已停止"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"停止协作任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"停止协作任务失败: {str(e)}")


@router.delete("/task/{task_id}")
async def delete_collaboration_task(task_id: str):
    """删除协作任务"""
    try:
        collaboration_manager = await get_collaboration_manager()
        success = await collaboration_manager.delete_task(task_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="协作任务不存在")
        
        return {
            "task_id": task_id,
            "status": "deleted",
            "message": "协作任务已删除"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除协作任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除协作任务失败: {str(e)}")


@router.websocket("/ws/{task_id}")
async def websocket_endpoint(websocket: WebSocket, task_id: str):
    """WebSocket连接用于实时监控协作任务"""
    await manager.connect(websocket)
    
    try:
        # 发送连接确认
        await websocket.send_text(json.dumps({
            "type": "connected",
            "task_id": task_id,
            "timestamp": datetime.now().isoformat()
        }))
        
        # 获取初始状态
        collaboration_manager = await get_collaboration_manager()
        initial_status = await collaboration_manager.get_task_status(task_id)
        
        if initial_status:
            await websocket.send_text(json.dumps({
                "type": "status_update",
                "task_id": task_id,
                "status": initial_status,
                "timestamp": datetime.now().isoformat()
            }))
        
        # 保持连接并处理消息
        while True:
            try:
                # 等待客户端消息
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # 处理客户端请求
                if message.get("type") == "get_status":
                    current_status = await collaboration_manager.get_task_status(task_id)
                    await websocket.send_text(json.dumps({
                        "type": "status_update",
                        "task_id": task_id,
                        "status": current_status,
                        "timestamp": datetime.now().isoformat()
                    }))
                
            except WebSocketDisconnect:
                break
            except Exception as e:
                logger.error(f"WebSocket处理消息失败: {str(e)}")
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": str(e),
                    "timestamp": datetime.now().isoformat()
                }))
    
    except WebSocketDisconnect:
        manager.disconnect(websocket)
        logger.info(f"WebSocket连接断开: {task_id}")
    except Exception as e:
        logger.error(f"WebSocket连接异常: {str(e)}")
        manager.disconnect(websocket)


@router.get("/workflows")
async def list_available_workflows():
    """获取可用的工作流类型"""
    try:
        collaboration_manager = await get_collaboration_manager()
        workflows = await collaboration_manager.get_available_workflows()
        
        return {
            "workflows": workflows,
            "total": len(workflows)
        }
        
    except Exception as e:
        logger.error(f"获取可用工作流失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取可用工作流失败: {str(e)}")


@router.post("/workflow/validate")
async def validate_workflow_config(config: Dict[str, Any]):
    """验证工作流配置"""
    try:
        collaboration_manager = await get_collaboration_manager()
        validation_result = await collaboration_manager.validate_workflow_config(config)
        
        return {
            "valid": validation_result.get("valid", False),
            "errors": validation_result.get("errors", []),
            "warnings": validation_result.get("warnings", []),
            "suggestions": validation_result.get("suggestions", [])
        }
        
    except Exception as e:
        logger.error(f"验证工作流配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"验证工作流配置失败: {str(e)}")


@router.get("/statistics")
async def get_collaboration_statistics():
    """获取协作统计信息"""
    try:
        collaboration_manager = await get_collaboration_manager()
        stats = await collaboration_manager.get_statistics()
        
        return {
            "total_tasks": stats.get("total_tasks", 0),
            "active_tasks": stats.get("active_tasks", 0),
            "completed_tasks": stats.get("completed_tasks", 0),
            "failed_tasks": stats.get("failed_tasks", 0),
            "average_duration": stats.get("average_duration", 0),
            "workflow_types": stats.get("workflow_types", {}),
            "agent_usage": stats.get("agent_usage", {}),
            "success_rate": stats.get("success_rate", 0.0)
        }
        
    except Exception as e:
        logger.error(f"获取协作统计信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取协作统计信息失败: {str(e)}")