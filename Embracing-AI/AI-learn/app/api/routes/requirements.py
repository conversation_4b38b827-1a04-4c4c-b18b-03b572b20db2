"""
需求管理API路由
"""

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, UploadFile, File, BackgroundTasks
from fastapi.responses import StreamingResponse
import json
import io

from models.schemas import (
    RequirementAnalysisRequest, RequirementAnalysisResponse,
    RequirementReviewRequest, RequirementReviewResponse,
    RequirementListResponse, RequirementResponse
)
from core.logger import get_agent_logger
from core.traceability import get_traceability_manager, ArtifactType, ArtifactStatus
from core.memory import get_memory_manager
from agents import RequirementAnalysisAgent, RequirementReviewAgent

router = APIRouter()
logger = get_agent_logger("RequirementsAPI")


@router.post("/analyze", response_model=RequirementAnalysisResponse)
async def analyze_requirements(
    request: RequirementAnalysisRequest,
    background_tasks: BackgroundTasks
):
    """分析需求文档"""
    try:
        # 创建需求分析Agent
        agent = RequirementAnalysisAgent("req_analysis_api")
        await agent.initialize()
        
        # 执行需求分析
        result = await agent.analyze_requirements(
            input_data=request.input_text,
            context=request.context or {}
        )
        
        # 保存到追溯矩阵
        traceability_manager = await get_traceability_manager()
        
        # 为每个需求创建工件
        requirement_ids = []
        for req in result.get("requirements", []):
            artifact_id = await traceability_manager.create_artifact(
                artifact_type=ArtifactType.REQUIREMENT,
                name=req.get("name", "未命名需求"),
                description=req.get("description", ""),
                content=req,
                created_by="requirement_analysis_api",
                tags=req.get("tags", [])
            )
            requirement_ids.append(artifact_id)
        
        # 保存分析结果到内存
        memory_manager = await get_memory_manager()
        await memory_manager.store_agent_result(
            agent_id="req_analysis_api",
            task_id=f"analysis_{hash(request.input_text)}",
            result=result
        )
        
        return RequirementAnalysisResponse(
            requirements=result.get("requirements", []),
            analysis_summary=result.get("analysis_summary", {}),
            version=result.get("version", 1),
            requirement_ids=requirement_ids
        )
        
    except Exception as e:
        logger.error(f"需求分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"需求分析失败: {str(e)}")


@router.post("/upload-document")
async def upload_requirement_document(
    file: UploadFile = File(...),
    context: Optional[str] = None
):
    """上传需求文档进行分析"""
    try:
        # 检查文件类型
        if not file.filename.endswith(('.docx', '.txt', '.md')):
            raise HTTPException(status_code=400, detail="不支持的文件类型，请上传.docx、.txt或.md文件")
        
        # 读取文件内容
        content = await file.read()
        
        # 创建需求分析Agent
        agent = RequirementAnalysisAgent("req_doc_analysis")
        await agent.initialize()
        
        # 根据文件类型处理
        if file.filename.endswith('.docx'):
            result = await agent.analyze_word_document(content)
        else:
            text_content = content.decode('utf-8')
            result = await agent.analyze_requirements(
                input_data=text_content,
                context={"source": "uploaded_document", "filename": file.filename}
            )
        
        # 保存到追溯矩阵
        traceability_manager = await get_traceability_manager()
        
        # 创建文档工件
        doc_artifact_id = await traceability_manager.create_artifact(
            artifact_type=ArtifactType.SPECIFICATION,
            name=f"需求文档_{file.filename}",
            description=f"上传的需求文档: {file.filename}",
            content={"filename": file.filename, "size": len(content)},
            created_by="document_upload_api",
            tags=["文档", "需求"]
        )
        
        # 为每个需求创建工件并建立链接
        requirement_ids = []
        for req in result.get("requirements", []):
            req_artifact_id = await traceability_manager.create_artifact(
                artifact_type=ArtifactType.REQUIREMENT,
                name=req.get("name", "未命名需求"),
                description=req.get("description", ""),
                content=req,
                created_by="document_upload_api",
                tags=req.get("tags", [])
            )
            requirement_ids.append(req_artifact_id)
            
            # 建立文档到需求的追溯链接
            await traceability_manager.create_link(
                source_id=doc_artifact_id,
                target_id=req_artifact_id,
                link_type="derives_from",
                created_by="document_upload_api"
            )
        
        return {
            "filename": file.filename,
            "document_id": doc_artifact_id,
            "requirements": result.get("requirements", []),
            "requirement_ids": requirement_ids,
            "analysis_summary": result.get("analysis_summary", {}),
            "status": "processed"
        }
        
    except Exception as e:
        logger.error(f"文档上传分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"文档上传分析失败: {str(e)}")


@router.post("/review", response_model=RequirementReviewResponse)
async def review_requirements(request: RequirementReviewRequest):
    """评审需求"""
    try:
        # 创建需求评审Agent
        agent = RequirementReviewAgent("req_review_api")
        await agent.initialize()
        
        # 执行需求评审
        result = await agent.review_requirements(
            requirements=request.requirements,
            context=request.context or {}
        )
        
        # 更新追溯矩阵中的需求状态
        traceability_manager = await get_traceability_manager()
        
        for req_id in request.requirement_ids or []:
            # 更新需求状态
            await traceability_manager.update_artifact(
                artifact_id=req_id,
                status=ArtifactStatus.UNDER_REVIEW,
                metadata={"review_result": result},
                updated_by="requirement_review_api"
            )
        
        # 创建评审记录工件
        review_artifact_id = await traceability_manager.create_artifact(
            artifact_type=ArtifactType.REVIEW,
            name="需求评审记录",
            description="需求评审结果记录",
            content=result,
            created_by="requirement_review_api",
            tags=["评审", "需求"]
        )
        
        return RequirementReviewResponse(
            review_issues=result.get("review_issues", []),
            updated_requirements=result.get("updated_requirements", []),
            traceability_matrix=result.get("traceability_matrix", []),
            version=result.get("version", 1),
            review_id=review_artifact_id
        )
        
    except Exception as e:
        logger.error(f"需求评审失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"需求评审失败: {str(e)}")


@router.get("/list", response_model=RequirementListResponse)
async def list_requirements(
    status: Optional[str] = None,
    tag: Optional[str] = None,
    limit: int = 50,
    offset: int = 0
):
    """获取需求列表"""
    try:
        traceability_manager = await get_traceability_manager()
        
        # 获取需求工件
        if status:
            artifacts = await traceability_manager.get_artifacts_by_status(
                ArtifactStatus(status)
            )
        else:
            artifacts = await traceability_manager.get_artifacts_by_type(
                ArtifactType.REQUIREMENT
            )
        
        # 按标签过滤
        if tag:
            artifacts = [a for a in artifacts if tag in a.tags]
        
        # 分页
        total = len(artifacts)
        artifacts = artifacts[offset:offset + limit]
        
        # 转换为响应格式
        requirements = []
        for artifact in artifacts:
            requirements.append(RequirementResponse(
                id=artifact.id,
                name=artifact.name,
                description=artifact.description,
                content=artifact.content,
                status=artifact.status.value,
                version=artifact.version,
                created_at=artifact.created_at,
                updated_at=artifact.updated_at,
                tags=artifact.tags
            ))
        
        return RequirementListResponse(
            requirements=requirements,
            total=total,
            limit=limit,
            offset=offset
        )
        
    except Exception as e:
        logger.error(f"获取需求列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取需求列表失败: {str(e)}")


@router.get("/{requirement_id}", response_model=RequirementResponse)
async def get_requirement(requirement_id: str):
    """获取单个需求详情"""
    try:
        traceability_manager = await get_traceability_manager()
        artifact = await traceability_manager.get_artifact(requirement_id)
        
        if not artifact:
            raise HTTPException(status_code=404, detail="需求不存在")
        
        return RequirementResponse(
            id=artifact.id,
            name=artifact.name,
            description=artifact.description,
            content=artifact.content,
            status=artifact.status.value,
            version=artifact.version,
            created_at=artifact.created_at,
            updated_at=artifact.updated_at,
            tags=artifact.tags
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取需求详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取需求详情失败: {str(e)}")


@router.put("/{requirement_id}")
async def update_requirement(
    requirement_id: str,
    name: Optional[str] = None,
    description: Optional[str] = None,
    content: Optional[Dict[str, Any]] = None,
    status: Optional[str] = None,
    tags: Optional[List[str]] = None
):
    """更新需求"""
    try:
        traceability_manager = await get_traceability_manager()
        
        # 准备更新参数
        update_params = {}
        if name is not None:
            update_params["name"] = name
        if description is not None:
            update_params["description"] = description
        if content is not None:
            update_params["content"] = content
        if status is not None:
            update_params["status"] = ArtifactStatus(status)
        if tags is not None:
            update_params["tags"] = tags
        
        # 执行更新
        success = await traceability_manager.update_artifact(
            artifact_id=requirement_id,
            updated_by="requirement_update_api",
            **update_params
        )
        
        if not success:
            raise HTTPException(status_code=404, detail="需求不存在")
        
        # 获取更新后的需求
        artifact = await traceability_manager.get_artifact(requirement_id)
        
        return RequirementResponse(
            id=artifact.id,
            name=artifact.name,
            description=artifact.description,
            content=artifact.content,
            status=artifact.status.value,
            version=artifact.version,
            created_at=artifact.created_at,
            updated_at=artifact.updated_at,
            tags=artifact.tags
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新需求失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新需求失败: {str(e)}")


@router.delete("/{requirement_id}")
async def delete_requirement(requirement_id: str):
    """删除需求"""
    try:
        traceability_manager = await get_traceability_manager()
        
        success = await traceability_manager.delete_artifact(
            artifact_id=requirement_id,
            deleted_by="requirement_delete_api"
        )
        
        if not success:
            raise HTTPException(status_code=404, detail="需求不存在")
        
        return {
            "requirement_id": requirement_id,
            "status": "deleted",
            "message": "需求已删除"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除需求失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除需求失败: {str(e)}")


@router.get("/{requirement_id}/related")
async def get_related_requirements(requirement_id: str):
    """获取相关需求"""
    try:
        traceability_manager = await get_traceability_manager()
        
        related = await traceability_manager.get_related_artifacts(
            artifact_id=requirement_id,
            direction="both"
        )
        
        related_requirements = []
        for artifact, link in related:
            related_requirements.append({
                "requirement": RequirementResponse(
                    id=artifact.id,
                    name=artifact.name,
                    description=artifact.description,
                    content=artifact.content,
                    status=artifact.status.value,
                    version=artifact.version,
                    created_at=artifact.created_at,
                    updated_at=artifact.updated_at,
                    tags=artifact.tags
                ),
                "relationship": {
                    "type": link.type.value,
                    "strength": link.strength,
                    "bidirectional": link.bidirectional
                }
            })
        
        return {
            "requirement_id": requirement_id,
            "related_requirements": related_requirements,
            "total": len(related_requirements)
        }
        
    except Exception as e:
        logger.error(f"获取相关需求失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取相关需求失败: {str(e)}")


@router.get("/{requirement_id}/export")
async def export_requirement(requirement_id: str):
    """导出需求数据"""
    try:
        traceability_manager = await get_traceability_manager()
        artifact = await traceability_manager.get_artifact(requirement_id)
        
        if not artifact:
            raise HTTPException(status_code=404, detail="需求不存在")
        
        # 获取相关数据
        related = await traceability_manager.get_related_artifacts(requirement_id)
        
        export_data = {
            "requirement": artifact.to_dict(),
            "related_artifacts": [
                {
                    "artifact": rel_artifact.to_dict(),
                    "relationship": rel_link.to_dict()
                }
                for rel_artifact, rel_link in related
            ],
            "export_timestamp": artifact.updated_at.isoformat()
        }
        
        # 创建JSON文件流
        json_str = json.dumps(export_data, ensure_ascii=False, indent=2)
        json_bytes = json_str.encode('utf-8')
        
        return StreamingResponse(
            io.BytesIO(json_bytes),
            media_type="application/json",
            headers={"Content-Disposition": f"attachment; filename=requirement_{requirement_id}_export.json"}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导出需求数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导出需求数据失败: {str(e)}")