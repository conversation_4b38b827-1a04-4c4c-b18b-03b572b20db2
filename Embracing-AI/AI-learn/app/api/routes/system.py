"""
系统管理API路由
"""

import asyncio
import psutil
import platform
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException
from datetime import datetime, timedelta

from core.logger import get_agent_logger
from core.memory import get_memory_manager
from core.traceability import get_traceability_manager
from config import settings

router = APIRouter()
logger = get_agent_logger("SystemAPI")


@router.get("/health")
async def health_check():
    """系统健康检查"""
    try:
        health_status = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0",
            "components": {}
        }
        
        # 检查内存管理器
        try:
            memory_manager = await get_memory_manager()
            await memory_manager.health_check()
            health_status["components"]["memory"] = "healthy"
        except Exception as e:
            health_status["components"]["memory"] = f"unhealthy: {str(e)}"
            health_status["status"] = "degraded"
        
        # 检查追溯矩阵
        try:
            traceability_manager = await get_traceability_manager()
            stats = await traceability_manager.get_statistics()
            health_status["components"]["traceability"] = "healthy"
            health_status["components"]["traceability_stats"] = stats
        except Exception as e:
            health_status["components"]["traceability"] = f"unhealthy: {str(e)}"
            health_status["status"] = "degraded"
        
        # 检查Ollama连接
        try:
            from services.ollama_client import get_ollama_client
            ollama_client = get_ollama_client()
            models = await ollama_client.list_models()
            health_status["components"]["ollama"] = "healthy"
            health_status["components"]["ollama_models"] = len(models)
        except Exception as e:
            health_status["components"]["ollama"] = f"unhealthy: {str(e)}"
            health_status["status"] = "degraded"
        
        return health_status
        
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        return {
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }


@router.get("/info")
async def get_system_info():
    """获取系统信息"""
    try:
        # 系统基本信息
        system_info = {
            "platform": {
                "system": platform.system(),
                "release": platform.release(),
                "version": platform.version(),
                "machine": platform.machine(),
                "processor": platform.processor(),
                "python_version": platform.python_version()
            },
            "hardware": {
                "cpu_count": psutil.cpu_count(),
                "cpu_percent": psutil.cpu_percent(interval=1),
                "memory": {
                    "total": psutil.virtual_memory().total,
                    "available": psutil.virtual_memory().available,
                    "percent": psutil.virtual_memory().percent,
                    "used": psutil.virtual_memory().used
                },
                "disk": {
                    "total": psutil.disk_usage('/').total,
                    "used": psutil.disk_usage('/').used,
                    "free": psutil.disk_usage('/').free,
                    "percent": psutil.disk_usage('/').percent
                }
            },
            "application": {
                "name": "异步多智能体测试用例生成框架",
                "version": "1.0.0",
                "debug_mode": settings.DEBUG,
                "host": settings.HOST,
                "port": settings.PORT,
                "environment": settings.ENVIRONMENT
            },
            "timestamp": datetime.now().isoformat()
        }
        
        return system_info
        
    except Exception as e:
        logger.error(f"获取系统信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取系统信息失败: {str(e)}")


@router.get("/metrics")
async def get_system_metrics():
    """获取系统性能指标"""
    try:
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1, percpu=True)
        
        # 内存使用情况
        memory = psutil.virtual_memory()
        
        # 磁盘使用情况
        disk = psutil.disk_usage('/')
        
        # 网络统计
        network = psutil.net_io_counters()
        
        # 进程信息
        process = psutil.Process()
        process_info = {
            "pid": process.pid,
            "cpu_percent": process.cpu_percent(),
            "memory_percent": process.memory_percent(),
            "memory_info": process.memory_info()._asdict(),
            "num_threads": process.num_threads(),
            "create_time": datetime.fromtimestamp(process.create_time()).isoformat()
        }
        
        metrics = {
            "cpu": {
                "overall_percent": sum(cpu_percent) / len(cpu_percent),
                "per_core": cpu_percent,
                "core_count": len(cpu_percent)
            },
            "memory": {
                "total_gb": round(memory.total / (1024**3), 2),
                "available_gb": round(memory.available / (1024**3), 2),
                "used_gb": round(memory.used / (1024**3), 2),
                "percent": memory.percent
            },
            "disk": {
                "total_gb": round(disk.total / (1024**3), 2),
                "used_gb": round(disk.used / (1024**3), 2),
                "free_gb": round(disk.free / (1024**3), 2),
                "percent": disk.percent
            },
            "network": {
                "bytes_sent": network.bytes_sent,
                "bytes_recv": network.bytes_recv,
                "packets_sent": network.packets_sent,
                "packets_recv": network.packets_recv
            },
            "process": process_info,
            "timestamp": datetime.now().isoformat()
        }
        
        return metrics
        
    except Exception as e:
        logger.error(f"获取系统指标失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取系统指标失败: {str(e)}")


@router.get("/logs")
async def get_system_logs(
    level: Optional[str] = None,
    limit: int = 100,
    hours: int = 24
):
    """获取系统日志"""
    try:
        # 这里应该从日志文件或日志系统中读取日志
        # 为了演示，返回模拟数据
        logs = [
            {
                "timestamp": (datetime.now() - timedelta(minutes=i)).isoformat(),
                "level": "INFO" if i % 3 == 0 else "DEBUG" if i % 3 == 1 else "WARNING",
                "logger": f"Agent_{i % 4}",
                "message": f"模拟日志消息 {i}",
                "module": "system"
            }
            for i in range(min(limit, 50))
        ]
        
        # 按级别过滤
        if level:
            logs = [log for log in logs if log["level"] == level.upper()]
        
        return {
            "logs": logs,
            "total": len(logs),
            "level_filter": level,
            "time_range_hours": hours
        }
        
    except Exception as e:
        logger.error(f"获取系统日志失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取系统日志失败: {str(e)}")


@router.get("/config")
async def get_system_config():
    """获取系统配置"""
    try:
        config = {
            "database": {
                "chromadb_host": settings.CHROMADB_HOST,
                "chromadb_port": settings.CHROMADB_PORT,
                "chromadb_path": settings.CHROMADB_PATH
            },
            "ollama": {
                "host": settings.OLLAMA_HOST,
                "port": settings.OLLAMA_PORT,
                "model": settings.OLLAMA_MODEL
            },
            "api": {
                "host": settings.HOST,
                "port": settings.PORT,
                "debug": settings.DEBUG,
                "environment": settings.ENVIRONMENT
            },
            "security": {
                "allowed_origins": settings.ALLOWED_ORIGINS,
                "cors_enabled": True
            },
            "features": {
                "vector_memory": True,
                "traceability_matrix": True,
                "multi_agent_collaboration": True,
                "async_processing": True
            }
        }
        
        return config
        
    except Exception as e:
        logger.error(f"获取系统配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取系统配置失败: {str(e)}")


@router.post("/maintenance/cleanup")
async def cleanup_system():
    """系统清理维护"""
    try:
        cleanup_results = {
            "memory_cleanup": False,
            "traceability_cleanup": False,
            "log_cleanup": False,
            "cache_cleanup": False
        }
        
        # 清理内存系统
        try:
            memory_manager = await get_memory_manager()
            await memory_manager.cleanup_expired_data()
            cleanup_results["memory_cleanup"] = True
        except Exception as e:
            logger.error(f"内存系统清理失败: {str(e)}")
        
        # 清理追溯矩阵缓存
        try:
            traceability_manager = await get_traceability_manager()
            traceability_manager._clear_cache()
            cleanup_results["traceability_cleanup"] = True
        except Exception as e:
            logger.error(f"追溯矩阵清理失败: {str(e)}")
        
        # 清理日志（模拟）
        try:
            # 这里应该实现实际的日志清理逻辑
            cleanup_results["log_cleanup"] = True
        except Exception as e:
            logger.error(f"日志清理失败: {str(e)}")
        
        # 清理系统缓存（模拟）
        try:
            # 这里应该实现实际的缓存清理逻辑
            cleanup_results["cache_cleanup"] = True
        except Exception as e:
            logger.error(f"缓存清理失败: {str(e)}")
        
        return {
            "status": "completed",
            "cleanup_results": cleanup_results,
            "timestamp": datetime.now().isoformat(),
            "message": "系统清理维护完成"
        }
        
    except Exception as e:
        logger.error(f"系统清理维护失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"系统清理维护失败: {str(e)}")


@router.post("/maintenance/backup")
async def backup_system():
    """系统备份"""
    try:
        backup_results = {
            "memory_backup": False,
            "traceability_backup": False,
            "config_backup": False
        }
        
        backup_data = {
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0",
            "components": {}
        }
        
        # 备份内存系统
        try:
            memory_manager = await get_memory_manager()
            memory_backup = await memory_manager.create_backup()
            backup_data["components"]["memory"] = memory_backup
            backup_results["memory_backup"] = True
        except Exception as e:
            logger.error(f"内存系统备份失败: {str(e)}")
        
        # 备份追溯矩阵
        try:
            traceability_manager = await get_traceability_manager()
            stats = await traceability_manager.get_statistics()
            changes = await traceability_manager.get_change_history(limit=1000)
            
            traceability_backup = {
                "statistics": stats,
                "change_history": [change.to_dict() for change in changes]
            }
            backup_data["components"]["traceability"] = traceability_backup
            backup_results["traceability_backup"] = True
        except Exception as e:
            logger.error(f"追溯矩阵备份失败: {str(e)}")
        
        # 备份配置
        try:
            config_backup = await get_system_config()
            backup_data["components"]["config"] = config_backup
            backup_results["config_backup"] = True
        except Exception as e:
            logger.error(f"配置备份失败: {str(e)}")
        
        return {
            "status": "completed",
            "backup_results": backup_results,
            "backup_data": backup_data,
            "timestamp": datetime.now().isoformat(),
            "message": "系统备份完成"
        }
        
    except Exception as e:
        logger.error(f"系统备份失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"系统备份失败: {str(e)}")


@router.get("/status")
async def get_system_status():
    """获取系统运行状态"""
    try:
        # 获取系统启动时间
        boot_time = datetime.fromtimestamp(psutil.boot_time())
        
        # 获取当前进程信息
        process = psutil.Process()
        process_start_time = datetime.fromtimestamp(process.create_time())
        
        # 计算运行时间
        uptime = datetime.now() - process_start_time
        system_uptime = datetime.now() - boot_time
        
        status = {
            "system": {
                "status": "running",
                "uptime_seconds": int(uptime.total_seconds()),
                "uptime_human": str(uptime),
                "system_uptime_seconds": int(system_uptime.total_seconds()),
                "system_uptime_human": str(system_uptime),
                "start_time": process_start_time.isoformat(),
                "current_time": datetime.now().isoformat()
            },
            "resources": {
                "cpu_percent": psutil.cpu_percent(interval=1),
                "memory_percent": psutil.virtual_memory().percent,
                "disk_percent": psutil.disk_usage('/').percent,
                "load_average": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None
            },
            "components": {
                "api_server": "running",
                "memory_system": "running",
                "traceability_system": "running",
                "ollama_client": "running"
            },
            "statistics": {
                "total_requests": 0,  # 这里应该从实际的请求计数器获取
                "active_agents": 0,   # 这里应该从Agent管理器获取
                "active_tasks": 0     # 这里应该从任务管理器获取
            }
        }
        
        return status
        
    except Exception as e:
        logger.error(f"获取系统状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取系统状态失败: {str(e)}")


@router.post("/restart")
async def restart_system():
    """重启系统（仅重启应用组件）"""
    try:
        # 注意：这里不能真正重启整个系统，只能重启应用组件
        restart_results = {
            "memory_system": False,
            "traceability_system": False,
            "agent_system": False
        }
        
        # 重启内存系统
        try:
            from core.memory import cleanup_memory
            await cleanup_memory()
            memory_manager = await get_memory_manager()
            restart_results["memory_system"] = True
        except Exception as e:
            logger.error(f"重启内存系统失败: {str(e)}")
        
        # 重启追溯系统
        try:
            from core.traceability import cleanup_traceability
            await cleanup_traceability()
            traceability_manager = await get_traceability_manager()
            restart_results["traceability_system"] = True
        except Exception as e:
            logger.error(f"重启追溯系统失败: {str(e)}")
        
        # 重启Agent系统（清理Agent实例）
        try:
            # 这里应该清理所有Agent实例
            restart_results["agent_system"] = True
        except Exception as e:
            logger.error(f"重启Agent系统失败: {str(e)}")
        
        return {
            "status": "completed",
            "restart_results": restart_results,
            "timestamp": datetime.now().isoformat(),
            "message": "系统组件重启完成"
        }
        
    except Exception as e:
        logger.error(f"系统重启失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"系统重启失败: {str(e)}")


@router.get("/version")
async def get_version_info():
    """获取版本信息"""
    return {
        "application": {
            "name": "异步多智能体测试用例生成框架",
            "version": "1.0.0",
            "build_date": "2024-01-01",
            "description": "基于Python + LangChain + AutoGen的智能化测试用例生成系统"
        },
        "dependencies": {
            "python": platform.python_version(),
            "fastapi": "0.104.1",
            "langchain": "0.1.0",
            "autogen": "0.2.0",
            "chromadb": "0.4.0",
            "ollama": "0.1.0"
        },
        "features": [
            "需求分析Agent",
            "需求评审Agent",
            "测试用例生成Agent", 
            "测试用例评审Agent",
            "异步协作机制",
            "向量记忆系统",
            "可追溯性矩阵",
            "多轮交互对齐"
        ],
        "api_version": "v1",
        "timestamp": datetime.now().isoformat()
    }
