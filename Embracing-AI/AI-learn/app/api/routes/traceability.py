"""
可追溯性管理API路由
"""

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
import json
import io

from models.schemas import (
    TraceabilityArtifactRequest, TraceabilityArtifactResponse,
    TraceabilityLinkRequest, TraceabilityLinkResponse,
    CoverageReportResponse, TraceabilityStatsResponse
)
from core.logger import get_agent_logger
from core.traceability import (
    get_traceability_manager, ArtifactType, ArtifactStatus, 
    TraceabilityType, ChangeType
)

router = APIRouter()
logger = get_agent_logger("TraceabilityAPI")


@router.post("/artifacts", response_model=TraceabilityArtifactResponse)
async def create_artifact(request: TraceabilityArtifactRequest):
    """创建追溯工件"""
    try:
        traceability_manager = await get_traceability_manager()
        
        artifact_id = await traceability_manager.create_artifact(
            artifact_type=ArtifactType(request.type),
            name=request.name,
            description=request.description,
            content=request.content,
            created_by=request.created_by,
            tags=request.tags or [],
            metadata=request.metadata or {}
        )
        
        # 获取创建的工件
        artifact = await traceability_manager.get_artifact(artifact_id)
        
        return TraceabilityArtifactResponse(
            id=artifact.id,
            type=artifact.type.value,
            name=artifact.name,
            description=artifact.description,
            content=artifact.content,
            status=artifact.status.value,
            version=artifact.version,
            created_by=artifact.created_by,
            created_at=artifact.created_at,
            updated_at=artifact.updated_at,
            tags=artifact.tags,
            metadata=artifact.metadata
        )
        
    except Exception as e:
        logger.error(f"创建追溯工件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建追溯工件失败: {str(e)}")


@router.get("/artifacts", response_model=List[TraceabilityArtifactResponse])
async def list_artifacts(
    type: Optional[str] = None,
    status: Optional[str] = None,
    tag: Optional[str] = None,
    limit: int = 50,
    offset: int = 0
):
    """获取追溯工件列表"""
    try:
        traceability_manager = await get_traceability_manager()
        
        # 根据条件获取工件
        if type:
            artifacts = await traceability_manager.get_artifacts_by_type(ArtifactType(type))
        elif status:
            artifacts = await traceability_manager.get_artifacts_by_status(ArtifactStatus(status))
        elif tag:
            artifacts = await traceability_manager.get_artifacts_by_tag(tag)
        else:
            # 获取所有工件
            all_types = [ArtifactType.REQUIREMENT, ArtifactType.TESTCASE, 
                        ArtifactType.REVIEW, ArtifactType.DESIGN]
            artifacts = []
            for artifact_type in all_types:
                type_artifacts = await traceability_manager.get_artifacts_by_type(artifact_type)
                artifacts.extend(type_artifacts)
        
        # 分页
        total = len(artifacts)
        artifacts = artifacts[offset:offset + limit]
        
        # 转换为响应格式
        artifact_responses = []
        for artifact in artifacts:
            artifact_responses.append(TraceabilityArtifactResponse(
                id=artifact.id,
                type=artifact.type.value,
                name=artifact.name,
                description=artifact.description,
                content=artifact.content,
                status=artifact.status.value,
                version=artifact.version,
                created_by=artifact.created_by,
                created_at=artifact.created_at,
                updated_at=artifact.updated_at,
                tags=artifact.tags,
                metadata=artifact.metadata
            ))
        
        return artifact_responses
        
    except Exception as e:
        logger.error(f"获取追溯工件列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取追溯工件列表失败: {str(e)}")


@router.get("/artifacts/{artifact_id}", response_model=TraceabilityArtifactResponse)
async def get_artifact(artifact_id: str):
    """获取单个追溯工件"""
    try:
        traceability_manager = await get_traceability_manager()
        artifact = await traceability_manager.get_artifact(artifact_id)
        
        if not artifact:
            raise HTTPException(status_code=404, detail="追溯工件不存在")
        
        return TraceabilityArtifactResponse(
            id=artifact.id,
            type=artifact.type.value,
            name=artifact.name,
            description=artifact.description,
            content=artifact.content,
            status=artifact.status.value,
            version=artifact.version,
            created_by=artifact.created_by,
            created_at=artifact.created_at,
            updated_at=artifact.updated_at,
            tags=artifact.tags,
            metadata=artifact.metadata
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取追溯工件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取追溯工件失败: {str(e)}")


@router.put("/artifacts/{artifact_id}")
async def update_artifact(
    artifact_id: str,
    name: Optional[str] = None,
    description: Optional[str] = None,
    content: Optional[Dict[str, Any]] = None,
    status: Optional[str] = None,
    tags: Optional[List[str]] = None,
    metadata: Optional[Dict[str, Any]] = None
):
    """更新追溯工件"""
    try:
        traceability_manager = await get_traceability_manager()
        
        # 准备更新参数
        update_params = {}
        if name is not None:
            update_params["name"] = name
        if description is not None:
            update_params["description"] = description
        if content is not None:
            update_params["content"] = content
        if status is not None:
            update_params["status"] = ArtifactStatus(status)
        if tags is not None:
            update_params["tags"] = tags
        if metadata is not None:
            update_params["metadata"] = metadata
        
        # 执行更新
        success = await traceability_manager.update_artifact(
            artifact_id=artifact_id,
            updated_by="traceability_api",
            **update_params
        )
        
        if not success:
            raise HTTPException(status_code=404, detail="追溯工件不存在")
        
        # 获取更新后的工件
        artifact = await traceability_manager.get_artifact(artifact_id)
        
        return TraceabilityArtifactResponse(
            id=artifact.id,
            type=artifact.type.value,
            name=artifact.name,
            description=artifact.description,
            content=artifact.content,
            status=artifact.status.value,
            version=artifact.version,
            created_by=artifact.created_by,
            created_at=artifact.created_at,
            updated_at=artifact.updated_at,
            tags=artifact.tags,
            metadata=artifact.metadata
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新追溯工件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新追溯工件失败: {str(e)}")


@router.delete("/artifacts/{artifact_id}")
async def delete_artifact(artifact_id: str):
    """删除追溯工件"""
    try:
        traceability_manager = await get_traceability_manager()
        
        success = await traceability_manager.delete_artifact(
            artifact_id=artifact_id,
            deleted_by="traceability_api"
        )
        
        if not success:
            raise HTTPException(status_code=404, detail="追溯工件不存在")
        
        return {
            "artifact_id": artifact_id,
            "status": "deleted",
            "message": "追溯工件已删除"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除追溯工件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除追溯工件失败: {str(e)}")


@router.post("/links", response_model=TraceabilityLinkResponse)
async def create_link(request: TraceabilityLinkRequest):
    """创建追溯链接"""
    try:
        traceability_manager = await get_traceability_manager()
        
        link_id = await traceability_manager.create_link(
            source_id=request.source_id,
            target_id=request.target_id,
            link_type=TraceabilityType(request.type),
            strength=request.strength,
            bidirectional=request.bidirectional,
            created_by=request.created_by,
            metadata=request.metadata or {}
        )
        
        return TraceabilityLinkResponse(
            id=link_id,
            source_id=request.source_id,
            target_id=request.target_id,
            type=request.type,
            strength=request.strength,
            bidirectional=request.bidirectional,
            created_by=request.created_by,
            metadata=request.metadata or {}
        )
        
    except Exception as e:
        logger.error(f"创建追溯链接失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建追溯链接失败: {str(e)}")


@router.get("/artifacts/{artifact_id}/related")
async def get_related_artifacts(
    artifact_id: str,
    link_types: Optional[List[str]] = None,
    direction: str = "both"
):
    """获取相关追溯工件"""
    try:
        traceability_manager = await get_traceability_manager()
        
        # 转换链接类型
        trace_link_types = None
        if link_types:
            trace_link_types = [TraceabilityType(lt) for lt in link_types]
        
        related = await traceability_manager.get_related_artifacts(
            artifact_id=artifact_id,
            link_types=trace_link_types,
            direction=direction
        )
        
        related_artifacts = []
        for artifact, link in related:
            related_artifacts.append({
                "artifact": TraceabilityArtifactResponse(
                    id=artifact.id,
                    type=artifact.type.value,
                    name=artifact.name,
                    description=artifact.description,
                    content=artifact.content,
                    status=artifact.status.value,
                    version=artifact.version,
                    created_by=artifact.created_by,
                    created_at=artifact.created_at,
                    updated_at=artifact.updated_at,
                    tags=artifact.tags,
                    metadata=artifact.metadata
                ),
                "relationship": TraceabilityLinkResponse(
                    id=link.id,
                    source_id=link.source_id,
                    target_id=link.target_id,
                    type=link.type.value,
                    strength=link.strength,
                    bidirectional=link.bidirectional,
                    created_by=link.created_by,
                    metadata=link.metadata
                )
            })
        
        return {
            "artifact_id": artifact_id,
            "related_artifacts": related_artifacts,
            "total": len(related_artifacts)
        }
        
    except Exception as e:
        logger.error(f"获取相关追溯工件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取相关追溯工件失败: {str(e)}")


@router.get("/coverage-report", response_model=CoverageReportResponse)
async def get_coverage_report(
    source_type: str,
    target_type: str,
    link_types: Optional[List[str]] = None
):
    """获取覆盖率报告"""
    try:
        traceability_manager = await get_traceability_manager()
        
        # 转换链接类型
        trace_link_types = None
        if link_types:
            trace_link_types = [TraceabilityType(lt) for lt in link_types]
        
        coverage_report = await traceability_manager.generate_coverage_report(
            source_type=ArtifactType(source_type),
            target_type=ArtifactType(target_type),
            link_types=trace_link_types
        )
        
        return CoverageReportResponse(
            total_requirements=coverage_report.total_requirements,
            covered_requirements=coverage_report.covered_requirements,
            uncovered_requirements=coverage_report.uncovered_requirements,
            coverage_percentage=coverage_report.coverage_percentage,
            coverage_by_type=coverage_report.coverage_by_type,
            gaps=coverage_report.gaps,
            recommendations=coverage_report.recommendations,
            generated_at=coverage_report.generated_at
        )
        
    except Exception as e:
        logger.error(f"获取覆盖率报告失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取覆盖率报告失败: {str(e)}")


@router.get("/statistics", response_model=TraceabilityStatsResponse)
async def get_traceability_statistics():
    """获取追溯统计信息"""
    try:
        traceability_manager = await get_traceability_manager()
        stats = await traceability_manager.get_statistics()
        
        return TraceabilityStatsResponse(
            artifacts=stats.get("artifacts", {}),
            links=stats.get("links", {}),
            coverage=stats.get("coverage", {}),
            graph=stats.get("graph", {})
        )
        
    except Exception as e:
        logger.error(f"获取追溯统计信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取追溯统计信息失败: {str(e)}")


@router.get("/paths/{source_id}/{target_id}")
async def get_traceability_paths(
    source_id: str,
    target_id: str,
    max_depth: int = 5
):
    """获取追溯路径"""
    try:
        traceability_manager = await get_traceability_manager()
        paths = await traceability_manager.get_traceability_path(
            source_id=source_id,
            target_id=target_id,
            max_depth=max_depth
        )
        
        return {
            "source_id": source_id,
            "target_id": target_id,
            "paths": paths,
            "path_count": len(paths),
            "max_depth": max_depth
        }
        
    except Exception as e:
        logger.error(f"获取追溯路径失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取追溯路径失败: {str(e)}")


@router.get("/impact-analysis/{artifact_id}")
async def get_impact_analysis(artifact_id: str):
    """获取影响分析"""
    try:
        traceability_manager = await get_traceability_manager()
        impact = await traceability_manager.get_impact_analysis(artifact_id)
        
        return impact
        
    except Exception as e:
        logger.error(f"获取影响分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取影响分析失败: {str(e)}")


@router.get("/changes")
async def get_change_history(
    artifact_id: Optional[str] = None,
    limit: int = 100
):
    """获取变更历史"""
    try:
        traceability_manager = await get_traceability_manager()
        changes = await traceability_manager.get_change_history(
            artifact_id=artifact_id,
            limit=limit
        )
        
        change_responses = []
        for change in changes:
            change_responses.append({
                "id": change.id,
                "artifact_id": change.artifact_id,
                "change_type": change.change_type.value,
                "old_value": change.old_value,
                "new_value": change.new_value,
                "changed_by": change.changed_by,
                "changed_at": change.changed_at.isoformat(),
                "reason": change.reason,
                "metadata": change.metadata
            })
        
        return {
            "changes": change_responses,
            "total": len(change_responses),
            "artifact_id": artifact_id
        }
        
    except Exception as e:
        logger.error(f"获取变更历史失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取变更历史失败: {str(e)}")


@router.get("/export")
async def export_traceability_matrix():
    """导出完整的追溯矩阵"""
    try:
        traceability_manager = await get_traceability_manager()
        
        # 获取所有工件
        all_artifacts = []
        artifact_types = [ArtifactType.REQUIREMENT, ArtifactType.TESTCASE, 
                         ArtifactType.REVIEW, ArtifactType.DESIGN]
        
        for artifact_type in artifact_types:
            artifacts = await traceability_manager.get_artifacts_by_type(artifact_type)
            for artifact in artifacts:
                all_artifacts.append(artifact.to_dict())
        
        # 获取统计信息
        stats = await traceability_manager.get_statistics()
        
        # 获取变更历史
        changes = await traceability_manager.get_change_history(limit=1000)
        change_data = [change.to_dict() for change in changes]
        
        export_data = {
            "artifacts": all_artifacts,
            "statistics": stats,
            "change_history": change_data,
            "export_timestamp": datetime.now().isoformat(),
            "version": "1.0"
        }
        
        # 创建JSON文件流
        json_str = json.dumps(export_data, ensure_ascii=False, indent=2)
        json_bytes = json_str.encode('utf-8')
        
        return StreamingResponse(
            io.BytesIO(json_bytes),
            media_type="application/json",
            headers={"Content-Disposition": "attachment; filename=traceability_matrix_export.json"}
        )
        
    except Exception as e:
        logger.error(f"导出追溯矩阵失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导出追溯矩阵失败: {str(e)}")
