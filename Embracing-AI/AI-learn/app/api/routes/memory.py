"""
向量记忆管理API路由
"""

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
import json
import io

from models.schemas import (
    MemoryStoreRequest, MemorySearchRequest, MemorySearchResponse,
    MemoryStatsResponse
)
from core.logger import get_agent_logger
from core.memory import get_memory_manager

router = APIRouter()
logger = get_agent_logger("MemoryAPI")


@router.post("/store")
async def store_memory(request: MemoryStoreRequest):
    """存储记忆数据"""
    try:
        memory_manager = await get_memory_manager()
        
        result = await memory_manager.store_memory(
            collection_name=request.collection_name,
            content=request.content,
            metadata=request.metadata or {},
            document_id=request.document_id
        )
        
        return {
            "success": result,
            "collection_name": request.collection_name,
            "document_id": request.document_id,
            "message": "记忆数据存储成功" if result else "记忆数据存储失败"
        }
        
    except Exception as e:
        logger.error(f"存储记忆数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"存储记忆数据失败: {str(e)}")


@router.post("/search", response_model=MemorySearchResponse)
async def search_memory(request: MemorySearchRequest):
    """搜索记忆数据"""
    try:
        memory_manager = await get_memory_manager()
        
        results = await memory_manager.search_memory(
            collection_name=request.collection_name,
            query=request.query,
            n_results=request.n_results,
            metadata_filter=request.metadata_filter
        )
        
        return MemorySearchResponse(
            results=results,
            total=len(results),
            collection_name=request.collection_name,
            query=request.query
        )
        
    except Exception as e:
        logger.error(f"搜索记忆数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"搜索记忆数据失败: {str(e)}")


@router.get("/collections")
async def list_collections():
    """获取所有记忆集合"""
    try:
        memory_manager = await get_memory_manager()
        collections = await memory_manager.list_collections()
        
        collection_info = []
        for collection in collections:
            info = await memory_manager.get_collection_info(collection)
            collection_info.append(info)
        
        return {
            "collections": collection_info,
            "total": len(collection_info)
        }
        
    except Exception as e:
        logger.error(f"获取记忆集合列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取记忆集合列表失败: {str(e)}")


@router.get("/collections/{collection_name}")
async def get_collection_info(collection_name: str):
    """获取记忆集合信息"""
    try:
        memory_manager = await get_memory_manager()
        info = await memory_manager.get_collection_info(collection_name)
        
        if not info:
            raise HTTPException(status_code=404, detail="记忆集合不存在")
        
        return info
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取记忆集合信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取记忆集合信息失败: {str(e)}")


@router.delete("/collections/{collection_name}")
async def delete_collection(collection_name: str):
    """删除记忆集合"""
    try:
        memory_manager = await get_memory_manager()
        success = await memory_manager.delete_collection(collection_name)
        
        if not success:
            raise HTTPException(status_code=404, detail="记忆集合不存在")
        
        return {
            "collection_name": collection_name,
            "status": "deleted",
            "message": "记忆集合已删除"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除记忆集合失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除记忆集合失败: {str(e)}")


@router.get("/collections/{collection_name}/documents")
async def list_documents(
    collection_name: str,
    limit: int = 50,
    offset: int = 0
):
    """获取集合中的文档列表"""
    try:
        memory_manager = await get_memory_manager()
        documents = await memory_manager.get_collection_documents(
            collection_name=collection_name,
            limit=limit,
            offset=offset
        )
        
        return {
            "collection_name": collection_name,
            "documents": documents,
            "limit": limit,
            "offset": offset
        }
        
    except Exception as e:
        logger.error(f"获取文档列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取文档列表失败: {str(e)}")


@router.get("/collections/{collection_name}/documents/{document_id}")
async def get_document(collection_name: str, document_id: str):
    """获取特定文档"""
    try:
        memory_manager = await get_memory_manager()
        document = await memory_manager.get_document(collection_name, document_id)
        
        if not document:
            raise HTTPException(status_code=404, detail="文档不存在")
        
        return document
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文档失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取文档失败: {str(e)}")


@router.delete("/collections/{collection_name}/documents/{document_id}")
async def delete_document(collection_name: str, document_id: str):
    """删除文档"""
    try:
        memory_manager = await get_memory_manager()
        success = await memory_manager.delete_document(collection_name, document_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="文档不存在")
        
        return {
            "collection_name": collection_name,
            "document_id": document_id,
            "status": "deleted",
            "message": "文档已删除"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除文档失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除文档失败: {str(e)}")


@router.get("/statistics", response_model=MemoryStatsResponse)
async def get_memory_statistics():
    """获取记忆系统统计信息"""
    try:
        memory_manager = await get_memory_manager()
        stats = await memory_manager.get_statistics()
        
        return MemoryStatsResponse(
            total_collections=stats.get("total_collections", 0),
            total_documents=stats.get("total_documents", 0),
            total_size=stats.get("total_size", 0),
            collections_info=stats.get("collections_info", {}),
            system_info=stats.get("system_info", {})
        )
        
    except Exception as e:
        logger.error(f"获取记忆统计信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取记忆统计信息失败: {str(e)}")


@router.post("/similarity")
async def compute_similarity(
    collection_name: str,
    text1: str,
    text2: str
):
    """计算文本相似度"""
    try:
        memory_manager = await get_memory_manager()
        similarity = await memory_manager.compute_similarity(
            collection_name=collection_name,
            text1=text1,
            text2=text2
        )
        
        return {
            "text1": text1,
            "text2": text2,
            "similarity": similarity,
            "collection_name": collection_name
        }
        
    except Exception as e:
        logger.error(f"计算文本相似度失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"计算文本相似度失败: {str(e)}")


@router.post("/batch-store")
async def batch_store_memory(
    collection_name: str,
    documents: List[Dict[str, Any]]
):
    """批量存储记忆数据"""
    try:
        memory_manager = await get_memory_manager()
        
        results = []
        for doc in documents:
            result = await memory_manager.store_memory(
                collection_name=collection_name,
                content=doc.get("content", ""),
                metadata=doc.get("metadata", {}),
                document_id=doc.get("document_id")
            )
            results.append({
                "document_id": doc.get("document_id"),
                "success": result
            })
        
        success_count = sum(1 for r in results if r["success"])
        
        return {
            "collection_name": collection_name,
            "total_documents": len(documents),
            "success_count": success_count,
            "failed_count": len(documents) - success_count,
            "results": results
        }
        
    except Exception as e:
        logger.error(f"批量存储记忆数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"批量存储记忆数据失败: {str(e)}")


@router.get("/agent-memory/{agent_id}")
async def get_agent_memory(agent_id: str):
    """获取Agent的记忆数据"""
    try:
        memory_manager = await get_memory_manager()
        memory_data = await memory_manager.get_agent_memory(agent_id)
        
        return {
            "agent_id": agent_id,
            "memory_data": memory_data,
            "total_items": len(memory_data) if memory_data else 0
        }
        
    except Exception as e:
        logger.error(f"获取Agent记忆数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取Agent记忆数据失败: {str(e)}")


@router.delete("/agent-memory/{agent_id}")
async def clear_agent_memory(agent_id: str):
    """清除Agent的记忆数据"""
    try:
        memory_manager = await get_memory_manager()
        success = await memory_manager.clear_agent_memory(agent_id)
        
        return {
            "agent_id": agent_id,
            "status": "cleared" if success else "failed",
            "message": f"Agent {agent_id} 的记忆数据已清除" if success else "清除失败"
        }
        
    except Exception as e:
        logger.error(f"清除Agent记忆数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清除Agent记忆数据失败: {str(e)}")


@router.get("/export/{collection_name}")
async def export_collection(collection_name: str):
    """导出记忆集合数据"""
    try:
        memory_manager = await get_memory_manager()
        
        # 获取集合信息
        collection_info = await memory_manager.get_collection_info(collection_name)
        if not collection_info:
            raise HTTPException(status_code=404, detail="记忆集合不存在")
        
        # 获取所有文档
        documents = await memory_manager.get_collection_documents(
            collection_name=collection_name,
            limit=10000  # 大量导出
        )
        
        export_data = {
            "collection_name": collection_name,
            "collection_info": collection_info,
            "documents": documents,
            "export_timestamp": datetime.now().isoformat(),
            "version": "1.0"
        }
        
        # 创建JSON文件流
        json_str = json.dumps(export_data, ensure_ascii=False, indent=2)
        json_bytes = json_str.encode('utf-8')
        
        return StreamingResponse(
            io.BytesIO(json_bytes),
            media_type="application/json",
            headers={"Content-Disposition": f"attachment; filename=memory_{collection_name}_export.json"}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导出记忆集合失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导出记忆集合失败: {str(e)}")


@router.post("/backup")
async def backup_memory_system():
    """备份整个记忆系统"""
    try:
        memory_manager = await get_memory_manager()
        backup_data = await memory_manager.create_backup()
        
        # 创建JSON文件流
        json_str = json.dumps(backup_data, ensure_ascii=False, indent=2)
        json_bytes = json_str.encode('utf-8')
        
        return StreamingResponse(
            io.BytesIO(json_bytes),
            media_type="application/json",
            headers={"Content-Disposition": f"attachment; filename=memory_system_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"}
        )
        
    except Exception as e:
        logger.error(f"备份记忆系统失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"备份记忆系统失败: {str(e)}")


@router.post("/restore")
async def restore_memory_system(backup_data: Dict[str, Any]):
    """恢复记忆系统"""
    try:
        memory_manager = await get_memory_manager()
        success = await memory_manager.restore_backup(backup_data)
        
        return {
            "status": "restored" if success else "failed",
            "message": "记忆系统恢复成功" if success else "记忆系统恢复失败"
        }
        
    except Exception as e:
        logger.error(f"恢复记忆系统失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"恢复记忆系统失败: {str(e)}")