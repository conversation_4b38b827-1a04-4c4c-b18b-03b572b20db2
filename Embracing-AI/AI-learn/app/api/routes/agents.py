"""
智能体管理API路由
"""

import asyncio
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends, UploadFile, File
from fastapi.responses import StreamingResponse
import json
import io

from models.schemas import (
    AgentCreateRequest, AgentResponse, AgentStatusResponse,
    AgentExecuteRequest, AgentExecuteResponse, AgentListResponse
)
from models.enums import AgentType, AgentStatus
from agents import (
    RequirementAnalysisAgent, RequirementReviewAgent,
    TestCaseGenerationAgent, TestCaseReviewAgent
)
from core.logger import get_agent_logger
from core.traceability import get_traceability_manager, ArtifactType
from core.memory import get_memory_manager

router = APIRouter()
logger = get_agent_logger("AgentsAPI")

# 全局Agent实例管理
_agent_instances: Dict[str, Any] = {}


async def get_agent_instance(agent_id: str, agent_type: AgentType):
    """获取或创建Agent实例"""
    if agent_id not in _agent_instances:
        # 根据类型创建Agent实例
        if agent_type == AgentType.REQUIREMENT_ANALYSIS:
            agent = RequirementAnalysisAgent(agent_id)
        elif agent_type == AgentType.REQUIREMENT_REVIEW:
            agent = RequirementReviewAgent(agent_id)
        elif agent_type == AgentType.TESTCASE_GENERATION:
            agent = TestCaseGenerationAgent(agent_id)
        elif agent_type == AgentType.TESTCASE_REVIEW:
            agent = TestCaseReviewAgent(agent_id)
        else:
            raise HTTPException(status_code=400, detail=f"不支持的Agent类型: {agent_type}")
        
        await agent.initialize()
        _agent_instances[agent_id] = agent
        
        logger.info(f"创建Agent实例: {agent_id} ({agent_type.value})")
    
    return _agent_instances[agent_id]


@router.post("/create", response_model=AgentResponse)
async def create_agent(request: AgentCreateRequest):
    """创建新的Agent实例"""
    try:
        agent = await get_agent_instance(request.agent_id, request.agent_type)
        
        # 记录到追溯矩阵
        traceability_manager = await get_traceability_manager()
        await traceability_manager.create_artifact(
            artifact_type=ArtifactType.DESIGN,
            name=f"Agent_{request.agent_id}",
            description=f"{request.agent_type.value} Agent实例",
            content={
                "agent_id": request.agent_id,
                "agent_type": request.agent_type.value,
                "config": request.config or {}
            },
            created_by="system",
            tags=["agent", request.agent_type.value]
        )
        
        return AgentResponse(
            agent_id=request.agent_id,
            agent_type=request.agent_type,
            status=AgentStatus.IDLE,
            created_at=agent.created_at,
            config=request.config or {}
        )
        
    except Exception as e:
        logger.error(f"创建Agent失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建Agent失败: {str(e)}")


@router.get("/list", response_model=AgentListResponse)
async def list_agents():
    """获取所有Agent实例列表"""
    try:
        agents = []
        for agent_id, agent in _agent_instances.items():
            agents.append(AgentResponse(
                agent_id=agent_id,
                agent_type=agent.agent_type,
                status=agent.status,
                created_at=agent.created_at,
                config=getattr(agent, 'config', {})
            ))
        
        return AgentListResponse(
            agents=agents,
            total=len(agents)
        )
        
    except Exception as e:
        logger.error(f"获取Agent列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取Agent列表失败: {str(e)}")


@router.get("/{agent_id}/status", response_model=AgentStatusResponse)
async def get_agent_status(agent_id: str):
    """获取Agent状态"""
    try:
        if agent_id not in _agent_instances:
            raise HTTPException(status_code=404, detail="Agent不存在")
        
        agent = _agent_instances[agent_id]
        
        return AgentStatusResponse(
            agent_id=agent_id,
            status=agent.status,
            current_task=getattr(agent, 'current_task', None),
            progress=getattr(agent, 'progress', 0.0),
            last_activity=getattr(agent, 'last_activity', None),
            error_message=getattr(agent, 'error_message', None)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取Agent状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取Agent状态失败: {str(e)}")


@router.post("/{agent_id}/execute", response_model=AgentExecuteResponse)
async def execute_agent(
    agent_id: str,
    request: AgentExecuteRequest,
    background_tasks: BackgroundTasks
):
    """执行Agent任务"""
    try:
        if agent_id not in _agent_instances:
            raise HTTPException(status_code=404, detail="Agent不存在")
        
        agent = _agent_instances[agent_id]
        
        # 异步执行任务
        async def execute_task():
            try:
                if agent.agent_type == AgentType.REQUIREMENT_ANALYSIS:
                    result = await agent.analyze_requirements(
                        input_data=request.input_data,
                        context=request.context or {}
                    )
                elif agent.agent_type == AgentType.REQUIREMENT_REVIEW:
                    result = await agent.review_requirements(
                        requirements=request.input_data,
                        context=request.context or {}
                    )
                elif agent.agent_type == AgentType.TESTCASE_GENERATION:
                    result = await agent.generate_test_cases(
                        requirements=request.input_data,
                        context=request.context or {}
                    )
                elif agent.agent_type == AgentType.TESTCASE_REVIEW:
                    result = await agent.review_test_cases(
                        test_cases=request.input_data,
                        context=request.context or {}
                    )
                else:
                    raise ValueError(f"不支持的Agent类型: {agent.agent_type}")
                
                # 保存结果到内存
                memory_manager = await get_memory_manager()
                await memory_manager.store_agent_result(
                    agent_id=agent_id,
                    task_id=request.task_id,
                    result=result
                )
                
                logger.info(f"Agent {agent_id} 任务执行完成: {request.task_id}")
                
            except Exception as e:
                logger.error(f"Agent {agent_id} 任务执行失败: {str(e)}")
                agent.error_message = str(e)
                agent.status = AgentStatus.ERROR
        
        # 添加到后台任务
        background_tasks.add_task(execute_task)
        
        # 更新Agent状态
        agent.status = AgentStatus.PROCESSING
        agent.current_task = request.task_id
        
        return AgentExecuteResponse(
            task_id=request.task_id,
            agent_id=agent_id,
            status="submitted",
            message="任务已提交，正在后台执行"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"执行Agent任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"执行Agent任务失败: {str(e)}")


@router.get("/{agent_id}/results/{task_id}")
async def get_task_result(agent_id: str, task_id: str):
    """获取任务执行结果"""
    try:
        memory_manager = await get_memory_manager()
        result = await memory_manager.get_agent_result(agent_id, task_id)
        
        if result is None:
            raise HTTPException(status_code=404, detail="任务结果不存在")
        
        return {
            "task_id": task_id,
            "agent_id": agent_id,
            "result": result,
            "status": "completed"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务结果失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务结果失败: {str(e)}")


@router.post("/{agent_id}/upload-document")
async def upload_document(
    agent_id: str,
    file: UploadFile = File(...),
    task_id: Optional[str] = None
):
    """上传文档给Agent处理"""
    try:
        if agent_id not in _agent_instances:
            raise HTTPException(status_code=404, detail="Agent不存在")
        
        agent = _agent_instances[agent_id]
        
        # 检查文件类型
        if not file.filename.endswith(('.docx', '.txt', '.md')):
            raise HTTPException(status_code=400, detail="不支持的文件类型")
        
        # 读取文件内容
        content = await file.read()
        
        # 根据Agent类型处理文档
        if agent.agent_type == AgentType.REQUIREMENT_ANALYSIS:
            if file.filename.endswith('.docx'):
                result = await agent.analyze_word_document(content)
            else:
                result = await agent.analyze_requirements(
                    input_data=content.decode('utf-8'),
                    context={"source": "uploaded_document", "filename": file.filename}
                )
        else:
            raise HTTPException(status_code=400, detail="该Agent类型不支持文档上传")
        
        # 保存结果
        memory_manager = await get_memory_manager()
        result_task_id = task_id or f"upload_{agent_id}_{int(asyncio.get_event_loop().time())}"
        await memory_manager.store_agent_result(
            agent_id=agent_id,
            task_id=result_task_id,
            result=result
        )
        
        return {
            "task_id": result_task_id,
            "agent_id": agent_id,
            "filename": file.filename,
            "status": "processed",
            "message": "文档处理完成"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文档上传处理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"文档上传处理失败: {str(e)}")


@router.delete("/{agent_id}")
async def delete_agent(agent_id: str):
    """删除Agent实例"""
    try:
        if agent_id not in _agent_instances:
            raise HTTPException(status_code=404, detail="Agent不存在")
        
        agent = _agent_instances[agent_id]
        
        # 清理资源
        if hasattr(agent, 'cleanup'):
            await agent.cleanup()
        
        # 从实例管理中移除
        del _agent_instances[agent_id]
        
        logger.info(f"删除Agent实例: {agent_id}")
        
        return {
            "agent_id": agent_id,
            "status": "deleted",
            "message": "Agent实例已删除"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除Agent失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除Agent失败: {str(e)}")


@router.post("/{agent_id}/reset")
async def reset_agent(agent_id: str):
    """重置Agent状态"""
    try:
        if agent_id not in _agent_instances:
            raise HTTPException(status_code=404, detail="Agent不存在")
        
        agent = _agent_instances[agent_id]
        
        # 重置状态
        agent.status = AgentStatus.IDLE
        agent.current_task = None
        agent.progress = 0.0
        agent.error_message = None
        
        # 清理内存中的临时数据
        if hasattr(agent, 'reset'):
            await agent.reset()
        
        logger.info(f"重置Agent状态: {agent_id}")
        
        return {
            "agent_id": agent_id,
            "status": "reset",
            "message": "Agent状态已重置"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重置Agent失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"重置Agent失败: {str(e)}")


@router.get("/{agent_id}/export")
async def export_agent_data(agent_id: str):
    """导出Agent数据"""
    try:
        if agent_id not in _agent_instances:
            raise HTTPException(status_code=404, detail="Agent不存在")
        
        agent = _agent_instances[agent_id]
        
        # 收集Agent数据
        export_data = {
            "agent_id": agent_id,
            "agent_type": agent.agent_type.value,
            "status": agent.status.value,
            "created_at": agent.created_at.isoformat(),
            "config": getattr(agent, 'config', {}),
            "statistics": getattr(agent, 'statistics', {}),
            "memory_data": []
        }
        
        # 获取相关的内存数据
        memory_manager = await get_memory_manager()
        memory_data = await memory_manager.get_agent_memory(agent_id)
        if memory_data:
            export_data["memory_data"] = memory_data
        
        # 创建JSON文件流
        json_str = json.dumps(export_data, ensure_ascii=False, indent=2)
        json_bytes = json_str.encode('utf-8')
        
        return StreamingResponse(
            io.BytesIO(json_bytes),
            media_type="application/json",
            headers={"Content-Disposition": f"attachment; filename=agent_{agent_id}_export.json"}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导出Agent数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导出Agent数据失败: {str(e)}")