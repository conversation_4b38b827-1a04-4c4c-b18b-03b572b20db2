"""
测试用例管理API路由
"""

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks
from fastapi.responses import StreamingResponse
import json
import io

from models.schemas import (
    TestCaseGenerationRequest, TestCaseGenerationResponse,
    TestCaseReviewRequest, TestCaseReviewResponse,
    TestCaseListResponse, TestCaseResponse
)
from core.logger import get_agent_logger
from core.traceability import get_traceability_manager, ArtifactType, ArtifactStatus, TraceabilityType
from core.memory import get_memory_manager
from agents import TestCaseGenerationAgent, TestCaseReviewAgent

router = APIRouter()
logger = get_agent_logger("TestCasesAPI")


@router.post("/generate", response_model=TestCaseGenerationResponse)
async def generate_test_cases(
    request: TestCaseGenerationRequest,
    background_tasks: BackgroundTasks
):
    """生成测试用例"""
    try:
        # 创建测试用例生成Agent
        agent = TestCaseGenerationAgent("tc_generation_api")
        await agent.initialize()
        
        # 执行测试用例生成
        result = await agent.generate_test_cases(
            requirements=request.requirements,
            context=request.context or {}
        )
        
        # 保存到追溯矩阵
        traceability_manager = await get_traceability_manager()
        
        # 为每个测试用例创建工件
        testcase_ids = []
        for tc in result.get("new_test_cases", []):
            artifact_id = await traceability_manager.create_artifact(
                artifact_type=ArtifactType.TESTCASE,
                name=tc.get("name", "未命名测试用例"),
                description=tc.get("description", ""),
                content=tc,
                created_by="testcase_generation_api",
                tags=tc.get("tags", [])
            )
            testcase_ids.append(artifact_id)
            
            # 建立需求到测试用例的追溯链接
            for req_id in request.requirement_ids or []:
                await traceability_manager.create_link(
                    source_id=req_id,
                    target_id=artifact_id,
                    link_type=TraceabilityType.TESTS,
                    created_by="testcase_generation_api"
                )
        
        # 保存生成结果到内存
        memory_manager = await get_memory_manager()
        await memory_manager.store_agent_result(
            agent_id="tc_generation_api",
            task_id=f"generation_{hash(str(request.requirements))}",
            result=result
        )
        
        return TestCaseGenerationResponse(
            feature_id=result.get("feature_id"),
            new_test_cases=result.get("new_test_cases", []),
            remaining_scenarios=result.get("remaining_scenarios", []),
            generated_ids=testcase_ids,
            has_more=result.get("has_more", False),
            version=result.get("version", 1)
        )
        
    except Exception as e:
        logger.error(f"测试用例生成失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"测试用例生成失败: {str(e)}")


@router.post("/review", response_model=TestCaseReviewResponse)
async def review_test_cases(request: TestCaseReviewRequest):
    """评审测试用例"""
    try:
        # 创建测试用例评审Agent
        agent = TestCaseReviewAgent("tc_review_api")
        await agent.initialize()
        
        # 执行测试用例评审
        result = await agent.review_test_cases(
            test_cases=request.test_cases,
            context=request.context or {}
        )
        
        # 更新追溯矩阵中的测试用例状态
        traceability_manager = await get_traceability_manager()
        
        for tc_id in request.testcase_ids or []:
            # 更新测试用例状态
            await traceability_manager.update_artifact(
                artifact_id=tc_id,
                status=ArtifactStatus.UNDER_REVIEW,
                metadata={"review_result": result},
                updated_by="testcase_review_api"
            )
        
        # 创建评审记录工件
        review_artifact_id = await traceability_manager.create_artifact(
            artifact_type=ArtifactType.REVIEW,
            name="测试用例评审记录",
            description="测试用例评审结果记录",
            content=result,
            created_by="testcase_review_api",
            tags=["评审", "测试用例"]
        )
        
        return TestCaseReviewResponse(
            feature_id=result.get("feature_id"),
            reviewed_cases=result.get("reviewed_cases", []),
            updated_remaining_scenarios=result.get("updated_remaining_scenarios", []),
            traceability_matrix=result.get("traceability_matrix", []),
            version=result.get("version", 1),
            has_more=result.get("has_more", False),
            review_id=review_artifact_id
        )
        
    except Exception as e:
        logger.error(f"测试用例评审失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"测试用例评审失败: {str(e)}")


@router.get("/list", response_model=TestCaseListResponse)
async def list_test_cases(
    status: Optional[str] = None,
    tag: Optional[str] = None,
    requirement_id: Optional[str] = None,
    limit: int = 50,
    offset: int = 0
):
    """获取测试用例列表"""
    try:
        traceability_manager = await get_traceability_manager()
        
        # 获取测试用例工件
        if status:
            artifacts = await traceability_manager.get_artifacts_by_status(
                ArtifactStatus(status)
            )
            # 过滤出测试用例类型
            artifacts = [a for a in artifacts if a.type == ArtifactType.TESTCASE]
        else:
            artifacts = await traceability_manager.get_artifacts_by_type(
                ArtifactType.TESTCASE
            )
        
        # 按标签过滤
        if tag:
            artifacts = [a for a in artifacts if tag in a.tags]
        
        # 按需求ID过滤
        if requirement_id:
            # 获取与指定需求相关的测试用例
            related = await traceability_manager.get_related_artifacts(
                artifact_id=requirement_id,
                link_types=[TraceabilityType.TESTS],
                direction="forward"
            )
            related_ids = {artifact.id for artifact, _ in related}
            artifacts = [a for a in artifacts if a.id in related_ids]
        
        # 分页
        total = len(artifacts)
        artifacts = artifacts[offset:offset + limit]
        
        # 转换为响应格式
        test_cases = []
        for artifact in artifacts:
            test_cases.append(TestCaseResponse(
                id=artifact.id,
                name=artifact.name,
                description=artifact.description,
                content=artifact.content,
                status=artifact.status.value,
                version=artifact.version,
                created_at=artifact.created_at,
                updated_at=artifact.updated_at,
                tags=artifact.tags
            ))
        
        return TestCaseListResponse(
            test_cases=test_cases,
            total=total,
            limit=limit,
            offset=offset
        )
        
    except Exception as e:
        logger.error(f"获取测试用例列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取测试用例列表失败: {str(e)}")


@router.get("/{testcase_id}", response_model=TestCaseResponse)
async def get_test_case(testcase_id: str):
    """获取单个测试用例详情"""
    try:
        traceability_manager = await get_traceability_manager()
        artifact = await traceability_manager.get_artifact(testcase_id)
        
        if not artifact or artifact.type != ArtifactType.TESTCASE:
            raise HTTPException(status_code=404, detail="测试用例不存在")
        
        return TestCaseResponse(
            id=artifact.id,
            name=artifact.name,
            description=artifact.description,
            content=artifact.content,
            status=artifact.status.value,
            version=artifact.version,
            created_at=artifact.created_at,
            updated_at=artifact.updated_at,
            tags=artifact.tags
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取测试用例详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取测试用例详情失败: {str(e)}")


@router.put("/{testcase_id}")
async def update_test_case(
    testcase_id: str,
    name: Optional[str] = None,
    description: Optional[str] = None,
    content: Optional[Dict[str, Any]] = None,
    status: Optional[str] = None,
    tags: Optional[List[str]] = None
):
    """更新测试用例"""
    try:
        traceability_manager = await get_traceability_manager()
        
        # 准备更新参数
        update_params = {}
        if name is not None:
            update_params["name"] = name
        if description is not None:
            update_params["description"] = description
        if content is not None:
            update_params["content"] = content
        if status is not None:
            update_params["status"] = ArtifactStatus(status)
        if tags is not None:
            update_params["tags"] = tags
        
        # 执行更新
        success = await traceability_manager.update_artifact(
            artifact_id=testcase_id,
            updated_by="testcase_update_api",
            **update_params
        )
        
        if not success:
            raise HTTPException(status_code=404, detail="测试用例不存在")
        
        # 获取更新后的测试用例
        artifact = await traceability_manager.get_artifact(testcase_id)
        
        return TestCaseResponse(
            id=artifact.id,
            name=artifact.name,
            description=artifact.description,
            content=artifact.content,
            status=artifact.status.value,
            version=artifact.version,
            created_at=artifact.created_at,
            updated_at=artifact.updated_at,
            tags=artifact.tags
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新测试用例失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新测试用例失败: {str(e)}")


@router.delete("/{testcase_id}")
async def delete_test_case(testcase_id: str):
    """删除测试用例"""
    try:
        traceability_manager = await get_traceability_manager()
        
        success = await traceability_manager.delete_artifact(
            artifact_id=testcase_id,
            deleted_by="testcase_delete_api"
        )
        
        if not success:
            raise HTTPException(status_code=404, detail="测试用例不存在")
        
        return {
            "testcase_id": testcase_id,
            "status": "deleted",
            "message": "测试用例已删除"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除测试用例失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除测试用例失败: {str(e)}")


@router.get("/{testcase_id}/coverage")
async def get_test_case_coverage(testcase_id: str):
    """获取测试用例覆盖情况"""
    try:
        traceability_manager = await get_traceability_manager()
        
        # 获取测试用例覆盖的需求
        related = await traceability_manager.get_related_artifacts(
            artifact_id=testcase_id,
            link_types=[TraceabilityType.TESTS],
            direction="backward"
        )
        
        covered_requirements = []
        for artifact, link in related:
            if artifact.type == ArtifactType.REQUIREMENT:
                covered_requirements.append({
                    "requirement_id": artifact.id,
                    "requirement_name": artifact.name,
                    "requirement_description": artifact.description,
                    "coverage_strength": link.strength
                })
        
        return {
            "testcase_id": testcase_id,
            "covered_requirements": covered_requirements,
            "coverage_count": len(covered_requirements)
        }
        
    except Exception as e:
        logger.error(f"获取测试用例覆盖情况失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取测试用例覆盖情况失败: {str(e)}")


@router.post("/batch-generate")
async def batch_generate_test_cases(
    requirement_ids: List[str],
    generation_config: Optional[Dict[str, Any]] = None,
    background_tasks: BackgroundTasks = None
):
    """批量生成测试用例"""
    try:
        traceability_manager = await get_traceability_manager()
        
        # 获取所有需求
        requirements = []
        for req_id in requirement_ids:
            artifact = await traceability_manager.get_artifact(req_id)
            if artifact and artifact.type == ArtifactType.REQUIREMENT:
                requirements.append(artifact.content)
        
        if not requirements:
            raise HTTPException(status_code=400, detail="未找到有效的需求")
        
        # 创建测试用例生成Agent
        agent = TestCaseGenerationAgent("batch_tc_generation")
        await agent.initialize()
        
        # 批量生成测试用例
        all_results = []
        generated_testcase_ids = []
        
        for i, req_content in enumerate(requirements):
            result = await agent.generate_test_cases(
                requirements=[req_content],
                context=generation_config or {}
            )
            
            # 为每个测试用例创建工件
            for tc in result.get("new_test_cases", []):
                artifact_id = await traceability_manager.create_artifact(
                    artifact_type=ArtifactType.TESTCASE,
                    name=tc.get("name", f"批量生成测试用例_{i+1}"),
                    description=tc.get("description", ""),
                    content=tc,
                    created_by="batch_testcase_generation_api",
                    tags=tc.get("tags", ["批量生成"])
                )
                generated_testcase_ids.append(artifact_id)
                
                # 建立需求到测试用例的追溯链接
                await traceability_manager.create_link(
                    source_id=requirement_ids[i],
                    target_id=artifact_id,
                    link_type=TraceabilityType.TESTS,
                    created_by="batch_testcase_generation_api"
                )
            
            all_results.append(result)
        
        return {
            "requirement_ids": requirement_ids,
            "generated_testcase_ids": generated_testcase_ids,
            "generation_results": all_results,
            "total_generated": len(generated_testcase_ids),
            "status": "completed"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量生成测试用例失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"批量生成测试用例失败: {str(e)}")


@router.get("/coverage-report")
async def get_coverage_report():
    """获取测试用例覆盖率报告"""
    try:
        traceability_manager = await get_traceability_manager()
        
        # 生成覆盖率报告
        coverage_report = await traceability_manager.generate_coverage_report(
            source_type=ArtifactType.REQUIREMENT,
            target_type=ArtifactType.TESTCASE,
            link_types=[TraceabilityType.TESTS]
        )
        
        return {
            "total_requirements": coverage_report.total_requirements,
            "covered_requirements": coverage_report.covered_requirements,
            "uncovered_requirements": coverage_report.uncovered_requirements,
            "coverage_percentage": coverage_report.coverage_percentage,
            "coverage_by_type": coverage_report.coverage_by_type,
            "gaps": coverage_report.gaps,
            "recommendations": coverage_report.recommendations,
            "generated_at": coverage_report.generated_at.isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取覆盖率报告失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取覆盖率报告失败: {str(e)}")


@router.get("/{testcase_id}/export")
async def export_test_case(testcase_id: str):
    """导出测试用例数据"""
    try:
        traceability_manager = await get_traceability_manager()
        artifact = await traceability_manager.get_artifact(testcase_id)
        
        if not artifact or artifact.type != ArtifactType.TESTCASE:
            raise HTTPException(status_code=404, detail="测试用例不存在")
        
        # 获取相关数据
        related = await traceability_manager.get_related_artifacts(testcase_id)
        
        export_data = {
            "test_case": artifact.to_dict(),
            "related_artifacts": [
                {
                    "artifact": rel_artifact.to_dict(),
                    "relationship": rel_link.to_dict()
                }
                for rel_artifact, rel_link in related
            ],
            "export_timestamp": artifact.updated_at.isoformat()
        }
        
        # 创建JSON文件流
        json_str = json.dumps(export_data, ensure_ascii=False, indent=2)
        json_bytes = json_str.encode('utf-8')
        
        return StreamingResponse(
            io.BytesIO(json_bytes),
            media_type="application/json",
            headers={"Content-Disposition": f"attachment; filename=testcase_{testcase_id}_export.json"}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导出测试用例数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"导出测试用例数据失败: {str(e)}")
