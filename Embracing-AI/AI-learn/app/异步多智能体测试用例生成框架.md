# 异步多智能体测试用例生成框架

## Core Features

- 需求分析Agent

- 需求评审Agent

- 测试用例生成Agent

- 测试用例评审Agent

- 异步协作机制

- 向量记忆系统

- 可追溯性矩阵

- 多轮交互对齐

## Tech Stack

{
  "Backend": "Python + FastAPI + LangChain + AutoGen + AsyncIO + ChromaDB + Ollama(DeepSeek-R1:1.5b) + Celery + Redis + python-docx + Pydantic + Loguru"
}

## Design

采用Material Design风格的企业级开发工具界面，深蓝色主色调配合科技蓝强调色，包含主控制台、需求输入、Agent协作监控和结果输出四个核心界面，注重工作流可视化和实时状态监控

## Plan

Note: 

- [ ] is holding
- [/] is doing
- [X] is done

---

[X] 环境配置和依赖安装

[X] ChromaDB向量数据库初始化和配置

[X] Ollama本地模型部署和API连接测试

[X] 基础Agent抽象类和公共组件开发

[X] 需求分析Agent实现（包含Word文档解析和思维链）

[X] 需求评审Agent实现（包含评审逻辑和反思机制）

[X] 测试用例生成Agent实现（包含多种设计方法和状态管理）

[X] 测试用例评审Agent实现（包含质量检查和完整性验证）

[X] 异步协作框架和消息传递机制开发

[X] 向量记忆系统和知识共享机制实现

[X] 可追溯性矩阵和状态管理系统开发

[X] FastAPI后端服务和路由配置

[X] JSON格式规范和数据验证实现

[X] 多轮交互和协作对齐机制测试

[X] 系统集成测试和性能优化
