# 异步多智能体测试用例生成框架依赖包

# Web框架和API
fastapi>=0.104.1
uvicorn[standard]>=0.24.0
starlette>=0.27.0
python-multipart>=0.0.6

# 数据验证和配置
pydantic>=2.5.0
pydantic-settings>=2.1.0

# 异步支持
aiofiles>=23.2.1

# HTTP客户端
httpx>=0.25.2
requests>=2.31.0

# 向量数据库
chromadb>=0.4.18

# Redis和缓存
redis>=5.0.1
aioredis>=2.0.1

# 任务队列
celery>=5.3.4

# 日志系统
loguru>=0.7.2

# 文档处理
python-docx>=1.1.0

# AI和机器学习
langchain>=0.0.350
langchain-community>=0.0.10
autogen-agentchat>=0.2.0

# Ollama客户端
ollama>=0.1.7

# 数据处理
numpy>=1.24.0
pandas>=2.0.0

# JSON处理
jsonschema>=4.20.0

# 时间处理
python-dateutil>=2.8.2

# 类型检查
typing-extensions>=4.8.0

# 开发和测试工具
pytest>=7.4.3
pytest-asyncio>=0.21.1
pytest-mock>=3.12.0

# 代码质量
black>=23.11.0
flake8>=6.1.0
mypy>=1.7.0

# 环境变量
python-dotenv>=1.0.0

# 系统工具
psutil>=5.9.6

# 加密
cryptography>=41.0.7

# 时区处理
pytz>=2023.3

# 文件监控
watchdog>=3.0.0

# 数据库连接池
sqlalchemy>=2.0.23
alembic>=1.13.0

# 模板引擎
jinja2>=3.1.2

# 文件格式支持
openpyxl>=3.1.2

# 图像处理
pillow>=10.1.0

# 网络请求增强
urllib3>=2.1.0

# 字符串处理
python-slugify>=8.0.1

# 缓存工具
cachetools>=5.3.2

# 线程池
threadpoolctl>=3.2.0

# WebSocket支持
websockets>=12.0

# 监控
prometheus-client>=0.19.0

# 错误追踪
sentry-sdk[fastapi]>=1.38.0

# 任务调度
apscheduler>=3.10.4

# 消息队列
kombu>=5.3.4

# 序列化优化
orjson>=3.9.10

# 编码检测
chardet>=5.2.0

# 文本处理
jieba>=0.42.1

# 机器学习工具
scikit-learn>=1.3.2

# 向量计算
faiss-cpu>=1.7.4

# 嵌入模型
sentence-transformers>=2.2.2

# XML处理
lxml>=4.9.3

# YAML处理
pyyaml>=6.0.1

# 文件锁
filelock>=3.13.1

# 文件监控
watchfiles>=0.21.0

# 装饰器
decorator>=5.1.1
