"""
系统配置管理
"""

import os
from typing import Optional, List
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """系统配置类"""
    
    # 应用基础配置
    app_name: str = Field(default="异步多智能体测试用例生成框架", description="应用名称")
    app_version: str = Field(default="1.0.0", description="应用版本")
    debug: bool = Field(default=True, description="调试模式")
    
    # 服务器配置
    host: str = Field(default="0.0.0.0", description="服务器地址")
    port: int = Field(default=8000, description="服务器端口")
    
    # Ollama配置
    ollama_base_url: str = Field(default="http://localhost:11434", description="Ollama服务地址")
    ollama_model: str = Field(default="deepseek-r1:1.5b", description="使用的模型名称")
    ollama_timeout: int = Field(default=300, description="模型请求超时时间(秒)")
    
    # ChromaDB配置
    chroma_host: str = Field(default="localhost", description="ChromaDB地址")
    chroma_port: int = Field(default=8001, description="ChromaDB端口")
    chroma_collection_name: str = Field(default="agent_memory", description="向量集合名称")
    chroma_persist_directory: str = Field(default="./chroma_db", description="持久化目录")
    
    # Redis配置
    redis_url: str = Field(default="redis://localhost:6379/0", description="Redis连接URL")
    redis_password: Optional[str] = Field(default=None, description="Redis密码")
    
    # Celery配置
    celery_broker_url: str = Field(default="redis://localhost:6379/1", description="Celery消息代理")
    celery_result_backend: str = Field(default="redis://localhost:6379/2", description="Celery结果后端")
    
    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    log_file: str = Field(default="logs/app.log", description="日志文件路径")
    log_rotation: str = Field(default="1 day", description="日志轮转周期")
    log_retention: str = Field(default="30 days", description="日志保留时间")
    
    # Agent配置
    max_iterations: int = Field(default=10, description="Agent最大迭代次数")
    reflection_threshold: float = Field(default=0.8, description="反思触发阈值")
    collaboration_timeout: int = Field(default=600, description="协作超时时间(秒)")
    
    # 文档处理配置
    max_file_size: int = Field(default=10 * 1024 * 1024, description="最大文件大小(字节)")
    supported_formats: list = Field(default=["docx", "doc", "txt"], description="支持的文档格式")
    
    # 测试用例配置
    max_test_cases_per_feature: int = Field(default=50, description="每个功能点最大测试用例数")
    test_case_priority_levels: list = Field(default=["high", "medium", "low"], description="测试用例优先级")

    # CORS配置
    ALLOWED_ORIGINS: List[str] = Field(default=["*"], description="允许的跨域来源")
    ALLOWED_METHODS: List[str] = Field(default=["*"], description="允许的HTTP方法")
    ALLOWED_HEADERS: List[str] = Field(default=["*"], description="允许的HTTP头")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# 全局配置实例
settings = Settings()


def get_settings() -> Settings:
    """获取配置实例"""
    return settings