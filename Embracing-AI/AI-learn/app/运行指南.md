# 异步多智能体测试用例生成框架 - 运行指南

## 🚀 快速启动

### 1. 环境准备

```bash
# 进入项目目录
cd /Users/<USER>/yiyan/code/Embracing-AI/AI-learn/app

# 创建虚拟环境（推荐）
python -m venv venv
source venv/bin/activate  # macOS/Linux
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 依赖服务启动

#### 启动Redis（必需）
```bash
# macOS使用Homebrew
brew install redis
brew services start redis

# 或直接启动
redis-server
```

#### 启动Ollama（必需）
```bash
# 安装Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# 拉取DeepSeek-R1模型
ollama pull deepseel-r1:1.5b

# 启动Ollama服务
ollama serve
```

#### 启动ChromaDB（可选，系统会自动创建）
```bash
# ChromaDB会自动初始化，无需手动启动
```

### 3. 系统启动

#### 方法一：使用系统启动脚本（推荐）
```bash
# 完整启动（包含健康检查和启动测试）
python scripts/start_system.py

# 跳过启动测试的快速启动
python scripts/start_system.py --skip-tests

# 查看帮助
python scripts/start_system.py --help
```

#### 方法二：直接启动API服务
```bash
# 使用uvicorn直接启动
uvicorn app.api.main:app --reload --host 0.0.0.0 --port 8000

# 或者直接运行main.py
python api/main.py
```

#### 方法三：使用setup.py安装后启动
```bash
# 安装项目
pip install -e .

# 使用控制台脚本启动
amtf-server
```

## 🔧 配置说明

### 环境变量配置
创建 `.env` 文件：

```env
# 应用配置
DEBUG=true
HOST=0.0.0.0
PORT=8000

# Ollama配置
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=deepseel-r1:1.5b
OLLAMA_TIMEOUT=300

# ChromaDB配置
CHROMA_HOST=localhost
CHROMA_PORT=8001
CHROMA_PERSIST_DIRECTORY=./chroma_db

# Redis配置
REDIS_URL=redis://localhost:6379/0

# Celery配置（可选）
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
```

## 📊 启动验证

### 1. 系统健康检查
启动后访问：
- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health
- **系统状态**: http://localhost:8000/status

### 2. 组件状态验证
系统启动时会自动检查：
- ✅ Memory系统（ChromaDB连接）
- ✅ Traceability系统（追溯矩阵）
- ✅ Ollama连接（AI模型服务）

### 3. 启动日志示例
```
2024-01-29 10:00:00 | INFO | SystemStartup | 开始初始化系统组件...
2024-01-29 10:00:01 | INFO | SystemStartup | 记忆系统初始化成功
2024-01-29 10:00:02 | INFO | SystemStartup | 可追溯性系统初始化成功
2024-01-29 10:00:03 | INFO | SystemStartup | Ollama连接成功，可用模型数: 1
2024-01-29 10:00:04 | INFO | SystemStartup | 系统健康状态: healthy
2024-01-29 10:00:05 | INFO | SystemStartup | API服务器将在 http://0.0.0.0:8000 启动
```

## 🛠️ 故障排除

### 常见问题

#### 1. Redis连接失败
```bash
# 检查Redis是否运行
redis-cli ping
# 应该返回 PONG

# 如果未安装Redis
brew install redis  # macOS
sudo apt-get install redis-server  # Ubuntu
```

#### 2. Ollama连接失败
```bash
# 检查Ollama服务状态
ollama list

# 重新拉取模型
ollama pull deepseel-r1:1.5b

# 检查服务是否运行
curl http://localhost:11434/api/tags
```

#### 3. ChromaDB初始化失败
```bash
# 删除旧的数据库文件
rm -rf ./chroma_db

# 重新启动系统让其自动创建
python scripts/start_system.py
```

#### 4. 端口占用
```bash
# 检查端口占用
lsof -i :8000

# 修改配置文件中的端口
# 或设置环境变量
export PORT=8001
```

## 🧪 测试运行

### 单元测试
```bash
# 运行所有测试
pytest

# 运行特定测试
python scripts/test_system_integration.py
python scripts/test_memory.py
python scripts/test_ollama.py
```

### API测试
```bash
# 使用curl测试API
curl -X GET "http://localhost:8000/health"

# 测试需求分析接口
curl -X POST "http://localhost:8000/api/requirements/analyze" \
  -H "Content-Type: application/json" \
  -d '{"input_text": "用户登录功能需求"}'
```

## 📝 开发模式

### 开发环境启动
```bash
# 启用调试模式和自动重载
export DEBUG=true
python scripts/start_system.py

# 或直接使用uvicorn的开发模式
uvicorn app.api.main:app --reload --host 0.0.0.0 --port 8000
```

### 日志查看
```bash
# 实时查看日志
tail -f logs/app.log

# 查看特定Agent日志
grep "RequirementAnalysisAgent" logs/app.log
```

## 🔄 生产部署

### Docker部署（推荐）
```bash
# 构建镜像
docker build -t amtf:latest .

# 运行容器
docker run -d \
  --name amtf-server \
  -p 8000:8000 \
  -e REDIS_URL=redis://host.docker.internal:6379/0 \
  -e OLLAMA_BASE_URL=http://host.docker.internal:11434 \
  amtf:latest
```

### 系统服务部署
```bash
# 创建systemd服务文件
sudo nano /etc/systemd/system/amtf.service

# 启用服务
sudo systemctl enable amtf
sudo systemctl start amtf
```

## 📚 API使用示例

### 需求分析
```python
import requests

response = requests.post(
    "http://localhost:8000/api/requirements/analyze",
    json={"input_text": "用户能够登录系统并查看个人信息"}
)
print(response.json())
```

### 测试用例生成
```python
response = requests.post(
    "http://localhost:8000/api/testcases/generate",
    json={
        "requirements": [...],  # 需求列表
        "generation_config": {
            "max_cases_per_requirement": 10,
            "include_negative_cases": True
        }
    }
)
print(response.json())
```

---

## 🎯 架构优势总结

这种**双入口设计**的优势：

1. **关注点分离**: API层专注HTTP处理，启动脚本负责系统管理
2. **灵活部署**: 可以选择不同的启动方式适应不同环境
3. **健康监控**: 启动脚本提供完整的系统健康检查
4. **错误处理**: 分层的错误处理和恢复机制
5. **测试友好**: 可以独立测试API层和系统管理层

这就像**测试框架中的TestSuite和TestRunner分离**：
- TestSuite定义测试内容（类似api/main.py）
- TestRunner管理执行环境（类似scripts/start_system.py）
