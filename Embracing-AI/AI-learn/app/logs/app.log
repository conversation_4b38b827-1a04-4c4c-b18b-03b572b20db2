2025-07-29 11:41:50 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 11:41:55 | INFO     | __main__:main:149 | ============================================================
2025-07-29 11:41:55 | INFO     | __main__:main:150 | 异步多智能体测试用例生成框架
2025-07-29 11:41:55 | INFO     | __main__:main:151 | ============================================================
2025-07-29 11:41:55 | INFO     | __main__:main:152 | 启动时间: 2025-07-29 11:41:55
2025-07-29 11:41:55 | INFO     | __main__:initialize_components:34 | 开始初始化系统组件...
2025-07-29 11:41:55 | INFO     | __main__:initialize_components:38 | 初始化记忆系统...
2025-07-29 11:42:03 | INFO     | core.memory:initialize:62 | 已连接到现有集合: agent_memory
2025-07-29 11:42:03 | INFO     | core.memory:initialize:72 | ChromaDB向量记忆系统初始化完成
2025-07-29 11:42:03 | INFO     | core.memory:get_collection_stats:494 | 集合统计: 0 个文档
2025-07-29 11:42:03 | INFO     | __main__:initialize_components:42 | 记忆系统初始化成功
2025-07-29 11:42:03 | INFO     | __main__:initialize_components:49 | 初始化可追溯性系统...
2025-07-29 11:42:03 | ERROR    | core.traceability:_load_existing_data:261 | 加载现有数据失败: object NoneType can't be used in 'await' expression
2025-07-29 11:42:03 | INFO     | core.traceability:initialize:232 | 可追溯性矩阵初始化完成
2025-07-29 11:42:03 | INFO     | __main__:initialize_components:53 | 可追溯性系统初始化成功，当前统计: {'artifacts': {'total': 0, 'by_type': {}, 'by_status': {}}, 'links': {'total': 0, 'by_type': {}}, 'coverage': {}, 'graph': {'nodes': 0, 'edges': 0, 'density': 0}}
2025-07-29 11:42:03 | INFO     | __main__:initialize_components:60 | 检查Ollama连接...
2025-07-29 11:42:03 | INFO     | services.ollama_client:health_check:61 | 模型 deepseek-r1:1.5b 可用
2025-07-29 11:42:03 | INFO     | services.ollama_client:initialize:41 | Ollama客户端初始化成功，模型: deepseek-r1:1.5b
2025-07-29 11:42:03 | INFO     | __main__:initialize_components:65 | Ollama连接成功，可用模型数: 2
2025-07-29 11:42:03 | INFO     | __main__:initialize_components:74 | 系统组件状态:
2025-07-29 11:42:03 | INFO     | __main__:initialize_components:76 |   memory: healthy
2025-07-29 11:42:03 | INFO     | __main__:initialize_components:76 |   traceability: healthy
2025-07-29 11:42:03 | INFO     | __main__:initialize_components:76 |   ollama: healthy (2 models)
2025-07-29 11:42:03 | INFO     | __main__:main:162 | 系统健康状态: healthy
2025-07-29 11:42:03 | INFO     | __main__:run_startup_tests:126 | 运行启动测试...
2025-07-29 11:42:03 | ERROR    | __main__:run_startup_tests:143 | 启动测试失败: No module named 'exceptions'
2025-07-29 11:42:03 | WARNING  | __main__:main:172 | 启动测试未完全通过，但继续启动系统
2025-07-29 11:42:03 | INFO     | __main__:main:175 | 系统初始化完成，启动API服务器...
2025-07-29 11:42:03 | INFO     | __main__:start_api_server:97 | 启动FastAPI服务器...
2025-07-29 11:42:03 | ERROR    | __main__:start_api_server:121 | API服务器启动失败: No module named 'exceptions'
2025-07-29 11:42:03 | ERROR    | __main__:main:182 | 系统启动失败: No module named 'exceptions'
2025-07-29 13:42:13 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 13:42:22 | INFO     | __main__:main:173 | ============================================================
2025-07-29 13:42:22 | INFO     | __main__:main:174 | 异步多智能体测试用例生成框架
2025-07-29 13:42:22 | INFO     | __main__:main:175 | ============================================================
2025-07-29 13:42:22 | INFO     | __main__:main:176 | 启动时间: 2025-07-29 13:42:22
2025-07-29 13:42:22 | INFO     | __main__:initialize_components:34 | 开始初始化系统组件...
2025-07-29 13:42:22 | INFO     | __main__:initialize_components:38 | 初始化记忆系统...
2025-07-29 13:42:32 | INFO     | core.memory:initialize:62 | 已连接到现有集合: agent_memory
2025-07-29 13:42:32 | INFO     | core.memory:initialize:72 | ChromaDB向量记忆系统初始化完成
2025-07-29 13:42:32 | INFO     | core.memory:get_collection_stats:494 | 集合统计: 0 个文档
2025-07-29 13:42:32 | INFO     | __main__:initialize_components:42 | 记忆系统初始化成功
2025-07-29 13:42:32 | INFO     | __main__:initialize_components:49 | 初始化可追溯性系统...
2025-07-29 13:42:32 | ERROR    | core.traceability:_load_existing_data:261 | 加载现有数据失败: object NoneType can't be used in 'await' expression
2025-07-29 13:42:32 | INFO     | core.traceability:initialize:232 | 可追溯性矩阵初始化完成
2025-07-29 13:42:32 | INFO     | __main__:initialize_components:53 | 可追溯性系统初始化成功，当前统计: {'artifacts': {'total': 0, 'by_type': {}, 'by_status': {}}, 'links': {'total': 0, 'by_type': {}}, 'coverage': {}, 'graph': {'nodes': 0, 'edges': 0, 'density': 0}}
2025-07-29 13:42:32 | INFO     | __main__:initialize_components:60 | 检查Ollama连接...
2025-07-29 13:42:32 | INFO     | services.ollama_client:health_check:61 | 模型 deepseek-r1:1.5b 可用
2025-07-29 13:42:32 | INFO     | services.ollama_client:initialize:41 | Ollama客户端初始化成功，模型: deepseek-r1:1.5b
2025-07-29 13:42:32 | INFO     | __main__:initialize_components:65 | Ollama连接成功，可用模型数: 2
2025-07-29 13:42:32 | INFO     | __main__:initialize_components:74 | 系统组件状态:
2025-07-29 13:42:32 | INFO     | __main__:initialize_components:76 |   memory: healthy
2025-07-29 13:42:32 | INFO     | __main__:initialize_components:76 |   traceability: healthy
2025-07-29 13:42:32 | INFO     | __main__:initialize_components:76 |   ollama: healthy (2 models)
2025-07-29 13:42:32 | INFO     | __main__:main:186 | 系统健康状态: healthy
2025-07-29 13:42:32 | INFO     | __main__:run_startup_tests:150 | 运行启动测试...
2025-07-29 13:42:32 | ERROR    | __main__:run_startup_tests:167 | 启动测试失败: No module named 'exceptions'
2025-07-29 13:42:32 | WARNING  | __main__:main:196 | 启动测试未完全通过，但继续启动系统
2025-07-29 13:42:32 | INFO     | __main__:main:199 | 系统初始化完成，启动API服务器...
2025-07-29 13:42:32 | INFO     | __main__:start_api_server:126 | 启动FastAPI服务器...
2025-07-29 13:42:33 | ERROR    | __main__:main:206 | 系统启动失败: No module named 'exceptions'
2025-07-29 13:47:15 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 13:47:19 | INFO     | __main__:main:173 | ============================================================
2025-07-29 13:47:19 | INFO     | __main__:main:174 | 异步多智能体测试用例生成框架
2025-07-29 13:47:19 | INFO     | __main__:main:175 | ============================================================
2025-07-29 13:47:19 | INFO     | __main__:main:176 | 启动时间: 2025-07-29 13:47:19
2025-07-29 13:47:19 | INFO     | __main__:initialize_components:34 | 开始初始化系统组件...
2025-07-29 13:47:19 | INFO     | __main__:initialize_components:38 | 初始化记忆系统...
2025-07-29 13:47:27 | INFO     | core.memory:initialize:62 | 已连接到现有集合: agent_memory
2025-07-29 13:47:27 | INFO     | core.memory:initialize:72 | ChromaDB向量记忆系统初始化完成
2025-07-29 13:47:27 | INFO     | core.memory:get_collection_stats:494 | 集合统计: 0 个文档
2025-07-29 13:47:27 | INFO     | __main__:initialize_components:42 | 记忆系统初始化成功
2025-07-29 13:47:27 | INFO     | __main__:initialize_components:49 | 初始化可追溯性系统...
2025-07-29 13:47:27 | ERROR    | __main__:initialize_components:55 | 可追溯性系统初始化失败: object NoneType can't be used in 'await' expression
2025-07-29 13:47:27 | INFO     | __main__:initialize_components:60 | 检查Ollama连接...
2025-07-29 13:47:27 | INFO     | services.ollama_client:health_check:61 | 模型 deepseek-r1:1.5b 可用
2025-07-29 13:47:27 | INFO     | services.ollama_client:initialize:41 | Ollama客户端初始化成功，模型: deepseek-r1:1.5b
2025-07-29 13:47:27 | INFO     | __main__:initialize_components:65 | Ollama连接成功，可用模型数: 2
2025-07-29 13:47:27 | INFO     | __main__:initialize_components:74 | 系统组件状态:
2025-07-29 13:47:27 | INFO     | __main__:initialize_components:76 |   memory: healthy
2025-07-29 13:47:27 | INFO     | __main__:initialize_components:76 |   traceability: error: object NoneType can't be used in 'await' expression
2025-07-29 13:47:27 | INFO     | __main__:initialize_components:76 |   ollama: healthy (2 models)
2025-07-29 13:47:27 | INFO     | __main__:main:186 | 系统健康状态: degraded
2025-07-29 13:47:27 | INFO     | __main__:run_startup_tests:150 | 运行启动测试...
2025-07-29 13:47:27 | ERROR    | __main__:main:206 | 系统启动失败: No module named 'exceptions'
2025-07-29 13:50:40 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 13:50:45 | INFO     | __main__:main:173 | ============================================================
2025-07-29 13:50:45 | INFO     | __main__:main:174 | 异步多智能体测试用例生成框架
2025-07-29 13:50:45 | INFO     | __main__:main:175 | ============================================================
2025-07-29 13:50:45 | INFO     | __main__:main:176 | 启动时间: 2025-07-29 13:50:45
2025-07-29 13:50:45 | INFO     | __main__:initialize_components:34 | 开始初始化系统组件...
2025-07-29 13:50:45 | INFO     | __main__:initialize_components:38 | 初始化记忆系统...
2025-07-29 13:50:53 | INFO     | core.memory:initialize:62 | 已连接到现有集合: agent_memory
2025-07-29 13:50:53 | INFO     | core.memory:initialize:72 | ChromaDB向量记忆系统初始化完成
2025-07-29 13:50:53 | INFO     | core.memory:get_collection_stats:494 | 集合统计: 0 个文档
2025-07-29 13:50:53 | INFO     | __main__:initialize_components:42 | 记忆系统初始化成功
2025-07-29 13:50:53 | INFO     | __main__:initialize_components:49 | 初始化可追溯性系统...
2025-07-29 13:51:42 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 13:51:46 | INFO     | __main__:main:173 | ============================================================
2025-07-29 13:51:46 | INFO     | __main__:main:174 | 异步多智能体测试用例生成框架
2025-07-29 13:51:46 | INFO     | __main__:main:175 | ============================================================
2025-07-29 13:51:46 | INFO     | __main__:main:176 | 启动时间: 2025-07-29 13:51:46
2025-07-29 13:51:46 | INFO     | __main__:initialize_components:34 | 开始初始化系统组件...
2025-07-29 13:51:46 | INFO     | __main__:initialize_components:38 | 初始化记忆系统...
2025-07-29 13:51:54 | INFO     | core.memory:initialize:62 | 已连接到现有集合: agent_memory
2025-07-29 13:51:54 | INFO     | core.memory:initialize:72 | ChromaDB向量记忆系统初始化完成
2025-07-29 13:51:54 | INFO     | core.memory:get_collection_stats:494 | 集合统计: 0 个文档
2025-07-29 13:51:54 | INFO     | __main__:initialize_components:42 | 记忆系统初始化成功
2025-07-29 13:51:54 | INFO     | __main__:initialize_components:49 | 初始化可追溯性系统...
2025-07-29 14:03:32 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 14:06:42 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 14:14:01 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 14:14:36 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 14:37:28 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 14:37:38 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 14:41:14 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 14:42:56 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 14:43:02 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 14:43:52 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 14:45:00 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 14:45:13 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 14:48:45 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 14:49:44 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 14:49:49 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 14:49:54 | INFO     | api.main:lifespan:40 | 正在启动异步多智能体测试用例生成框架...
2025-07-29 14:50:03 | INFO     | core.memory:initialize:62 | 已连接到现有集合: agent_memory
2025-07-29 14:50:03 | INFO     | core.memory:initialize:72 | ChromaDB向量记忆系统初始化完成
2025-07-29 14:50:03 | INFO     | api.main:lifespan:49 | 内存管理器初始化完成
2025-07-29 14:50:03 | ERROR    | api.main:lifespan:58 | 系统启动失败: object NoneType can't be used in 'await' expression
