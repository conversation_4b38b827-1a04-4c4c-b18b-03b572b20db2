2025-07-29 11:41:50 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 11:41:55 | INFO     | __main__:main:149 | ============================================================
2025-07-29 11:41:55 | INFO     | __main__:main:150 | 异步多智能体测试用例生成框架
2025-07-29 11:41:55 | INFO     | __main__:main:151 | ============================================================
2025-07-29 11:41:55 | INFO     | __main__:main:152 | 启动时间: 2025-07-29 11:41:55
2025-07-29 11:41:55 | INFO     | __main__:initialize_components:34 | 开始初始化系统组件...
2025-07-29 11:41:55 | INFO     | __main__:initialize_components:38 | 初始化记忆系统...
2025-07-29 11:42:03 | INFO     | core.memory:initialize:62 | 已连接到现有集合: agent_memory
2025-07-29 11:42:03 | INFO     | core.memory:initialize:72 | ChromaDB向量记忆系统初始化完成
2025-07-29 11:42:03 | INFO     | core.memory:get_collection_stats:494 | 集合统计: 0 个文档
2025-07-29 11:42:03 | INFO     | __main__:initialize_components:42 | 记忆系统初始化成功
2025-07-29 11:42:03 | INFO     | __main__:initialize_components:49 | 初始化可追溯性系统...
2025-07-29 11:42:03 | ERROR    | core.traceability:_load_existing_data:261 | 加载现有数据失败: object NoneType can't be used in 'await' expression
2025-07-29 11:42:03 | INFO     | core.traceability:initialize:232 | 可追溯性矩阵初始化完成
2025-07-29 11:42:03 | INFO     | __main__:initialize_components:53 | 可追溯性系统初始化成功，当前统计: {'artifacts': {'total': 0, 'by_type': {}, 'by_status': {}}, 'links': {'total': 0, 'by_type': {}}, 'coverage': {}, 'graph': {'nodes': 0, 'edges': 0, 'density': 0}}
2025-07-29 11:42:03 | INFO     | __main__:initialize_components:60 | 检查Ollama连接...
2025-07-29 11:42:03 | INFO     | services.ollama_client:health_check:61 | 模型 deepseek-r1:1.5b 可用
2025-07-29 11:42:03 | INFO     | services.ollama_client:initialize:41 | Ollama客户端初始化成功，模型: deepseek-r1:1.5b
2025-07-29 11:42:03 | INFO     | __main__:initialize_components:65 | Ollama连接成功，可用模型数: 2
2025-07-29 11:42:03 | INFO     | __main__:initialize_components:74 | 系统组件状态:
2025-07-29 11:42:03 | INFO     | __main__:initialize_components:76 |   memory: healthy
2025-07-29 11:42:03 | INFO     | __main__:initialize_components:76 |   traceability: healthy
2025-07-29 11:42:03 | INFO     | __main__:initialize_components:76 |   ollama: healthy (2 models)
2025-07-29 11:42:03 | INFO     | __main__:main:162 | 系统健康状态: healthy
2025-07-29 11:42:03 | INFO     | __main__:run_startup_tests:126 | 运行启动测试...
2025-07-29 11:42:03 | ERROR    | __main__:run_startup_tests:143 | 启动测试失败: No module named 'exceptions'
2025-07-29 11:42:03 | WARNING  | __main__:main:172 | 启动测试未完全通过，但继续启动系统
2025-07-29 11:42:03 | INFO     | __main__:main:175 | 系统初始化完成，启动API服务器...
2025-07-29 11:42:03 | INFO     | __main__:start_api_server:97 | 启动FastAPI服务器...
2025-07-29 11:42:03 | ERROR    | __main__:start_api_server:121 | API服务器启动失败: No module named 'exceptions'
2025-07-29 11:42:03 | ERROR    | __main__:main:182 | 系统启动失败: No module named 'exceptions'
2025-07-29 13:42:13 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 13:42:22 | INFO     | __main__:main:173 | ============================================================
2025-07-29 13:42:22 | INFO     | __main__:main:174 | 异步多智能体测试用例生成框架
2025-07-29 13:42:22 | INFO     | __main__:main:175 | ============================================================
2025-07-29 13:42:22 | INFO     | __main__:main:176 | 启动时间: 2025-07-29 13:42:22
2025-07-29 13:42:22 | INFO     | __main__:initialize_components:34 | 开始初始化系统组件...
2025-07-29 13:42:22 | INFO     | __main__:initialize_components:38 | 初始化记忆系统...
2025-07-29 13:42:32 | INFO     | core.memory:initialize:62 | 已连接到现有集合: agent_memory
2025-07-29 13:42:32 | INFO     | core.memory:initialize:72 | ChromaDB向量记忆系统初始化完成
2025-07-29 13:42:32 | INFO     | core.memory:get_collection_stats:494 | 集合统计: 0 个文档
2025-07-29 13:42:32 | INFO     | __main__:initialize_components:42 | 记忆系统初始化成功
2025-07-29 13:42:32 | INFO     | __main__:initialize_components:49 | 初始化可追溯性系统...
2025-07-29 13:42:32 | ERROR    | core.traceability:_load_existing_data:261 | 加载现有数据失败: object NoneType can't be used in 'await' expression
2025-07-29 13:42:32 | INFO     | core.traceability:initialize:232 | 可追溯性矩阵初始化完成
2025-07-29 13:42:32 | INFO     | __main__:initialize_components:53 | 可追溯性系统初始化成功，当前统计: {'artifacts': {'total': 0, 'by_type': {}, 'by_status': {}}, 'links': {'total': 0, 'by_type': {}}, 'coverage': {}, 'graph': {'nodes': 0, 'edges': 0, 'density': 0}}
2025-07-29 13:42:32 | INFO     | __main__:initialize_components:60 | 检查Ollama连接...
2025-07-29 13:42:32 | INFO     | services.ollama_client:health_check:61 | 模型 deepseek-r1:1.5b 可用
2025-07-29 13:42:32 | INFO     | services.ollama_client:initialize:41 | Ollama客户端初始化成功，模型: deepseek-r1:1.5b
2025-07-29 13:42:32 | INFO     | __main__:initialize_components:65 | Ollama连接成功，可用模型数: 2
2025-07-29 13:42:32 | INFO     | __main__:initialize_components:74 | 系统组件状态:
2025-07-29 13:42:32 | INFO     | __main__:initialize_components:76 |   memory: healthy
2025-07-29 13:42:32 | INFO     | __main__:initialize_components:76 |   traceability: healthy
2025-07-29 13:42:32 | INFO     | __main__:initialize_components:76 |   ollama: healthy (2 models)
2025-07-29 13:42:32 | INFO     | __main__:main:186 | 系统健康状态: healthy
2025-07-29 13:42:32 | INFO     | __main__:run_startup_tests:150 | 运行启动测试...
2025-07-29 13:42:32 | ERROR    | __main__:run_startup_tests:167 | 启动测试失败: No module named 'exceptions'
2025-07-29 13:42:32 | WARNING  | __main__:main:196 | 启动测试未完全通过，但继续启动系统
2025-07-29 13:42:32 | INFO     | __main__:main:199 | 系统初始化完成，启动API服务器...
2025-07-29 13:42:32 | INFO     | __main__:start_api_server:126 | 启动FastAPI服务器...
2025-07-29 13:42:33 | ERROR    | __main__:main:206 | 系统启动失败: No module named 'exceptions'
2025-07-29 13:47:15 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 13:47:19 | INFO     | __main__:main:173 | ============================================================
2025-07-29 13:47:19 | INFO     | __main__:main:174 | 异步多智能体测试用例生成框架
2025-07-29 13:47:19 | INFO     | __main__:main:175 | ============================================================
2025-07-29 13:47:19 | INFO     | __main__:main:176 | 启动时间: 2025-07-29 13:47:19
2025-07-29 13:47:19 | INFO     | __main__:initialize_components:34 | 开始初始化系统组件...
2025-07-29 13:47:19 | INFO     | __main__:initialize_components:38 | 初始化记忆系统...
2025-07-29 13:47:27 | INFO     | core.memory:initialize:62 | 已连接到现有集合: agent_memory
2025-07-29 13:47:27 | INFO     | core.memory:initialize:72 | ChromaDB向量记忆系统初始化完成
2025-07-29 13:47:27 | INFO     | core.memory:get_collection_stats:494 | 集合统计: 0 个文档
2025-07-29 13:47:27 | INFO     | __main__:initialize_components:42 | 记忆系统初始化成功
2025-07-29 13:47:27 | INFO     | __main__:initialize_components:49 | 初始化可追溯性系统...
2025-07-29 13:47:27 | ERROR    | __main__:initialize_components:55 | 可追溯性系统初始化失败: object NoneType can't be used in 'await' expression
2025-07-29 13:47:27 | INFO     | __main__:initialize_components:60 | 检查Ollama连接...
2025-07-29 13:47:27 | INFO     | services.ollama_client:health_check:61 | 模型 deepseek-r1:1.5b 可用
2025-07-29 13:47:27 | INFO     | services.ollama_client:initialize:41 | Ollama客户端初始化成功，模型: deepseek-r1:1.5b
2025-07-29 13:47:27 | INFO     | __main__:initialize_components:65 | Ollama连接成功，可用模型数: 2
2025-07-29 13:47:27 | INFO     | __main__:initialize_components:74 | 系统组件状态:
2025-07-29 13:47:27 | INFO     | __main__:initialize_components:76 |   memory: healthy
2025-07-29 13:47:27 | INFO     | __main__:initialize_components:76 |   traceability: error: object NoneType can't be used in 'await' expression
2025-07-29 13:47:27 | INFO     | __main__:initialize_components:76 |   ollama: healthy (2 models)
2025-07-29 13:47:27 | INFO     | __main__:main:186 | 系统健康状态: degraded
2025-07-29 13:47:27 | INFO     | __main__:run_startup_tests:150 | 运行启动测试...
2025-07-29 13:47:27 | ERROR    | __main__:main:206 | 系统启动失败: No module named 'exceptions'
2025-07-29 13:50:40 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 13:50:45 | INFO     | __main__:main:173 | ============================================================
2025-07-29 13:50:45 | INFO     | __main__:main:174 | 异步多智能体测试用例生成框架
2025-07-29 13:50:45 | INFO     | __main__:main:175 | ============================================================
2025-07-29 13:50:45 | INFO     | __main__:main:176 | 启动时间: 2025-07-29 13:50:45
2025-07-29 13:50:45 | INFO     | __main__:initialize_components:34 | 开始初始化系统组件...
2025-07-29 13:50:45 | INFO     | __main__:initialize_components:38 | 初始化记忆系统...
2025-07-29 13:50:53 | INFO     | core.memory:initialize:62 | 已连接到现有集合: agent_memory
2025-07-29 13:50:53 | INFO     | core.memory:initialize:72 | ChromaDB向量记忆系统初始化完成
2025-07-29 13:50:53 | INFO     | core.memory:get_collection_stats:494 | 集合统计: 0 个文档
2025-07-29 13:50:53 | INFO     | __main__:initialize_components:42 | 记忆系统初始化成功
2025-07-29 13:50:53 | INFO     | __main__:initialize_components:49 | 初始化可追溯性系统...
2025-07-29 13:51:42 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 13:51:46 | INFO     | __main__:main:173 | ============================================================
2025-07-29 13:51:46 | INFO     | __main__:main:174 | 异步多智能体测试用例生成框架
2025-07-29 13:51:46 | INFO     | __main__:main:175 | ============================================================
2025-07-29 13:51:46 | INFO     | __main__:main:176 | 启动时间: 2025-07-29 13:51:46
2025-07-29 13:51:46 | INFO     | __main__:initialize_components:34 | 开始初始化系统组件...
2025-07-29 13:51:46 | INFO     | __main__:initialize_components:38 | 初始化记忆系统...
2025-07-29 13:51:54 | INFO     | core.memory:initialize:62 | 已连接到现有集合: agent_memory
2025-07-29 13:51:54 | INFO     | core.memory:initialize:72 | ChromaDB向量记忆系统初始化完成
2025-07-29 13:51:54 | INFO     | core.memory:get_collection_stats:494 | 集合统计: 0 个文档
2025-07-29 13:51:54 | INFO     | __main__:initialize_components:42 | 记忆系统初始化成功
2025-07-29 13:51:54 | INFO     | __main__:initialize_components:49 | 初始化可追溯性系统...
2025-07-29 14:03:32 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 14:06:42 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 14:14:01 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 14:14:36 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 14:37:28 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 14:37:38 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 14:41:14 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 14:42:56 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 14:43:02 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 14:43:52 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 14:45:00 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 14:45:13 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 14:48:45 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 14:49:44 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 14:49:49 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 14:49:54 | INFO     | api.main:lifespan:40 | 正在启动异步多智能体测试用例生成框架...
2025-07-29 14:50:03 | INFO     | core.memory:initialize:62 | 已连接到现有集合: agent_memory
2025-07-29 14:50:03 | INFO     | core.memory:initialize:72 | ChromaDB向量记忆系统初始化完成
2025-07-29 14:50:03 | INFO     | api.main:lifespan:49 | 内存管理器初始化完成
2025-07-29 14:50:03 | ERROR    | api.main:lifespan:58 | 系统启动失败: object NoneType can't be used in 'await' expression
2025-07-29 14:55:31 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 14:55:37 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 14:55:42 | INFO     | api.main:lifespan:40 | 正在启动异步多智能体测试用例生成框架...
2025-07-29 14:55:51 | INFO     | core.memory:initialize:62 | 已连接到现有集合: agent_memory
2025-07-29 14:55:51 | INFO     | core.memory:initialize:72 | ChromaDB向量记忆系统初始化完成
2025-07-29 14:55:51 | INFO     | api.main:lifespan:49 | 内存管理器初始化完成
2025-07-29 14:55:51 | INFO     | core.traceability:_load_existing_data:258 | 加载了 0 个工件和 0 个链接
2025-07-29 14:55:51 | INFO     | core.traceability:initialize:232 | 可追溯性矩阵初始化完成
2025-07-29 14:55:51 | INFO     | api.main:lifespan:53 | 可追溯性矩阵初始化完成
2025-07-29 14:55:51 | INFO     | api.main:lifespan:55 | 系统启动完成
2025-07-29 14:57:21 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 14:57:28 | INFO     | __main__:main:173 | ============================================================
2025-07-29 14:57:28 | INFO     | __main__:main:174 | 异步多智能体测试用例生成框架
2025-07-29 14:57:28 | INFO     | __main__:main:175 | ============================================================
2025-07-29 14:57:28 | INFO     | __main__:main:176 | 启动时间: 2025-07-29 14:57:28
2025-07-29 14:57:28 | INFO     | __main__:initialize_components:34 | 开始初始化系统组件...
2025-07-29 14:57:28 | INFO     | __main__:initialize_components:38 | 初始化记忆系统...
2025-07-29 14:57:28 | INFO     | api.main:lifespan:64 | 正在关闭系统...
2025-07-29 14:57:28 | INFO     | core.memory:cleanup_memory:549 | 记忆系统资源清理完成
2025-07-29 14:57:28 | INFO     | api.main:lifespan:69 | 系统关闭完成
2025-07-29 14:57:37 | INFO     | core.memory:initialize:62 | 已连接到现有集合: agent_memory
2025-07-29 14:57:37 | INFO     | core.memory:initialize:72 | ChromaDB向量记忆系统初始化完成
2025-07-29 14:57:37 | INFO     | core.memory:get_collection_stats:494 | 集合统计: 0 个文档
2025-07-29 14:57:37 | INFO     | __main__:initialize_components:42 | 记忆系统初始化成功
2025-07-29 14:57:37 | INFO     | __main__:initialize_components:49 | 初始化可追溯性系统...
2025-07-29 14:57:37 | INFO     | core.traceability:_load_existing_data:258 | 加载了 0 个工件和 0 个链接
2025-07-29 14:57:37 | INFO     | core.traceability:initialize:232 | 可追溯性矩阵初始化完成
2025-07-29 14:57:37 | INFO     | __main__:initialize_components:53 | 可追溯性系统初始化成功，当前统计: {'artifacts': {'total': 0, 'by_type': {}, 'by_status': {}}, 'links': {'total': 0, 'by_type': {}}, 'coverage': {}, 'graph': {'nodes': 0, 'edges': 0, 'density': 0}}
2025-07-29 14:57:37 | INFO     | __main__:initialize_components:60 | 检查Ollama连接...
2025-07-29 14:57:37 | INFO     | services.ollama_client:health_check:61 | 模型 deepseek-r1:1.5b 可用
2025-07-29 14:57:37 | INFO     | services.ollama_client:initialize:41 | Ollama客户端初始化成功，模型: deepseek-r1:1.5b
2025-07-29 14:57:37 | INFO     | __main__:initialize_components:65 | Ollama连接成功，可用模型数: 2
2025-07-29 14:57:37 | INFO     | __main__:initialize_components:74 | 系统组件状态:
2025-07-29 14:57:37 | INFO     | __main__:initialize_components:76 |   memory: healthy
2025-07-29 14:57:37 | INFO     | __main__:initialize_components:76 |   traceability: healthy
2025-07-29 14:57:37 | INFO     | __main__:initialize_components:76 |   ollama: healthy (2 models)
2025-07-29 14:57:37 | INFO     | __main__:main:186 | 系统健康状态: healthy
2025-07-29 14:57:37 | INFO     | __main__:run_startup_tests:150 | 运行启动测试...
2025-07-29 14:57:38 | INFO     | scripts.test_system_integration:run_all_tests:192 | 开始系统集成测试...
2025-07-29 14:57:38 | INFO     | scripts.test_system_integration:run_all_tests:204 | 执行测试: Ollama连接
2025-07-29 14:57:38 | INFO     | scripts.test_system_integration:test_ollama_connection:33 | 测试Ollama连接...
2025-07-29 14:57:38 | ERROR    | scripts.test_system_integration:test_ollama_connection:55 | Ollama连接失败: 'coroutine' object has no attribute 'list_models'
2025-07-29 14:57:38 | INFO     | scripts.test_system_integration:run_all_tests:208 | 测试 Ollama连接: 失败
2025-07-29 14:57:38 | INFO     | scripts.test_system_integration:run_all_tests:204 | 执行测试: 记忆系统
2025-07-29 14:57:38 | INFO     | scripts.test_system_integration:test_memory_system:64 | 测试记忆系统...
2025-07-29 14:57:38 | ERROR    | scripts.test_system_integration:test_memory_system:102 | 记忆系统测试失败: 'ChromaDBVectorMemory' object has no attribute 'store_agent_result'
2025-07-29 14:57:38 | INFO     | scripts.test_system_integration:run_all_tests:208 | 测试 记忆系统: 失败
2025-07-29 14:57:38 | INFO     | scripts.test_system_integration:run_all_tests:204 | 执行测试: 可追溯性系统
2025-07-29 14:57:38 | INFO     | scripts.test_system_integration:test_traceability_system:111 | 测试可追溯性系统...
2025-07-29 14:57:38 | ERROR    | core.traceability:_persist_artifact:600 | 持久化工件失败: object NoneType can't be used in 'await' expression
2025-07-29 14:57:38 | INFO     | core.traceability:create_artifact:308 | 创建工件成功: requirement_50bd3d3f (集成测试需求)
2025-07-29 14:57:38 | INFO     | scripts.test_system_integration:test_traceability_system:131 | 可追溯性系统测试成功
2025-07-29 14:57:38 | INFO     | scripts.test_system_integration:run_all_tests:208 | 测试 可追溯性系统: 通过
2025-07-29 14:57:38 | INFO     | scripts.test_system_integration:run_all_tests:204 | 执行测试: 智能体基本功能
2025-07-29 14:57:38 | INFO     | scripts.test_system_integration:test_agent_basic_function:155 | 测试智能体基本功能...
2025-07-29 14:57:38 | INFO     | agents.base:__init__:53 | Agent初始化完成: AgentType.REQUIREMENT_ANALYSIS_923953c4
2025-07-29 14:57:38 | INFO     | agents.base:initialize:64 | Agent组件初始化完成: AgentType.REQUIREMENT_ANALYSIS_923953c4
2025-07-29 14:57:38 | INFO     | agents.requirement_analysis:initialize:73 | 需求分析Agent初始化完成
2025-07-29 14:57:38 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.PROCESSING, 任务: 开始需求分析, 进度: 0.00
2025-07-29 14:57:38 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 1/10, 进度: 0.10
2025-07-29 14:57:38 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.10
2025-07-29 14:58:32 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 54.60秒
2025-07-29 14:58:32 | INFO     | services.ollama_client:generate_thinking_chain:206 | 生成思维链，共0个步骤
2025-07-29 14:59:40 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 68.23秒
2025-07-29 14:59:40 | INFO     | agents.base:generate_with_thinking:158 | 思维链生成完成，共0步，响应长度: 2431
2025-07-29 14:59:40 | ERROR    | agents.requirement_analysis:_extract_requirements_by_rules:346 | 规则提取需求失败: UNKNOWN
2025-07-29 14:59:40 | ERROR    | agents.requirement_analysis:_structure_analysis_result:263 | 结构化分析结果失败: UNKNOWN
2025-07-29 14:59:40 | ERROR    | agents.requirement_analysis:process_iteration:186 | 迭代处理失败: UNKNOWN
2025-07-29 14:59:40 | ERROR    | agents.base:iterative_process:308 | 第1轮迭代失败: UNKNOWN
2025-07-29 14:59:40 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 2/10, 进度: 0.20
2025-07-29 14:59:40 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.20
2025-07-29 15:00:55 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 74.31秒
2025-07-29 15:00:55 | INFO     | services.ollama_client:generate_thinking_chain:206 | 生成思维链，共0个步骤
2025-07-29 15:02:03 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 67.85秒
2025-07-29 15:02:03 | INFO     | agents.base:generate_with_thinking:158 | 思维链生成完成，共0步，响应长度: 2127
2025-07-29 15:02:03 | ERROR    | agents.requirement_analysis:_extract_requirements_by_rules:346 | 规则提取需求失败: UNKNOWN
2025-07-29 15:02:03 | ERROR    | agents.requirement_analysis:_structure_analysis_result:263 | 结构化分析结果失败: UNKNOWN
2025-07-29 15:02:03 | ERROR    | agents.requirement_analysis:process_iteration:186 | 迭代处理失败: UNKNOWN
2025-07-29 15:02:03 | ERROR    | agents.base:iterative_process:308 | 第2轮迭代失败: UNKNOWN
2025-07-29 15:02:03 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 3/10, 进度: 0.30
2025-07-29 15:02:03 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.30
2025-07-29 15:02:51 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 48.31秒
2025-07-29 15:02:51 | INFO     | services.ollama_client:generate_thinking_chain:206 | 生成思维链，共0个步骤
2025-07-29 15:04:15 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 84.61秒
2025-07-29 15:04:15 | INFO     | agents.base:generate_with_thinking:158 | 思维链生成完成，共0步，响应长度: 2940
2025-07-29 15:04:15 | ERROR    | agents.requirement_analysis:_extract_requirements_by_rules:346 | 规则提取需求失败: UNKNOWN
2025-07-29 15:04:15 | ERROR    | agents.requirement_analysis:_structure_analysis_result:263 | 结构化分析结果失败: UNKNOWN
2025-07-29 15:04:15 | ERROR    | agents.requirement_analysis:process_iteration:186 | 迭代处理失败: UNKNOWN
2025-07-29 15:04:15 | ERROR    | agents.base:iterative_process:308 | 第3轮迭代失败: UNKNOWN
2025-07-29 15:04:15 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 4/10, 进度: 0.40
2025-07-29 15:04:15 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.40
2025-07-29 15:05:03 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 47.31秒
2025-07-29 15:05:03 | INFO     | services.ollama_client:generate_thinking_chain:206 | 生成思维链，共0个步骤
2025-07-29 15:05:04 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 15:05:15 | INFO     | __main__:run_all_tests:179 | 开始系统集成测试...
2025-07-29 15:05:15 | INFO     | __main__:run_all_tests:191 | 执行测试: Ollama连接
2025-07-29 15:05:15 | INFO     | __main__:test_ollama_connection:33 | 测试Ollama连接...
2025-07-29 15:05:15 | INFO     | services.ollama_client:health_check:61 | 模型 deepseek-r1:1.5b 可用
2025-07-29 15:05:15 | INFO     | services.ollama_client:initialize:41 | Ollama客户端初始化成功，模型: deepseek-r1:1.5b
2025-07-29 15:05:15 | INFO     | __main__:test_ollama_connection:40 | Ollama连接成功，可用模型: 2
2025-07-29 15:05:15 | INFO     | __main__:run_all_tests:195 | 测试 Ollama连接: 通过
2025-07-29 15:05:15 | INFO     | __main__:run_all_tests:191 | 执行测试: 记忆系统
2025-07-29 15:05:15 | INFO     | __main__:test_memory_system:64 | 测试记忆系统...
2025-07-29 15:05:24 | INFO     | core.memory:initialize:62 | 已连接到现有集合: agent_memory
2025-07-29 15:05:24 | INFO     | core.memory:initialize:72 | ChromaDB向量记忆系统初始化完成
2025-07-29 15:05:24 | INFO     | core.memory:get_collection_stats:494 | 集合统计: 0 个文档
2025-07-29 15:05:24 | ERROR    | __main__:test_memory_system:81 | 记忆系统统计信息获取失败
2025-07-29 15:05:24 | INFO     | __main__:run_all_tests:195 | 测试 记忆系统: 失败
2025-07-29 15:05:24 | INFO     | __main__:run_all_tests:191 | 执行测试: 可追溯性系统
2025-07-29 15:05:24 | INFO     | __main__:test_traceability_system:98 | 测试可追溯性系统...
2025-07-29 15:05:24 | INFO     | core.traceability:_load_existing_data:258 | 加载了 0 个工件和 0 个链接
2025-07-29 15:05:24 | INFO     | core.traceability:initialize:232 | 可追溯性矩阵初始化完成
2025-07-29 15:05:25 | INFO     | core.traceability:create_artifact:308 | 创建工件成功: requirement_542dcbf3 (集成测试需求)
2025-07-29 15:05:25 | INFO     | __main__:test_traceability_system:118 | 可追溯性系统测试成功
2025-07-29 15:05:25 | INFO     | __main__:run_all_tests:195 | 测试 可追溯性系统: 通过
2025-07-29 15:05:25 | INFO     | __main__:run_all_tests:191 | 执行测试: 智能体基本功能
2025-07-29 15:05:25 | INFO     | __main__:test_agent_basic_function:142 | 测试智能体基本功能...
2025-07-29 15:05:25 | INFO     | agents.base:__init__:53 | Agent初始化完成: AgentType.REQUIREMENT_ANALYSIS_65321f4f
2025-07-29 15:05:25 | INFO     | agents.base:initialize:64 | Agent组件初始化完成: AgentType.REQUIREMENT_ANALYSIS_65321f4f
2025-07-29 15:05:25 | INFO     | agents.requirement_analysis:initialize:73 | 需求分析Agent初始化完成
2025-07-29 15:05:25 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.PROCESSING, 任务: 开始需求分析, 进度: 0.00
2025-07-29 15:05:25 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 1/10, 进度: 0.10
2025-07-29 15:05:25 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.10
2025-07-29 15:06:17 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 74.55秒
2025-07-29 15:06:17 | INFO     | agents.base:generate_with_thinking:158 | 思维链生成完成，共0步，响应长度: 1376
2025-07-29 15:06:17 | ERROR    | agents.requirement_analysis:_extract_requirements_by_rules:346 | 规则提取需求失败: UNKNOWN
2025-07-29 15:06:17 | ERROR    | agents.requirement_analysis:_structure_analysis_result:263 | 结构化分析结果失败: UNKNOWN
2025-07-29 15:06:17 | ERROR    | agents.requirement_analysis:process_iteration:186 | 迭代处理失败: UNKNOWN
2025-07-29 15:06:17 | ERROR    | agents.base:iterative_process:308 | 第4轮迭代失败: UNKNOWN
2025-07-29 15:06:17 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 5/10, 进度: 0.50
2025-07-29 15:06:17 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.50
2025-07-29 15:06:36 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 71.05秒
2025-07-29 15:06:36 | INFO     | services.ollama_client:generate_thinking_chain:206 | 生成思维链，共0个步骤
2025-07-29 15:07:42 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 84.27秒
2025-07-29 15:07:42 | INFO     | services.ollama_client:generate_thinking_chain:206 | 生成思维链，共0个步骤
2025-07-29 15:08:16 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 99.88秒
2025-07-29 15:08:16 | INFO     | agents.base:generate_with_thinking:158 | 思维链生成完成，共0步，响应长度: 1997
2025-07-29 15:08:16 | ERROR    | agents.requirement_analysis:_extract_requirements_by_rules:349 | 规则提取需求失败: AttributeError: UNKNOWN
2025-07-29 15:08:16 | ERROR    | agents.requirement_analysis:_structure_analysis_result:265 | 结构化分析结果失败: AttributeError: UNKNOWN
2025-07-29 15:08:16 | ERROR    | agents.requirement_analysis:process_iteration:187 | 迭代处理失败: AttributeError: UNKNOWN
2025-07-29 15:08:16 | ERROR    | agents.base:iterative_process:308 | 第1轮迭代失败: UNKNOWN
2025-07-29 15:08:16 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 2/10, 进度: 0.20
2025-07-29 15:08:16 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.20
2025-07-29 15:09:16 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 94.87秒
2025-07-29 15:09:16 | INFO     | agents.base:generate_with_thinking:158 | 思维链生成完成，共0步，响应长度: 1887
2025-07-29 15:09:16 | ERROR    | agents.requirement_analysis:_extract_requirements_by_rules:346 | 规则提取需求失败: UNKNOWN
2025-07-29 15:09:16 | ERROR    | agents.requirement_analysis:_structure_analysis_result:263 | 结构化分析结果失败: UNKNOWN
2025-07-29 15:09:16 | ERROR    | agents.requirement_analysis:process_iteration:186 | 迭代处理失败: UNKNOWN
2025-07-29 15:09:16 | ERROR    | agents.base:iterative_process:308 | 第5轮迭代失败: UNKNOWN
2025-07-29 15:09:16 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 6/10, 进度: 0.60
2025-07-29 15:09:16 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.60
2025-07-29 15:09:53 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 96.88秒
2025-07-29 15:09:53 | INFO     | services.ollama_client:generate_thinking_chain:206 | 生成思维链，共0个步骤
2025-07-29 15:11:01 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 67.34秒
2025-07-29 15:11:01 | INFO     | agents.base:generate_with_thinking:158 | 思维链生成完成，共0步，响应长度: 1402
2025-07-29 15:11:01 | ERROR    | agents.requirement_analysis:_extract_requirements_by_rules:349 | 规则提取需求失败: AttributeError: UNKNOWN
2025-07-29 15:11:01 | ERROR    | agents.requirement_analysis:_structure_analysis_result:265 | 结构化分析结果失败: AttributeError: UNKNOWN
2025-07-29 15:11:01 | ERROR    | agents.requirement_analysis:process_iteration:187 | 迭代处理失败: AttributeError: UNKNOWN
2025-07-29 15:11:01 | ERROR    | agents.base:iterative_process:308 | 第2轮迭代失败: UNKNOWN
2025-07-29 15:11:01 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 3/10, 进度: 0.30
2025-07-29 15:11:01 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.30
2025-07-29 15:11:37 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 140.50秒
2025-07-29 15:11:37 | INFO     | services.ollama_client:generate_thinking_chain:206 | 生成思维链，共0个步骤
2025-07-29 15:12:01 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 60.12秒
2025-07-29 15:12:01 | INFO     | services.ollama_client:generate_thinking_chain:206 | 生成思维链，共0个步骤
2025-07-29 15:12:49 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 71.99秒
2025-07-29 15:12:49 | INFO     | agents.base:generate_with_thinking:158 | 思维链生成完成，共0步，响应长度: 1448
2025-07-29 15:12:49 | ERROR    | agents.requirement_analysis:_extract_requirements_by_rules:346 | 规则提取需求失败: UNKNOWN
2025-07-29 15:12:49 | ERROR    | agents.requirement_analysis:_structure_analysis_result:263 | 结构化分析结果失败: UNKNOWN
2025-07-29 15:12:49 | ERROR    | agents.requirement_analysis:process_iteration:186 | 迭代处理失败: UNKNOWN
2025-07-29 15:12:49 | ERROR    | agents.base:iterative_process:308 | 第6轮迭代失败: UNKNOWN
2025-07-29 15:12:49 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 7/10, 进度: 0.70
2025-07-29 15:12:49 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.70
2025-07-29 15:13:01 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 59.93秒
2025-07-29 15:13:01 | INFO     | agents.base:generate_with_thinking:158 | 思维链生成完成，共0步，响应长度: 1090
2025-07-29 15:13:01 | ERROR    | agents.requirement_analysis:_extract_requirements_by_rules:349 | 规则提取需求失败: AttributeError: UNKNOWN
2025-07-29 15:13:01 | ERROR    | agents.requirement_analysis:_structure_analysis_result:265 | 结构化分析结果失败: AttributeError: UNKNOWN
2025-07-29 15:13:01 | ERROR    | agents.requirement_analysis:process_iteration:187 | 迭代处理失败: AttributeError: UNKNOWN
2025-07-29 15:13:01 | ERROR    | agents.base:iterative_process:308 | 第3轮迭代失败: UNKNOWN
2025-07-29 15:13:01 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 4/10, 进度: 0.40
2025-07-29 15:13:01 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.40
2025-07-29 15:13:37 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 48.13秒
2025-07-29 15:13:37 | INFO     | services.ollama_client:generate_thinking_chain:206 | 生成思维链，共0个步骤
2025-07-29 15:14:39 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 98.70秒
2025-07-29 15:14:39 | INFO     | services.ollama_client:generate_thinking_chain:206 | 生成思维链，共0个步骤
2025-07-29 15:15:47 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 130.08秒
2025-07-29 15:15:47 | INFO     | agents.base:generate_with_thinking:158 | 思维链生成完成，共0步，响应长度: 3346
2025-07-29 15:15:47 | ERROR    | agents.requirement_analysis:_extract_requirements_by_rules:346 | 规则提取需求失败: UNKNOWN
2025-07-29 15:15:47 | ERROR    | agents.requirement_analysis:_structure_analysis_result:263 | 结构化分析结果失败: UNKNOWN
2025-07-29 15:15:47 | ERROR    | agents.requirement_analysis:process_iteration:186 | 迭代处理失败: UNKNOWN
2025-07-29 15:15:47 | ERROR    | agents.base:iterative_process:308 | 第7轮迭代失败: UNKNOWN
2025-07-29 15:15:47 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 8/10, 进度: 0.80
2025-07-29 15:15:47 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.80
2025-07-29 15:16:10 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 90.71秒
2025-07-29 15:16:10 | INFO     | agents.base:generate_with_thinking:158 | 思维链生成完成，共0步，响应长度: 1926
2025-07-29 15:16:10 | ERROR    | agents.requirement_analysis:_extract_requirements_by_rules:349 | 规则提取需求失败: AttributeError: UNKNOWN
2025-07-29 15:16:10 | ERROR    | agents.requirement_analysis:_structure_analysis_result:265 | 结构化分析结果失败: AttributeError: UNKNOWN
2025-07-29 15:16:10 | ERROR    | agents.requirement_analysis:process_iteration:187 | 迭代处理失败: AttributeError: UNKNOWN
2025-07-29 15:16:10 | ERROR    | agents.base:iterative_process:308 | 第4轮迭代失败: UNKNOWN
2025-07-29 15:16:10 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 5/10, 进度: 0.50
2025-07-29 15:16:10 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.50
2025-07-29 15:17:11 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 61.39秒
2025-07-29 15:17:11 | INFO     | services.ollama_client:generate_thinking_chain:206 | 生成思维链，共0个步骤
2025-07-29 15:17:35 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 107.55秒
2025-07-29 15:17:35 | INFO     | services.ollama_client:generate_thinking_chain:206 | 生成思维链，共0个步骤
2025-07-29 15:18:33 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 81.24秒
2025-07-29 15:18:33 | INFO     | agents.base:generate_with_thinking:158 | 思维链生成完成，共0步，响应长度: 1598
2025-07-29 15:18:33 | ERROR    | agents.requirement_analysis:_extract_requirements_by_rules:349 | 规则提取需求失败: AttributeError: UNKNOWN
2025-07-29 15:18:33 | ERROR    | agents.requirement_analysis:_structure_analysis_result:265 | 结构化分析结果失败: AttributeError: UNKNOWN
2025-07-29 15:18:33 | ERROR    | agents.requirement_analysis:process_iteration:187 | 迭代处理失败: AttributeError: UNKNOWN
2025-07-29 15:18:33 | ERROR    | agents.base:iterative_process:308 | 第5轮迭代失败: UNKNOWN
2025-07-29 15:18:33 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 6/10, 进度: 0.60
2025-07-29 15:18:33 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.60
2025-07-29 15:18:44 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 69.68秒
2025-07-29 15:18:44 | INFO     | agents.base:generate_with_thinking:158 | 思维链生成完成，共0步，响应长度: 1304
2025-07-29 15:18:44 | ERROR    | agents.requirement_analysis:_extract_requirements_by_rules:346 | 规则提取需求失败: UNKNOWN
2025-07-29 15:18:44 | ERROR    | agents.requirement_analysis:_structure_analysis_result:263 | 结构化分析结果失败: UNKNOWN
2025-07-29 15:18:44 | ERROR    | agents.requirement_analysis:process_iteration:186 | 迭代处理失败: UNKNOWN
2025-07-29 15:18:44 | ERROR    | agents.base:iterative_process:308 | 第8轮迭代失败: UNKNOWN
2025-07-29 15:18:44 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 9/10, 进度: 0.90
2025-07-29 15:18:44 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.90
2025-07-29 15:19:55 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 82.43秒
2025-07-29 15:19:55 | INFO     | services.ollama_client:generate_thinking_chain:206 | 生成思维链，共0个步骤
2025-07-29 15:20:14 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 89.11秒
2025-07-29 15:20:14 | INFO     | services.ollama_client:generate_thinking_chain:206 | 生成思维链，共0个步骤
2025-07-29 15:21:04 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 50.80秒
2025-07-29 15:21:04 | INFO     | agents.base:generate_with_thinking:158 | 思维链生成完成，共0步，响应长度: 1068
2025-07-29 15:21:04 | ERROR    | agents.requirement_analysis:_extract_requirements_by_rules:346 | 规则提取需求失败: UNKNOWN
2025-07-29 15:21:04 | ERROR    | agents.requirement_analysis:_structure_analysis_result:263 | 结构化分析结果失败: UNKNOWN
2025-07-29 15:21:04 | ERROR    | agents.requirement_analysis:process_iteration:186 | 迭代处理失败: UNKNOWN
2025-07-29 15:21:04 | ERROR    | agents.base:iterative_process:308 | 第9轮迭代失败: UNKNOWN
2025-07-29 15:21:04 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 10/10, 进度: 1.00
2025-07-29 15:21:04 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 1.00
2025-07-29 15:21:39 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 104.21秒
2025-07-29 15:21:39 | INFO     | agents.base:generate_with_thinking:158 | 思维链生成完成，共0步，响应长度: 2212
2025-07-29 15:21:39 | ERROR    | agents.requirement_analysis:_extract_requirements_by_rules:349 | 规则提取需求失败: AttributeError: UNKNOWN
2025-07-29 15:21:39 | ERROR    | agents.requirement_analysis:_structure_analysis_result:265 | 结构化分析结果失败: AttributeError: UNKNOWN
2025-07-29 15:21:39 | ERROR    | agents.requirement_analysis:process_iteration:187 | 迭代处理失败: AttributeError: UNKNOWN
2025-07-29 15:21:39 | ERROR    | agents.base:iterative_process:308 | 第6轮迭代失败: UNKNOWN
2025-07-29 15:21:39 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 7/10, 进度: 0.70
2025-07-29 15:21:39 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.70
2025-07-29 15:22:12 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 67.47秒
2025-07-29 15:22:12 | INFO     | services.ollama_client:generate_thinking_chain:206 | 生成思维链，共0个步骤
2025-07-29 15:23:00 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 48.50秒
2025-07-29 15:23:00 | INFO     | agents.base:generate_with_thinking:158 | 思维链生成完成，共0步，响应长度: 1077
2025-07-29 15:23:00 | ERROR    | agents.requirement_analysis:_extract_requirements_by_rules:346 | 规则提取需求失败: UNKNOWN
2025-07-29 15:23:00 | ERROR    | agents.requirement_analysis:_structure_analysis_result:263 | 结构化分析结果失败: UNKNOWN
2025-07-29 15:23:00 | ERROR    | agents.requirement_analysis:process_iteration:186 | 迭代处理失败: UNKNOWN
2025-07-29 15:23:00 | ERROR    | agents.base:iterative_process:308 | 第10轮迭代失败: UNKNOWN
2025-07-29 15:23:00 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.ERROR, 任务: 需求分析失败: UNKNOWN, 进度: 1.00
2025-07-29 15:23:00 | ERROR    | agents.requirement_analysis:process:134 | 需求分析处理失败: UNKNOWN
2025-07-29 15:23:00 | ERROR    | scripts.test_system_integration:test_agent_basic_function:183 | 智能体基本功能测试失败: UNKNOWN
2025-07-29 15:23:00 | INFO     | scripts.test_system_integration:run_all_tests:208 | 测试 智能体基本功能: 失败
2025-07-29 15:23:51 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 131.69秒
2025-07-29 15:23:51 | INFO     | services.ollama_client:generate_thinking_chain:206 | 生成思维链，共0个步骤
2025-07-29 15:24:31 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 39.63秒
2025-07-29 15:24:31 | INFO     | agents.base:generate_with_thinking:158 | 思维链生成完成，共0步，响应长度: 1233
2025-07-29 15:24:31 | ERROR    | agents.requirement_analysis:_extract_requirements_by_rules:349 | 规则提取需求失败: AttributeError: UNKNOWN
2025-07-29 15:24:31 | ERROR    | agents.requirement_analysis:_structure_analysis_result:265 | 结构化分析结果失败: AttributeError: UNKNOWN
2025-07-29 15:24:31 | ERROR    | agents.requirement_analysis:process_iteration:187 | 迭代处理失败: AttributeError: UNKNOWN
2025-07-29 15:24:31 | ERROR    | agents.base:iterative_process:308 | 第7轮迭代失败: UNKNOWN
2025-07-29 15:24:31 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 8/10, 进度: 0.80
2025-07-29 15:24:31 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.80
2025-07-29 15:25:23 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 52.82秒
2025-07-29 15:25:23 | INFO     | services.ollama_client:generate_thinking_chain:206 | 生成思维链，共0个步骤
2025-07-29 15:26:07 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 43.56秒
2025-07-29 15:26:07 | INFO     | agents.base:generate_with_thinking:158 | 思维链生成完成，共0步，响应长度: 1425
2025-07-29 15:26:07 | ERROR    | agents.requirement_analysis:_extract_requirements_by_rules:349 | 规则提取需求失败: AttributeError: UNKNOWN
2025-07-29 15:26:07 | ERROR    | agents.requirement_analysis:_structure_analysis_result:265 | 结构化分析结果失败: AttributeError: UNKNOWN
2025-07-29 15:26:07 | ERROR    | agents.requirement_analysis:process_iteration:187 | 迭代处理失败: AttributeError: UNKNOWN
2025-07-29 15:26:07 | ERROR    | agents.base:iterative_process:308 | 第8轮迭代失败: UNKNOWN
2025-07-29 15:26:07 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 9/10, 进度: 0.90
2025-07-29 15:26:07 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.90
2025-07-29 15:27:11 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 64.18秒
2025-07-29 15:27:11 | INFO     | services.ollama_client:generate_thinking_chain:206 | 生成思维链，共0个步骤
2025-07-29 15:28:57 | INFO     | core.logger:setup_logger:72 | 日志系统初始化完成
2025-07-29 15:29:05 | INFO     | __main__:main:173 | ============================================================
2025-07-29 15:29:05 | INFO     | __main__:main:174 | 异步多智能体测试用例生成框架
2025-07-29 15:29:05 | INFO     | __main__:main:175 | ============================================================
2025-07-29 15:29:05 | INFO     | __main__:main:176 | 启动时间: 2025-07-29 15:29:05
2025-07-29 15:29:05 | INFO     | __main__:initialize_components:34 | 开始初始化系统组件...
2025-07-29 15:29:05 | INFO     | __main__:initialize_components:38 | 初始化记忆系统...
2025-07-29 15:29:13 | INFO     | core.memory:initialize:62 | 已连接到现有集合: agent_memory
2025-07-29 15:29:13 | INFO     | core.memory:initialize:72 | ChromaDB向量记忆系统初始化完成
2025-07-29 15:29:13 | INFO     | core.memory:get_collection_stats:494 | 集合统计: 0 个文档
2025-07-29 15:29:13 | INFO     | __main__:initialize_components:42 | 记忆系统初始化成功
2025-07-29 15:29:13 | INFO     | __main__:initialize_components:49 | 初始化可追溯性系统...
2025-07-29 15:29:13 | INFO     | core.traceability:_load_existing_data:258 | 加载了 1 个工件和 0 个链接
2025-07-29 15:29:13 | INFO     | core.traceability:initialize:232 | 可追溯性矩阵初始化完成
2025-07-29 15:29:13 | INFO     | __main__:initialize_components:53 | 可追溯性系统初始化成功，当前统计: {'artifacts': {'total': 1, 'by_type': {'requirement': 1}, 'by_status': {'draft': 1}}, 'links': {'total': 0, 'by_type': {}}, 'coverage': {}, 'graph': {'nodes': 0, 'edges': 0, 'density': 0}}
2025-07-29 15:29:13 | INFO     | __main__:initialize_components:60 | 检查Ollama连接...
2025-07-29 15:29:14 | INFO     | services.ollama_client:health_check:61 | 模型 deepseek-r1:1.5b 可用
2025-07-29 15:29:14 | INFO     | services.ollama_client:initialize:41 | Ollama客户端初始化成功，模型: deepseek-r1:1.5b
2025-07-29 15:29:14 | INFO     | __main__:initialize_components:65 | Ollama连接成功，可用模型数: 2
2025-07-29 15:29:14 | INFO     | __main__:initialize_components:74 | 系统组件状态:
2025-07-29 15:29:14 | INFO     | __main__:initialize_components:76 |   memory: healthy
2025-07-29 15:29:14 | INFO     | __main__:initialize_components:76 |   traceability: healthy
2025-07-29 15:29:14 | INFO     | __main__:initialize_components:76 |   ollama: healthy (2 models)
2025-07-29 15:29:14 | INFO     | __main__:main:186 | 系统健康状态: healthy
2025-07-29 15:29:14 | INFO     | __main__:run_startup_tests:150 | 运行启动测试...
2025-07-29 15:29:14 | INFO     | scripts.test_system_integration:run_all_tests:179 | 开始系统集成测试...
2025-07-29 15:29:14 | INFO     | scripts.test_system_integration:run_all_tests:191 | 执行测试: Ollama连接
2025-07-29 15:29:14 | INFO     | scripts.test_system_integration:test_ollama_connection:33 | 测试Ollama连接...
2025-07-29 15:29:14 | INFO     | scripts.test_system_integration:test_ollama_connection:40 | Ollama连接成功，可用模型: 2
2025-07-29 15:29:14 | INFO     | scripts.test_system_integration:run_all_tests:195 | 测试 Ollama连接: 通过
2025-07-29 15:29:14 | INFO     | scripts.test_system_integration:run_all_tests:191 | 执行测试: 记忆系统
2025-07-29 15:29:14 | INFO     | scripts.test_system_integration:test_memory_system:64 | 测试记忆系统...
2025-07-29 15:29:14 | INFO     | core.memory:get_collection_stats:494 | 集合统计: 0 个文档
2025-07-29 15:29:14 | INFO     | scripts.test_system_integration:test_memory_system:73 | 记忆系统测试成功，文档数量: 0
2025-07-29 15:29:14 | INFO     | scripts.test_system_integration:run_all_tests:195 | 测试 记忆系统: 通过
2025-07-29 15:29:14 | INFO     | scripts.test_system_integration:run_all_tests:191 | 执行测试: 可追溯性系统
2025-07-29 15:29:14 | INFO     | scripts.test_system_integration:test_traceability_system:98 | 测试可追溯性系统...
2025-07-29 15:29:14 | INFO     | core.traceability:create_artifact:308 | 创建工件成功: requirement_e45f61f8 (集成测试需求)
2025-07-29 15:29:14 | INFO     | scripts.test_system_integration:test_traceability_system:118 | 可追溯性系统测试成功
2025-07-29 15:29:14 | INFO     | scripts.test_system_integration:run_all_tests:195 | 测试 可追溯性系统: 通过
2025-07-29 15:29:14 | INFO     | scripts.test_system_integration:run_all_tests:191 | 执行测试: 智能体基本功能
2025-07-29 15:29:14 | INFO     | scripts.test_system_integration:test_agent_basic_function:142 | 测试智能体基本功能...
2025-07-29 15:29:14 | INFO     | agents.base:__init__:53 | Agent初始化完成: AgentType.REQUIREMENT_ANALYSIS_4733a55c
2025-07-29 15:29:14 | INFO     | agents.base:initialize:64 | Agent组件初始化完成: AgentType.REQUIREMENT_ANALYSIS_4733a55c
2025-07-29 15:29:14 | INFO     | agents.requirement_analysis:initialize:73 | 需求分析Agent初始化完成
2025-07-29 15:29:14 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.PROCESSING, 任务: 开始需求分析, 进度: 0.00
2025-07-29 15:29:14 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 1/10, 进度: 0.10
2025-07-29 15:29:14 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.10
2025-07-29 15:29:53 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 39.11秒
2025-07-29 15:29:53 | INFO     | services.ollama_client:generate_thinking_chain:206 | 生成思维链，共0个步骤
2025-07-29 15:30:44 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 51.30秒
2025-07-29 15:30:44 | INFO     | agents.base:generate_with_thinking:158 | 思维链生成完成，共0步，响应长度: 1774
2025-07-29 15:30:44 | ERROR    | agents.requirement_analysis:_extract_requirements_by_rules:349 | 规则提取需求失败: AttributeError: UNKNOWN
2025-07-29 15:30:44 | ERROR    | agents.requirement_analysis:_structure_analysis_result:265 | 结构化分析结果失败: AttributeError: UNKNOWN
2025-07-29 15:30:44 | ERROR    | agents.requirement_analysis:process_iteration:187 | 迭代处理失败: AttributeError: UNKNOWN
2025-07-29 15:30:44 | ERROR    | agents.base:iterative_process:308 | 第1轮迭代失败: UNKNOWN
2025-07-29 15:30:44 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 2/10, 进度: 0.20
2025-07-29 15:30:44 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.20
2025-07-29 15:31:41 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 56.99秒
2025-07-29 15:31:41 | INFO     | services.ollama_client:generate_thinking_chain:206 | 生成思维链，共0个步骤
2025-07-29 15:32:30 | INFO     | services.ollama_client:generate_response:137 | 生成完成，耗时: 48.58秒
2025-07-29 15:32:30 | INFO     | agents.base:generate_with_thinking:158 | 思维链生成完成，共0步，响应长度: 1666
2025-07-29 15:32:30 | ERROR    | agents.requirement_analysis:_extract_requirements_by_rules:349 | 规则提取需求失败: AttributeError: UNKNOWN
2025-07-29 15:32:30 | ERROR    | agents.requirement_analysis:_structure_analysis_result:265 | 结构化分析结果失败: AttributeError: UNKNOWN
2025-07-29 15:32:30 | ERROR    | agents.requirement_analysis:process_iteration:187 | 迭代处理失败: AttributeError: UNKNOWN
2025-07-29 15:32:30 | ERROR    | agents.base:iterative_process:308 | 第2轮迭代失败: UNKNOWN
2025-07-29 15:32:30 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 3/10, 进度: 0.30
2025-07-29 15:32:30 | INFO     | agents.base:update_status:84 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.30
