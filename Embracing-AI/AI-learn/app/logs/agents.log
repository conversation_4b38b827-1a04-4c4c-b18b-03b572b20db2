2025-07-29 11:41:55 | INFO     | SystemStartup | ============================================================
2025-07-29 11:41:55 | INFO     | SystemStartup | 异步多智能体测试用例生成框架
2025-07-29 11:41:55 | INFO     | SystemStartup | ============================================================
2025-07-29 11:41:55 | INFO     | SystemStartup | 启动时间: 2025-07-29 11:41:55
2025-07-29 11:41:55 | INFO     | SystemStartup | 开始初始化系统组件...
2025-07-29 11:41:55 | INFO     | SystemStartup | 初始化记忆系统...
2025-07-29 11:42:03 | INFO     | VectorMemory | 已连接到现有集合: agent_memory
2025-07-29 11:42:03 | INFO     | VectorMemory | ChromaDB向量记忆系统初始化完成
2025-07-29 11:42:03 | INFO     | VectorMemory | 集合统计: 0 个文档
2025-07-29 11:42:03 | INFO     | SystemStartup | 记忆系统初始化成功
2025-07-29 11:42:03 | INFO     | SystemStartup | 初始化可追溯性系统...
2025-07-29 11:42:03 | ERROR    | TraceabilityMatrix | 加载现有数据失败: object NoneType can't be used in 'await' expression
2025-07-29 11:42:03 | INFO     | TraceabilityMatrix | 可追溯性矩阵初始化完成
2025-07-29 11:42:03 | INFO     | SystemStartup | 可追溯性系统初始化成功，当前统计: {'artifacts': {'total': 0, 'by_type': {}, 'by_status': {}}, 'links': {'total': 0, 'by_type': {}}, 'coverage': {}, 'graph': {'nodes': 0, 'edges': 0, 'density': 0}}
2025-07-29 11:42:03 | INFO     | SystemStartup | 检查Ollama连接...
2025-07-29 11:42:03 | INFO     | OllamaClient | 模型 deepseek-r1:1.5b 可用
2025-07-29 11:42:03 | INFO     | OllamaClient | Ollama客户端初始化成功，模型: deepseek-r1:1.5b
2025-07-29 11:42:03 | INFO     | SystemStartup | Ollama连接成功，可用模型数: 2
2025-07-29 11:42:03 | INFO     | SystemStartup | 系统组件状态:
2025-07-29 11:42:03 | INFO     | SystemStartup |   memory: healthy
2025-07-29 11:42:03 | INFO     | SystemStartup |   traceability: healthy
2025-07-29 11:42:03 | INFO     | SystemStartup |   ollama: healthy (2 models)
2025-07-29 11:42:03 | INFO     | SystemStartup | 系统健康状态: healthy
2025-07-29 11:42:03 | INFO     | SystemStartup | 运行启动测试...
2025-07-29 11:42:03 | ERROR    | SystemStartup | 启动测试失败: No module named 'exceptions'
2025-07-29 11:42:03 | WARNING  | SystemStartup | 启动测试未完全通过，但继续启动系统
2025-07-29 11:42:03 | INFO     | SystemStartup | 系统初始化完成，启动API服务器...
2025-07-29 11:42:03 | INFO     | SystemStartup | 启动FastAPI服务器...
2025-07-29 11:42:03 | ERROR    | SystemStartup | API服务器启动失败: No module named 'exceptions'
2025-07-29 11:42:03 | ERROR    | SystemStartup | 系统启动失败: No module named 'exceptions'
2025-07-29 13:42:22 | INFO     | SystemStartup | ============================================================
2025-07-29 13:42:22 | INFO     | SystemStartup | 异步多智能体测试用例生成框架
2025-07-29 13:42:22 | INFO     | SystemStartup | ============================================================
2025-07-29 13:42:22 | INFO     | SystemStartup | 启动时间: 2025-07-29 13:42:22
2025-07-29 13:42:22 | INFO     | SystemStartup | 开始初始化系统组件...
2025-07-29 13:42:22 | INFO     | SystemStartup | 初始化记忆系统...
2025-07-29 13:42:32 | INFO     | VectorMemory | 已连接到现有集合: agent_memory
2025-07-29 13:42:32 | INFO     | VectorMemory | ChromaDB向量记忆系统初始化完成
2025-07-29 13:42:32 | INFO     | VectorMemory | 集合统计: 0 个文档
2025-07-29 13:42:32 | INFO     | SystemStartup | 记忆系统初始化成功
2025-07-29 13:42:32 | INFO     | SystemStartup | 初始化可追溯性系统...
2025-07-29 13:42:32 | ERROR    | TraceabilityMatrix | 加载现有数据失败: object NoneType can't be used in 'await' expression
2025-07-29 13:42:32 | INFO     | TraceabilityMatrix | 可追溯性矩阵初始化完成
2025-07-29 13:42:32 | INFO     | SystemStartup | 可追溯性系统初始化成功，当前统计: {'artifacts': {'total': 0, 'by_type': {}, 'by_status': {}}, 'links': {'total': 0, 'by_type': {}}, 'coverage': {}, 'graph': {'nodes': 0, 'edges': 0, 'density': 0}}
2025-07-29 13:42:32 | INFO     | SystemStartup | 检查Ollama连接...
2025-07-29 13:42:32 | INFO     | OllamaClient | 模型 deepseek-r1:1.5b 可用
2025-07-29 13:42:32 | INFO     | OllamaClient | Ollama客户端初始化成功，模型: deepseek-r1:1.5b
2025-07-29 13:42:32 | INFO     | SystemStartup | Ollama连接成功，可用模型数: 2
2025-07-29 13:42:32 | INFO     | SystemStartup | 系统组件状态:
2025-07-29 13:42:32 | INFO     | SystemStartup |   memory: healthy
2025-07-29 13:42:32 | INFO     | SystemStartup |   traceability: healthy
2025-07-29 13:42:32 | INFO     | SystemStartup |   ollama: healthy (2 models)
2025-07-29 13:42:32 | INFO     | SystemStartup | 系统健康状态: healthy
2025-07-29 13:42:32 | INFO     | SystemStartup | 运行启动测试...
2025-07-29 13:42:32 | ERROR    | SystemStartup | 启动测试失败: No module named 'exceptions'
2025-07-29 13:42:32 | WARNING  | SystemStartup | 启动测试未完全通过，但继续启动系统
2025-07-29 13:42:32 | INFO     | SystemStartup | 系统初始化完成，启动API服务器...
2025-07-29 13:42:32 | INFO     | SystemStartup | 启动FastAPI服务器...
2025-07-29 13:42:33 | ERROR    | SystemStartup | 系统启动失败: No module named 'exceptions'
2025-07-29 13:47:19 | INFO     | SystemStartup | ============================================================
2025-07-29 13:47:19 | INFO     | SystemStartup | 异步多智能体测试用例生成框架
2025-07-29 13:47:19 | INFO     | SystemStartup | ============================================================
2025-07-29 13:47:19 | INFO     | SystemStartup | 启动时间: 2025-07-29 13:47:19
2025-07-29 13:47:19 | INFO     | SystemStartup | 开始初始化系统组件...
2025-07-29 13:47:19 | INFO     | SystemStartup | 初始化记忆系统...
2025-07-29 13:47:27 | INFO     | VectorMemory | 已连接到现有集合: agent_memory
2025-07-29 13:47:27 | INFO     | VectorMemory | ChromaDB向量记忆系统初始化完成
2025-07-29 13:47:27 | INFO     | VectorMemory | 集合统计: 0 个文档
2025-07-29 13:47:27 | INFO     | SystemStartup | 记忆系统初始化成功
2025-07-29 13:47:27 | INFO     | SystemStartup | 初始化可追溯性系统...
2025-07-29 13:47:27 | ERROR    | SystemStartup | 可追溯性系统初始化失败: object NoneType can't be used in 'await' expression
2025-07-29 13:47:27 | INFO     | SystemStartup | 检查Ollama连接...
2025-07-29 13:47:27 | INFO     | OllamaClient | 模型 deepseek-r1:1.5b 可用
2025-07-29 13:47:27 | INFO     | OllamaClient | Ollama客户端初始化成功，模型: deepseek-r1:1.5b
2025-07-29 13:47:27 | INFO     | SystemStartup | Ollama连接成功，可用模型数: 2
2025-07-29 13:47:27 | INFO     | SystemStartup | 系统组件状态:
2025-07-29 13:47:27 | INFO     | SystemStartup |   memory: healthy
2025-07-29 13:47:27 | INFO     | SystemStartup |   traceability: error: object NoneType can't be used in 'await' expression
2025-07-29 13:47:27 | INFO     | SystemStartup |   ollama: healthy (2 models)
2025-07-29 13:47:27 | INFO     | SystemStartup | 系统健康状态: degraded
2025-07-29 13:47:27 | INFO     | SystemStartup | 运行启动测试...
2025-07-29 13:47:27 | ERROR    | SystemStartup | 系统启动失败: No module named 'exceptions'
2025-07-29 13:50:45 | INFO     | SystemStartup | ============================================================
2025-07-29 13:50:45 | INFO     | SystemStartup | 异步多智能体测试用例生成框架
2025-07-29 13:50:45 | INFO     | SystemStartup | ============================================================
2025-07-29 13:50:45 | INFO     | SystemStartup | 启动时间: 2025-07-29 13:50:45
2025-07-29 13:50:45 | INFO     | SystemStartup | 开始初始化系统组件...
2025-07-29 13:50:45 | INFO     | SystemStartup | 初始化记忆系统...
2025-07-29 13:50:53 | INFO     | VectorMemory | 已连接到现有集合: agent_memory
2025-07-29 13:50:53 | INFO     | VectorMemory | ChromaDB向量记忆系统初始化完成
2025-07-29 13:50:53 | INFO     | VectorMemory | 集合统计: 0 个文档
2025-07-29 13:50:53 | INFO     | SystemStartup | 记忆系统初始化成功
2025-07-29 13:50:53 | INFO     | SystemStartup | 初始化可追溯性系统...
2025-07-29 13:51:46 | INFO     | SystemStartup | ============================================================
2025-07-29 13:51:46 | INFO     | SystemStartup | 异步多智能体测试用例生成框架
2025-07-29 13:51:46 | INFO     | SystemStartup | ============================================================
2025-07-29 13:51:46 | INFO     | SystemStartup | 启动时间: 2025-07-29 13:51:46
2025-07-29 13:51:46 | INFO     | SystemStartup | 开始初始化系统组件...
2025-07-29 13:51:46 | INFO     | SystemStartup | 初始化记忆系统...
2025-07-29 13:51:54 | INFO     | VectorMemory | 已连接到现有集合: agent_memory
2025-07-29 13:51:54 | INFO     | VectorMemory | ChromaDB向量记忆系统初始化完成
2025-07-29 13:51:54 | INFO     | VectorMemory | 集合统计: 0 个文档
2025-07-29 13:51:54 | INFO     | SystemStartup | 记忆系统初始化成功
2025-07-29 13:51:54 | INFO     | SystemStartup | 初始化可追溯性系统...
2025-07-29 14:49:54 | INFO     | FastAPI | 正在启动异步多智能体测试用例生成框架...
2025-07-29 14:50:03 | INFO     | VectorMemory | 已连接到现有集合: agent_memory
2025-07-29 14:50:03 | INFO     | VectorMemory | ChromaDB向量记忆系统初始化完成
2025-07-29 14:50:03 | INFO     | FastAPI | 内存管理器初始化完成
2025-07-29 14:50:03 | ERROR    | FastAPI | 系统启动失败: object NoneType can't be used in 'await' expression
2025-07-29 14:55:42 | INFO     | FastAPI | 正在启动异步多智能体测试用例生成框架...
2025-07-29 14:55:51 | INFO     | VectorMemory | 已连接到现有集合: agent_memory
2025-07-29 14:55:51 | INFO     | VectorMemory | ChromaDB向量记忆系统初始化完成
2025-07-29 14:55:51 | INFO     | FastAPI | 内存管理器初始化完成
2025-07-29 14:55:51 | INFO     | TraceabilityMatrix | 加载了 0 个工件和 0 个链接
2025-07-29 14:55:51 | INFO     | TraceabilityMatrix | 可追溯性矩阵初始化完成
2025-07-29 14:55:51 | INFO     | FastAPI | 可追溯性矩阵初始化完成
2025-07-29 14:55:51 | INFO     | FastAPI | 系统启动完成
2025-07-29 14:57:28 | INFO     | SystemStartup | ============================================================
2025-07-29 14:57:28 | INFO     | SystemStartup | 异步多智能体测试用例生成框架
2025-07-29 14:57:28 | INFO     | SystemStartup | ============================================================
2025-07-29 14:57:28 | INFO     | SystemStartup | 启动时间: 2025-07-29 14:57:28
2025-07-29 14:57:28 | INFO     | SystemStartup | 开始初始化系统组件...
2025-07-29 14:57:28 | INFO     | SystemStartup | 初始化记忆系统...
2025-07-29 14:57:28 | INFO     | FastAPI | 正在关闭系统...
2025-07-29 14:57:28 | INFO     | VectorMemory | 记忆系统资源清理完成
2025-07-29 14:57:28 | INFO     | FastAPI | 系统关闭完成
