2025-07-29 11:41:55 | INFO     | SystemStartup | ============================================================
2025-07-29 11:41:55 | INFO     | SystemStartup | 异步多智能体测试用例生成框架
2025-07-29 11:41:55 | INFO     | SystemStartup | ============================================================
2025-07-29 11:41:55 | INFO     | SystemStartup | 启动时间: 2025-07-29 11:41:55
2025-07-29 11:41:55 | INFO     | SystemStartup | 开始初始化系统组件...
2025-07-29 11:41:55 | INFO     | SystemStartup | 初始化记忆系统...
2025-07-29 11:42:03 | INFO     | VectorMemory | 已连接到现有集合: agent_memory
2025-07-29 11:42:03 | INFO     | VectorMemory | ChromaDB向量记忆系统初始化完成
2025-07-29 11:42:03 | INFO     | VectorMemory | 集合统计: 0 个文档
2025-07-29 11:42:03 | INFO     | SystemStartup | 记忆系统初始化成功
2025-07-29 11:42:03 | INFO     | SystemStartup | 初始化可追溯性系统...
2025-07-29 11:42:03 | ERROR    | TraceabilityMatrix | 加载现有数据失败: object NoneType can't be used in 'await' expression
2025-07-29 11:42:03 | INFO     | TraceabilityMatrix | 可追溯性矩阵初始化完成
2025-07-29 11:42:03 | INFO     | SystemStartup | 可追溯性系统初始化成功，当前统计: {'artifacts': {'total': 0, 'by_type': {}, 'by_status': {}}, 'links': {'total': 0, 'by_type': {}}, 'coverage': {}, 'graph': {'nodes': 0, 'edges': 0, 'density': 0}}
2025-07-29 11:42:03 | INFO     | SystemStartup | 检查Ollama连接...
2025-07-29 11:42:03 | INFO     | OllamaClient | 模型 deepseek-r1:1.5b 可用
2025-07-29 11:42:03 | INFO     | OllamaClient | Ollama客户端初始化成功，模型: deepseek-r1:1.5b
2025-07-29 11:42:03 | INFO     | SystemStartup | Ollama连接成功，可用模型数: 2
2025-07-29 11:42:03 | INFO     | SystemStartup | 系统组件状态:
2025-07-29 11:42:03 | INFO     | SystemStartup |   memory: healthy
2025-07-29 11:42:03 | INFO     | SystemStartup |   traceability: healthy
2025-07-29 11:42:03 | INFO     | SystemStartup |   ollama: healthy (2 models)
2025-07-29 11:42:03 | INFO     | SystemStartup | 系统健康状态: healthy
2025-07-29 11:42:03 | INFO     | SystemStartup | 运行启动测试...
2025-07-29 11:42:03 | ERROR    | SystemStartup | 启动测试失败: No module named 'exceptions'
2025-07-29 11:42:03 | WARNING  | SystemStartup | 启动测试未完全通过，但继续启动系统
2025-07-29 11:42:03 | INFO     | SystemStartup | 系统初始化完成，启动API服务器...
2025-07-29 11:42:03 | INFO     | SystemStartup | 启动FastAPI服务器...
2025-07-29 11:42:03 | ERROR    | SystemStartup | API服务器启动失败: No module named 'exceptions'
2025-07-29 11:42:03 | ERROR    | SystemStartup | 系统启动失败: No module named 'exceptions'
2025-07-29 13:42:22 | INFO     | SystemStartup | ============================================================
2025-07-29 13:42:22 | INFO     | SystemStartup | 异步多智能体测试用例生成框架
2025-07-29 13:42:22 | INFO     | SystemStartup | ============================================================
2025-07-29 13:42:22 | INFO     | SystemStartup | 启动时间: 2025-07-29 13:42:22
2025-07-29 13:42:22 | INFO     | SystemStartup | 开始初始化系统组件...
2025-07-29 13:42:22 | INFO     | SystemStartup | 初始化记忆系统...
2025-07-29 13:42:32 | INFO     | VectorMemory | 已连接到现有集合: agent_memory
2025-07-29 13:42:32 | INFO     | VectorMemory | ChromaDB向量记忆系统初始化完成
2025-07-29 13:42:32 | INFO     | VectorMemory | 集合统计: 0 个文档
2025-07-29 13:42:32 | INFO     | SystemStartup | 记忆系统初始化成功
2025-07-29 13:42:32 | INFO     | SystemStartup | 初始化可追溯性系统...
2025-07-29 13:42:32 | ERROR    | TraceabilityMatrix | 加载现有数据失败: object NoneType can't be used in 'await' expression
2025-07-29 13:42:32 | INFO     | TraceabilityMatrix | 可追溯性矩阵初始化完成
2025-07-29 13:42:32 | INFO     | SystemStartup | 可追溯性系统初始化成功，当前统计: {'artifacts': {'total': 0, 'by_type': {}, 'by_status': {}}, 'links': {'total': 0, 'by_type': {}}, 'coverage': {}, 'graph': {'nodes': 0, 'edges': 0, 'density': 0}}
2025-07-29 13:42:32 | INFO     | SystemStartup | 检查Ollama连接...
2025-07-29 13:42:32 | INFO     | OllamaClient | 模型 deepseek-r1:1.5b 可用
2025-07-29 13:42:32 | INFO     | OllamaClient | Ollama客户端初始化成功，模型: deepseek-r1:1.5b
2025-07-29 13:42:32 | INFO     | SystemStartup | Ollama连接成功，可用模型数: 2
2025-07-29 13:42:32 | INFO     | SystemStartup | 系统组件状态:
2025-07-29 13:42:32 | INFO     | SystemStartup |   memory: healthy
2025-07-29 13:42:32 | INFO     | SystemStartup |   traceability: healthy
2025-07-29 13:42:32 | INFO     | SystemStartup |   ollama: healthy (2 models)
2025-07-29 13:42:32 | INFO     | SystemStartup | 系统健康状态: healthy
2025-07-29 13:42:32 | INFO     | SystemStartup | 运行启动测试...
2025-07-29 13:42:32 | ERROR    | SystemStartup | 启动测试失败: No module named 'exceptions'
2025-07-29 13:42:32 | WARNING  | SystemStartup | 启动测试未完全通过，但继续启动系统
2025-07-29 13:42:32 | INFO     | SystemStartup | 系统初始化完成，启动API服务器...
2025-07-29 13:42:32 | INFO     | SystemStartup | 启动FastAPI服务器...
2025-07-29 13:42:33 | ERROR    | SystemStartup | 系统启动失败: No module named 'exceptions'
2025-07-29 13:47:19 | INFO     | SystemStartup | ============================================================
2025-07-29 13:47:19 | INFO     | SystemStartup | 异步多智能体测试用例生成框架
2025-07-29 13:47:19 | INFO     | SystemStartup | ============================================================
2025-07-29 13:47:19 | INFO     | SystemStartup | 启动时间: 2025-07-29 13:47:19
2025-07-29 13:47:19 | INFO     | SystemStartup | 开始初始化系统组件...
2025-07-29 13:47:19 | INFO     | SystemStartup | 初始化记忆系统...
2025-07-29 13:47:27 | INFO     | VectorMemory | 已连接到现有集合: agent_memory
2025-07-29 13:47:27 | INFO     | VectorMemory | ChromaDB向量记忆系统初始化完成
2025-07-29 13:47:27 | INFO     | VectorMemory | 集合统计: 0 个文档
2025-07-29 13:47:27 | INFO     | SystemStartup | 记忆系统初始化成功
2025-07-29 13:47:27 | INFO     | SystemStartup | 初始化可追溯性系统...
2025-07-29 13:47:27 | ERROR    | SystemStartup | 可追溯性系统初始化失败: object NoneType can't be used in 'await' expression
2025-07-29 13:47:27 | INFO     | SystemStartup | 检查Ollama连接...
2025-07-29 13:47:27 | INFO     | OllamaClient | 模型 deepseek-r1:1.5b 可用
2025-07-29 13:47:27 | INFO     | OllamaClient | Ollama客户端初始化成功，模型: deepseek-r1:1.5b
2025-07-29 13:47:27 | INFO     | SystemStartup | Ollama连接成功，可用模型数: 2
2025-07-29 13:47:27 | INFO     | SystemStartup | 系统组件状态:
2025-07-29 13:47:27 | INFO     | SystemStartup |   memory: healthy
2025-07-29 13:47:27 | INFO     | SystemStartup |   traceability: error: object NoneType can't be used in 'await' expression
2025-07-29 13:47:27 | INFO     | SystemStartup |   ollama: healthy (2 models)
2025-07-29 13:47:27 | INFO     | SystemStartup | 系统健康状态: degraded
2025-07-29 13:47:27 | INFO     | SystemStartup | 运行启动测试...
2025-07-29 13:47:27 | ERROR    | SystemStartup | 系统启动失败: No module named 'exceptions'
2025-07-29 13:50:45 | INFO     | SystemStartup | ============================================================
2025-07-29 13:50:45 | INFO     | SystemStartup | 异步多智能体测试用例生成框架
2025-07-29 13:50:45 | INFO     | SystemStartup | ============================================================
2025-07-29 13:50:45 | INFO     | SystemStartup | 启动时间: 2025-07-29 13:50:45
2025-07-29 13:50:45 | INFO     | SystemStartup | 开始初始化系统组件...
2025-07-29 13:50:45 | INFO     | SystemStartup | 初始化记忆系统...
2025-07-29 13:50:53 | INFO     | VectorMemory | 已连接到现有集合: agent_memory
2025-07-29 13:50:53 | INFO     | VectorMemory | ChromaDB向量记忆系统初始化完成
2025-07-29 13:50:53 | INFO     | VectorMemory | 集合统计: 0 个文档
2025-07-29 13:50:53 | INFO     | SystemStartup | 记忆系统初始化成功
2025-07-29 13:50:53 | INFO     | SystemStartup | 初始化可追溯性系统...
2025-07-29 13:51:46 | INFO     | SystemStartup | ============================================================
2025-07-29 13:51:46 | INFO     | SystemStartup | 异步多智能体测试用例生成框架
2025-07-29 13:51:46 | INFO     | SystemStartup | ============================================================
2025-07-29 13:51:46 | INFO     | SystemStartup | 启动时间: 2025-07-29 13:51:46
2025-07-29 13:51:46 | INFO     | SystemStartup | 开始初始化系统组件...
2025-07-29 13:51:46 | INFO     | SystemStartup | 初始化记忆系统...
2025-07-29 13:51:54 | INFO     | VectorMemory | 已连接到现有集合: agent_memory
2025-07-29 13:51:54 | INFO     | VectorMemory | ChromaDB向量记忆系统初始化完成
2025-07-29 13:51:54 | INFO     | VectorMemory | 集合统计: 0 个文档
2025-07-29 13:51:54 | INFO     | SystemStartup | 记忆系统初始化成功
2025-07-29 13:51:54 | INFO     | SystemStartup | 初始化可追溯性系统...
2025-07-29 14:49:54 | INFO     | FastAPI | 正在启动异步多智能体测试用例生成框架...
2025-07-29 14:50:03 | INFO     | VectorMemory | 已连接到现有集合: agent_memory
2025-07-29 14:50:03 | INFO     | VectorMemory | ChromaDB向量记忆系统初始化完成
2025-07-29 14:50:03 | INFO     | FastAPI | 内存管理器初始化完成
2025-07-29 14:50:03 | ERROR    | FastAPI | 系统启动失败: object NoneType can't be used in 'await' expression
2025-07-29 14:55:42 | INFO     | FastAPI | 正在启动异步多智能体测试用例生成框架...
2025-07-29 14:55:51 | INFO     | VectorMemory | 已连接到现有集合: agent_memory
2025-07-29 14:55:51 | INFO     | VectorMemory | ChromaDB向量记忆系统初始化完成
2025-07-29 14:55:51 | INFO     | FastAPI | 内存管理器初始化完成
2025-07-29 14:55:51 | INFO     | TraceabilityMatrix | 加载了 0 个工件和 0 个链接
2025-07-29 14:55:51 | INFO     | TraceabilityMatrix | 可追溯性矩阵初始化完成
2025-07-29 14:55:51 | INFO     | FastAPI | 可追溯性矩阵初始化完成
2025-07-29 14:55:51 | INFO     | FastAPI | 系统启动完成
2025-07-29 14:57:28 | INFO     | SystemStartup | ============================================================
2025-07-29 14:57:28 | INFO     | SystemStartup | 异步多智能体测试用例生成框架
2025-07-29 14:57:28 | INFO     | SystemStartup | ============================================================
2025-07-29 14:57:28 | INFO     | SystemStartup | 启动时间: 2025-07-29 14:57:28
2025-07-29 14:57:28 | INFO     | SystemStartup | 开始初始化系统组件...
2025-07-29 14:57:28 | INFO     | SystemStartup | 初始化记忆系统...
2025-07-29 14:57:28 | INFO     | FastAPI | 正在关闭系统...
2025-07-29 14:57:28 | INFO     | VectorMemory | 记忆系统资源清理完成
2025-07-29 14:57:28 | INFO     | FastAPI | 系统关闭完成
2025-07-29 14:57:37 | INFO     | VectorMemory | 已连接到现有集合: agent_memory
2025-07-29 14:57:37 | INFO     | VectorMemory | ChromaDB向量记忆系统初始化完成
2025-07-29 14:57:37 | INFO     | VectorMemory | 集合统计: 0 个文档
2025-07-29 14:57:37 | INFO     | SystemStartup | 记忆系统初始化成功
2025-07-29 14:57:37 | INFO     | SystemStartup | 初始化可追溯性系统...
2025-07-29 14:57:37 | INFO     | TraceabilityMatrix | 加载了 0 个工件和 0 个链接
2025-07-29 14:57:37 | INFO     | TraceabilityMatrix | 可追溯性矩阵初始化完成
2025-07-29 14:57:37 | INFO     | SystemStartup | 可追溯性系统初始化成功，当前统计: {'artifacts': {'total': 0, 'by_type': {}, 'by_status': {}}, 'links': {'total': 0, 'by_type': {}}, 'coverage': {}, 'graph': {'nodes': 0, 'edges': 0, 'density': 0}}
2025-07-29 14:57:37 | INFO     | SystemStartup | 检查Ollama连接...
2025-07-29 14:57:37 | INFO     | OllamaClient | 模型 deepseek-r1:1.5b 可用
2025-07-29 14:57:37 | INFO     | OllamaClient | Ollama客户端初始化成功，模型: deepseek-r1:1.5b
2025-07-29 14:57:37 | INFO     | SystemStartup | Ollama连接成功，可用模型数: 2
2025-07-29 14:57:37 | INFO     | SystemStartup | 系统组件状态:
2025-07-29 14:57:37 | INFO     | SystemStartup |   memory: healthy
2025-07-29 14:57:37 | INFO     | SystemStartup |   traceability: healthy
2025-07-29 14:57:37 | INFO     | SystemStartup |   ollama: healthy (2 models)
2025-07-29 14:57:37 | INFO     | SystemStartup | 系统健康状态: healthy
2025-07-29 14:57:37 | INFO     | SystemStartup | 运行启动测试...
2025-07-29 14:57:38 | INFO     | SystemIntegrationTest | 开始系统集成测试...
2025-07-29 14:57:38 | INFO     | SystemIntegrationTest | 执行测试: Ollama连接
2025-07-29 14:57:38 | INFO     | SystemIntegrationTest | 测试Ollama连接...
2025-07-29 14:57:38 | ERROR    | SystemIntegrationTest | Ollama连接失败: 'coroutine' object has no attribute 'list_models'
2025-07-29 14:57:38 | INFO     | SystemIntegrationTest | 测试 Ollama连接: 失败
2025-07-29 14:57:38 | INFO     | SystemIntegrationTest | 执行测试: 记忆系统
2025-07-29 14:57:38 | INFO     | SystemIntegrationTest | 测试记忆系统...
2025-07-29 14:57:38 | ERROR    | SystemIntegrationTest | 记忆系统测试失败: 'ChromaDBVectorMemory' object has no attribute 'store_agent_result'
2025-07-29 14:57:38 | INFO     | SystemIntegrationTest | 测试 记忆系统: 失败
2025-07-29 14:57:38 | INFO     | SystemIntegrationTest | 执行测试: 可追溯性系统
2025-07-29 14:57:38 | INFO     | SystemIntegrationTest | 测试可追溯性系统...
2025-07-29 14:57:38 | ERROR    | TraceabilityMatrix | 持久化工件失败: object NoneType can't be used in 'await' expression
2025-07-29 14:57:38 | INFO     | TraceabilityMatrix | 创建工件成功: requirement_50bd3d3f (集成测试需求)
2025-07-29 14:57:38 | INFO     | SystemIntegrationTest | 可追溯性系统测试成功
2025-07-29 14:57:38 | INFO     | SystemIntegrationTest | 测试 可追溯性系统: 通过
2025-07-29 14:57:38 | INFO     | SystemIntegrationTest | 执行测试: 智能体基本功能
2025-07-29 14:57:38 | INFO     | SystemIntegrationTest | 测试智能体基本功能...
2025-07-29 14:57:38 | INFO     | AgentType.REQUIREMENT_ANALYSIS_AgentType.REQUIREMENT_ANALYSIS_923953c4 | Agent初始化完成: AgentType.REQUIREMENT_ANALYSIS_923953c4
2025-07-29 14:57:38 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | Agent组件初始化完成: AgentType.REQUIREMENT_ANALYSIS_923953c4
2025-07-29 14:57:38 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 需求分析Agent初始化完成
2025-07-29 14:57:38 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.PROCESSING, 任务: 开始需求分析, 进度: 0.00
2025-07-29 14:57:38 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 1/10, 进度: 0.10
2025-07-29 14:57:38 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.10
2025-07-29 14:58:32 | INFO     | OllamaClient | 生成完成，耗时: 54.60秒
2025-07-29 14:58:32 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 14:59:40 | INFO     | OllamaClient | 生成完成，耗时: 68.23秒
2025-07-29 14:59:40 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 思维链生成完成，共0步，响应长度: 2431
2025-07-29 14:59:40 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 规则提取需求失败: UNKNOWN
2025-07-29 14:59:40 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 结构化分析结果失败: UNKNOWN
2025-07-29 14:59:40 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 迭代处理失败: UNKNOWN
2025-07-29 14:59:40 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 第1轮迭代失败: UNKNOWN
2025-07-29 14:59:40 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 2/10, 进度: 0.20
2025-07-29 14:59:40 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.20
2025-07-29 15:00:55 | INFO     | OllamaClient | 生成完成，耗时: 74.31秒
2025-07-29 15:00:55 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:02:03 | INFO     | OllamaClient | 生成完成，耗时: 67.85秒
2025-07-29 15:02:03 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 思维链生成完成，共0步，响应长度: 2127
2025-07-29 15:02:03 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 规则提取需求失败: UNKNOWN
2025-07-29 15:02:03 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 结构化分析结果失败: UNKNOWN
2025-07-29 15:02:03 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 迭代处理失败: UNKNOWN
2025-07-29 15:02:03 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 第2轮迭代失败: UNKNOWN
2025-07-29 15:02:03 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 3/10, 进度: 0.30
2025-07-29 15:02:03 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.30
2025-07-29 15:02:51 | INFO     | OllamaClient | 生成完成，耗时: 48.31秒
2025-07-29 15:02:51 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:04:15 | INFO     | OllamaClient | 生成完成，耗时: 84.61秒
2025-07-29 15:04:15 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 思维链生成完成，共0步，响应长度: 2940
2025-07-29 15:04:15 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 规则提取需求失败: UNKNOWN
2025-07-29 15:04:15 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 结构化分析结果失败: UNKNOWN
2025-07-29 15:04:15 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 迭代处理失败: UNKNOWN
2025-07-29 15:04:15 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 第3轮迭代失败: UNKNOWN
2025-07-29 15:04:15 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 4/10, 进度: 0.40
2025-07-29 15:04:15 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.40
2025-07-29 15:05:03 | INFO     | OllamaClient | 生成完成，耗时: 47.31秒
2025-07-29 15:05:03 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:05:15 | INFO     | SystemIntegrationTest | 开始系统集成测试...
2025-07-29 15:05:15 | INFO     | SystemIntegrationTest | 执行测试: Ollama连接
2025-07-29 15:05:15 | INFO     | SystemIntegrationTest | 测试Ollama连接...
2025-07-29 15:05:15 | INFO     | OllamaClient | 模型 deepseek-r1:1.5b 可用
2025-07-29 15:05:15 | INFO     | OllamaClient | Ollama客户端初始化成功，模型: deepseek-r1:1.5b
2025-07-29 15:05:15 | INFO     | SystemIntegrationTest | Ollama连接成功，可用模型: 2
2025-07-29 15:05:15 | INFO     | SystemIntegrationTest | 测试 Ollama连接: 通过
2025-07-29 15:05:15 | INFO     | SystemIntegrationTest | 执行测试: 记忆系统
2025-07-29 15:05:15 | INFO     | SystemIntegrationTest | 测试记忆系统...
2025-07-29 15:05:24 | INFO     | VectorMemory | 已连接到现有集合: agent_memory
2025-07-29 15:05:24 | INFO     | VectorMemory | ChromaDB向量记忆系统初始化完成
2025-07-29 15:05:24 | INFO     | VectorMemory | 集合统计: 0 个文档
2025-07-29 15:05:24 | ERROR    | SystemIntegrationTest | 记忆系统统计信息获取失败
2025-07-29 15:05:24 | INFO     | SystemIntegrationTest | 测试 记忆系统: 失败
2025-07-29 15:05:24 | INFO     | SystemIntegrationTest | 执行测试: 可追溯性系统
2025-07-29 15:05:24 | INFO     | SystemIntegrationTest | 测试可追溯性系统...
2025-07-29 15:05:24 | INFO     | TraceabilityMatrix | 加载了 0 个工件和 0 个链接
2025-07-29 15:05:24 | INFO     | TraceabilityMatrix | 可追溯性矩阵初始化完成
2025-07-29 15:05:25 | INFO     | TraceabilityMatrix | 创建工件成功: requirement_542dcbf3 (集成测试需求)
2025-07-29 15:05:25 | INFO     | SystemIntegrationTest | 可追溯性系统测试成功
2025-07-29 15:05:25 | INFO     | SystemIntegrationTest | 测试 可追溯性系统: 通过
2025-07-29 15:05:25 | INFO     | SystemIntegrationTest | 执行测试: 智能体基本功能
2025-07-29 15:05:25 | INFO     | SystemIntegrationTest | 测试智能体基本功能...
2025-07-29 15:05:25 | INFO     | AgentType.REQUIREMENT_ANALYSIS_AgentType.REQUIREMENT_ANALYSIS_65321f4f | Agent初始化完成: AgentType.REQUIREMENT_ANALYSIS_65321f4f
2025-07-29 15:05:25 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | Agent组件初始化完成: AgentType.REQUIREMENT_ANALYSIS_65321f4f
2025-07-29 15:05:25 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 需求分析Agent初始化完成
2025-07-29 15:05:25 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.PROCESSING, 任务: 开始需求分析, 进度: 0.00
2025-07-29 15:05:25 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 1/10, 进度: 0.10
2025-07-29 15:05:25 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.10
2025-07-29 15:06:17 | INFO     | OllamaClient | 生成完成，耗时: 74.55秒
2025-07-29 15:06:17 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 思维链生成完成，共0步，响应长度: 1376
2025-07-29 15:06:17 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 规则提取需求失败: UNKNOWN
2025-07-29 15:06:17 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 结构化分析结果失败: UNKNOWN
2025-07-29 15:06:17 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 迭代处理失败: UNKNOWN
2025-07-29 15:06:17 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 第4轮迭代失败: UNKNOWN
2025-07-29 15:06:17 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 5/10, 进度: 0.50
2025-07-29 15:06:17 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.50
2025-07-29 15:06:36 | INFO     | OllamaClient | 生成完成，耗时: 71.05秒
2025-07-29 15:06:36 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:07:42 | INFO     | OllamaClient | 生成完成，耗时: 84.27秒
2025-07-29 15:07:42 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:08:16 | INFO     | OllamaClient | 生成完成，耗时: 99.88秒
2025-07-29 15:08:16 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 思维链生成完成，共0步，响应长度: 1997
2025-07-29 15:08:16 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 规则提取需求失败: AttributeError: UNKNOWN
2025-07-29 15:08:16 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 结构化分析结果失败: AttributeError: UNKNOWN
2025-07-29 15:08:16 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 迭代处理失败: AttributeError: UNKNOWN
2025-07-29 15:08:16 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 第1轮迭代失败: UNKNOWN
2025-07-29 15:08:16 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 2/10, 进度: 0.20
2025-07-29 15:08:16 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.20
2025-07-29 15:09:16 | INFO     | OllamaClient | 生成完成，耗时: 94.87秒
2025-07-29 15:09:16 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 思维链生成完成，共0步，响应长度: 1887
2025-07-29 15:09:16 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 规则提取需求失败: UNKNOWN
2025-07-29 15:09:16 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 结构化分析结果失败: UNKNOWN
2025-07-29 15:09:16 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 迭代处理失败: UNKNOWN
2025-07-29 15:09:16 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 第5轮迭代失败: UNKNOWN
2025-07-29 15:09:16 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 6/10, 进度: 0.60
2025-07-29 15:09:16 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.60
2025-07-29 15:09:53 | INFO     | OllamaClient | 生成完成，耗时: 96.88秒
2025-07-29 15:09:53 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:11:01 | INFO     | OllamaClient | 生成完成，耗时: 67.34秒
2025-07-29 15:11:01 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 思维链生成完成，共0步，响应长度: 1402
2025-07-29 15:11:01 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 规则提取需求失败: AttributeError: UNKNOWN
2025-07-29 15:11:01 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 结构化分析结果失败: AttributeError: UNKNOWN
2025-07-29 15:11:01 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 迭代处理失败: AttributeError: UNKNOWN
2025-07-29 15:11:01 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 第2轮迭代失败: UNKNOWN
2025-07-29 15:11:01 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 3/10, 进度: 0.30
2025-07-29 15:11:01 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.30
2025-07-29 15:11:37 | INFO     | OllamaClient | 生成完成，耗时: 140.50秒
2025-07-29 15:11:37 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:12:01 | INFO     | OllamaClient | 生成完成，耗时: 60.12秒
2025-07-29 15:12:01 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:12:49 | INFO     | OllamaClient | 生成完成，耗时: 71.99秒
2025-07-29 15:12:49 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 思维链生成完成，共0步，响应长度: 1448
2025-07-29 15:12:49 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 规则提取需求失败: UNKNOWN
2025-07-29 15:12:49 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 结构化分析结果失败: UNKNOWN
2025-07-29 15:12:49 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 迭代处理失败: UNKNOWN
2025-07-29 15:12:49 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 第6轮迭代失败: UNKNOWN
2025-07-29 15:12:49 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 7/10, 进度: 0.70
2025-07-29 15:12:49 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.70
2025-07-29 15:13:01 | INFO     | OllamaClient | 生成完成，耗时: 59.93秒
2025-07-29 15:13:01 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 思维链生成完成，共0步，响应长度: 1090
2025-07-29 15:13:01 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 规则提取需求失败: AttributeError: UNKNOWN
2025-07-29 15:13:01 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 结构化分析结果失败: AttributeError: UNKNOWN
2025-07-29 15:13:01 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 迭代处理失败: AttributeError: UNKNOWN
2025-07-29 15:13:01 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 第3轮迭代失败: UNKNOWN
2025-07-29 15:13:01 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 4/10, 进度: 0.40
2025-07-29 15:13:01 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.40
2025-07-29 15:13:37 | INFO     | OllamaClient | 生成完成，耗时: 48.13秒
2025-07-29 15:13:37 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:14:39 | INFO     | OllamaClient | 生成完成，耗时: 98.70秒
2025-07-29 15:14:39 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:15:47 | INFO     | OllamaClient | 生成完成，耗时: 130.08秒
2025-07-29 15:15:47 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 思维链生成完成，共0步，响应长度: 3346
2025-07-29 15:15:47 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 规则提取需求失败: UNKNOWN
2025-07-29 15:15:47 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 结构化分析结果失败: UNKNOWN
2025-07-29 15:15:47 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 迭代处理失败: UNKNOWN
2025-07-29 15:15:47 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 第7轮迭代失败: UNKNOWN
2025-07-29 15:15:47 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 8/10, 进度: 0.80
2025-07-29 15:15:47 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.80
2025-07-29 15:16:10 | INFO     | OllamaClient | 生成完成，耗时: 90.71秒
2025-07-29 15:16:10 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 思维链生成完成，共0步，响应长度: 1926
2025-07-29 15:16:10 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 规则提取需求失败: AttributeError: UNKNOWN
2025-07-29 15:16:10 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 结构化分析结果失败: AttributeError: UNKNOWN
2025-07-29 15:16:10 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 迭代处理失败: AttributeError: UNKNOWN
2025-07-29 15:16:10 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 第4轮迭代失败: UNKNOWN
2025-07-29 15:16:10 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 5/10, 进度: 0.50
2025-07-29 15:16:10 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.50
2025-07-29 15:17:11 | INFO     | OllamaClient | 生成完成，耗时: 61.39秒
2025-07-29 15:17:11 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:17:35 | INFO     | OllamaClient | 生成完成，耗时: 107.55秒
2025-07-29 15:17:35 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:18:33 | INFO     | OllamaClient | 生成完成，耗时: 81.24秒
2025-07-29 15:18:33 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 思维链生成完成，共0步，响应长度: 1598
2025-07-29 15:18:33 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 规则提取需求失败: AttributeError: UNKNOWN
2025-07-29 15:18:33 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 结构化分析结果失败: AttributeError: UNKNOWN
2025-07-29 15:18:33 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 迭代处理失败: AttributeError: UNKNOWN
2025-07-29 15:18:33 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 第5轮迭代失败: UNKNOWN
2025-07-29 15:18:33 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 6/10, 进度: 0.60
2025-07-29 15:18:33 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.60
2025-07-29 15:18:44 | INFO     | OllamaClient | 生成完成，耗时: 69.68秒
2025-07-29 15:18:44 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 思维链生成完成，共0步，响应长度: 1304
2025-07-29 15:18:44 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 规则提取需求失败: UNKNOWN
2025-07-29 15:18:44 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 结构化分析结果失败: UNKNOWN
2025-07-29 15:18:44 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 迭代处理失败: UNKNOWN
2025-07-29 15:18:44 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 第8轮迭代失败: UNKNOWN
2025-07-29 15:18:44 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 9/10, 进度: 0.90
2025-07-29 15:18:44 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.90
2025-07-29 15:19:55 | INFO     | OllamaClient | 生成完成，耗时: 82.43秒
2025-07-29 15:19:55 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:20:14 | INFO     | OllamaClient | 生成完成，耗时: 89.11秒
2025-07-29 15:20:14 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:21:04 | INFO     | OllamaClient | 生成完成，耗时: 50.80秒
2025-07-29 15:21:04 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 思维链生成完成，共0步，响应长度: 1068
2025-07-29 15:21:04 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 规则提取需求失败: UNKNOWN
2025-07-29 15:21:04 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 结构化分析结果失败: UNKNOWN
2025-07-29 15:21:04 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 迭代处理失败: UNKNOWN
2025-07-29 15:21:04 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 第9轮迭代失败: UNKNOWN
2025-07-29 15:21:04 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 10/10, 进度: 1.00
2025-07-29 15:21:04 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 1.00
2025-07-29 15:21:39 | INFO     | OllamaClient | 生成完成，耗时: 104.21秒
2025-07-29 15:21:39 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 思维链生成完成，共0步，响应长度: 2212
2025-07-29 15:21:39 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 规则提取需求失败: AttributeError: UNKNOWN
2025-07-29 15:21:39 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 结构化分析结果失败: AttributeError: UNKNOWN
2025-07-29 15:21:39 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 迭代处理失败: AttributeError: UNKNOWN
2025-07-29 15:21:39 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 第6轮迭代失败: UNKNOWN
2025-07-29 15:21:39 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 7/10, 进度: 0.70
2025-07-29 15:21:39 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.70
2025-07-29 15:22:12 | INFO     | OllamaClient | 生成完成，耗时: 67.47秒
2025-07-29 15:22:12 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:23:00 | INFO     | OllamaClient | 生成完成，耗时: 48.50秒
2025-07-29 15:23:00 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 思维链生成完成，共0步，响应长度: 1077
2025-07-29 15:23:00 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 规则提取需求失败: UNKNOWN
2025-07-29 15:23:00 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 结构化分析结果失败: UNKNOWN
2025-07-29 15:23:00 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 迭代处理失败: UNKNOWN
2025-07-29 15:23:00 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 第10轮迭代失败: UNKNOWN
2025-07-29 15:23:00 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.ERROR, 任务: 需求分析失败: UNKNOWN, 进度: 1.00
2025-07-29 15:23:00 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 需求分析处理失败: UNKNOWN
2025-07-29 15:23:00 | ERROR    | SystemIntegrationTest | 智能体基本功能测试失败: UNKNOWN
2025-07-29 15:23:00 | INFO     | SystemIntegrationTest | 测试 智能体基本功能: 失败
2025-07-29 15:23:51 | INFO     | OllamaClient | 生成完成，耗时: 131.69秒
2025-07-29 15:23:51 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:24:31 | INFO     | OllamaClient | 生成完成，耗时: 39.63秒
2025-07-29 15:24:31 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 思维链生成完成，共0步，响应长度: 1233
2025-07-29 15:24:31 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 规则提取需求失败: AttributeError: UNKNOWN
2025-07-29 15:24:31 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 结构化分析结果失败: AttributeError: UNKNOWN
2025-07-29 15:24:31 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 迭代处理失败: AttributeError: UNKNOWN
2025-07-29 15:24:31 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 第7轮迭代失败: UNKNOWN
2025-07-29 15:24:31 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 8/10, 进度: 0.80
2025-07-29 15:24:31 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.80
2025-07-29 15:25:23 | INFO     | OllamaClient | 生成完成，耗时: 52.82秒
2025-07-29 15:25:23 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:26:07 | INFO     | OllamaClient | 生成完成，耗时: 43.56秒
2025-07-29 15:26:07 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 思维链生成完成，共0步，响应长度: 1425
2025-07-29 15:26:07 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 规则提取需求失败: AttributeError: UNKNOWN
2025-07-29 15:26:07 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 结构化分析结果失败: AttributeError: UNKNOWN
2025-07-29 15:26:07 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 迭代处理失败: AttributeError: UNKNOWN
2025-07-29 15:26:07 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 第8轮迭代失败: UNKNOWN
2025-07-29 15:26:07 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 9/10, 进度: 0.90
2025-07-29 15:26:07 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.90
2025-07-29 15:27:11 | INFO     | OllamaClient | 生成完成，耗时: 64.18秒
2025-07-29 15:27:11 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:29:05 | INFO     | SystemStartup | ============================================================
2025-07-29 15:29:05 | INFO     | SystemStartup | 异步多智能体测试用例生成框架
2025-07-29 15:29:05 | INFO     | SystemStartup | ============================================================
2025-07-29 15:29:05 | INFO     | SystemStartup | 启动时间: 2025-07-29 15:29:05
2025-07-29 15:29:05 | INFO     | SystemStartup | 开始初始化系统组件...
2025-07-29 15:29:05 | INFO     | SystemStartup | 初始化记忆系统...
2025-07-29 15:29:13 | INFO     | VectorMemory | 已连接到现有集合: agent_memory
2025-07-29 15:29:13 | INFO     | VectorMemory | ChromaDB向量记忆系统初始化完成
2025-07-29 15:29:13 | INFO     | VectorMemory | 集合统计: 0 个文档
2025-07-29 15:29:13 | INFO     | SystemStartup | 记忆系统初始化成功
2025-07-29 15:29:13 | INFO     | SystemStartup | 初始化可追溯性系统...
2025-07-29 15:29:13 | INFO     | TraceabilityMatrix | 加载了 1 个工件和 0 个链接
2025-07-29 15:29:13 | INFO     | TraceabilityMatrix | 可追溯性矩阵初始化完成
2025-07-29 15:29:13 | INFO     | SystemStartup | 可追溯性系统初始化成功，当前统计: {'artifacts': {'total': 1, 'by_type': {'requirement': 1}, 'by_status': {'draft': 1}}, 'links': {'total': 0, 'by_type': {}}, 'coverage': {}, 'graph': {'nodes': 0, 'edges': 0, 'density': 0}}
2025-07-29 15:29:13 | INFO     | SystemStartup | 检查Ollama连接...
2025-07-29 15:29:14 | INFO     | OllamaClient | 模型 deepseek-r1:1.5b 可用
2025-07-29 15:29:14 | INFO     | OllamaClient | Ollama客户端初始化成功，模型: deepseek-r1:1.5b
2025-07-29 15:29:14 | INFO     | SystemStartup | Ollama连接成功，可用模型数: 2
2025-07-29 15:29:14 | INFO     | SystemStartup | 系统组件状态:
2025-07-29 15:29:14 | INFO     | SystemStartup |   memory: healthy
2025-07-29 15:29:14 | INFO     | SystemStartup |   traceability: healthy
2025-07-29 15:29:14 | INFO     | SystemStartup |   ollama: healthy (2 models)
2025-07-29 15:29:14 | INFO     | SystemStartup | 系统健康状态: healthy
2025-07-29 15:29:14 | INFO     | SystemStartup | 运行启动测试...
2025-07-29 15:29:14 | INFO     | SystemIntegrationTest | 开始系统集成测试...
2025-07-29 15:29:14 | INFO     | SystemIntegrationTest | 执行测试: Ollama连接
2025-07-29 15:29:14 | INFO     | SystemIntegrationTest | 测试Ollama连接...
2025-07-29 15:29:14 | INFO     | SystemIntegrationTest | Ollama连接成功，可用模型: 2
2025-07-29 15:29:14 | INFO     | SystemIntegrationTest | 测试 Ollama连接: 通过
2025-07-29 15:29:14 | INFO     | SystemIntegrationTest | 执行测试: 记忆系统
2025-07-29 15:29:14 | INFO     | SystemIntegrationTest | 测试记忆系统...
2025-07-29 15:29:14 | INFO     | VectorMemory | 集合统计: 0 个文档
2025-07-29 15:29:14 | INFO     | SystemIntegrationTest | 记忆系统测试成功，文档数量: 0
2025-07-29 15:29:14 | INFO     | SystemIntegrationTest | 测试 记忆系统: 通过
2025-07-29 15:29:14 | INFO     | SystemIntegrationTest | 执行测试: 可追溯性系统
2025-07-29 15:29:14 | INFO     | SystemIntegrationTest | 测试可追溯性系统...
2025-07-29 15:29:14 | INFO     | TraceabilityMatrix | 创建工件成功: requirement_e45f61f8 (集成测试需求)
2025-07-29 15:29:14 | INFO     | SystemIntegrationTest | 可追溯性系统测试成功
2025-07-29 15:29:14 | INFO     | SystemIntegrationTest | 测试 可追溯性系统: 通过
2025-07-29 15:29:14 | INFO     | SystemIntegrationTest | 执行测试: 智能体基本功能
2025-07-29 15:29:14 | INFO     | SystemIntegrationTest | 测试智能体基本功能...
2025-07-29 15:29:14 | INFO     | AgentType.REQUIREMENT_ANALYSIS_AgentType.REQUIREMENT_ANALYSIS_4733a55c | Agent初始化完成: AgentType.REQUIREMENT_ANALYSIS_4733a55c
2025-07-29 15:29:14 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | Agent组件初始化完成: AgentType.REQUIREMENT_ANALYSIS_4733a55c
2025-07-29 15:29:14 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | 需求分析Agent初始化完成
2025-07-29 15:29:14 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | 状态更新: AgentStatus.PROCESSING, 任务: 开始需求分析, 进度: 0.00
2025-07-29 15:29:14 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 1/10, 进度: 0.10
2025-07-29 15:29:14 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.10
2025-07-29 15:29:53 | INFO     | OllamaClient | 生成完成，耗时: 39.11秒
2025-07-29 15:29:53 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:30:44 | INFO     | OllamaClient | 生成完成，耗时: 51.30秒
2025-07-29 15:30:44 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | 思维链生成完成，共0步，响应长度: 1774
2025-07-29 15:30:44 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | 规则提取需求失败: AttributeError: UNKNOWN
2025-07-29 15:30:44 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | 结构化分析结果失败: AttributeError: UNKNOWN
2025-07-29 15:30:44 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | 迭代处理失败: AttributeError: UNKNOWN
2025-07-29 15:30:44 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | 第1轮迭代失败: UNKNOWN
2025-07-29 15:30:44 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 2/10, 进度: 0.20
2025-07-29 15:30:44 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.20
2025-07-29 15:31:41 | INFO     | OllamaClient | 生成完成，耗时: 56.99秒
2025-07-29 15:31:41 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:32:30 | INFO     | OllamaClient | 生成完成，耗时: 48.58秒
2025-07-29 15:32:30 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | 思维链生成完成，共0步，响应长度: 1666
2025-07-29 15:32:30 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | 规则提取需求失败: AttributeError: UNKNOWN
2025-07-29 15:32:30 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | 结构化分析结果失败: AttributeError: UNKNOWN
2025-07-29 15:32:30 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | 迭代处理失败: AttributeError: UNKNOWN
2025-07-29 15:32:30 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | 第2轮迭代失败: UNKNOWN
2025-07-29 15:32:30 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 3/10, 进度: 0.30
2025-07-29 15:32:30 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.30
