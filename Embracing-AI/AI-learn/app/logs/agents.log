2025-07-29 11:41:55 | INFO     | SystemStartup | ============================================================
2025-07-29 11:41:55 | INFO     | SystemStartup | 异步多智能体测试用例生成框架
2025-07-29 11:41:55 | INFO     | SystemStartup | ============================================================
2025-07-29 11:41:55 | INFO     | SystemStartup | 启动时间: 2025-07-29 11:41:55
2025-07-29 11:41:55 | INFO     | SystemStartup | 开始初始化系统组件...
2025-07-29 11:41:55 | INFO     | SystemStartup | 初始化记忆系统...
2025-07-29 11:42:03 | INFO     | VectorMemory | 已连接到现有集合: agent_memory
2025-07-29 11:42:03 | INFO     | VectorMemory | ChromaDB向量记忆系统初始化完成
2025-07-29 11:42:03 | INFO     | VectorMemory | 集合统计: 0 个文档
2025-07-29 11:42:03 | INFO     | SystemStartup | 记忆系统初始化成功
2025-07-29 11:42:03 | INFO     | SystemStartup | 初始化可追溯性系统...
2025-07-29 11:42:03 | ERROR    | TraceabilityMatrix | 加载现有数据失败: object NoneType can't be used in 'await' expression
2025-07-29 11:42:03 | INFO     | TraceabilityMatrix | 可追溯性矩阵初始化完成
2025-07-29 11:42:03 | INFO     | SystemStartup | 可追溯性系统初始化成功，当前统计: {'artifacts': {'total': 0, 'by_type': {}, 'by_status': {}}, 'links': {'total': 0, 'by_type': {}}, 'coverage': {}, 'graph': {'nodes': 0, 'edges': 0, 'density': 0}}
2025-07-29 11:42:03 | INFO     | SystemStartup | 检查Ollama连接...
2025-07-29 11:42:03 | INFO     | OllamaClient | 模型 deepseek-r1:1.5b 可用
2025-07-29 11:42:03 | INFO     | OllamaClient | Ollama客户端初始化成功，模型: deepseek-r1:1.5b
2025-07-29 11:42:03 | INFO     | SystemStartup | Ollama连接成功，可用模型数: 2
2025-07-29 11:42:03 | INFO     | SystemStartup | 系统组件状态:
2025-07-29 11:42:03 | INFO     | SystemStartup |   memory: healthy
2025-07-29 11:42:03 | INFO     | SystemStartup |   traceability: healthy
2025-07-29 11:42:03 | INFO     | SystemStartup |   ollama: healthy (2 models)
2025-07-29 11:42:03 | INFO     | SystemStartup | 系统健康状态: healthy
2025-07-29 11:42:03 | INFO     | SystemStartup | 运行启动测试...
2025-07-29 11:42:03 | ERROR    | SystemStartup | 启动测试失败: No module named 'exceptions'
2025-07-29 11:42:03 | WARNING  | SystemStartup | 启动测试未完全通过，但继续启动系统
2025-07-29 11:42:03 | INFO     | SystemStartup | 系统初始化完成，启动API服务器...
2025-07-29 11:42:03 | INFO     | SystemStartup | 启动FastAPI服务器...
2025-07-29 11:42:03 | ERROR    | SystemStartup | API服务器启动失败: No module named 'exceptions'
2025-07-29 11:42:03 | ERROR    | SystemStartup | 系统启动失败: No module named 'exceptions'
2025-07-29 13:42:22 | INFO     | SystemStartup | ============================================================
2025-07-29 13:42:22 | INFO     | SystemStartup | 异步多智能体测试用例生成框架
2025-07-29 13:42:22 | INFO     | SystemStartup | ============================================================
2025-07-29 13:42:22 | INFO     | SystemStartup | 启动时间: 2025-07-29 13:42:22
2025-07-29 13:42:22 | INFO     | SystemStartup | 开始初始化系统组件...
2025-07-29 13:42:22 | INFO     | SystemStartup | 初始化记忆系统...
2025-07-29 13:42:32 | INFO     | VectorMemory | 已连接到现有集合: agent_memory
2025-07-29 13:42:32 | INFO     | VectorMemory | ChromaDB向量记忆系统初始化完成
2025-07-29 13:42:32 | INFO     | VectorMemory | 集合统计: 0 个文档
2025-07-29 13:42:32 | INFO     | SystemStartup | 记忆系统初始化成功
2025-07-29 13:42:32 | INFO     | SystemStartup | 初始化可追溯性系统...
2025-07-29 13:42:32 | ERROR    | TraceabilityMatrix | 加载现有数据失败: object NoneType can't be used in 'await' expression
2025-07-29 13:42:32 | INFO     | TraceabilityMatrix | 可追溯性矩阵初始化完成
2025-07-29 13:42:32 | INFO     | SystemStartup | 可追溯性系统初始化成功，当前统计: {'artifacts': {'total': 0, 'by_type': {}, 'by_status': {}}, 'links': {'total': 0, 'by_type': {}}, 'coverage': {}, 'graph': {'nodes': 0, 'edges': 0, 'density': 0}}
2025-07-29 13:42:32 | INFO     | SystemStartup | 检查Ollama连接...
2025-07-29 13:42:32 | INFO     | OllamaClient | 模型 deepseek-r1:1.5b 可用
2025-07-29 13:42:32 | INFO     | OllamaClient | Ollama客户端初始化成功，模型: deepseek-r1:1.5b
2025-07-29 13:42:32 | INFO     | SystemStartup | Ollama连接成功，可用模型数: 2
2025-07-29 13:42:32 | INFO     | SystemStartup | 系统组件状态:
2025-07-29 13:42:32 | INFO     | SystemStartup |   memory: healthy
2025-07-29 13:42:32 | INFO     | SystemStartup |   traceability: healthy
2025-07-29 13:42:32 | INFO     | SystemStartup |   ollama: healthy (2 models)
2025-07-29 13:42:32 | INFO     | SystemStartup | 系统健康状态: healthy
2025-07-29 13:42:32 | INFO     | SystemStartup | 运行启动测试...
2025-07-29 13:42:32 | ERROR    | SystemStartup | 启动测试失败: No module named 'exceptions'
2025-07-29 13:42:32 | WARNING  | SystemStartup | 启动测试未完全通过，但继续启动系统
2025-07-29 13:42:32 | INFO     | SystemStartup | 系统初始化完成，启动API服务器...
2025-07-29 13:42:32 | INFO     | SystemStartup | 启动FastAPI服务器...
2025-07-29 13:42:33 | ERROR    | SystemStartup | 系统启动失败: No module named 'exceptions'
2025-07-29 13:47:19 | INFO     | SystemStartup | ============================================================
2025-07-29 13:47:19 | INFO     | SystemStartup | 异步多智能体测试用例生成框架
2025-07-29 13:47:19 | INFO     | SystemStartup | ============================================================
2025-07-29 13:47:19 | INFO     | SystemStartup | 启动时间: 2025-07-29 13:47:19
2025-07-29 13:47:19 | INFO     | SystemStartup | 开始初始化系统组件...
2025-07-29 13:47:19 | INFO     | SystemStartup | 初始化记忆系统...
2025-07-29 13:47:27 | INFO     | VectorMemory | 已连接到现有集合: agent_memory
2025-07-29 13:47:27 | INFO     | VectorMemory | ChromaDB向量记忆系统初始化完成
2025-07-29 13:47:27 | INFO     | VectorMemory | 集合统计: 0 个文档
2025-07-29 13:47:27 | INFO     | SystemStartup | 记忆系统初始化成功
2025-07-29 13:47:27 | INFO     | SystemStartup | 初始化可追溯性系统...
2025-07-29 13:47:27 | ERROR    | SystemStartup | 可追溯性系统初始化失败: object NoneType can't be used in 'await' expression
2025-07-29 13:47:27 | INFO     | SystemStartup | 检查Ollama连接...
2025-07-29 13:47:27 | INFO     | OllamaClient | 模型 deepseek-r1:1.5b 可用
2025-07-29 13:47:27 | INFO     | OllamaClient | Ollama客户端初始化成功，模型: deepseek-r1:1.5b
2025-07-29 13:47:27 | INFO     | SystemStartup | Ollama连接成功，可用模型数: 2
2025-07-29 13:47:27 | INFO     | SystemStartup | 系统组件状态:
2025-07-29 13:47:27 | INFO     | SystemStartup |   memory: healthy
2025-07-29 13:47:27 | INFO     | SystemStartup |   traceability: error: object NoneType can't be used in 'await' expression
2025-07-29 13:47:27 | INFO     | SystemStartup |   ollama: healthy (2 models)
2025-07-29 13:47:27 | INFO     | SystemStartup | 系统健康状态: degraded
2025-07-29 13:47:27 | INFO     | SystemStartup | 运行启动测试...
2025-07-29 13:47:27 | ERROR    | SystemStartup | 系统启动失败: No module named 'exceptions'
2025-07-29 13:50:45 | INFO     | SystemStartup | ============================================================
2025-07-29 13:50:45 | INFO     | SystemStartup | 异步多智能体测试用例生成框架
2025-07-29 13:50:45 | INFO     | SystemStartup | ============================================================
2025-07-29 13:50:45 | INFO     | SystemStartup | 启动时间: 2025-07-29 13:50:45
2025-07-29 13:50:45 | INFO     | SystemStartup | 开始初始化系统组件...
2025-07-29 13:50:45 | INFO     | SystemStartup | 初始化记忆系统...
2025-07-29 13:50:53 | INFO     | VectorMemory | 已连接到现有集合: agent_memory
2025-07-29 13:50:53 | INFO     | VectorMemory | ChromaDB向量记忆系统初始化完成
2025-07-29 13:50:53 | INFO     | VectorMemory | 集合统计: 0 个文档
2025-07-29 13:50:53 | INFO     | SystemStartup | 记忆系统初始化成功
2025-07-29 13:50:53 | INFO     | SystemStartup | 初始化可追溯性系统...
2025-07-29 13:51:46 | INFO     | SystemStartup | ============================================================
2025-07-29 13:51:46 | INFO     | SystemStartup | 异步多智能体测试用例生成框架
2025-07-29 13:51:46 | INFO     | SystemStartup | ============================================================
2025-07-29 13:51:46 | INFO     | SystemStartup | 启动时间: 2025-07-29 13:51:46
2025-07-29 13:51:46 | INFO     | SystemStartup | 开始初始化系统组件...
2025-07-29 13:51:46 | INFO     | SystemStartup | 初始化记忆系统...
2025-07-29 13:51:54 | INFO     | VectorMemory | 已连接到现有集合: agent_memory
2025-07-29 13:51:54 | INFO     | VectorMemory | ChromaDB向量记忆系统初始化完成
2025-07-29 13:51:54 | INFO     | VectorMemory | 集合统计: 0 个文档
2025-07-29 13:51:54 | INFO     | SystemStartup | 记忆系统初始化成功
2025-07-29 13:51:54 | INFO     | SystemStartup | 初始化可追溯性系统...
2025-07-29 14:49:54 | INFO     | FastAPI | 正在启动异步多智能体测试用例生成框架...
2025-07-29 14:50:03 | INFO     | VectorMemory | 已连接到现有集合: agent_memory
2025-07-29 14:50:03 | INFO     | VectorMemory | ChromaDB向量记忆系统初始化完成
2025-07-29 14:50:03 | INFO     | FastAPI | 内存管理器初始化完成
2025-07-29 14:50:03 | ERROR    | FastAPI | 系统启动失败: object NoneType can't be used in 'await' expression
2025-07-29 14:55:42 | INFO     | FastAPI | 正在启动异步多智能体测试用例生成框架...
2025-07-29 14:55:51 | INFO     | VectorMemory | 已连接到现有集合: agent_memory
2025-07-29 14:55:51 | INFO     | VectorMemory | ChromaDB向量记忆系统初始化完成
2025-07-29 14:55:51 | INFO     | FastAPI | 内存管理器初始化完成
2025-07-29 14:55:51 | INFO     | TraceabilityMatrix | 加载了 0 个工件和 0 个链接
2025-07-29 14:55:51 | INFO     | TraceabilityMatrix | 可追溯性矩阵初始化完成
2025-07-29 14:55:51 | INFO     | FastAPI | 可追溯性矩阵初始化完成
2025-07-29 14:55:51 | INFO     | FastAPI | 系统启动完成
2025-07-29 14:57:28 | INFO     | SystemStartup | ============================================================
2025-07-29 14:57:28 | INFO     | SystemStartup | 异步多智能体测试用例生成框架
2025-07-29 14:57:28 | INFO     | SystemStartup | ============================================================
2025-07-29 14:57:28 | INFO     | SystemStartup | 启动时间: 2025-07-29 14:57:28
2025-07-29 14:57:28 | INFO     | SystemStartup | 开始初始化系统组件...
2025-07-29 14:57:28 | INFO     | SystemStartup | 初始化记忆系统...
2025-07-29 14:57:28 | INFO     | FastAPI | 正在关闭系统...
2025-07-29 14:57:28 | INFO     | VectorMemory | 记忆系统资源清理完成
2025-07-29 14:57:28 | INFO     | FastAPI | 系统关闭完成
2025-07-29 14:57:37 | INFO     | VectorMemory | 已连接到现有集合: agent_memory
2025-07-29 14:57:37 | INFO     | VectorMemory | ChromaDB向量记忆系统初始化完成
2025-07-29 14:57:37 | INFO     | VectorMemory | 集合统计: 0 个文档
2025-07-29 14:57:37 | INFO     | SystemStartup | 记忆系统初始化成功
2025-07-29 14:57:37 | INFO     | SystemStartup | 初始化可追溯性系统...
2025-07-29 14:57:37 | INFO     | TraceabilityMatrix | 加载了 0 个工件和 0 个链接
2025-07-29 14:57:37 | INFO     | TraceabilityMatrix | 可追溯性矩阵初始化完成
2025-07-29 14:57:37 | INFO     | SystemStartup | 可追溯性系统初始化成功，当前统计: {'artifacts': {'total': 0, 'by_type': {}, 'by_status': {}}, 'links': {'total': 0, 'by_type': {}}, 'coverage': {}, 'graph': {'nodes': 0, 'edges': 0, 'density': 0}}
2025-07-29 14:57:37 | INFO     | SystemStartup | 检查Ollama连接...
2025-07-29 14:57:37 | INFO     | OllamaClient | 模型 deepseek-r1:1.5b 可用
2025-07-29 14:57:37 | INFO     | OllamaClient | Ollama客户端初始化成功，模型: deepseek-r1:1.5b
2025-07-29 14:57:37 | INFO     | SystemStartup | Ollama连接成功，可用模型数: 2
2025-07-29 14:57:37 | INFO     | SystemStartup | 系统组件状态:
2025-07-29 14:57:37 | INFO     | SystemStartup |   memory: healthy
2025-07-29 14:57:37 | INFO     | SystemStartup |   traceability: healthy
2025-07-29 14:57:37 | INFO     | SystemStartup |   ollama: healthy (2 models)
2025-07-29 14:57:37 | INFO     | SystemStartup | 系统健康状态: healthy
2025-07-29 14:57:37 | INFO     | SystemStartup | 运行启动测试...
2025-07-29 14:57:38 | INFO     | SystemIntegrationTest | 开始系统集成测试...
2025-07-29 14:57:38 | INFO     | SystemIntegrationTest | 执行测试: Ollama连接
2025-07-29 14:57:38 | INFO     | SystemIntegrationTest | 测试Ollama连接...
2025-07-29 14:57:38 | ERROR    | SystemIntegrationTest | Ollama连接失败: 'coroutine' object has no attribute 'list_models'
2025-07-29 14:57:38 | INFO     | SystemIntegrationTest | 测试 Ollama连接: 失败
2025-07-29 14:57:38 | INFO     | SystemIntegrationTest | 执行测试: 记忆系统
2025-07-29 14:57:38 | INFO     | SystemIntegrationTest | 测试记忆系统...
2025-07-29 14:57:38 | ERROR    | SystemIntegrationTest | 记忆系统测试失败: 'ChromaDBVectorMemory' object has no attribute 'store_agent_result'
2025-07-29 14:57:38 | INFO     | SystemIntegrationTest | 测试 记忆系统: 失败
2025-07-29 14:57:38 | INFO     | SystemIntegrationTest | 执行测试: 可追溯性系统
2025-07-29 14:57:38 | INFO     | SystemIntegrationTest | 测试可追溯性系统...
2025-07-29 14:57:38 | ERROR    | TraceabilityMatrix | 持久化工件失败: object NoneType can't be used in 'await' expression
2025-07-29 14:57:38 | INFO     | TraceabilityMatrix | 创建工件成功: requirement_50bd3d3f (集成测试需求)
2025-07-29 14:57:38 | INFO     | SystemIntegrationTest | 可追溯性系统测试成功
2025-07-29 14:57:38 | INFO     | SystemIntegrationTest | 测试 可追溯性系统: 通过
2025-07-29 14:57:38 | INFO     | SystemIntegrationTest | 执行测试: 智能体基本功能
2025-07-29 14:57:38 | INFO     | SystemIntegrationTest | 测试智能体基本功能...
2025-07-29 14:57:38 | INFO     | AgentType.REQUIREMENT_ANALYSIS_AgentType.REQUIREMENT_ANALYSIS_923953c4 | Agent初始化完成: AgentType.REQUIREMENT_ANALYSIS_923953c4
2025-07-29 14:57:38 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | Agent组件初始化完成: AgentType.REQUIREMENT_ANALYSIS_923953c4
2025-07-29 14:57:38 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 需求分析Agent初始化完成
2025-07-29 14:57:38 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.PROCESSING, 任务: 开始需求分析, 进度: 0.00
2025-07-29 14:57:38 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 1/10, 进度: 0.10
2025-07-29 14:57:38 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.10
2025-07-29 14:58:32 | INFO     | OllamaClient | 生成完成，耗时: 54.60秒
2025-07-29 14:58:32 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 14:59:40 | INFO     | OllamaClient | 生成完成，耗时: 68.23秒
2025-07-29 14:59:40 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 思维链生成完成，共0步，响应长度: 2431
2025-07-29 14:59:40 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 规则提取需求失败: UNKNOWN
2025-07-29 14:59:40 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 结构化分析结果失败: UNKNOWN
2025-07-29 14:59:40 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 迭代处理失败: UNKNOWN
2025-07-29 14:59:40 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 第1轮迭代失败: UNKNOWN
2025-07-29 14:59:40 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 2/10, 进度: 0.20
2025-07-29 14:59:40 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.20
2025-07-29 15:00:55 | INFO     | OllamaClient | 生成完成，耗时: 74.31秒
2025-07-29 15:00:55 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:02:03 | INFO     | OllamaClient | 生成完成，耗时: 67.85秒
2025-07-29 15:02:03 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 思维链生成完成，共0步，响应长度: 2127
2025-07-29 15:02:03 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 规则提取需求失败: UNKNOWN
2025-07-29 15:02:03 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 结构化分析结果失败: UNKNOWN
2025-07-29 15:02:03 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 迭代处理失败: UNKNOWN
2025-07-29 15:02:03 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 第2轮迭代失败: UNKNOWN
2025-07-29 15:02:03 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 3/10, 进度: 0.30
2025-07-29 15:02:03 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.30
2025-07-29 15:02:51 | INFO     | OllamaClient | 生成完成，耗时: 48.31秒
2025-07-29 15:02:51 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:04:15 | INFO     | OllamaClient | 生成完成，耗时: 84.61秒
2025-07-29 15:04:15 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 思维链生成完成，共0步，响应长度: 2940
2025-07-29 15:04:15 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 规则提取需求失败: UNKNOWN
2025-07-29 15:04:15 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 结构化分析结果失败: UNKNOWN
2025-07-29 15:04:15 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 迭代处理失败: UNKNOWN
2025-07-29 15:04:15 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 第3轮迭代失败: UNKNOWN
2025-07-29 15:04:15 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 4/10, 进度: 0.40
2025-07-29 15:04:15 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.40
2025-07-29 15:05:03 | INFO     | OllamaClient | 生成完成，耗时: 47.31秒
2025-07-29 15:05:03 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:05:15 | INFO     | SystemIntegrationTest | 开始系统集成测试...
2025-07-29 15:05:15 | INFO     | SystemIntegrationTest | 执行测试: Ollama连接
2025-07-29 15:05:15 | INFO     | SystemIntegrationTest | 测试Ollama连接...
2025-07-29 15:05:15 | INFO     | OllamaClient | 模型 deepseek-r1:1.5b 可用
2025-07-29 15:05:15 | INFO     | OllamaClient | Ollama客户端初始化成功，模型: deepseek-r1:1.5b
2025-07-29 15:05:15 | INFO     | SystemIntegrationTest | Ollama连接成功，可用模型: 2
2025-07-29 15:05:15 | INFO     | SystemIntegrationTest | 测试 Ollama连接: 通过
2025-07-29 15:05:15 | INFO     | SystemIntegrationTest | 执行测试: 记忆系统
2025-07-29 15:05:15 | INFO     | SystemIntegrationTest | 测试记忆系统...
2025-07-29 15:05:24 | INFO     | VectorMemory | 已连接到现有集合: agent_memory
2025-07-29 15:05:24 | INFO     | VectorMemory | ChromaDB向量记忆系统初始化完成
2025-07-29 15:05:24 | INFO     | VectorMemory | 集合统计: 0 个文档
2025-07-29 15:05:24 | ERROR    | SystemIntegrationTest | 记忆系统统计信息获取失败
2025-07-29 15:05:24 | INFO     | SystemIntegrationTest | 测试 记忆系统: 失败
2025-07-29 15:05:24 | INFO     | SystemIntegrationTest | 执行测试: 可追溯性系统
2025-07-29 15:05:24 | INFO     | SystemIntegrationTest | 测试可追溯性系统...
2025-07-29 15:05:24 | INFO     | TraceabilityMatrix | 加载了 0 个工件和 0 个链接
2025-07-29 15:05:24 | INFO     | TraceabilityMatrix | 可追溯性矩阵初始化完成
2025-07-29 15:05:25 | INFO     | TraceabilityMatrix | 创建工件成功: requirement_542dcbf3 (集成测试需求)
2025-07-29 15:05:25 | INFO     | SystemIntegrationTest | 可追溯性系统测试成功
2025-07-29 15:05:25 | INFO     | SystemIntegrationTest | 测试 可追溯性系统: 通过
2025-07-29 15:05:25 | INFO     | SystemIntegrationTest | 执行测试: 智能体基本功能
2025-07-29 15:05:25 | INFO     | SystemIntegrationTest | 测试智能体基本功能...
2025-07-29 15:05:25 | INFO     | AgentType.REQUIREMENT_ANALYSIS_AgentType.REQUIREMENT_ANALYSIS_65321f4f | Agent初始化完成: AgentType.REQUIREMENT_ANALYSIS_65321f4f
2025-07-29 15:05:25 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | Agent组件初始化完成: AgentType.REQUIREMENT_ANALYSIS_65321f4f
2025-07-29 15:05:25 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 需求分析Agent初始化完成
2025-07-29 15:05:25 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.PROCESSING, 任务: 开始需求分析, 进度: 0.00
2025-07-29 15:05:25 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 1/10, 进度: 0.10
2025-07-29 15:05:25 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.10
2025-07-29 15:06:17 | INFO     | OllamaClient | 生成完成，耗时: 74.55秒
2025-07-29 15:06:17 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 思维链生成完成，共0步，响应长度: 1376
2025-07-29 15:06:17 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 规则提取需求失败: UNKNOWN
2025-07-29 15:06:17 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 结构化分析结果失败: UNKNOWN
2025-07-29 15:06:17 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 迭代处理失败: UNKNOWN
2025-07-29 15:06:17 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 第4轮迭代失败: UNKNOWN
2025-07-29 15:06:17 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 5/10, 进度: 0.50
2025-07-29 15:06:17 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.50
2025-07-29 15:06:36 | INFO     | OllamaClient | 生成完成，耗时: 71.05秒
2025-07-29 15:06:36 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:07:42 | INFO     | OllamaClient | 生成完成，耗时: 84.27秒
2025-07-29 15:07:42 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:08:16 | INFO     | OllamaClient | 生成完成，耗时: 99.88秒
2025-07-29 15:08:16 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 思维链生成完成，共0步，响应长度: 1997
2025-07-29 15:08:16 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 规则提取需求失败: AttributeError: UNKNOWN
2025-07-29 15:08:16 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 结构化分析结果失败: AttributeError: UNKNOWN
2025-07-29 15:08:16 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 迭代处理失败: AttributeError: UNKNOWN
2025-07-29 15:08:16 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 第1轮迭代失败: UNKNOWN
2025-07-29 15:08:16 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 2/10, 进度: 0.20
2025-07-29 15:08:16 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.20
2025-07-29 15:09:16 | INFO     | OllamaClient | 生成完成，耗时: 94.87秒
2025-07-29 15:09:16 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 思维链生成完成，共0步，响应长度: 1887
2025-07-29 15:09:16 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 规则提取需求失败: UNKNOWN
2025-07-29 15:09:16 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 结构化分析结果失败: UNKNOWN
2025-07-29 15:09:16 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 迭代处理失败: UNKNOWN
2025-07-29 15:09:16 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 第5轮迭代失败: UNKNOWN
2025-07-29 15:09:16 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 6/10, 进度: 0.60
2025-07-29 15:09:16 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.60
2025-07-29 15:09:53 | INFO     | OllamaClient | 生成完成，耗时: 96.88秒
2025-07-29 15:09:53 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:11:01 | INFO     | OllamaClient | 生成完成，耗时: 67.34秒
2025-07-29 15:11:01 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 思维链生成完成，共0步，响应长度: 1402
2025-07-29 15:11:01 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 规则提取需求失败: AttributeError: UNKNOWN
2025-07-29 15:11:01 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 结构化分析结果失败: AttributeError: UNKNOWN
2025-07-29 15:11:01 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 迭代处理失败: AttributeError: UNKNOWN
2025-07-29 15:11:01 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 第2轮迭代失败: UNKNOWN
2025-07-29 15:11:01 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 3/10, 进度: 0.30
2025-07-29 15:11:01 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.30
2025-07-29 15:11:37 | INFO     | OllamaClient | 生成完成，耗时: 140.50秒
2025-07-29 15:11:37 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:12:01 | INFO     | OllamaClient | 生成完成，耗时: 60.12秒
2025-07-29 15:12:01 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:12:49 | INFO     | OllamaClient | 生成完成，耗时: 71.99秒
2025-07-29 15:12:49 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 思维链生成完成，共0步，响应长度: 1448
2025-07-29 15:12:49 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 规则提取需求失败: UNKNOWN
2025-07-29 15:12:49 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 结构化分析结果失败: UNKNOWN
2025-07-29 15:12:49 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 迭代处理失败: UNKNOWN
2025-07-29 15:12:49 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 第6轮迭代失败: UNKNOWN
2025-07-29 15:12:49 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 7/10, 进度: 0.70
2025-07-29 15:12:49 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.70
2025-07-29 15:13:01 | INFO     | OllamaClient | 生成完成，耗时: 59.93秒
2025-07-29 15:13:01 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 思维链生成完成，共0步，响应长度: 1090
2025-07-29 15:13:01 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 规则提取需求失败: AttributeError: UNKNOWN
2025-07-29 15:13:01 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 结构化分析结果失败: AttributeError: UNKNOWN
2025-07-29 15:13:01 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 迭代处理失败: AttributeError: UNKNOWN
2025-07-29 15:13:01 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 第3轮迭代失败: UNKNOWN
2025-07-29 15:13:01 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 4/10, 进度: 0.40
2025-07-29 15:13:01 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.40
2025-07-29 15:13:37 | INFO     | OllamaClient | 生成完成，耗时: 48.13秒
2025-07-29 15:13:37 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:14:39 | INFO     | OllamaClient | 生成完成，耗时: 98.70秒
2025-07-29 15:14:39 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:15:47 | INFO     | OllamaClient | 生成完成，耗时: 130.08秒
2025-07-29 15:15:47 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 思维链生成完成，共0步，响应长度: 3346
2025-07-29 15:15:47 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 规则提取需求失败: UNKNOWN
2025-07-29 15:15:47 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 结构化分析结果失败: UNKNOWN
2025-07-29 15:15:47 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 迭代处理失败: UNKNOWN
2025-07-29 15:15:47 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 第7轮迭代失败: UNKNOWN
2025-07-29 15:15:47 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 8/10, 进度: 0.80
2025-07-29 15:15:47 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.80
2025-07-29 15:16:10 | INFO     | OllamaClient | 生成完成，耗时: 90.71秒
2025-07-29 15:16:10 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 思维链生成完成，共0步，响应长度: 1926
2025-07-29 15:16:10 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 规则提取需求失败: AttributeError: UNKNOWN
2025-07-29 15:16:10 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 结构化分析结果失败: AttributeError: UNKNOWN
2025-07-29 15:16:10 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 迭代处理失败: AttributeError: UNKNOWN
2025-07-29 15:16:10 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 第4轮迭代失败: UNKNOWN
2025-07-29 15:16:10 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 5/10, 进度: 0.50
2025-07-29 15:16:10 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.50
2025-07-29 15:17:11 | INFO     | OllamaClient | 生成完成，耗时: 61.39秒
2025-07-29 15:17:11 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:17:35 | INFO     | OllamaClient | 生成完成，耗时: 107.55秒
2025-07-29 15:17:35 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:18:33 | INFO     | OllamaClient | 生成完成，耗时: 81.24秒
2025-07-29 15:18:33 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 思维链生成完成，共0步，响应长度: 1598
2025-07-29 15:18:33 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 规则提取需求失败: AttributeError: UNKNOWN
2025-07-29 15:18:33 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 结构化分析结果失败: AttributeError: UNKNOWN
2025-07-29 15:18:33 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 迭代处理失败: AttributeError: UNKNOWN
2025-07-29 15:18:33 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 第5轮迭代失败: UNKNOWN
2025-07-29 15:18:33 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 6/10, 进度: 0.60
2025-07-29 15:18:33 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.60
2025-07-29 15:18:44 | INFO     | OllamaClient | 生成完成，耗时: 69.68秒
2025-07-29 15:18:44 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 思维链生成完成，共0步，响应长度: 1304
2025-07-29 15:18:44 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 规则提取需求失败: UNKNOWN
2025-07-29 15:18:44 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 结构化分析结果失败: UNKNOWN
2025-07-29 15:18:44 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 迭代处理失败: UNKNOWN
2025-07-29 15:18:44 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 第8轮迭代失败: UNKNOWN
2025-07-29 15:18:44 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 9/10, 进度: 0.90
2025-07-29 15:18:44 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.90
2025-07-29 15:19:55 | INFO     | OllamaClient | 生成完成，耗时: 82.43秒
2025-07-29 15:19:55 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:20:14 | INFO     | OllamaClient | 生成完成，耗时: 89.11秒
2025-07-29 15:20:14 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:21:04 | INFO     | OllamaClient | 生成完成，耗时: 50.80秒
2025-07-29 15:21:04 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 思维链生成完成，共0步，响应长度: 1068
2025-07-29 15:21:04 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 规则提取需求失败: UNKNOWN
2025-07-29 15:21:04 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 结构化分析结果失败: UNKNOWN
2025-07-29 15:21:04 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 迭代处理失败: UNKNOWN
2025-07-29 15:21:04 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 第9轮迭代失败: UNKNOWN
2025-07-29 15:21:04 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 10/10, 进度: 1.00
2025-07-29 15:21:04 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 1.00
2025-07-29 15:21:39 | INFO     | OllamaClient | 生成完成，耗时: 104.21秒
2025-07-29 15:21:39 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 思维链生成完成，共0步，响应长度: 2212
2025-07-29 15:21:39 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 规则提取需求失败: AttributeError: UNKNOWN
2025-07-29 15:21:39 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 结构化分析结果失败: AttributeError: UNKNOWN
2025-07-29 15:21:39 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 迭代处理失败: AttributeError: UNKNOWN
2025-07-29 15:21:39 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 第6轮迭代失败: UNKNOWN
2025-07-29 15:21:39 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 7/10, 进度: 0.70
2025-07-29 15:21:39 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.70
2025-07-29 15:22:12 | INFO     | OllamaClient | 生成完成，耗时: 67.47秒
2025-07-29 15:22:12 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:23:00 | INFO     | OllamaClient | 生成完成，耗时: 48.50秒
2025-07-29 15:23:00 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 思维链生成完成，共0步，响应长度: 1077
2025-07-29 15:23:00 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 规则提取需求失败: UNKNOWN
2025-07-29 15:23:00 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 结构化分析结果失败: UNKNOWN
2025-07-29 15:23:00 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 迭代处理失败: UNKNOWN
2025-07-29 15:23:00 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 第10轮迭代失败: UNKNOWN
2025-07-29 15:23:00 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 状态更新: AgentStatus.ERROR, 任务: 需求分析失败: UNKNOWN, 进度: 1.00
2025-07-29 15:23:00 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_923953c4 | 需求分析处理失败: UNKNOWN
2025-07-29 15:23:00 | ERROR    | SystemIntegrationTest | 智能体基本功能测试失败: UNKNOWN
2025-07-29 15:23:00 | INFO     | SystemIntegrationTest | 测试 智能体基本功能: 失败
2025-07-29 15:23:51 | INFO     | OllamaClient | 生成完成，耗时: 131.69秒
2025-07-29 15:23:51 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:24:31 | INFO     | OllamaClient | 生成完成，耗时: 39.63秒
2025-07-29 15:24:31 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 思维链生成完成，共0步，响应长度: 1233
2025-07-29 15:24:31 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 规则提取需求失败: AttributeError: UNKNOWN
2025-07-29 15:24:31 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 结构化分析结果失败: AttributeError: UNKNOWN
2025-07-29 15:24:31 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 迭代处理失败: AttributeError: UNKNOWN
2025-07-29 15:24:31 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 第7轮迭代失败: UNKNOWN
2025-07-29 15:24:31 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 8/10, 进度: 0.80
2025-07-29 15:24:31 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.80
2025-07-29 15:25:23 | INFO     | OllamaClient | 生成完成，耗时: 52.82秒
2025-07-29 15:25:23 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:26:07 | INFO     | OllamaClient | 生成完成，耗时: 43.56秒
2025-07-29 15:26:07 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 思维链生成完成，共0步，响应长度: 1425
2025-07-29 15:26:07 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 规则提取需求失败: AttributeError: UNKNOWN
2025-07-29 15:26:07 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 结构化分析结果失败: AttributeError: UNKNOWN
2025-07-29 15:26:07 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 迭代处理失败: AttributeError: UNKNOWN
2025-07-29 15:26:07 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 第8轮迭代失败: UNKNOWN
2025-07-29 15:26:07 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 9/10, 进度: 0.90
2025-07-29 15:26:07 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_65321f4f | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.90
2025-07-29 15:27:11 | INFO     | OllamaClient | 生成完成，耗时: 64.18秒
2025-07-29 15:27:11 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:29:05 | INFO     | SystemStartup | ============================================================
2025-07-29 15:29:05 | INFO     | SystemStartup | 异步多智能体测试用例生成框架
2025-07-29 15:29:05 | INFO     | SystemStartup | ============================================================
2025-07-29 15:29:05 | INFO     | SystemStartup | 启动时间: 2025-07-29 15:29:05
2025-07-29 15:29:05 | INFO     | SystemStartup | 开始初始化系统组件...
2025-07-29 15:29:05 | INFO     | SystemStartup | 初始化记忆系统...
2025-07-29 15:29:13 | INFO     | VectorMemory | 已连接到现有集合: agent_memory
2025-07-29 15:29:13 | INFO     | VectorMemory | ChromaDB向量记忆系统初始化完成
2025-07-29 15:29:13 | INFO     | VectorMemory | 集合统计: 0 个文档
2025-07-29 15:29:13 | INFO     | SystemStartup | 记忆系统初始化成功
2025-07-29 15:29:13 | INFO     | SystemStartup | 初始化可追溯性系统...
2025-07-29 15:29:13 | INFO     | TraceabilityMatrix | 加载了 1 个工件和 0 个链接
2025-07-29 15:29:13 | INFO     | TraceabilityMatrix | 可追溯性矩阵初始化完成
2025-07-29 15:29:13 | INFO     | SystemStartup | 可追溯性系统初始化成功，当前统计: {'artifacts': {'total': 1, 'by_type': {'requirement': 1}, 'by_status': {'draft': 1}}, 'links': {'total': 0, 'by_type': {}}, 'coverage': {}, 'graph': {'nodes': 0, 'edges': 0, 'density': 0}}
2025-07-29 15:29:13 | INFO     | SystemStartup | 检查Ollama连接...
2025-07-29 15:29:14 | INFO     | OllamaClient | 模型 deepseek-r1:1.5b 可用
2025-07-29 15:29:14 | INFO     | OllamaClient | Ollama客户端初始化成功，模型: deepseek-r1:1.5b
2025-07-29 15:29:14 | INFO     | SystemStartup | Ollama连接成功，可用模型数: 2
2025-07-29 15:29:14 | INFO     | SystemStartup | 系统组件状态:
2025-07-29 15:29:14 | INFO     | SystemStartup |   memory: healthy
2025-07-29 15:29:14 | INFO     | SystemStartup |   traceability: healthy
2025-07-29 15:29:14 | INFO     | SystemStartup |   ollama: healthy (2 models)
2025-07-29 15:29:14 | INFO     | SystemStartup | 系统健康状态: healthy
2025-07-29 15:29:14 | INFO     | SystemStartup | 运行启动测试...
2025-07-29 15:29:14 | INFO     | SystemIntegrationTest | 开始系统集成测试...
2025-07-29 15:29:14 | INFO     | SystemIntegrationTest | 执行测试: Ollama连接
2025-07-29 15:29:14 | INFO     | SystemIntegrationTest | 测试Ollama连接...
2025-07-29 15:29:14 | INFO     | SystemIntegrationTest | Ollama连接成功，可用模型: 2
2025-07-29 15:29:14 | INFO     | SystemIntegrationTest | 测试 Ollama连接: 通过
2025-07-29 15:29:14 | INFO     | SystemIntegrationTest | 执行测试: 记忆系统
2025-07-29 15:29:14 | INFO     | SystemIntegrationTest | 测试记忆系统...
2025-07-29 15:29:14 | INFO     | VectorMemory | 集合统计: 0 个文档
2025-07-29 15:29:14 | INFO     | SystemIntegrationTest | 记忆系统测试成功，文档数量: 0
2025-07-29 15:29:14 | INFO     | SystemIntegrationTest | 测试 记忆系统: 通过
2025-07-29 15:29:14 | INFO     | SystemIntegrationTest | 执行测试: 可追溯性系统
2025-07-29 15:29:14 | INFO     | SystemIntegrationTest | 测试可追溯性系统...
2025-07-29 15:29:14 | INFO     | TraceabilityMatrix | 创建工件成功: requirement_e45f61f8 (集成测试需求)
2025-07-29 15:29:14 | INFO     | SystemIntegrationTest | 可追溯性系统测试成功
2025-07-29 15:29:14 | INFO     | SystemIntegrationTest | 测试 可追溯性系统: 通过
2025-07-29 15:29:14 | INFO     | SystemIntegrationTest | 执行测试: 智能体基本功能
2025-07-29 15:29:14 | INFO     | SystemIntegrationTest | 测试智能体基本功能...
2025-07-29 15:29:14 | INFO     | AgentType.REQUIREMENT_ANALYSIS_AgentType.REQUIREMENT_ANALYSIS_4733a55c | Agent初始化完成: AgentType.REQUIREMENT_ANALYSIS_4733a55c
2025-07-29 15:29:14 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | Agent组件初始化完成: AgentType.REQUIREMENT_ANALYSIS_4733a55c
2025-07-29 15:29:14 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | 需求分析Agent初始化完成
2025-07-29 15:29:14 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | 状态更新: AgentStatus.PROCESSING, 任务: 开始需求分析, 进度: 0.00
2025-07-29 15:29:14 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 1/10, 进度: 0.10
2025-07-29 15:29:14 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.10
2025-07-29 15:29:53 | INFO     | OllamaClient | 生成完成，耗时: 39.11秒
2025-07-29 15:29:53 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:30:44 | INFO     | OllamaClient | 生成完成，耗时: 51.30秒
2025-07-29 15:30:44 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | 思维链生成完成，共0步，响应长度: 1774
2025-07-29 15:30:44 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | 规则提取需求失败: AttributeError: UNKNOWN
2025-07-29 15:30:44 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | 结构化分析结果失败: AttributeError: UNKNOWN
2025-07-29 15:30:44 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | 迭代处理失败: AttributeError: UNKNOWN
2025-07-29 15:30:44 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | 第1轮迭代失败: UNKNOWN
2025-07-29 15:30:44 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 2/10, 进度: 0.20
2025-07-29 15:30:44 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.20
2025-07-29 15:31:41 | INFO     | OllamaClient | 生成完成，耗时: 56.99秒
2025-07-29 15:31:41 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:32:30 | INFO     | OllamaClient | 生成完成，耗时: 48.58秒
2025-07-29 15:32:30 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | 思维链生成完成，共0步，响应长度: 1666
2025-07-29 15:32:30 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | 规则提取需求失败: AttributeError: UNKNOWN
2025-07-29 15:32:30 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | 结构化分析结果失败: AttributeError: UNKNOWN
2025-07-29 15:32:30 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | 迭代处理失败: AttributeError: UNKNOWN
2025-07-29 15:32:30 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | 第2轮迭代失败: UNKNOWN
2025-07-29 15:32:30 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 3/10, 进度: 0.30
2025-07-29 15:32:30 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_4733a55c | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.30
2025-07-29 15:39:09 | INFO     | QuickTest | ============================================================
2025-07-29 15:39:09 | INFO     | QuickTest | 快速系统测试
2025-07-29 15:39:09 | INFO     | QuickTest | ============================================================
2025-07-29 15:39:09 | INFO     | QuickTest | 🧪 开始基本组件测试
2025-07-29 15:39:09 | INFO     | QuickTest | 测试记忆系统...
2025-07-29 15:39:17 | INFO     | VectorMemory | 已连接到现有集合: agent_memory
2025-07-29 15:39:17 | INFO     | VectorMemory | ChromaDB向量记忆系统初始化完成
2025-07-29 15:39:17 | INFO     | VectorMemory | 集合统计: 0 个文档
2025-07-29 15:39:17 | INFO     | QuickTest | ✅ 记忆系统正常，文档数量: 0
2025-07-29 15:39:17 | INFO     | QuickTest | 测试可追溯性系统...
2025-07-29 15:39:17 | INFO     | TraceabilityMatrix | 加载了 2 个工件和 0 个链接
2025-07-29 15:39:17 | INFO     | TraceabilityMatrix | 可追溯性矩阵初始化完成
2025-07-29 15:39:17 | INFO     | QuickTest | ✅ 可追溯性系统正常，工件数量: 2
2025-07-29 15:39:17 | INFO     | QuickTest | 测试Ollama连接...
2025-07-29 15:39:17 | INFO     | OllamaClient | 模型 deepseek-r1:1.5b 可用
2025-07-29 15:39:17 | INFO     | OllamaClient | Ollama客户端初始化成功，模型: deepseek-r1:1.5b
2025-07-29 15:39:17 | INFO     | QuickTest | ✅ Ollama连接正常，可用模型: 2
2025-07-29 15:39:17 | INFO     | QuickTest | 🎉 所有基本组件测试通过！
2025-07-29 15:39:17 | INFO     | QuickTest | 🤖 测试智能体导入
2025-07-29 15:39:17 | INFO     | QuickTest | ✅ 所有智能体导入成功
2025-07-29 15:39:17 | INFO     | AgentType.REQUIREMENT_ANALYSIS_AgentType.REQUIREMENT_ANALYSIS_ba9b34e1 | Agent初始化完成: AgentType.REQUIREMENT_ANALYSIS_ba9b34e1
2025-07-29 15:39:17 | INFO     | QuickTest | ✅ 需求分析智能体创建成功
2025-07-29 15:39:17 | INFO     | QuickTest | ============================================================
2025-07-29 15:39:17 | INFO     | QuickTest | 🎉 所有测试通过！系统准备就绪
2025-07-29 15:39:45 | INFO     | WorkflowDemo | 🚀 启动完整工作流程演示
2025-07-29 15:39:45 | INFO     | WorkflowDemo | 使用预定义需求: 用户登录
2025-07-29 15:39:45 | INFO     | WorkflowDemo | 初始化系统组件...
2025-07-29 15:39:45 | INFO     | SystemStartup | 开始初始化系统组件...
2025-07-29 15:39:45 | INFO     | SystemStartup | 初始化记忆系统...
2025-07-29 15:39:53 | INFO     | VectorMemory | 已连接到现有集合: agent_memory
2025-07-29 15:39:53 | INFO     | VectorMemory | ChromaDB向量记忆系统初始化完成
2025-07-29 15:39:53 | INFO     | VectorMemory | 集合统计: 0 个文档
2025-07-29 15:39:53 | INFO     | SystemStartup | 记忆系统初始化成功
2025-07-29 15:39:53 | INFO     | SystemStartup | 初始化可追溯性系统...
2025-07-29 15:39:53 | INFO     | TraceabilityMatrix | 加载了 2 个工件和 0 个链接
2025-07-29 15:39:53 | INFO     | TraceabilityMatrix | 可追溯性矩阵初始化完成
2025-07-29 15:39:53 | INFO     | SystemStartup | 可追溯性系统初始化成功，当前统计: {'artifacts': {'total': 2, 'by_type': {'requirement': 2}, 'by_status': {'draft': 2}}, 'links': {'total': 0, 'by_type': {}}, 'coverage': {}, 'graph': {'nodes': 0, 'edges': 0, 'density': 0}}
2025-07-29 15:39:53 | INFO     | SystemStartup | 检查Ollama连接...
2025-07-29 15:39:53 | INFO     | OllamaClient | 模型 deepseek-r1:1.5b 可用
2025-07-29 15:39:53 | INFO     | OllamaClient | Ollama客户端初始化成功，模型: deepseek-r1:1.5b
2025-07-29 15:39:53 | INFO     | SystemStartup | Ollama连接成功，可用模型数: 2
2025-07-29 15:39:53 | INFO     | SystemStartup | 系统组件状态:
2025-07-29 15:39:53 | INFO     | SystemStartup |   memory: healthy
2025-07-29 15:39:53 | INFO     | SystemStartup |   traceability: healthy
2025-07-29 15:39:53 | INFO     | SystemStartup |   ollama: healthy (2 models)
2025-07-29 15:39:53 | INFO     | WorkflowDemo | 系统健康状态: healthy
2025-07-29 15:39:53 | INFO     | SystemStartup | ================================================================================
2025-07-29 15:39:53 | INFO     | SystemStartup | 开始完整的需求处理工作流程演示
2025-07-29 15:39:53 | INFO     | SystemStartup | ================================================================================
2025-07-29 15:39:53 | INFO     | SystemStartup | 输入需求: 
用户登录功能需求：
1. 用户可以通过用户名和密码登录系统
2. 登录成功后跳转到主页面
3. 登录失败时显示错误提示
4. 支持记住密码功能
5. 连续登录失败3次后锁定账户30分钟

2025-07-29 15:39:53 | INFO     | SystemStartup | 
🔍 第一步：需求分析
2025-07-29 15:39:53 | INFO     | SystemStartup | --------------------------------------------------
2025-07-29 15:39:53 | INFO     | AgentType.REQUIREMENT_ANALYSIS_AgentType.REQUIREMENT_ANALYSIS_d4b3dac7 | Agent初始化完成: AgentType.REQUIREMENT_ANALYSIS_d4b3dac7
2025-07-29 15:39:53 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_d4b3dac7 | Agent组件初始化完成: AgentType.REQUIREMENT_ANALYSIS_d4b3dac7
2025-07-29 15:39:53 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_d4b3dac7 | 需求分析Agent初始化完成
2025-07-29 15:39:53 | ERROR    | SystemStartup | 完整工作流程执行失败: AttributeError: 'RequirementAnalysisAgent' object has no attribute 'process_requirement'
2025-07-29 15:39:53 | ERROR    | WorkflowDemo | 演示失败: 'RequirementAnalysisAgent' object has no attribute 'process_requirement'
2025-07-29 15:41:20 | INFO     | WorkflowDemo | 🚀 启动完整工作流程演示
2025-07-29 15:41:20 | INFO     | WorkflowDemo | 使用预定义需求: 用户登录
2025-07-29 15:41:20 | INFO     | WorkflowDemo | 初始化系统组件...
2025-07-29 15:41:20 | INFO     | SystemStartup | 开始初始化系统组件...
2025-07-29 15:41:20 | INFO     | SystemStartup | 初始化记忆系统...
2025-07-29 15:41:27 | INFO     | VectorMemory | 已连接到现有集合: agent_memory
2025-07-29 15:41:27 | INFO     | VectorMemory | ChromaDB向量记忆系统初始化完成
2025-07-29 15:41:27 | INFO     | VectorMemory | 集合统计: 0 个文档
2025-07-29 15:41:27 | INFO     | SystemStartup | 记忆系统初始化成功
2025-07-29 15:41:27 | INFO     | SystemStartup | 初始化可追溯性系统...
2025-07-29 15:41:27 | INFO     | TraceabilityMatrix | 加载了 2 个工件和 0 个链接
2025-07-29 15:41:27 | INFO     | TraceabilityMatrix | 可追溯性矩阵初始化完成
2025-07-29 15:41:27 | INFO     | SystemStartup | 可追溯性系统初始化成功，当前统计: {'artifacts': {'total': 2, 'by_type': {'requirement': 2}, 'by_status': {'draft': 2}}, 'links': {'total': 0, 'by_type': {}}, 'coverage': {}, 'graph': {'nodes': 0, 'edges': 0, 'density': 0}}
2025-07-29 15:41:27 | INFO     | SystemStartup | 检查Ollama连接...
2025-07-29 15:41:27 | INFO     | OllamaClient | 模型 deepseek-r1:1.5b 可用
2025-07-29 15:41:27 | INFO     | OllamaClient | Ollama客户端初始化成功，模型: deepseek-r1:1.5b
2025-07-29 15:41:27 | INFO     | SystemStartup | Ollama连接成功，可用模型数: 2
2025-07-29 15:41:27 | INFO     | SystemStartup | 系统组件状态:
2025-07-29 15:41:27 | INFO     | SystemStartup |   memory: healthy
2025-07-29 15:41:27 | INFO     | SystemStartup |   traceability: healthy
2025-07-29 15:41:27 | INFO     | SystemStartup |   ollama: healthy (2 models)
2025-07-29 15:41:27 | INFO     | WorkflowDemo | 系统健康状态: healthy
2025-07-29 15:41:27 | INFO     | SystemStartup | ================================================================================
2025-07-29 15:41:27 | INFO     | SystemStartup | 开始完整的需求处理工作流程演示
2025-07-29 15:41:27 | INFO     | SystemStartup | ================================================================================
2025-07-29 15:41:27 | INFO     | SystemStartup | 输入需求: 
用户登录功能需求：
1. 用户可以通过用户名和密码登录系统
2. 登录成功后跳转到主页面
3. 登录失败时显示错误提示
4. 支持记住密码功能
5. 连续登录失败3次后锁定账户30分钟

2025-07-29 15:41:27 | INFO     | SystemStartup | 
🔍 第一步：需求分析
2025-07-29 15:41:27 | INFO     | SystemStartup | --------------------------------------------------
2025-07-29 15:41:27 | INFO     | AgentType.REQUIREMENT_ANALYSIS_AgentType.REQUIREMENT_ANALYSIS_90678414 | Agent初始化完成: AgentType.REQUIREMENT_ANALYSIS_90678414
2025-07-29 15:41:27 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_90678414 | Agent组件初始化完成: AgentType.REQUIREMENT_ANALYSIS_90678414
2025-07-29 15:41:27 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_90678414 | 需求分析Agent初始化完成
2025-07-29 15:41:27 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_90678414 | 状态更新: AgentStatus.PROCESSING, 任务: 开始需求分析, 进度: 0.00
2025-07-29 15:41:27 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_90678414 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 1/10, 进度: 0.10
2025-07-29 15:41:27 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_90678414 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.10
2025-07-29 15:42:26 | INFO     | OllamaClient | 生成完成，耗时: 58.70秒
2025-07-29 15:42:26 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:43:25 | INFO     | OllamaClient | 生成完成，耗时: 58.58秒
2025-07-29 15:43:25 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_90678414 | 思维链生成完成，共0步，响应长度: 1635
2025-07-29 15:43:25 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_90678414 | 规则提取需求失败: AttributeError: UNKNOWN
2025-07-29 15:43:25 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_90678414 | 结构化分析结果失败: AttributeError: UNKNOWN
2025-07-29 15:43:25 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_90678414 | 迭代处理失败: AttributeError: UNKNOWN
2025-07-29 15:43:25 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_90678414 | 第1轮迭代失败: UNKNOWN
2025-07-29 15:43:25 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_90678414 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 2/10, 进度: 0.20
2025-07-29 15:43:25 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_90678414 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.20
2025-07-29 15:44:15 | INFO     | OllamaClient | 生成完成，耗时: 50.45秒
2025-07-29 15:44:15 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:45:31 | INFO     | OllamaClient | 生成完成，耗时: 75.50秒
2025-07-29 15:45:31 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_90678414 | 思维链生成完成，共0步，响应长度: 2025
2025-07-29 15:45:31 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_90678414 | 规则提取需求失败: AttributeError: UNKNOWN
2025-07-29 15:45:31 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_90678414 | 结构化分析结果失败: AttributeError: UNKNOWN
2025-07-29 15:45:31 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_90678414 | 迭代处理失败: AttributeError: UNKNOWN
2025-07-29 15:45:31 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_90678414 | 第2轮迭代失败: UNKNOWN
2025-07-29 15:45:31 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_90678414 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 3/10, 进度: 0.30
2025-07-29 15:45:31 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_90678414 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.30
2025-07-29 15:46:27 | INFO     | SimpleWorkflowDemo | 🔧 初始化系统组件...
2025-07-29 15:46:37 | INFO     | VectorMemory | 已连接到现有集合: agent_memory
2025-07-29 15:46:37 | INFO     | VectorMemory | ChromaDB向量记忆系统初始化完成
2025-07-29 15:46:37 | INFO     | TraceabilityMatrix | 加载了 2 个工件和 0 个链接
2025-07-29 15:46:37 | INFO     | TraceabilityMatrix | 可追溯性矩阵初始化完成
2025-07-29 15:46:37 | INFO     | OllamaClient | 模型 deepseek-r1:1.5b 可用
2025-07-29 15:46:37 | INFO     | OllamaClient | Ollama客户端初始化成功，模型: deepseek-r1:1.5b
2025-07-29 15:46:37 | INFO     | SimpleWorkflowDemo | ✅ 系统组件初始化完成
2025-07-29 15:46:37 | INFO     | SimpleWorkflowDemo | 
============================================================
2025-07-29 15:46:37 | INFO     | SimpleWorkflowDemo | 🔍 第一步：需求分析
2025-07-29 15:46:37 | INFO     | SimpleWorkflowDemo | ============================================================
2025-07-29 15:46:37 | INFO     | SimpleWorkflowDemo | 输入需求：

用户登录功能需求：
1. 用户可以通过用户名和密码登录系统
2. 登录成功后跳转到主页面
3. 登录失败时显示错误提示
4. 支持记住密码功能
5. 连续登录失败3次后锁定账户30分钟

2025-07-29 15:46:37 | INFO     | SimpleWorkflowDemo | 📝 正在分析需求...
2025-07-29 15:46:37 | ERROR    | SimpleWorkflowDemo | 演示过程中发生异常: AttributeError: 'ChromaDBVectorMemory' object has no attribute 'store_document'
2025-07-29 15:48:13 | INFO     | SimpleWorkflowDemo | 🔧 初始化系统组件...
2025-07-29 15:48:22 | INFO     | VectorMemory | 已连接到现有集合: agent_memory
2025-07-29 15:48:22 | INFO     | VectorMemory | ChromaDB向量记忆系统初始化完成
2025-07-29 15:48:22 | INFO     | TraceabilityMatrix | 加载了 2 个工件和 0 个链接
2025-07-29 15:48:22 | INFO     | TraceabilityMatrix | 可追溯性矩阵初始化完成
2025-07-29 15:48:22 | INFO     | OllamaClient | 模型 deepseek-r1:1.5b 可用
2025-07-29 15:48:22 | INFO     | OllamaClient | Ollama客户端初始化成功，模型: deepseek-r1:1.5b
2025-07-29 15:48:22 | INFO     | SimpleWorkflowDemo | ✅ 系统组件初始化完成
2025-07-29 15:48:22 | INFO     | SimpleWorkflowDemo | 
============================================================
2025-07-29 15:48:22 | INFO     | SimpleWorkflowDemo | 🔍 第一步：需求分析
2025-07-29 15:48:22 | INFO     | SimpleWorkflowDemo | ============================================================
2025-07-29 15:48:22 | INFO     | SimpleWorkflowDemo | 输入需求：

用户登录功能需求：
1. 用户可以通过用户名和密码登录系统
2. 登录成功后跳转到主页面
3. 登录失败时显示错误提示
4. 支持记住密码功能
5. 连续登录失败3次后锁定账户30分钟

2025-07-29 15:48:22 | INFO     | SimpleWorkflowDemo | 📝 正在分析需求...
2025-07-29 15:48:22 | DEBUG    | SimpleWorkflowDemo | 需求分析结果: {
  "session_id": "demo_20250729_154813",
  "original_text": "\n用户登录功能需求：\n1. 用户可以通过用户名和密码登录系统\n2. 登录成功后跳转到主页面\n3. 登录失败时显示错误提示\n4. 支持记住密码功能\n5. 连续登录失败3次后锁定账户30分钟\n",
  "requirements": [
    {
      "id": "REQ_001",
      "description": "用户登录功能需求：",
      "priority": "高",
      "type": "功能性需求",
      "status": "待评审"
    }
  ],
  "total_count": 1,
  "analysis_time": "2025-07-29T15:48:22.471080"
}
2025-07-29 15:48:22 | INFO     | SimpleWorkflowDemo | ✅ 需求分析完成，提取到 1 个需求
2025-07-29 15:48:22 | INFO     | SimpleWorkflowDemo |    - REQ_001: 用户登录功能需求：...
2025-07-29 15:48:22 | INFO     | SimpleWorkflowDemo | 
============================================================
2025-07-29 15:48:22 | INFO     | SimpleWorkflowDemo | 📋 第二步：需求评审
2025-07-29 15:48:22 | INFO     | SimpleWorkflowDemo | ============================================================
2025-07-29 15:48:22 | INFO     | SimpleWorkflowDemo | 📝 正在评审 1 个需求...
2025-07-29 15:48:22 | DEBUG    | SimpleWorkflowDemo | 需求评审结果: {
  "session_id": "demo_20250729_154813",
  "reviewed_requirements": [
    {
      "id": "REQ_001",
      "description": "用户登录功能需求：",
      "priority": "高",
      "type": "功能性需求",
      "status": "待评审",
      "review_score": 85,
      "review_status": "通过",
      "review_comments": "需求描述清晰"
    }
  ],
  "overall_score": 85.0,
  "total_issues": 0,
  "issues": [],
  "overall_status": "通过",
  "review_time": "2025-07-29T15:48:22.472804"
}
2025-07-29 15:48:22 | INFO     | SimpleWorkflowDemo | ✅ 需求评审完成，总体评分: 85.0
2025-07-29 15:48:22 | INFO     | SimpleWorkflowDemo |    评审状态: 通过
2025-07-29 15:48:22 | INFO     | SimpleWorkflowDemo | 
============================================================
2025-07-29 15:48:22 | INFO     | SimpleWorkflowDemo | 🧪 第三步：测试用例生成
2025-07-29 15:48:22 | INFO     | SimpleWorkflowDemo | ============================================================
2025-07-29 15:48:22 | INFO     | SimpleWorkflowDemo | 📝 正在为 1 个需求生成测试用例...
2025-07-29 15:48:22 | DEBUG    | SimpleWorkflowDemo | 测试用例生成结果: {
  "session_id": "demo_20250729_154813",
  "testcases": [
    {
      "id": "TC_001_01",
      "requirement_id": "REQ_001",
      "title": "测试用户登录功能需求：...场景1",
      "type": "正向测试",
      "priority": "高",
      "steps": [
        "步骤1: 准备测试数据",
        "步骤2: 执行用户登录功能需求：操作",
        "步骤3: 验证结果"
      ],
      "expected_result": "期望结果：用户登录功能需求：成功",
      "status": "待执行"
    },
    {
      "id": "TC_001_02",
      "requirement_id": "REQ_001",
      "title": "测试用户登录功能需求：...场景2",
      "type": "负向测试",
      "priority": "高",
      "steps": [
        "步骤1: 准备测试数据",
        "步骤2: 执行用户登录功能需求：操作",
        "步骤3: 验证结果"
      ],
      "expected_result": "期望结果：用户登录功能需求：成功",
      "status": "待执行"
    },
    {
      "id": "TC_001_03",
      "requirement_id": "REQ_001",
      "title": "测试用户登录功能需求：...场景3",
      "type": "边界测试",
      "priority": "高",
      "steps": [
        "步骤1: 准备测试数据",
        "步骤2: 执行用户登录功能需求：操作",
        "步骤3: 验证结果"
      ],
      "expected_result": "期望结果：用户登录功能需求：成功",
      "status": "待执行"
    }
  ],
  "total_count": 3,
  "coverage": {
    "requirement_coverage": "100%",
    "scenario_coverage": "85%"
  },
  "generation_time": "2025-07-29T15:48:22.474442"
}
2025-07-29 15:48:22 | INFO     | SimpleWorkflowDemo | ✅ 测试用例生成完成，共生成 3 个测试用例
2025-07-29 15:48:22 | INFO     | SimpleWorkflowDemo |    需求覆盖率: 100%
2025-07-29 15:48:22 | INFO     | SimpleWorkflowDemo |    场景覆盖率: 85%
2025-07-29 15:48:22 | INFO     | SimpleWorkflowDemo | 
============================================================
2025-07-29 15:48:22 | INFO     | SimpleWorkflowDemo | ✅ 第四步：测试用例评审
2025-07-29 15:48:22 | INFO     | SimpleWorkflowDemo | ============================================================
2025-07-29 15:48:22 | INFO     | SimpleWorkflowDemo | 📝 正在评审 3 个测试用例...
2025-07-29 15:48:22 | DEBUG    | SimpleWorkflowDemo | 测试用例评审结果: {
  "session_id": "demo_20250729_154813",
  "reviewed_testcases": [
    {
      "id": "TC_001_01",
      "requirement_id": "REQ_001",
      "title": "测试用户登录功能需求：...场景1",
      "type": "正向测试",
      "priority": "高",
      "steps": [
        "步骤1: 准备测试数据",
        "步骤2: 执行用户登录功能需求：操作",
        "步骤3: 验证结果"
      ],
      "expected_result": "期望结果：用户登录功能需求：成功",
      "status": "待执行",
      "review_score": 88,
      "review_status": "通过",
      "review_comments": "测试用例设计合理"
    },
    {
      "id": "TC_001_02",
      "requirement_id": "REQ_001",
      "title": "测试用户登录功能需求：...场景2",
      "type": "负向测试",
      "priority": "高",
      "steps": [
        "步骤1: 准备测试数据",
        "步骤2: 执行用户登录功能需求：操作",
        "步骤3: 验证结果"
      ],
      "expected_result": "期望结果：用户登录功能需求：成功",
      "status": "待执行",
      "review_score": 85,
      "review_status": "通过",
      "review_comments": "测试用例设计合理"
    },
    {
      "id": "TC_001_03",
      "requirement_id": "REQ_001",
      "title": "测试用户登录功能需求：...场景3",
      "type": "边界测试",
      "priority": "高",
      "steps": [
        "步骤1: 准备测试数据",
        "步骤2: 执行用户登录功能需求：操作",
        "步骤3: 验证结果"
      ],
      "expected_result": "期望结果：用户登录功能需求：成功",
      "status": "待执行",
      "review_score": 85,
      "review_status": "通过",
      "review_comments": "测试用例设计合理"
    }
  ],
  "overall_score": 86.0,
  "total_issues": 0,
  "issues": [],
  "final_status": "通过评审",
  "review_time": "2025-07-29T15:48:22.475891"
}
2025-07-29 15:48:22 | INFO     | SimpleWorkflowDemo | ✅ 测试用例评审完成，总体评分: 86.0
2025-07-29 15:48:22 | INFO     | SimpleWorkflowDemo |    最终状态: 通过评审
2025-07-29 15:48:22 | INFO     | SimpleWorkflowDemo | 
================================================================================
2025-07-29 15:48:22 | INFO     | SimpleWorkflowDemo | 📊 完整工作流程总结
2025-07-29 15:48:22 | INFO     | SimpleWorkflowDemo | ================================================================================
2025-07-29 15:48:22 | INFO     | SimpleWorkflowDemo | 🎯 会话ID: demo_20250729_154813
2025-07-29 15:48:22 | INFO     | SimpleWorkflowDemo | 📝 原始需求: 
用户登录功能需求：
1. 用户可以通过用户名和密码登录系统
2. 登录成功后跳转到主页面
3. 登录失败时显示错误提示
4. 支持记住密码功能
5. 连续登录失败3次后锁定账户30分钟
...
2025-07-29 15:48:22 | INFO     | SimpleWorkflowDemo | 🔍 需求分析: 提取 1 个需求
2025-07-29 15:48:22 | INFO     | SimpleWorkflowDemo | 📋 需求评审: 总体评分 85.0，状态 通过
2025-07-29 15:48:22 | INFO     | SimpleWorkflowDemo | 🧪 用例生成: 生成 3 个测试用例
2025-07-29 15:48:22 | INFO     | SimpleWorkflowDemo | ✅ 用例评审: 总体评分 86.0，状态 通过评审
2025-07-29 15:48:22 | ERROR    | TraceabilityMatrix | 创建链接失败: 源工件或目标工件不存在
2025-07-29 15:48:22 | ERROR    | SimpleWorkflowDemo | 演示过程中发生异常: ValueError: 源工件或目标工件不存在
2025-07-29 15:49:04 | INFO     | SimpleWorkflowDemo | 🔧 初始化系统组件...
2025-07-29 15:49:12 | INFO     | VectorMemory | 已连接到现有集合: agent_memory
2025-07-29 15:49:12 | INFO     | VectorMemory | ChromaDB向量记忆系统初始化完成
2025-07-29 15:49:12 | INFO     | TraceabilityMatrix | 加载了 2 个工件和 0 个链接
2025-07-29 15:49:12 | INFO     | TraceabilityMatrix | 可追溯性矩阵初始化完成
2025-07-29 15:49:12 | INFO     | OllamaClient | 模型 deepseek-r1:1.5b 可用
2025-07-29 15:49:12 | INFO     | OllamaClient | Ollama客户端初始化成功，模型: deepseek-r1:1.5b
2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo | ✅ 系统组件初始化完成
2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo | 
============================================================
2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo | 🔍 第一步：需求分析
2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo | ============================================================
2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo | 输入需求：

用户登录功能需求：
1. 用户可以通过用户名和密码登录系统
2. 登录成功后跳转到主页面
3. 登录失败时显示错误提示
4. 支持记住密码功能
5. 连续登录失败3次后锁定账户30分钟

2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo | 📝 正在分析需求...
2025-07-29 15:49:12 | DEBUG    | SimpleWorkflowDemo | 需求分析结果: {
  "session_id": "demo_20250729_154904",
  "original_text": "\n用户登录功能需求：\n1. 用户可以通过用户名和密码登录系统\n2. 登录成功后跳转到主页面\n3. 登录失败时显示错误提示\n4. 支持记住密码功能\n5. 连续登录失败3次后锁定账户30分钟\n",
  "requirements": [
    {
      "id": "REQ_001",
      "description": "用户登录功能需求：",
      "priority": "高",
      "type": "功能性需求",
      "status": "待评审"
    }
  ],
  "total_count": 1,
  "analysis_time": "2025-07-29T15:49:12.911983"
}
2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo | ✅ 需求分析完成，提取到 1 个需求
2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo |    - REQ_001: 用户登录功能需求：...
2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo | 
============================================================
2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo | 📋 第二步：需求评审
2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo | ============================================================
2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo | 📝 正在评审 1 个需求...
2025-07-29 15:49:12 | DEBUG    | SimpleWorkflowDemo | 需求评审结果: {
  "session_id": "demo_20250729_154904",
  "reviewed_requirements": [
    {
      "id": "REQ_001",
      "description": "用户登录功能需求：",
      "priority": "高",
      "type": "功能性需求",
      "status": "待评审",
      "review_score": 85,
      "review_status": "通过",
      "review_comments": "需求描述清晰"
    }
  ],
  "overall_score": 85.0,
  "total_issues": 0,
  "issues": [],
  "overall_status": "通过",
  "review_time": "2025-07-29T15:49:12.913663"
}
2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo | ✅ 需求评审完成，总体评分: 85.0
2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo |    评审状态: 通过
2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo | 
============================================================
2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo | 🧪 第三步：测试用例生成
2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo | ============================================================
2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo | 📝 正在为 1 个需求生成测试用例...
2025-07-29 15:49:12 | DEBUG    | SimpleWorkflowDemo | 测试用例生成结果: {
  "session_id": "demo_20250729_154904",
  "testcases": [
    {
      "id": "TC_001_01",
      "requirement_id": "REQ_001",
      "title": "测试用户登录功能需求：...场景1",
      "type": "正向测试",
      "priority": "高",
      "steps": [
        "步骤1: 准备测试数据",
        "步骤2: 执行用户登录功能需求：操作",
        "步骤3: 验证结果"
      ],
      "expected_result": "期望结果：用户登录功能需求：成功",
      "status": "待执行"
    },
    {
      "id": "TC_001_02",
      "requirement_id": "REQ_001",
      "title": "测试用户登录功能需求：...场景2",
      "type": "负向测试",
      "priority": "高",
      "steps": [
        "步骤1: 准备测试数据",
        "步骤2: 执行用户登录功能需求：操作",
        "步骤3: 验证结果"
      ],
      "expected_result": "期望结果：用户登录功能需求：成功",
      "status": "待执行"
    },
    {
      "id": "TC_001_03",
      "requirement_id": "REQ_001",
      "title": "测试用户登录功能需求：...场景3",
      "type": "边界测试",
      "priority": "高",
      "steps": [
        "步骤1: 准备测试数据",
        "步骤2: 执行用户登录功能需求：操作",
        "步骤3: 验证结果"
      ],
      "expected_result": "期望结果：用户登录功能需求：成功",
      "status": "待执行"
    }
  ],
  "total_count": 3,
  "coverage": {
    "requirement_coverage": "100%",
    "scenario_coverage": "85%"
  },
  "generation_time": "2025-07-29T15:49:12.914982"
}
2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo | ✅ 测试用例生成完成，共生成 3 个测试用例
2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo |    需求覆盖率: 100%
2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo |    场景覆盖率: 85%
2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo | 
============================================================
2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo | ✅ 第四步：测试用例评审
2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo | ============================================================
2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo | 📝 正在评审 3 个测试用例...
2025-07-29 15:49:12 | DEBUG    | SimpleWorkflowDemo | 测试用例评审结果: {
  "session_id": "demo_20250729_154904",
  "reviewed_testcases": [
    {
      "id": "TC_001_01",
      "requirement_id": "REQ_001",
      "title": "测试用户登录功能需求：...场景1",
      "type": "正向测试",
      "priority": "高",
      "steps": [
        "步骤1: 准备测试数据",
        "步骤2: 执行用户登录功能需求：操作",
        "步骤3: 验证结果"
      ],
      "expected_result": "期望结果：用户登录功能需求：成功",
      "status": "待执行",
      "review_score": 88,
      "review_status": "通过",
      "review_comments": "测试用例设计合理"
    },
    {
      "id": "TC_001_02",
      "requirement_id": "REQ_001",
      "title": "测试用户登录功能需求：...场景2",
      "type": "负向测试",
      "priority": "高",
      "steps": [
        "步骤1: 准备测试数据",
        "步骤2: 执行用户登录功能需求：操作",
        "步骤3: 验证结果"
      ],
      "expected_result": "期望结果：用户登录功能需求：成功",
      "status": "待执行",
      "review_score": 85,
      "review_status": "通过",
      "review_comments": "测试用例设计合理"
    },
    {
      "id": "TC_001_03",
      "requirement_id": "REQ_001",
      "title": "测试用户登录功能需求：...场景3",
      "type": "边界测试",
      "priority": "高",
      "steps": [
        "步骤1: 准备测试数据",
        "步骤2: 执行用户登录功能需求：操作",
        "步骤3: 验证结果"
      ],
      "expected_result": "期望结果：用户登录功能需求：成功",
      "status": "待执行",
      "review_score": 85,
      "review_status": "通过",
      "review_comments": "测试用例设计合理"
    }
  ],
  "overall_score": 86.0,
  "total_issues": 0,
  "issues": [],
  "final_status": "通过评审",
  "review_time": "2025-07-29T15:49:12.917222"
}
2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo | ✅ 测试用例评审完成，总体评分: 86.0
2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo |    最终状态: 通过评审
2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo | 
================================================================================
2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo | 📊 完整工作流程总结
2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo | ================================================================================
2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo | 🎯 会话ID: demo_20250729_154904
2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo | 📝 原始需求: 
用户登录功能需求：
1. 用户可以通过用户名和密码登录系统
2. 登录成功后跳转到主页面
3. 登录失败时显示错误提示
4. 支持记住密码功能
5. 连续登录失败3次后锁定账户30分钟
...
2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo | 🔍 需求分析: 提取 1 个需求
2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo | 📋 需求评审: 总体评分 85.0，状态 通过
2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo | 🧪 用例生成: 生成 3 个测试用例
2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo | ✅ 用例评审: 总体评分 86.0，状态 通过评审
2025-07-29 15:49:12 | WARNING  | SimpleWorkflowDemo | 创建可追溯性链接失败: TraceabilityMatrix.create_artifact() got an unexpected keyword argument 'artifact_id'，但不影响演示
2025-07-29 15:49:12 | INFO     | SimpleWorkflowDemo | 🎉 完整工作流程演示成功完成！
2025-07-29 15:49:45 | INFO     | SimpleWorkflowDemo | 🔧 初始化系统组件...
2025-07-29 15:49:54 | INFO     | VectorMemory | 已连接到现有集合: agent_memory
2025-07-29 15:49:54 | INFO     | VectorMemory | ChromaDB向量记忆系统初始化完成
2025-07-29 15:49:54 | INFO     | TraceabilityMatrix | 加载了 2 个工件和 0 个链接
2025-07-29 15:49:54 | INFO     | TraceabilityMatrix | 可追溯性矩阵初始化完成
2025-07-29 15:49:54 | INFO     | OllamaClient | 模型 deepseek-r1:1.5b 可用
2025-07-29 15:49:54 | INFO     | OllamaClient | Ollama客户端初始化成功，模型: deepseek-r1:1.5b
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo | ✅ 系统组件初始化完成
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo | 
============================================================
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo | 🔍 第一步：需求分析
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo | ============================================================
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo | 输入需求：
用户注册功能：1. 用户填写用户名、邮箱、密码进行注册 2. 系统验证邮箱格式和密码强度 3. 发送验证邮件到用户邮箱 4. 用户点击邮件链接完成注册
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo | 📝 正在分析需求...
2025-07-29 15:49:54 | DEBUG    | SimpleWorkflowDemo | 需求分析结果: {
  "session_id": "demo_20250729_154945",
  "original_text": "用户注册功能：1. 用户填写用户名、邮箱、密码进行注册 2. 系统验证邮箱格式和密码强度 3. 发送验证邮件到用户邮箱 4. 用户点击邮件链接完成注册",
  "requirements": [
    {
      "id": "REQ_001",
      "description": "用户注册功能：1. 用户填写用户名、邮箱、密码进行注册 2. 系统验证邮箱格式和密码强度 3. 发送验证邮件到用户邮箱 4. 用户点击邮件链接完成注册",
      "priority": "高",
      "type": "功能性需求",
      "status": "待评审"
    }
  ],
  "total_count": 1,
  "analysis_time": "2025-07-29T15:49:54.897563"
}
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo | ✅ 需求分析完成，提取到 1 个需求
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo |    - REQ_001: 用户注册功能：1. 用户填写用户名、邮箱、密码进行注册 2. 系统验证邮箱格式和密码强度 3. 发送...
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo | 
============================================================
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo | 📋 第二步：需求评审
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo | ============================================================
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo | 📝 正在评审 1 个需求...
2025-07-29 15:49:54 | DEBUG    | SimpleWorkflowDemo | 需求评审结果: {
  "session_id": "demo_20250729_154945",
  "reviewed_requirements": [
    {
      "id": "REQ_001",
      "description": "用户注册功能：1. 用户填写用户名、邮箱、密码进行注册 2. 系统验证邮箱格式和密码强度 3. 发送验证邮件到用户邮箱 4. 用户点击邮件链接完成注册",
      "priority": "高",
      "type": "功能性需求",
      "status": "待评审",
      "review_score": 90,
      "review_status": "通过",
      "review_comments": "需求描述清晰"
    }
  ],
  "overall_score": 90.0,
  "total_issues": 0,
  "issues": [],
  "overall_status": "通过",
  "review_time": "2025-07-29T15:49:54.899444"
}
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo | ✅ 需求评审完成，总体评分: 90.0
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo |    评审状态: 通过
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo | 
============================================================
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo | 🧪 第三步：测试用例生成
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo | ============================================================
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo | 📝 正在为 1 个需求生成测试用例...
2025-07-29 15:49:54 | DEBUG    | SimpleWorkflowDemo | 测试用例生成结果: {
  "session_id": "demo_20250729_154945",
  "testcases": [
    {
      "id": "TC_001_01",
      "requirement_id": "REQ_001",
      "title": "测试用户注册功能：1. 用户填写用户名、邮箱...场景1",
      "type": "正向测试",
      "priority": "高",
      "steps": [
        "步骤1: 准备测试数据",
        "步骤2: 执行用户注册功能：1. 操作",
        "步骤3: 验证结果"
      ],
      "expected_result": "期望结果：用户注册功能：1. 用户填写用户名、邮箱成功",
      "status": "待执行"
    },
    {
      "id": "TC_001_02",
      "requirement_id": "REQ_001",
      "title": "测试用户注册功能：1. 用户填写用户名、邮箱...场景2",
      "type": "负向测试",
      "priority": "高",
      "steps": [
        "步骤1: 准备测试数据",
        "步骤2: 执行用户注册功能：1. 操作",
        "步骤3: 验证结果"
      ],
      "expected_result": "期望结果：用户注册功能：1. 用户填写用户名、邮箱成功",
      "status": "待执行"
    },
    {
      "id": "TC_001_03",
      "requirement_id": "REQ_001",
      "title": "测试用户注册功能：1. 用户填写用户名、邮箱...场景3",
      "type": "边界测试",
      "priority": "高",
      "steps": [
        "步骤1: 准备测试数据",
        "步骤2: 执行用户注册功能：1. 操作",
        "步骤3: 验证结果"
      ],
      "expected_result": "期望结果：用户注册功能：1. 用户填写用户名、邮箱成功",
      "status": "待执行"
    }
  ],
  "total_count": 3,
  "coverage": {
    "requirement_coverage": "100%",
    "scenario_coverage": "85%"
  },
  "generation_time": "2025-07-29T15:49:54.902962"
}
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo | ✅ 测试用例生成完成，共生成 3 个测试用例
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo |    需求覆盖率: 100%
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo |    场景覆盖率: 85%
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo | 
============================================================
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo | ✅ 第四步：测试用例评审
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo | ============================================================
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo | 📝 正在评审 3 个测试用例...
2025-07-29 15:49:54 | DEBUG    | SimpleWorkflowDemo | 测试用例评审结果: {
  "session_id": "demo_20250729_154945",
  "reviewed_testcases": [
    {
      "id": "TC_001_01",
      "requirement_id": "REQ_001",
      "title": "测试用户注册功能：1. 用户填写用户名、邮箱...场景1",
      "type": "正向测试",
      "priority": "高",
      "steps": [
        "步骤1: 准备测试数据",
        "步骤2: 执行用户注册功能：1. 操作",
        "步骤3: 验证结果"
      ],
      "expected_result": "期望结果：用户注册功能：1. 用户填写用户名、邮箱成功",
      "status": "待执行",
      "review_score": 88,
      "review_status": "通过",
      "review_comments": "测试用例设计合理"
    },
    {
      "id": "TC_001_02",
      "requirement_id": "REQ_001",
      "title": "测试用户注册功能：1. 用户填写用户名、邮箱...场景2",
      "type": "负向测试",
      "priority": "高",
      "steps": [
        "步骤1: 准备测试数据",
        "步骤2: 执行用户注册功能：1. 操作",
        "步骤3: 验证结果"
      ],
      "expected_result": "期望结果：用户注册功能：1. 用户填写用户名、邮箱成功",
      "status": "待执行",
      "review_score": 85,
      "review_status": "通过",
      "review_comments": "测试用例设计合理"
    },
    {
      "id": "TC_001_03",
      "requirement_id": "REQ_001",
      "title": "测试用户注册功能：1. 用户填写用户名、邮箱...场景3",
      "type": "边界测试",
      "priority": "高",
      "steps": [
        "步骤1: 准备测试数据",
        "步骤2: 执行用户注册功能：1. 操作",
        "步骤3: 验证结果"
      ],
      "expected_result": "期望结果：用户注册功能：1. 用户填写用户名、邮箱成功",
      "status": "待执行",
      "review_score": 85,
      "review_status": "通过",
      "review_comments": "测试用例设计合理"
    }
  ],
  "overall_score": 86.0,
  "total_issues": 0,
  "issues": [],
  "final_status": "通过评审",
  "review_time": "2025-07-29T15:49:54.906862"
}
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo | ✅ 测试用例评审完成，总体评分: 86.0
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo |    最终状态: 通过评审
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo | 
================================================================================
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo | 📊 完整工作流程总结
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo | ================================================================================
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo | 🎯 会话ID: demo_20250729_154945
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo | 📝 原始需求: 用户注册功能：1. 用户填写用户名、邮箱、密码进行注册 2. 系统验证邮箱格式和密码强度 3. 发送验证邮件到用户邮箱 4. 用户点击邮件链接完成注册...
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo | 🔍 需求分析: 提取 1 个需求
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo | 📋 需求评审: 总体评分 90.0，状态 通过
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo | 🧪 用例生成: 生成 3 个测试用例
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo | ✅ 用例评审: 总体评分 86.0，状态 通过评审
2025-07-29 15:49:54 | WARNING  | SimpleWorkflowDemo | 创建可追溯性链接失败: TraceabilityMatrix.create_artifact() got an unexpected keyword argument 'artifact_id'，但不影响演示
2025-07-29 15:49:54 | INFO     | SimpleWorkflowDemo | 🎉 完整工作流程演示成功完成！
2025-07-29 15:50:31 | ERROR    | OllamaClient | 生成响应失败: 
2025-07-29 15:50:31 | ERROR    | OllamaClient | 生成思维链失败: 
2025-07-29 15:52:26 | INFO     | OllamaClient | 生成完成，耗时: 115.62秒
2025-07-29 15:52:26 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_90678414 | 思维链生成完成，共0步，响应长度: 3086
2025-07-29 15:52:26 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_90678414 | 规则提取需求失败: AttributeError: UNKNOWN
2025-07-29 15:52:26 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_90678414 | 结构化分析结果失败: AttributeError: UNKNOWN
2025-07-29 15:52:26 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_90678414 | 迭代处理失败: AttributeError: UNKNOWN
2025-07-29 15:52:26 | ERROR    | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_90678414 | 第3轮迭代失败: UNKNOWN
2025-07-29 15:52:26 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_90678414 | 状态更新: AgentStatus.PROCESSING, 任务: 迭代处理 4/10, 进度: 0.40
2025-07-29 15:52:26 | INFO     | RequirementAnalysis_AgentType.REQUIREMENT_ANALYSIS_90678414 | 状态更新: AgentStatus.THINKING, 任务: 生成思维链, 进度: 0.40
2025-07-29 15:53:13 | INFO     | OllamaClient | 生成完成，耗时: 46.49秒
2025-07-29 15:53:13 | INFO     | OllamaClient | 生成思维链，共0个步骤
2025-07-29 15:54:17 | INFO     | WorkflowDemo | 演示被用户中断
2025-07-29 16:17:00 | INFO     | SimpleWorkflowDemo | 🔧 初始化系统组件...
2025-07-29 16:17:09 | INFO     | VectorMemory | 已连接到现有集合: agent_memory
2025-07-29 16:17:09 | INFO     | VectorMemory | ChromaDB向量记忆系统初始化完成
2025-07-29 16:17:09 | INFO     | TraceabilityMatrix | 加载了 2 个工件和 0 个链接
2025-07-29 16:17:09 | INFO     | TraceabilityMatrix | 可追溯性矩阵初始化完成
2025-07-29 16:17:09 | INFO     | OllamaClient | 模型 deepseek-r1:1.5b 可用
2025-07-29 16:17:09 | INFO     | OllamaClient | Ollama客户端初始化成功，模型: deepseek-r1:1.5b
2025-07-29 16:17:09 | INFO     | SimpleWorkflowDemo | ✅ 系统组件初始化完成
2025-07-29 16:17:09 | INFO     | SimpleWorkflowDemo | 
============================================================
2025-07-29 16:17:09 | INFO     | SimpleWorkflowDemo | 🔍 第一步：需求分析
2025-07-29 16:17:09 | INFO     | SimpleWorkflowDemo | ============================================================
2025-07-29 16:17:09 | INFO     | SimpleWorkflowDemo | 输入需求：
用户登录功能:填写用户名\密码,点击登录
2025-07-29 16:17:09 | INFO     | SimpleWorkflowDemo | 📝 正在分析需求...
2025-07-29 16:17:09 | DEBUG    | SimpleWorkflowDemo | 需求分析结果: {
  "session_id": "demo_20250729_161700",
  "original_text": "用户登录功能:填写用户名\\密码,点击登录",
  "requirements": [],
  "total_count": 0,
  "analysis_time": "2025-07-29T16:17:09.803794"
}
2025-07-29 16:17:09 | INFO     | SimpleWorkflowDemo | ✅ 需求分析完成，提取到 0 个需求
2025-07-29 16:17:09 | INFO     | SimpleWorkflowDemo | 
============================================================
2025-07-29 16:17:09 | INFO     | SimpleWorkflowDemo | 📋 第二步：需求评审
2025-07-29 16:17:09 | INFO     | SimpleWorkflowDemo | ============================================================
2025-07-29 16:17:09 | INFO     | SimpleWorkflowDemo | 📝 正在评审 0 个需求...
2025-07-29 16:17:09 | ERROR    | SimpleWorkflowDemo | 演示过程中发生异常: ZeroDivisionError: division by zero
