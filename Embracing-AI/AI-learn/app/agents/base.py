"""
基础Agent抽象类
"""

import asyncio
import uuid
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import json

from config import get_settings
from core.logger import get_agent_logger
from core.memory import get_vector_memory
from services.ollama_client import get_ollama_client
from models.schemas import (
    AgentState, AgentMessage, ThinkingChain, ReflectionResult,
    BaseSchema
)
from models.enums import (
    AgentType, AgentStatus, MessageType
)

settings = get_settings()


class BaseAgent(ABC):
    """基础Agent抽象类"""
    
    def __init__(self, agent_type: AgentType, session_id: str):
        self.agent_type = agent_type
        self.session_id = session_id
        self.agent_id = f"{agent_type}_{uuid.uuid4().hex[:8]}"
        
        # 初始化状态
        self.state = AgentState(
            agent_type=agent_type,
            status=AgentStatus.IDLE,
            progress=0.0
        )
        
        # 初始化日志器
        self.logger = get_agent_logger(f"{agent_type}_{self.agent_id}")
        
        # 组件引用
        self.memory = None
        self.ollama_client = None
        
        # 配置参数
        self.max_iterations = settings.max_iterations
        self.reflection_threshold = settings.reflection_threshold
        
        self.logger.info(f"Agent初始化完成: {self.agent_id}")
    
    async def initialize(self):
        """初始化Agent组件"""
        try:
            # 初始化向量记忆
            self.memory = await get_vector_memory()
            
            # 初始化Ollama客户端
            self.ollama_client = await get_ollama_client()
            
            self.logger.info(f"Agent组件初始化完成: {self.agent_id}")
            
        except Exception as e:
            self.logger.error(f"Agent组件初始化失败: {str(e)}")
            raise
    
    def _ensure_initialized(self):
        """确保Agent已初始化"""
        if not self.memory or not self.ollama_client:
            raise RuntimeError(f"Agent {self.agent_id} 未初始化，请先调用initialize()")
    
    async def update_status(self, status: AgentStatus, task: Optional[str] = None, progress: float = None):
        """更新Agent状态"""
        self.state.status = status
        if task:
            self.state.current_task = task
        if progress is not None:
            self.state.progress = max(0.0, min(1.0, progress))
        self.state.last_activity = datetime.now()
        
        self.logger.info(f"状态更新: {status}, 任务: {task}, 进度: {self.state.progress:.2f}")
    
    async def add_thinking_step(self, content: str, reasoning: str, confidence: float = 0.8):
        """添加思维链步骤"""
        step = len(self.state.thinking_chain) + 1
        thinking_step = ThinkingChain(
            step=step,
            content=content,
            reasoning=reasoning,
            confidence=confidence
        )
        self.state.thinking_chain.append(thinking_step)
        
        self.logger.info(f"思维链步骤{step}: {content[:50]}... (置信度: {confidence})")
    
    async def add_reflection(self, question: str, answer: str, improvement: Optional[str] = None, score: float = 0.8):
        """添加反思结果"""
        reflection = ReflectionResult(
            question=question,
            answer=answer,
            improvement=improvement,
            score=score
        )
        self.state.reflection_results.append(reflection)
        
        self.logger.info(f"反思添加: {question[:30]}... (评分: {score})")
    
    async def generate_with_thinking(
        self, 
        task_description: str,
        context: Optional[str] = None,
        system_prompt: Optional[str] = None,
        max_steps: int = 5
    ) -> tuple[str, List[ThinkingChain]]:
        """使用思维链生成响应"""
        self._ensure_initialized()
        
        try:
            await self.update_status(AgentStatus.THINKING, "生成思维链")
            
            # 生成思维链
            thinking_chain = await self.ollama_client.generate_thinking_chain(
                task_description=task_description,
                context=context,
                max_steps=max_steps
            )
            
            # 保存思维链到状态
            self.state.thinking_chain.extend(thinking_chain)
            
            # 基于思维链生成最终响应
            thinking_context = "\n".join([
                f"步骤{chain.step}: {chain.content}\n推理: {chain.reasoning}"
                for chain in thinking_chain
            ])
            
            final_prompt = f"""
基于以下思维链分析，生成最终的结构化响应：

任务: {task_description}
{f"上下文: {context}" if context else ""}

思维链分析:
{thinking_context}

请基于上述分析生成最终的详细响应。
"""
            
            response = await self.ollama_client.generate_response(
                prompt=final_prompt,
                system_prompt=system_prompt,
                temperature=0.3
            )
            
            self.logger.info(f"思维链生成完成，共{len(thinking_chain)}步，响应长度: {len(response)}")
            return response, thinking_chain
            
        except Exception as e:
            self.logger.error(f"思维链生成失败: {str(e)}")
            raise
    
    async def reflect_on_result(
        self, 
        task_description: str,
        result: str,
        thinking_chain: Optional[List[ThinkingChain]] = None
    ) -> ReflectionResult:
        """对结果进行反思"""
        self._ensure_initialized()
        
        try:
            await self.update_status(AgentStatus.REFLECTING, "反思结果质量")
            
            reflection = await self.ollama_client.generate_reflection(
                task_description=task_description,
                current_result=result,
                thinking_chain=thinking_chain
            )
            
            # 保存反思结果到状态
            self.state.reflection_results.append(reflection)
            
            self.logger.info(f"反思完成，评分: {reflection.score}")
            return reflection
            
        except Exception as e:
            self.logger.error(f"反思失败: {str(e)}")
            # 返回默认反思结果
            return ReflectionResult(
                question="反思过程出错",
                answer=f"反思失败: {str(e)}",
                score=0.0
            )
    
    async def send_message(
        self, 
        receiver: Optional[AgentType],
        message_type: MessageType,
        content: Dict[str, Any],
        correlation_id: Optional[str] = None
    ) -> AgentMessage:
        """发送消息"""
        message = AgentMessage(
            id=f"msg_{uuid.uuid4().hex[:8]}",
            sender=self.agent_type,
            receiver=receiver,
            message_type=message_type,
            content=content,
            correlation_id=correlation_id
        )
        
        # 存储消息到记忆系统
        if self.memory:
            await self.memory.store_agent_message(message, self.session_id)
        
        self.logger.info(f"发送消息: {self.agent_type} -> {receiver or '广播'}, 类型: {message_type}")
        return message
    
    async def retrieve_context(
        self, 
        query: str, 
        context_type: Optional[str] = None,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """从记忆中检索相关上下文"""
        self._ensure_initialized()
        
        try:
            if context_type == "requirement":
                results = await self.memory.search_similar_requirements(
                    query=query,
                    session_id=self.session_id,
                    limit=limit
                )
            elif context_type == "testcase":
                results = await self.memory.search_similar_testcases(
                    query=query,
                    session_id=self.session_id,
                    limit=limit
                )
            else:
                # 获取会话历史
                results = await self.memory.get_session_history(
                    session_id=self.session_id,
                    limit=limit
                )
            
            self.logger.info(f"检索到 {len(results)} 条相关上下文")
            return results
            
        except Exception as e:
            self.logger.error(f"上下文检索失败: {str(e)}")
            return []
    
    async def iterative_process(
        self,
        task_description: str,
        initial_input: Any,
        max_iterations: Optional[int] = None
    ) -> Any:
        """迭代处理流程"""
        max_iter = max_iterations or self.max_iterations
        iteration = 0
        current_result = initial_input
        
        while iteration < max_iter:
            iteration += 1
            await self.update_status(
                AgentStatus.PROCESSING, 
                f"迭代处理 {iteration}/{max_iter}",
                iteration / max_iter
            )
            
            try:
                # 执行具体处理逻辑
                new_result = await self.process_iteration(
                    task_description=task_description,
                    current_input=current_result,
                    iteration=iteration
                )
                
                # 反思结果质量
                reflection = await self.reflect_on_result(
                    task_description=f"{task_description} (第{iteration}轮)",
                    result=str(new_result)
                )
                
                # 检查是否需要继续迭代
                if reflection.score >= self.reflection_threshold:
                    self.logger.info(f"迭代完成，质量达标 (评分: {reflection.score})")
                    await self.update_status(AgentStatus.COMPLETED, "处理完成", 1.0)
                    return new_result
                
                # 如果有改进建议，应用改进
                if reflection.improvement:
                    self.logger.info(f"应用改进建议: {reflection.improvement[:50]}...")
                    current_result = await self.apply_improvement(
                        current_result=new_result,
                        improvement=reflection.improvement
                    )
                else:
                    current_result = new_result
                
            except Exception as e:
                self.logger.error(f"第{iteration}轮迭代失败: {str(e)}")
                if iteration == max_iter:
                    raise
                continue
        
        # 达到最大迭代次数
        self.logger.warning(f"达到最大迭代次数 {max_iter}，返回当前结果")
        await self.update_status(AgentStatus.COMPLETED, "达到最大迭代次数", 1.0)
        return current_result
    
    @abstractmethod
    async def process_iteration(
        self,
        task_description: str,
        current_input: Any,
        iteration: int
    ) -> Any:
        """处理单次迭代（子类实现）"""
        pass
    
    async def apply_improvement(
        self,
        current_result: Any,
        improvement: str
    ) -> Any:
        """应用改进建议（子类可重写）"""
        # 默认实现：返回原结果
        return current_result
    
    @abstractmethod
    async def process(self, input_data: Any) -> Any:
        """主要处理方法（子类实现）"""
        pass
    
    @abstractmethod
    def get_system_prompt(self) -> str:
        """获取系统提示词（子类实现）"""
        pass
    
    async def get_state(self) -> AgentState:
        """获取Agent状态"""
        return self.state
    
    async def reset_state(self):
        """重置Agent状态"""
        self.state = AgentState(
            agent_type=self.agent_type,
            status=AgentStatus.IDLE,
            progress=0.0
        )
        self.logger.info(f"Agent状态已重置: {self.agent_id}")
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self.ollama_client:
                await self.ollama_client.close()
            
            self.logger.info(f"Agent资源清理完成: {self.agent_id}")
            
        except Exception as e:
            self.logger.error(f"Agent资源清理失败: {str(e)}")
    
    def __str__(self) -> str:
        return f"{self.agent_type}({self.agent_id})"
    
    def __repr__(self) -> str:
        return f"<{self.__class__.__name__} {self.agent_id} status={self.state.status}>"