"""
需求分析Agent实现
"""

import asyncio
import uuid
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import json
import re
from pathlib import Path

from docx import Document
from docx.shared import Inches

from agents.base import BaseAgent
from models.schemas import (
    RequirementModel, AcceptanceCriteria, ThinkingChain, ReflectionResult
)
from models.enums import (
    AgentType, RequirementType, RequirementStatus, RiskLevel, AgentStatus
)
from core.logger import get_agent_logger
from core.traceability import get_traceability_manager


class RequirementAnalysisAgent(BaseAgent):
    """需求分析Agent"""
    
    def __init__(self, session_id: str):
        super().__init__(AgentType.REQUIREMENT_ANALYSIS, session_id)
        self.logger = get_agent_logger(f"RequirementAnalysis_{self.agent_id}")
        self.traceability_manager = None
        
        # 需求分析配置
        self.requirement_patterns = {
            "功能性": [
                r"用户.*能够.*",
                r"系统.*应该.*",
                r"当.*时.*",
                r".*功能.*",
                r".*操作.*",
                r".*处理.*"
            ],
            "性能": [
                r"响应时间.*",
                r"并发.*用户.*",
                r"吞吐量.*",
                r".*性能.*",
                r".*速度.*",
                r".*延迟.*"
            ],
            "安全": [
                r"权限.*",
                r"认证.*",
                r"授权.*",
                r"加密.*",
                r"安全.*",
                r"防护.*"
            ],
            "可用性": [
                r"可用性.*",
                r"稳定性.*",
                r"容错.*",
                r"恢复.*",
                r"备份.*"
            ]
        }
    
    async def initialize(self):
        """初始化Agent"""
        await super().initialize()
        self.traceability_manager = await get_traceability_manager()
        self.logger.info("需求分析Agent初始化完成")
    
    def get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """
你是一个专业的需求分析专家，具备以下能力：
1. 深入理解业务需求和技术约束
2. 识别功能性和非功能性需求
3. 提取验收标准和测试场景
4. 评估需求风险和优先级
5. 生成结构化的需求文档

分析原则：
- 使用思维链方式逐步分析
- 识别需求的完整性和一致性
- 提取可测试的验收标准
- 标记不明确或需要澄清的点
- 为每个需求分配唯一的追溯ID

输出格式必须严格遵循JSON结构，包含完整的需求信息、验收标准和待处理场景。
"""
    
    async def process(self, input_data: Any) -> Dict[str, Any]:
        """处理需求分析任务"""
        self._ensure_initialized()
        
        try:
            await self.update_status(AgentStatus.PROCESSING, "开始需求分析")
            
            # 解析输入数据
            if isinstance(input_data, dict):
                requirement_text = input_data.get("requirement_text", "")
                document_path = input_data.get("document_path")
                context = input_data.get("context", {})
            else:
                requirement_text = str(input_data)
                document_path = None
                context = {}
            
            # 如果有Word文档，先解析文档
            if document_path:
                document_content = await self._parse_word_document(document_path)
                requirement_text = f"{requirement_text}\n\n{document_content}".strip()
            
            if not requirement_text:
                raise ValueError("需求文本不能为空")
            
            # 使用迭代处理进行需求分析
            result = await self.iterative_process(
                task_description="需求分析和结构化处理",
                initial_input={
                    "requirement_text": requirement_text,
                    "context": context
                }
            )
            
            await self.update_status(AgentStatus.COMPLETED, "需求分析完成", 1.0)
            return result
            
        except Exception as e:
            await self.update_status(AgentStatus.ERROR, f"需求分析失败: {str(e)}")
            self.logger.error(f"需求分析处理失败: {str(e)}")
            raise
    
    async def process_iteration(
        self,
        task_description: str,
        current_input: Any,
        iteration: int
    ) -> Dict[str, Any]:
        """处理单次迭代"""
        try:
            requirement_text = current_input["requirement_text"]
            context = current_input.get("context", {})
            
            # 第一步：使用思维链分析需求
            analysis_prompt = f"""
请对以下需求进行深入分析：

需求文本：
{requirement_text}

上下文信息：
{json.dumps(context, ensure_ascii=False, indent=2)}

分析任务：
1. 识别所有功能性需求和非功能性需求
2. 为每个需求提取验收标准（正向、负向、边界、异常）
3. 识别待处理的测试场景
4. 评估需求的风险级别和优先级
5. 标记需要澄清的模糊点

请使用思维链方式逐步分析，最后生成结构化的JSON输出。
"""
            
            # 生成思维链分析
            analysis_result, thinking_chain = await self.generate_with_thinking(
                task_description=analysis_prompt,
                system_prompt=self.get_system_prompt(),
                max_steps=6
            )
            
            # 第二步：结构化处理分析结果
            structured_result = await self._structure_analysis_result(
                analysis_result, requirement_text, context
            )
            
            # 第三步：添加到追溯矩阵
            await self._add_requirements_to_traceability(structured_result)
            
            return structured_result
            
        except Exception as e:
            self.logger.error(f"迭代处理失败: {str(e)}")
            raise

    async def analyze_requirements(self, requirement_text: str) -> Dict[str, Any]:
        """分析需求文本（兼容性方法）"""
        return await self.process({"requirement_text": requirement_text})

    async def _parse_word_document(self, document_path: str) -> str:
        """解析Word文档"""
        try:
            self.logger.info(f"开始解析Word文档: {document_path}")
            
            if not Path(document_path).exists():
                raise FileNotFoundError(f"文档文件不存在: {document_path}")
            
            doc = Document(document_path)
            content_parts = []
            
            # 提取段落内容
            for paragraph in doc.paragraphs:
                text = paragraph.text.strip()
                if text:
                    # 识别标题级别
                    if paragraph.style.name.startswith('Heading'):
                        level = paragraph.style.name.replace('Heading ', '')
                        content_parts.append(f"\n{'#' * int(level)} {text}\n")
                    else:
                        content_parts.append(text)
            
            # 提取表格内容
            for table in doc.tables:
                table_content = []
                for row in table.rows:
                    row_content = []
                    for cell in row.cells:
                        cell_text = cell.text.strip()
                        if cell_text:
                            row_content.append(cell_text)
                    if row_content:
                        table_content.append(" | ".join(row_content))
                
                if table_content:
                    content_parts.append("\n表格内容：")
                    content_parts.extend(table_content)
                    content_parts.append("")
            
            document_content = "\n".join(content_parts)
            self.logger.info(f"Word文档解析完成，提取内容长度: {len(document_content)} 字符")
            
            return document_content
            
        except Exception as e:
            self.logger.error(f"Word文档解析失败: {str(e)}")
            raise
    
    async def _structure_analysis_result(
        self,
        analysis_result: str,
        original_text: str,
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """结构化分析结果"""
        try:
            # 尝试从分析结果中提取JSON
            json_match = re.search(r'\{.*\}', analysis_result, re.DOTALL)
            if json_match:
                try:
                    parsed_json = json.loads(json_match.group())
                    if "requirements" in parsed_json:
                        return await self._validate_and_enhance_requirements(parsed_json)
                except json.JSONDecodeError:
                    pass
            
            # 如果无法解析JSON，使用规则提取
            return await self._extract_requirements_by_rules(analysis_result, original_text, context)
            
        except Exception as e:
            self.logger.error(f"结构化分析结果失败: {str(e)}")
            raise
    
    async def _extract_requirements_by_rules(
        self,
        analysis_text: str,
        original_text: str,
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """使用规则提取需求"""
        try:
            requirements = []
            requirement_id = 1
            
            # 分析文本，识别需求类型
            sentences = re.split(r'[。！？\n]', original_text)
            
            for sentence in sentences:
                sentence = sentence.strip()
                if len(sentence) < 10:  # 过滤太短的句子
                    continue
                
                # 识别需求类型
                req_type = self._classify_requirement_type(sentence)
                if req_type == RequirementType.UNKNOWN:
                    continue
                
                # 生成需求对象
                requirement = RequirementModel(
                    id=requirement_id,
                    trace_id=f"R{requirement_id}-{datetime.now().strftime('%Y%m%d')}-{uuid.uuid4().hex[:4]}",
                    name=self._extract_requirement_name(sentence),
                    type=req_type,
                    description=sentence,
                    acceptance_criteria=self._generate_acceptance_criteria(sentence, req_type),
                    pending_scenarios=self._extract_test_scenarios(sentence, req_type),
                    status=RequirementStatus.DRAFT,
                    risk_level=self._assess_risk_level(sentence, req_type),
                    priority="medium",
                    pending_clarifications=[]
                )
                
                requirements.append(requirement)
                requirement_id += 1
            
            # 如果没有识别到需求，创建一个通用需求
            if not requirements:
                requirement = RequirementModel(
                    id=1,
                    trace_id=f"R1-{datetime.now().strftime('%Y%m%d')}-{uuid.uuid4().hex[:4]}",
                    name="通用功能需求",
                    type=RequirementType.FUNCTIONAL,
                    description=original_text[:200] + "..." if len(original_text) > 200 else original_text,
                    acceptance_criteria=AcceptanceCriteria(
                        positive=["系统能够正常处理用户请求"],
                        negative=["系统不应该处理无效请求"],
                        boundary=["处理边界条件"],
                        error=["正确处理错误情况"]
                    ),
                    pending_scenarios=["正常流程测试", "异常流程测试", "边界值测试"],
                    status=RequirementStatus.CLARIFY,
                    risk_level=RiskLevel.MEDIUM,
                    priority="medium",
                    pending_clarifications=["需要进一步明确具体功能需求"]
                )
                requirements.append(requirement)
            
            result = {
                "requirements": [req.dict() for req in requirements],
                "version": 1,
                "analysis_summary": {
                    "total_requirements": len(requirements),
                    "functional_count": sum(1 for req in requirements if req.type == RequirementType.FUNCTIONAL),
                    "non_functional_count": sum(1 for req in requirements if req.type != RequirementType.FUNCTIONAL),
                    "high_risk_count": sum(1 for req in requirements if req.risk_level == RiskLevel.HIGH),
                    "clarification_needed": sum(1 for req in requirements if req.status == RequirementStatus.CLARIFY)
                },
                "extracted_at": datetime.now().isoformat()
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"规则提取需求失败: {str(e)}")
            raise
    
    def _classify_requirement_type(self, text: str) -> RequirementType:
        """分类需求类型"""
        text_lower = text.lower()
        
        # 检查各种需求类型的模式
        for req_type, patterns in self.requirement_patterns.items():
            for pattern in patterns:
                if re.search(pattern, text_lower):
                    if req_type == "功能性":
                        return RequirementType.FUNCTIONAL
                    elif req_type == "性能":
                        return RequirementType.PERFORMANCE
                    elif req_type == "安全":
                        return RequirementType.SECURITY
                    elif req_type == "可用性":
                        return RequirementType.USABILITY
        
        # 默认为功能性需求
        if any(keyword in text_lower for keyword in ["功能", "操作", "处理", "管理", "查询", "添加", "删除", "修改"]):
            return RequirementType.FUNCTIONAL
        
        return RequirementType.UNKNOWN
    
    def _extract_requirement_name(self, text: str) -> str:
        """提取需求名称"""
        # 简化处理：取前30个字符作为名称
        name = text.strip()[:30]
        if len(text) > 30:
            name += "..."
        return name
    
    def _generate_acceptance_criteria(self, text: str, req_type: RequirementType) -> AcceptanceCriteria:
        """生成验收标准"""
        if req_type == RequirementType.FUNCTIONAL:
            return AcceptanceCriteria(
                positive=[f"能够正确执行：{text[:50]}..."],
                negative=["不应该执行无效操作", "不应该返回错误结果"],
                boundary=["处理输入边界值", "处理最大最小值"],
                error=["正确处理异常情况", "显示适当的错误信息"]
            )
        elif req_type == RequirementType.PERFORMANCE:
            return AcceptanceCriteria(
                positive=["满足性能指标要求"],
                negative=["不应该超过性能阈值"],
                boundary=["在负载边界下正常工作"],
                error=["性能异常时正确处理"]
            )
        elif req_type == RequirementType.SECURITY:
            return AcceptanceCriteria(
                positive=["通过安全验证"],
                negative=["拒绝未授权访问"],
                boundary=["处理权限边界情况"],
                error=["安全异常时正确响应"]
            )
        else:
            return AcceptanceCriteria(
                positive=["满足基本要求"],
                negative=["不违反约束条件"],
                boundary=["处理边界情况"],
                error=["正确处理错误"]
            )
    
    def _extract_test_scenarios(self, text: str, req_type: RequirementType) -> List[str]:
        """提取测试场景"""
        base_scenarios = ["正常流程", "异常流程", "边界值测试"]
        
        if req_type == RequirementType.FUNCTIONAL:
            base_scenarios.extend(["输入验证", "业务规则验证", "数据完整性"])
        elif req_type == RequirementType.PERFORMANCE:
            base_scenarios.extend(["负载测试", "压力测试", "并发测试"])
        elif req_type == RequirementType.SECURITY:
            base_scenarios.extend(["权限验证", "数据加密", "安全攻击防护"])
        elif req_type == RequirementType.USABILITY:
            base_scenarios.extend(["用户体验", "界面友好性", "操作便捷性"])
        
        return base_scenarios
    
    def _assess_risk_level(self, text: str, req_type: RequirementType) -> RiskLevel:
        """评估风险级别"""
        text_lower = text.lower()
        
        # 高风险关键词
        high_risk_keywords = ["安全", "支付", "数据", "权限", "核心", "关键", "重要"]
        if any(keyword in text_lower for keyword in high_risk_keywords):
            return RiskLevel.HIGH
        
        # 中风险关键词
        medium_risk_keywords = ["性能", "并发", "集成", "接口", "复杂"]
        if any(keyword in text_lower for keyword in medium_risk_keywords):
            return RiskLevel.MEDIUM
        
        # 根据需求类型评估
        if req_type in [RequirementType.SECURITY, RequirementType.PERFORMANCE]:
            return RiskLevel.HIGH
        elif req_type == RequirementType.FUNCTIONAL:
            return RiskLevel.MEDIUM
        
        return RiskLevel.LOW
    
    async def _validate_and_enhance_requirements(self, parsed_json: Dict[str, Any]) -> Dict[str, Any]:
        """验证和增强需求数据"""
        try:
            requirements = parsed_json.get("requirements", [])
            enhanced_requirements = []
            
            for req_data in requirements:
                # 确保必要字段存在
                if not req_data.get("trace_id"):
                    req_data["trace_id"] = f"R{req_data.get('id', 1)}-{datetime.now().strftime('%Y%m%d')}-{uuid.uuid4().hex[:4]}"
                
                if not req_data.get("status"):
                    req_data["status"] = RequirementStatus.DRAFT
                
                if not req_data.get("risk_level"):
                    req_data["risk_level"] = RiskLevel.MEDIUM
                
                if not req_data.get("priority"):
                    req_data["priority"] = "medium"
                
                # 验证验收标准格式
                if "acceptance_criteria" not in req_data:
                    req_data["acceptance_criteria"] = {
                        "positive": ["待定义"],
                        "negative": ["待定义"],
                        "boundary": ["待定义"],
                        "error": ["待定义"]
                    }
                
                # 验证待处理场景
                if "pending_scenarios" not in req_data:
                    req_data["pending_scenarios"] = ["待定义场景"]
                
                enhanced_requirements.append(req_data)
            
            # 更新结果
            parsed_json["requirements"] = enhanced_requirements
            parsed_json["version"] = parsed_json.get("version", 1)
            
            # 添加分析摘要
            if "analysis_summary" not in parsed_json:
                parsed_json["analysis_summary"] = {
                    "total_requirements": len(enhanced_requirements),
                    "functional_count": sum(1 for req in enhanced_requirements if req.get("type") == "functional"),
                    "non_functional_count": sum(1 for req in enhanced_requirements if req.get("type") != "functional"),
                    "high_risk_count": sum(1 for req in enhanced_requirements if req.get("risk_level") == "high"),
                    "clarification_needed": sum(1 for req in enhanced_requirements if req.get("status") == "clarify")
                }
            
            parsed_json["extracted_at"] = datetime.now().isoformat()
            
            return parsed_json
            
        except Exception as e:
            self.logger.error(f"验证和增强需求数据失败: {str(e)}")
            raise
    
    async def _add_requirements_to_traceability(self, result: Dict[str, Any]):
        """添加需求到追溯矩阵"""
        try:
            requirements = result.get("requirements", [])
            
            for req_data in requirements:
                requirement = RequirementModel(**req_data)
                await self.traceability_manager.add_requirement(self.session_id, requirement)
            
            self.logger.info(f"已添加 {len(requirements)} 个需求到追溯矩阵")
            
        except Exception as e:
            self.logger.error(f"添加需求到追溯矩阵失败: {str(e)}")
            # 不抛出异常，避免影响主流程
    
    async def apply_improvement(
        self,
        current_result: Any,
        improvement: str
    ) -> Any:
        """应用改进建议"""
        try:
            # 基于改进建议调整结果
            if isinstance(current_result, dict) and "requirements" in current_result:
                requirements = current_result["requirements"]
                
                # 根据改进建议调整需求
                if "增加验收标准" in improvement:
                    for req in requirements:
                        if len(req.get("acceptance_criteria", {}).get("positive", [])) < 2:
                            req["acceptance_criteria"]["positive"].append("补充的验收标准")
                
                if "明确测试场景" in improvement:
                    for req in requirements:
                        if len(req.get("pending_scenarios", [])) < 3:
                            req["pending_scenarios"].append("补充的测试场景")
                
                if "提高风险评估" in improvement:
                    for req in requirements:
                        if req.get("risk_level") == "low":
                            req["risk_level"] = "medium"
                
                # 更新版本号
                current_result["version"] = current_result.get("version", 1) + 1
                current_result["improved_at"] = datetime.now().isoformat()
            
            return current_result
            
        except Exception as e:
            self.logger.error(f"应用改进建议失败: {str(e)}")
            return current_result
    
    async def export_requirements(self, requirements_data: Dict[str, Any], format: str = "json") -> str:
        """导出需求文档"""
        try:
            if format.lower() == "json":
                return json.dumps(requirements_data, ensure_ascii=False, indent=2)
            
            elif format.lower() == "markdown":
                lines = ["# 需求分析文档\n"]
                
                # 添加摘要
                summary = requirements_data.get("analysis_summary", {})
                lines.append("## 分析摘要")
                lines.append(f"- 总需求数: {summary.get('total_requirements', 0)}")
                lines.append(f"- 功能性需求: {summary.get('functional_count', 0)}")
                lines.append(f"- 非功能性需求: {summary.get('non_functional_count', 0)}")
                lines.append(f"- 高风险需求: {summary.get('high_risk_count', 0)}")
                lines.append(f"- 需要澄清: {summary.get('clarification_needed', 0)}")
                lines.append("")
                
                # 添加需求详情
                lines.append("## 需求详情\n")
                
                for req in requirements_data.get("requirements", []):
                    lines.append(f"### {req.get('name', '未命名需求')}")
                    lines.append(f"- **追溯ID**: {req.get('trace_id', 'N/A')}")
                    lines.append(f"- **类型**: {req.get('type', 'unknown')}")
                    lines.append(f"- **状态**: {req.get('status', 'draft')}")
                    lines.append(f"- **风险级别**: {req.get('risk_level', 'medium')}")
                    lines.append(f"- **优先级**: {req.get('priority', 'medium')}")
                    lines.append(f"- **描述**: {req.get('description', 'N/A')}")
                    
                    # 验收标准
                    criteria = req.get("acceptance_criteria", {})
                    lines.append("- **验收标准**:")
                    lines.append(f"  - 正向: {', '.join(criteria.get('positive', []))}")
                    lines.append(f"  - 负向: {', '.join(criteria.get('negative', []))}")
                    lines.append(f"  - 边界: {', '.join(criteria.get('boundary', []))}")
                    lines.append(f"  - 异常: {', '.join(criteria.get('error', []))}")
                    
                    # 测试场景
                    scenarios = req.get("pending_scenarios", [])
                    lines.append(f"- **测试场景**: {', '.join(scenarios)}")
                    
                    # 澄清问题
                    clarifications = req.get("pending_clarifications", [])
                    if clarifications:
                        lines.append(f"- **待澄清**: {', '.join(clarifications)}")
                    
                    lines.append("")
                
                return "\n".join(lines)
            
            else:
                raise ValueError(f"不支持的导出格式: {format}")
                
        except Exception as e:
            self.logger.error(f"导出需求文档失败: {str(e)}")
            raise