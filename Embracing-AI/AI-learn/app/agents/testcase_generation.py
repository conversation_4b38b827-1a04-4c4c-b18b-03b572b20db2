"""
测试用例生成Agent实现
"""

import asyncio
import uuid
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime
import json
import re

from agents.base import BaseAgent
from models.schemas import (
    TestCaseModel, RequirementModel, TestStep, ThinkingChain, ReflectionResult
)
from models.enums import (
    AgentType, TestCaseType, TestCaseStatus, TestPriority, AgentStatus
)
from core.logger import get_agent_logger
from core.traceability import get_traceability_manager


class TestCaseGenerationAgent(BaseAgent):
    """测试用例生成Agent"""
    
    def __init__(self, session_id: str):
        super().__init__(AgentType.TESTCASE_GENERATION, session_id)
        self.logger = get_agent_logger(f"TestCaseGeneration_{self.agent_id}")
        self.traceability_manager = None
        
        # 测试设计方法配置
        self.test_design_methods = {
            "等价类划分": {
                "description": "将输入域划分为若干等价类，从每个等价类中选取代表性测试数据",
                "applicable_scenarios": ["输入验证", "数据处理", "参数检查"],
                "generation_strategy": "equivalence_partitioning"
            },
            "边界值分析": {
                "description": "重点测试输入域边界上和边界附近的值",
                "applicable_scenarios": ["数值范围", "字符串长度", "数组大小"],
                "generation_strategy": "boundary_value_analysis"
            },
            "决策表测试": {
                "description": "基于业务规则和条件组合生成测试用例",
                "applicable_scenarios": ["复杂业务逻辑", "多条件判断", "规则引擎"],
                "generation_strategy": "decision_table"
            },
            "状态转换测试": {
                "description": "基于系统状态转换图生成测试用例",
                "applicable_scenarios": ["状态机", "工作流", "生命周期"],
                "generation_strategy": "state_transition"
            },
            "场景测试": {
                "description": "基于用户使用场景和业务流程生成测试用例",
                "applicable_scenarios": ["端到端测试", "用户故事", "业务流程"],
                "generation_strategy": "scenario_based"
            },
            "错误推测": {
                "description": "基于经验和直觉推测可能的错误情况",
                "applicable_scenarios": ["异常处理", "容错性", "安全测试"],
                "generation_strategy": "error_guessing"
            },
            "正交实验": {
                "description": "使用正交表设计多因子测试用例",
                "applicable_scenarios": ["配置测试", "兼容性测试", "参数组合"],
                "generation_strategy": "orthogonal_array"
            }
        }
        
        # 测试用例模板
        self.testcase_templates = {
            "功能测试": {
                "positive": "验证{功能}在正常条件下的行为",
                "negative": "验证{功能}在异常条件下的处理",
                "boundary": "验证{功能}在边界条件下的行为",
                "integration": "验证{功能}与其他模块的集成"
            },
            "性能测试": {
                "load": "验证{功能}在正常负载下的性能表现",
                "stress": "验证{功能}在压力条件下的稳定性",
                "volume": "验证{功能}处理大量数据的能力",
                "concurrent": "验证{功能}的并发处理能力"
            },
            "安全测试": {
                "authentication": "验证{功能}的身份认证机制",
                "authorization": "验证{功能}的权限控制",
                "data_protection": "验证{功能}的数据保护措施",
                "injection": "验证{功能}对注入攻击的防护"
            },
            "可用性测试": {
                "usability": "验证{功能}的易用性",
                "accessibility": "验证{功能}的可访问性",
                "compatibility": "验证{功能}的兼容性",
                "responsiveness": "验证{功能}的响应性"
            }
        }
        
        # 状态管理
        self.generation_state = {
            "current_feature": None,
            "generated_testcases": [],
            "remaining_scenarios": [],
            "coverage_matrix": {},
            "has_more": True
        }
    
    async def initialize(self):
        """初始化Agent"""
        await super().initialize()
        self.traceability_manager = await get_traceability_manager()
        self.logger.info("测试用例生成Agent初始化完成")
    
    def get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """
你是一个资深的测试用例设计专家，精通各种测试设计方法和最佳实践：

核心能力：
1. 等价类划分和边界值分析
2. 决策表和状态转换测试
3. 场景测试和错误推测
4. 正交实验设计
5. 风险驱动的测试设计
6. 可追溯性管理

设计原则：
- 基于需求和验收标准设计测试用例
- 确保测试覆盖的完整性和有效性
- 优先考虑高风险和高价值场景
- 平衡测试成本和质量收益
- 保持测试用例的可维护性

测试用例要素：
- 清晰的测试目标和前置条件
- 详细的测试步骤和预期结果
- 明确的优先级和分类标签
- 完整的追溯关系和覆盖信息

输出格式必须严格遵循JSON结构，包含完整的测试用例信息和状态管理数据。
"""
    
    async def process(self, input_data: Any) -> Dict[str, Any]:
        """处理测试用例生成任务"""
        self._ensure_initialized()
        
        try:
            await self.update_status(AgentStatus.PROCESSING, "开始测试用例生成")
            
            # 解析输入数据
            if isinstance(input_data, dict):
                requirements = input_data.get("requirements", [])
                feature_id = input_data.get("feature_id")
                generation_config = input_data.get("config", {})
                context = input_data.get("context", {})
            else:
                requirements = input_data if isinstance(input_data, list) else []
                feature_id = None
                generation_config = {}
                context = {}
            
            if not requirements:
                raise ValueError("需求数据不能为空")
            
            # 初始化生成状态
            await self._initialize_generation_state(requirements, feature_id, generation_config)
            
            # 使用迭代处理进行测试用例生成
            result = await self.iterative_process(
                task_description="测试用例生成和覆盖分析",
                initial_input={
                    "requirements": requirements,
                    "feature_id": feature_id,
                    "config": generation_config,
                    "context": context
                }
            )
            
            await self.update_status(AgentStatus.COMPLETED, "测试用例生成完成", 1.0)
            return result

        except Exception as e:
            await self.update_status(AgentStatus.ERROR, f"测试用例生成失败: {str(e)}")
            self.logger.error(f"测试用例生成处理失败: {str(e)}")
            raise
    
    async def _generate_error_guessing(
        self,
        requirement: Dict[str, Any],
        scenario: str,
        context: Dict[str, Any]
    ) -> List[TestCaseModel]:
        """错误推测法生成测试用例"""
        testcases = []
        
        try:
            req_id = requirement.get("id")
            req_name = requirement.get("name", "未命名需求")
            
            # 常见错误场景
            error_scenarios = [
                {"name": "空值输入", "description": "输入为空或null时的处理"},
                {"name": "超长输入", "description": "输入超过最大长度限制"},
                {"name": "特殊字符", "description": "输入包含特殊字符或SQL注入"},
                {"name": "网络异常", "description": "网络连接中断或超时"},
                {"name": "并发冲突", "description": "多用户同时操作相同资源"}
            ]
            
            for i, error_scenario in enumerate(error_scenarios):
                testcase = TestCaseModel(
                    id=f"TC-{req_id}-EG-{i+1}-{uuid.uuid4().hex[:6]}",
                    trace_id=f"T{req_id}-EG-{i+1}-{datetime.now().strftime('%Y%m%d')}-{uuid.uuid4().hex[:4]}",
                    name=f"{req_name} - {error_scenario['name']}测试",
                    description=f"验证{scenario}在{error_scenario['description']}情况下的处理",
                    requirement_ids=[req_id],
                    type=TestCaseType.NEGATIVE,
                    priority=TestPriority.MEDIUM,
                    status=TestCaseStatus.DRAFT,
                    preconditions=["系统正常运行"],
                    test_steps=[
                        TestStep(
                            step_number=1,
                            action=f"模拟{error_scenario['name']}情况",
                            expected_result="错误情况模拟成功"
                        ),
                        TestStep(
                            step_number=2,
                            action=f"执行{scenario}操作",
                            expected_result="系统检测到异常情况"
                        ),
                        TestStep(
                            step_number=3,
                            action="验证错误处理机制",
                            expected_result="系统正确处理异常并给出提示"
                        )
                    ],
                    expected_result="系统能够优雅地处理异常情况",
                    tags=["错误推测", error_scenario["name"], scenario],
                    generation_method="错误推测",
                    coverage_scenarios=[scenario]
                )
                testcases.append(testcase)
                
        except Exception as e:
            self.logger.error(f"错误推测生成失败: {str(e)}")
        
        return testcases
    
    async def _generate_state_transition(
        self,
        requirement: Dict[str, Any],
        scenario: str,
        context: Dict[str, Any]
    ) -> List[TestCaseModel]:
        """状态转换测试法生成测试用例"""
        testcases = []
        
        try:
            req_id = requirement.get("id")
            req_name = requirement.get("name", "未命名需求")
            
            # 简化的状态转换
            state_transitions = [
                {"from": "初始状态", "to": "处理中", "trigger": "开始操作"},
                {"from": "处理中", "to": "完成", "trigger": "操作成功"},
                {"from": "处理中", "to": "错误", "trigger": "操作失败"},
                {"from": "错误", "to": "初始状态", "trigger": "重置操作"}
            ]
            
            for i, transition in enumerate(state_transitions):
                testcase = TestCaseModel(
                    id=f"TC-{req_id}-ST-{i+1}-{uuid.uuid4().hex[:6]}",
                    trace_id=f"T{req_id}-ST-{i+1}-{datetime.now().strftime('%Y%m%d')}-{uuid.uuid4().hex[:4]}",
                    name=f"{req_name} - 状态转换测试({transition['from']}→{transition['to']})",
                    description=f"验证{scenario}从{transition['from']}到{transition['to']}的状态转换",
                    requirement_ids=[req_id],
                    type=TestCaseType.FUNCTIONAL,
                    priority=TestPriority.MEDIUM,
                    status=TestCaseStatus.DRAFT,
                    preconditions=[f"系统处于{transition['from']}状态"],
                    test_steps=[
                        TestStep(
                            step_number=1,
                            action=f"确认当前状态为{transition['from']}",
                            expected_result=f"系统状态确认为{transition['from']}"
                        ),
                        TestStep(
                            step_number=2,
                            action=f"执行{transition['trigger']}",
                            expected_result="触发条件执行成功"
                        ),
                        TestStep(
                            step_number=3,
                            action="验证状态转换",
                            expected_result=f"系统状态转换为{transition['to']}"
                        )
                    ],
                    expected_result=f"状态成功从{transition['from']}转换到{transition['to']}",
                    tags=["状态转换", transition["from"], transition["to"], scenario],
                    generation_method="状态转换测试",
                    coverage_scenarios=[scenario]
                )
                testcases.append(testcase)
                
        except Exception as e:
            self.logger.error(f"状态转换生成失败: {str(e)}")
        
        return testcases
    
    async def _generate_orthogonal_array(
        self,
        requirement: Dict[str, Any],
        scenario: str,
        context: Dict[str, Any]
    ) -> List[TestCaseModel]:
        """正交实验法生成测试用例"""
        testcases = []
        
        try:
            req_id = requirement.get("id")
            req_name = requirement.get("name", "未命名需求")
            
            # 简化的正交表L4(2^3)
            factors = ["因子A", "因子B", "因子C"]
            orthogonal_combinations = [
                {"因子A": "水平1", "因子B": "水平1", "因子C": "水平1"},
                {"因子A": "水平1", "因子B": "水平2", "因子C": "水平2"},
                {"因子A": "水平2", "因子B": "水平1", "因子C": "水平2"},
                {"因子A": "水平2", "因子B": "水平2", "因子C": "水平1"}
            ]
            
            for i, combo in enumerate(orthogonal_combinations):
                testcase = TestCaseModel(
                    id=f"TC-{req_id}-OA-{i+1}-{uuid.uuid4().hex[:6]}",
                    trace_id=f"T{req_id}-OA-{i+1}-{datetime.now().strftime('%Y%m%d')}-{uuid.uuid4().hex[:4]}",
                    name=f"{req_name} - 正交实验{i+1}",
                    description=f"验证{scenario}在正交组合{i+1}下的行为",
                    requirement_ids=[req_id],
                    type=TestCaseType.FUNCTIONAL,
                    priority=TestPriority.LOW,
                    status=TestCaseStatus.DRAFT,
                    preconditions=["系统正常运行", "测试环境配置完成"],
                    test_steps=[
                        TestStep(
                            step_number=1,
                            action=f"配置因子组合: {combo}",
                            expected_result="因子配置完成"
                        ),
                        TestStep(
                            step_number=2,
                            action=f"执行{scenario}测试",
                            expected_result="测试执行完成"
                        ),
                        TestStep(
                            step_number=3,
                            action="收集测试结果",
                            expected_result="结果数据收集完成"
                        )
                    ],
                    expected_result="正交组合测试通过",
                    tags=["正交实验", f"组合{i+1}", scenario],
                    generation_method="正交实验",
                    coverage_scenarios=[scenario]
                )
                testcases.append(testcase)
                
        except Exception as e:
            self.logger.error(f"正交实验生成失败: {str(e)}")
        
        return testcases
    
    async def _generate_default(
        self,
        requirement: Dict[str, Any],
        scenario: str,
        context: Dict[str, Any]
    ) -> List[TestCaseModel]:
        """默认方法生成测试用例"""
        testcases = []
        
        try:
            req_id = requirement.get("id")
            req_name = requirement.get("name", "未命名需求")
            
            testcase = TestCaseModel(
                id=f"TC-{req_id}-DEF-{uuid.uuid4().hex[:6]}",
                trace_id=f"T{req_id}-DEF-{datetime.now().strftime('%Y%m%d')}-{uuid.uuid4().hex[:4]}",
                name=f"{req_name} - 基础功能测试",
                description=f"验证{scenario}的基础功能",
                requirement_ids=[req_id],
                type=TestCaseType.FUNCTIONAL,
                priority=TestPriority.MEDIUM,
                status=TestCaseStatus.DRAFT,
                preconditions=["系统正常运行"],
                test_steps=[
                    TestStep(
                        step_number=1,
                        action="准备测试数据",
                        expected_result="测试数据准备完成"
                    ),
                    TestStep(
                        step_number=2,
                        action=f"执行{scenario}操作",
                        expected_result="操作执行成功"
                    ),
                    TestStep(
                        step_number=3,
                        action="验证结果",
                        expected_result="结果符合预期"
                    )
                ],
                expected_result="基础功能正常工作",
                tags=["基础测试", scenario],
                generation_method="默认方法",
                coverage_scenarios=[scenario]
            )
            testcases.append(testcase)
            
        except Exception as e:
            self.logger.error(f"默认方法生成失败: {str(e)}")
        
        return testcases
    
    async def _update_generation_state(
        self,
        new_testcases: List[TestCaseModel],
        requirements: List[Dict[str, Any]]
    ):
        """更新生成状态"""
        try:
            # 添加新生成的测试用例
            self.generation_state["generated_testcases"].extend(new_testcases)
            
            # 更新已覆盖的场景
            covered_scenarios = []
            for testcase in new_testcases:
                covered_scenarios.extend(testcase.coverage_scenarios)
            
            # 从剩余场景中移除已覆盖的
            remaining = []
            for scenario_info in self.generation_state["remaining_scenarios"]:
                if scenario_info["scenario"] not in covered_scenarios:
                    remaining.append(scenario_info)
            
            self.generation_state["remaining_scenarios"] = remaining
            self.generation_state["has_more"] = len(remaining) > 0
            
            # 更新覆盖矩阵
            for testcase in new_testcases:
                for req_id in testcase.requirement_ids:
                    if req_id not in self.generation_state["coverage_matrix"]:
                        self.generation_state["coverage_matrix"][req_id] = []
                    self.generation_state["coverage_matrix"][req_id].append(testcase.id)
            
            self.logger.info(f"状态更新: 新增{len(new_testcases)}个用例, 剩余{len(remaining)}个场景")
            
        except Exception as e:
            self.logger.error(f"更新生成状态失败: {str(e)}")
    
    async def _analyze_coverage_completeness(
        self,
        requirements: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """分析覆盖完整性"""
        try:
            total_scenarios = 0
            covered_scenarios = 0
            coverage_by_requirement = {}
            
            for req in requirements:
                req_id = req.get("id")
                req_scenarios = req.get("pending_scenarios", [])
                total_scenarios += len(req_scenarios)
                
                # 计算该需求的覆盖情况
                req_testcases = self.generation_state["coverage_matrix"].get(req_id, [])
                req_covered = 0
                
                for testcase in self.generation_state["generated_testcases"]:
                    if req_id in testcase.requirement_ids:
                        req_covered += len(testcase.coverage_scenarios)
                
                coverage_by_requirement[req_id] = {
                    "total_scenarios": len(req_scenarios),
                    "covered_scenarios": min(req_covered, len(req_scenarios)),
                    "coverage_percentage": (min(req_covered, len(req_scenarios)) / len(req_scenarios) * 100) if req_scenarios else 100,
                    "testcase_count": len(req_testcases)
                }
                
                covered_scenarios += min(req_covered, len(req_scenarios))
            
            overall_coverage = (covered_scenarios / total_scenarios * 100) if total_scenarios > 0 else 100
            
            analysis = {
                "overall_coverage": round(overall_coverage, 2),
                "total_scenarios": total_scenarios,
                "covered_scenarios": covered_scenarios,
                "remaining_scenarios": total_scenarios - covered_scenarios,
                "coverage_by_requirement": coverage_by_requirement,
                "total_testcases": len(self.generation_state["generated_testcases"]),
                "generation_methods_used": list(set(tc.generation_method for tc in self.generation_state["generated_testcases"]))
            }
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"分析覆盖完整性失败: {str(e)}")
            return {"error": str(e)}
    
    async def _update_traceability_matrix(self, testcases: List[TestCaseModel]):
        """更新追溯矩阵"""
        try:
            for testcase in testcases:
                await self.traceability_manager.add_testcase(self.session_id, testcase)
            
            self.logger.info(f"已添加 {len(testcases)} 个测试用例到追溯矩阵")
            
        except Exception as e:
            self.logger.error(f"更新追溯矩阵失败: {str(e)}")
            # 不抛出异常，避免影响主流程
    
    async def apply_improvement(
        self,
        current_result: Any,
        improvement: str
    ) -> Any:
        """应用改进建议"""
        try:
            if isinstance(current_result, dict) and "new_test_cases" in current_result:
                testcases = current_result["new_test_cases"]
                
                # 根据改进建议调整测试用例
                if "增加测试步骤" in improvement:
                    for tc in testcases:
                        steps = tc.get("test_steps", [])
                        if len(steps) < 4:
                            steps.append({
                                "step_number": len(steps) + 1,
                                "action": "补充的验证步骤",
                                "expected_result": "验证通过"
                            })
                            tc["test_steps"] = steps
                
                if "提高优先级" in improvement:
                    for tc in testcases:
                        if tc.get("priority") == "low":
                            tc["priority"] = "medium"
                        elif tc.get("priority") == "medium":
                            tc["priority"] = "high"
                
                if "补充前置条件" in improvement:
                    for tc in testcases:
                        preconditions = tc.get("preconditions", [])
                        if len(preconditions) < 2:
                            preconditions.append("补充的前置条件")
                            tc["preconditions"] = preconditions
                
                # 更新版本号
                current_result["version"] = current_result.get("version", 1) + 1
                current_result["improved_at"] = datetime.now().isoformat()
            
            return current_result
            
        except Exception as e:
            self.logger.error(f"应用改进建议失败: {str(e)}")
            return current_result
    
    async def get_generation_statistics(self) -> Dict[str, Any]:
        """获取生成统计信息"""
        try:
            testcases = self.generation_state.get("generated_testcases", [])
            
            # 按类型统计
            type_counts = {}
            for tc in testcases:
                tc_type = tc.type
                type_counts[tc_type] = type_counts.get(tc_type, 0) + 1
            
            # 按优先级统计
            priority_counts = {}
            for tc in testcases:
                priority = tc.priority
                priority_counts[priority] = priority_counts.get(priority, 0) + 1
            
            # 按生成方法统计
            method_counts = {}
            for tc in testcases:
                method = tc.generation_method
                method_counts[method] = method_counts.get(method, 0) + 1
            
            stats = {
                "total_testcases": len(testcases),
                "type_distribution": type_counts,
                "priority_distribution": priority_counts,
                "method_distribution": method_counts,
                "coverage_matrix": self.generation_state.get("coverage_matrix", {}),
                "remaining_scenarios": len(self.generation_state.get("remaining_scenarios", [])),
                "has_more": self.generation_state.get("has_more", False)
            }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取生成统计信息失败: {str(e)}")
            return {"error": str(e)}
    
    async def export_testcases(self, testcases_data: Dict[str, Any], format: str = "json") -> str:
        """导出测试用例"""
        try:
            if format.lower() == "json":
                return json.dumps(testcases_data, ensure_ascii=False, indent=2)
            
            elif format.lower() == "excel":
                # 简化的Excel格式数据
                excel_data = []
                testcases = testcases_data.get("new_test_cases", [])
                
                for tc in testcases:
                    excel_data.append({
                        "用例ID": tc.get("id", ""),
                        "用例名称": tc.get("name", ""),
                        "用例描述": tc.get("description", ""),
                        "用例类型": tc.get("type", ""),
                        "优先级": tc.get("priority", ""),
                        "状态": tc.get("status", ""),
                        "前置条件": "; ".join(tc.get("preconditions", [])),
                        "测试步骤": "; ".join([f"{step.get('step_number', '')}: {step.get('action', '')}" for step in tc.get("test_steps", [])]),
                        "预期结果": tc.get("expected_result", ""),
                        "标签": "; ".join(tc.get("tags", [])),
                        "生成方法": tc.get("generation_method", ""),
                        "关联需求": "; ".join(map(str, tc.get("requirement_ids", [])))
                    })
                
                return json.dumps(excel_data, ensure_ascii=False, indent=2)
            
            elif format.lower() == "markdown":
                lines = ["# 测试用例文档\n"]
                
                # 添加摘要
                summary = testcases_data.get("generation_summary", {})
                lines.append("## 生成摘要")
                lines.append(f"- 本轮生成: {summary.get('total_generated', 0)} 个用例")
                lines.append(f"- 累计生成: {summary.get('total_accumulated', 0)} 个用例")
                lines.append(f"- 覆盖率: {summary.get('coverage_percentage', 0)}%")
                lines.append(f"- 使用方法: {', '.join(summary.get('methods_used', []))}")
                lines.append("")
                
                # 添加测试用例详情
                testcases = testcases_data.get("new_test_cases", [])
                lines.append("## 测试用例详情\n")
                
                for i, tc in enumerate(testcases, 1):
                    lines.append(f"### {i}. {tc.get('name', '未命名用例')}")
                    lines.append(f"- **用例ID**: {tc.get('id', 'N/A')}")
                    lines.append(f"- **追溯ID**: {tc.get('trace_id', 'N/A')}")
                    lines.append(f"- **类型**: {tc.get('type', 'N/A')}")
                    lines.append(f"- **优先级**: {tc.get('priority', 'N/A')}")
                    lines.append(f"- **状态**: {tc.get('status', 'N/A')}")
                    lines.append(f"- **描述**: {tc.get('description', 'N/A')}")
                    
                    # 前置条件
                    preconditions = tc.get("preconditions", [])
                    if preconditions:
                        lines.append("- **前置条件**:")
                        for condition in preconditions:
                            lines.append(f"  - {condition}")
                    
                    # 测试步骤
                    steps = tc.get("test_steps", [])
                    if steps:
                        lines.append("- **测试步骤**:")
                        for step in steps:
                            lines.append(f"  {step.get('step_number', '')}: {step.get('action', '')}")
                            lines.append(f"     预期: {step.get('expected_result', '')}")
                    
                    lines.append(f"- **预期结果**: {tc.get('expected_result', 'N/A')}")
                    lines.append(f"- **标签**: {', '.join(tc.get('tags', []))}")
                    lines.append(f"- **生成方法**: {tc.get('generation_method', 'N/A')}")
                    lines.append(f"- **关联需求**: {', '.join(map(str, tc.get('requirement_ids', [])))}")
                    lines.append("")
                
                return "\n".join(lines)
            
            else:
                raise ValueError(f"不支持的导出格式: {format}")
                
        except Exception as e:
            self.logger.error(f"导出测试用例失败: {str(e)}")
            raise
            await self.update_status(AgentStatus.ERROR, f"测试用例生成失败: {str(e)}")
            self.logger.error(f"测试用例生成处理失败: {str(e)}")
            raise
    
    async def process_iteration(
        self,
        task_description: str,
        current_input: Any,
        iteration: int
    ) -> Dict[str, Any]:
        """处理单次迭代"""
        try:
            requirements = current_input["requirements"]
            feature_id = current_input.get("feature_id")
            config = current_input.get("config", {})
            context = current_input.get("context", {})
            
            # 第一步：分析需求和选择测试设计方法
            analysis_prompt = f"""
请分析以下需求并生成测试用例：

需求信息：
{json.dumps(requirements, ensure_ascii=False, indent=2)}

生成配置：
{json.dumps(config, ensure_ascii=False, indent=2)}

当前状态：
- 功能ID: {feature_id}
- 剩余场景: {len(self.generation_state.get('remaining_scenarios', []))}
- 已生成用例数: {len(self.generation_state.get('generated_testcases', []))}

任务要求：
1. 根据需求类型选择合适的测试设计方法
2. 基于验收标准生成具体的测试用例
3. 确保测试用例的完整性和可执行性
4. 为每个测试用例分配优先级和分类标签
5. 维护需求到测试用例的追溯关系
6. 评估当前覆盖情况和剩余工作

请使用思维链方式分析，最后生成结构化的测试用例。
"""
            
            # 生成思维链分析
            generation_analysis, thinking_chain = await self.generate_with_thinking(
                task_description=analysis_prompt,
                system_prompt=self.get_system_prompt(),
                max_steps=6
            )
            
            # 第二步：执行具体的测试用例生成
            new_testcases = await self._generate_testcases_by_methods(
                requirements, config, context
            )
            
            # 第三步：更新生成状态和覆盖矩阵
            await self._update_generation_state(new_testcases, requirements)
            
            # 第四步：评估覆盖完整性
            coverage_analysis = await self._analyze_coverage_completeness(requirements)
            
            # 第五步：更新追溯矩阵
            await self._update_traceability_matrix(new_testcases)
            
            return {
                "feature_id": feature_id,
                "new_test_cases": [tc.dict() for tc in new_testcases],
                "remaining_scenarios": self.generation_state["remaining_scenarios"],
                "generated_ids": [tc.id for tc in self.generation_state["generated_testcases"]],
                "has_more": self.generation_state["has_more"],
                "coverage_analysis": coverage_analysis,
                "generation_summary": {
                    "total_generated": len(new_testcases),
                    "total_accumulated": len(self.generation_state["generated_testcases"]),
                    "coverage_percentage": coverage_analysis.get("overall_coverage", 0),
                    "methods_used": list(set(tc.generation_method for tc in new_testcases))
                },
                "version": current_input.get("version", 1) + 1,
                "generated_at": datetime.now().isoformat(),
                "generator_agent": self.agent_id
            }
            
        except Exception as e:
            self.logger.error(f"生成迭代处理失败: {str(e)}")
            raise
    
    async def _initialize_generation_state(
        self,
        requirements: List[Dict[str, Any]],
        feature_id: Optional[str],
        config: Dict[str, Any]
    ):
        """初始化生成状态"""
        try:
            # 提取所有待处理场景
            all_scenarios = []
            for req in requirements:
                scenarios = req.get("pending_scenarios", [])
                for scenario in scenarios:
                    all_scenarios.append({
                        "requirement_id": req.get("id"),
                        "scenario": scenario,
                        "priority": req.get("priority", "medium"),
                        "risk_level": req.get("risk_level", "medium")
                    })
            
            # 按优先级和风险级别排序
            all_scenarios.sort(key=lambda x: (
                {"high": 3, "medium": 2, "low": 1}.get(x["risk_level"], 2),
                {"critical": 4, "high": 3, "medium": 2, "low": 1}.get(x["priority"], 2)
            ), reverse=True)
            
            self.generation_state = {
                "current_feature": feature_id,
                "generated_testcases": [],
                "remaining_scenarios": all_scenarios,
                "coverage_matrix": {},
                "has_more": len(all_scenarios) > 0,
                "config": config
            }
            
            self.logger.info(f"初始化生成状态: {len(all_scenarios)} 个待处理场景")
            
        except Exception as e:
            self.logger.error(f"初始化生成状态失败: {str(e)}")
            raise
    
    async def _generate_testcases_by_methods(
        self,
        requirements: List[Dict[str, Any]],
        config: Dict[str, Any],
        context: Dict[str, Any]
    ) -> List[TestCaseModel]:
        """使用多种方法生成测试用例"""
        try:
            new_testcases = []
            batch_size = config.get("batch_size", 5)  # 每次生成的用例数量
            
            # 获取当前批次的场景
            current_batch = self.generation_state["remaining_scenarios"][:batch_size]
            
            for scenario_info in current_batch:
                req_id = scenario_info["requirement_id"]
                scenario = scenario_info["scenario"]
                
                # 找到对应的需求
                requirement = next((req for req in requirements if req.get("id") == req_id), None)
                if not requirement:
                    continue
                
                # 选择合适的测试设计方法
                design_methods = await self._select_design_methods(requirement, scenario)
                
                # 为每种方法生成测试用例
                for method in design_methods:
                    testcases = await self._generate_by_method(
                        requirement, scenario, method, context
                    )
                    new_testcases.extend(testcases)
            
            self.logger.info(f"本轮生成 {len(new_testcases)} 个测试用例")
            return new_testcases
            
        except Exception as e:
            self.logger.error(f"按方法生成测试用例失败: {str(e)}")
            return []
    
    async def _select_design_methods(
        self,
        requirement: Dict[str, Any],
        scenario: str
    ) -> List[str]:
        """选择合适的测试设计方法"""
        try:
            req_type = requirement.get("type", "functional")
            req_description = requirement.get("description", "").lower()
            scenario_lower = scenario.lower()
            
            selected_methods = []
            
            # 基于需求类型选择方法
            if req_type == "functional":
                selected_methods.extend(["等价类划分", "边界值分析", "场景测试"])
                
                # 检查是否涉及复杂业务逻辑
                if any(keyword in req_description for keyword in ["规则", "条件", "判断", "逻辑"]):
                    selected_methods.append("决策表测试")
                
                # 检查是否涉及状态转换
                if any(keyword in req_description for keyword in ["状态", "流程", "步骤", "阶段"]):
                    selected_methods.append("状态转换测试")
            
            elif req_type == "performance":
                selected_methods.extend(["边界值分析", "正交实验"])
                
            elif req_type == "security":
                selected_methods.extend(["错误推测", "边界值分析"])
                
            elif req_type == "usability":
                selected_methods.extend(["场景测试", "错误推测"])
            
            # 基于场景内容调整方法
            if "异常" in scenario_lower or "错误" in scenario_lower:
                if "错误推测" not in selected_methods:
                    selected_methods.append("错误推测")
            
            if "边界" in scenario_lower or "范围" in scenario_lower:
                if "边界值分析" not in selected_methods:
                    selected_methods.append("边界值分析")
            
            # 确保至少有一种方法
            if not selected_methods:
                selected_methods = ["等价类划分"]
            
            return selected_methods[:2]  # 限制每个场景最多使用2种方法
            
        except Exception as e:
            self.logger.error(f"选择测试设计方法失败: {str(e)}")
            return ["等价类划分"]
    
    async def _generate_by_method(
        self,
        requirement: Dict[str, Any],
        scenario: str,
        method: str,
        context: Dict[str, Any]
    ) -> List[TestCaseModel]:
        """使用特定方法生成测试用例"""
        try:
            method_config = self.test_design_methods.get(method, {})
            strategy = method_config.get("generation_strategy", "default")
            
            if strategy == "equivalence_partitioning":
                return await self._generate_equivalence_partitioning(requirement, scenario, context)
            elif strategy == "boundary_value_analysis":
                return await self._generate_boundary_value_analysis(requirement, scenario, context)
            elif strategy == "decision_table":
                return await self._generate_decision_table(requirement, scenario, context)
            elif strategy == "state_transition":
                return await self._generate_state_transition(requirement, scenario, context)
            elif strategy == "scenario_based":
                return await self._generate_scenario_based(requirement, scenario, context)
            elif strategy == "error_guessing":
                return await self._generate_error_guessing(requirement, scenario, context)
            elif strategy == "orthogonal_array":
                return await self._generate_orthogonal_array(requirement, scenario, context)
            else:
                return await self._generate_default(requirement, scenario, context)
                
        except Exception as e:
            self.logger.error(f"使用方法 {method} 生成测试用例失败: {str(e)}")
            return []
    
    async def _generate_equivalence_partitioning(
        self,
        requirement: Dict[str, Any],
        scenario: str,
        context: Dict[str, Any]
    ) -> List[TestCaseModel]:
        """等价类划分法生成测试用例"""
        testcases = []
        
        try:
            req_id = requirement.get("id")
            req_name = requirement.get("name", "未命名需求")
            
            # 生成有效等价类测试用例
            valid_testcase = TestCaseModel(
                id=f"TC-{req_id}-EQ-V-{uuid.uuid4().hex[:6]}",
                trace_id=f"T{req_id}-EQ-V-{datetime.now().strftime('%Y%m%d')}-{uuid.uuid4().hex[:4]}",
                name=f"{req_name} - 有效等价类测试",
                description=f"验证{scenario}在有效输入条件下的行为",
                requirement_ids=[req_id],
                type=TestCaseType.FUNCTIONAL,
                priority=TestPriority.HIGH,
                status=TestCaseStatus.DRAFT,
                preconditions=["系统正常运行", "用户已登录"],
                test_steps=[
                    TestStep(
                        step_number=1,
                        action="准备有效的测试数据",
                        expected_result="测试数据准备完成"
                    ),
                    TestStep(
                        step_number=2,
                        action=f"执行{scenario}操作",
                        expected_result="操作成功执行"
                    ),
                    TestStep(
                        step_number=3,
                        action="验证结果",
                        expected_result="结果符合预期"
                    )
                ],
                expected_result="功能正常工作，返回预期结果",
                tags=["等价类划分", "有效输入", scenario],
                generation_method="等价类划分",
                coverage_scenarios=[scenario]
            )
            testcases.append(valid_testcase)
            
            # 生成无效等价类测试用例
            invalid_testcase = TestCaseModel(
                id=f"TC-{req_id}-EQ-I-{uuid.uuid4().hex[:6]}",
                trace_id=f"T{req_id}-EQ-I-{datetime.now().strftime('%Y%m%d')}-{uuid.uuid4().hex[:4]}",
                name=f"{req_name} - 无效等价类测试",
                description=f"验证{scenario}在无效输入条件下的处理",
                requirement_ids=[req_id],
                type=TestCaseType.NEGATIVE,
                priority=TestPriority.MEDIUM,
                status=TestCaseStatus.DRAFT,
                preconditions=["系统正常运行", "用户已登录"],
                test_steps=[
                    TestStep(
                        step_number=1,
                        action="准备无效的测试数据",
                        expected_result="测试数据准备完成"
                    ),
                    TestStep(
                        step_number=2,
                        action=f"执行{scenario}操作",
                        expected_result="系统检测到无效输入"
                    ),
                    TestStep(
                        step_number=3,
                        action="验证错误处理",
                        expected_result="显示适当的错误信息"
                    )
                ],
                expected_result="系统正确处理无效输入，显示错误信息",
                tags=["等价类划分", "无效输入", scenario],
                generation_method="等价类划分",
                coverage_scenarios=[scenario]
            )
            testcases.append(invalid_testcase)
            
        except Exception as e:
            self.logger.error(f"等价类划分生成失败: {str(e)}")
        
        return testcases
    
    async def _generate_boundary_value_analysis(
        self,
        requirement: Dict[str, Any],
        scenario: str,
        context: Dict[str, Any]
    ) -> List[TestCaseModel]:
        """边界值分析法生成测试用例"""
        testcases = []
        
        try:
            req_id = requirement.get("id")
            req_name = requirement.get("name", "未命名需求")
            
            boundary_points = ["最小值", "最小值+1", "正常值", "最大值-1", "最大值"]
            
            for i, point in enumerate(boundary_points):
                testcase = TestCaseModel(
                    id=f"TC-{req_id}-BV-{i+1}-{uuid.uuid4().hex[:6]}",
                    trace_id=f"T{req_id}-BV-{i+1}-{datetime.now().strftime('%Y%m%d')}-{uuid.uuid4().hex[:4]}",
                    name=f"{req_name} - 边界值测试({point})",
                    description=f"验证{scenario}在{point}条件下的行为",
                    requirement_ids=[req_id],
                    type=TestCaseType.BOUNDARY,
                    priority=TestPriority.HIGH if point in ["最小值", "最大值"] else TestPriority.MEDIUM,
                    status=TestCaseStatus.DRAFT,
                    preconditions=["系统正常运行"],
                    test_steps=[
                        TestStep(
                            step_number=1,
                            action=f"设置输入值为{point}",
                            expected_result="输入值设置完成"
                        ),
                        TestStep(
                            step_number=2,
                            action=f"执行{scenario}操作",
                            expected_result="操作执行完成"
                        ),
                        TestStep(
                            step_number=3,
                            action="验证处理结果",
                            expected_result="结果符合边界值处理预期"
                        )
                    ],
                    expected_result=f"系统正确处理{point}情况",
                    tags=["边界值分析", point, scenario],
                    generation_method="边界值分析",
                    coverage_scenarios=[scenario]
                )
                testcases.append(testcase)
                
        except Exception as e:
            self.logger.error(f"边界值分析生成失败: {str(e)}")
        
        return testcases
    
    async def _generate_decision_table(
        self,
        requirement: Dict[str, Any],
        scenario: str,
        context: Dict[str, Any]
    ) -> List[TestCaseModel]:
        """决策表法生成测试用例"""
        testcases = []
        
        try:
            req_id = requirement.get("id")
            req_name = requirement.get("name", "未命名需求")
            
            # 简化的决策表组合
            conditions = ["条件A", "条件B"]
            combinations = [
                {"条件A": True, "条件B": True, "结果": "执行操作1"},
                {"条件A": True, "条件B": False, "结果": "执行操作2"},
                {"条件A": False, "条件B": True, "结果": "执行操作3"},
                {"条件A": False, "条件B": False, "结果": "执行操作4"}
            ]
            
            for i, combo in enumerate(combinations):
                testcase = TestCaseModel(
                    id=f"TC-{req_id}-DT-{i+1}-{uuid.uuid4().hex[:6]}",
                    trace_id=f"T{req_id}-DT-{i+1}-{datetime.now().strftime('%Y%m%d')}-{uuid.uuid4().hex[:4]}",
                    name=f"{req_name} - 决策表测试{i+1}",
                    description=f"验证{scenario}在条件组合{i+1}下的决策逻辑",
                    requirement_ids=[req_id],
                    type=TestCaseType.FUNCTIONAL,
                    priority=TestPriority.MEDIUM,
                    status=TestCaseStatus.DRAFT,
                    preconditions=["系统正常运行", "业务规则已配置"],
                    test_steps=[
                        TestStep(
                            step_number=1,
                            action=f"设置条件: {combo['条件A']}, {combo['条件B']}",
                            expected_result="条件设置完成"
                        ),
                        TestStep(
                            step_number=2,
                            action=f"触发{scenario}决策",
                            expected_result="决策逻辑执行"
                        ),
                        TestStep(
                            step_number=3,
                            action="验证决策结果",
                            expected_result=combo["结果"]
                        )
                    ],
                    expected_result=combo["结果"],
                    tags=["决策表", f"组合{i+1}", scenario],
                    generation_method="决策表测试",
                    coverage_scenarios=[scenario]
                )
                testcases.append(testcase)
                
        except Exception as e:
            self.logger.error(f"决策表生成失败: {str(e)}")
        
        return testcases
    
    async def _generate_scenario_based(
        self,
        requirement: Dict[str, Any],
        scenario: str,
        context: Dict[str, Any]
    ) -> List[TestCaseModel]:
        """场景测试法生成测试用例"""
        testcases = []
        
        try:
            req_id = requirement.get("id")
            req_name = requirement.get("name", "未命名需求")
            
            testcase = TestCaseModel(
                id=f"TC-{req_id}-SC-{uuid.uuid4().hex[:6]}",
                trace_id=f"T{req_id}-SC-{datetime.now().strftime('%Y%m%d')}-{uuid.uuid4().hex[:4]}",
                name=f"{req_name} - 场景测试",
                description=f"基于真实用户场景验证{scenario}的完整流程",
                requirement_ids=[req_id],
                type=TestCaseType.SCENARIO,
                priority=TestPriority.HIGH,
                status=TestCaseStatus.DRAFT,
                preconditions=["系统正常运行", "用户环境准备就绪"],
                test_steps=[
                    TestStep(
                        step_number=1,
                        action="用户进入系统",
                        expected_result="成功进入系统主界面"
                    ),
                    TestStep(
                        step_number=2,
                        action=f"用户执行{scenario}相关操作",
                        expected_result="操作界面正常显示"
                    ),
                    TestStep(
                        step_number=3,
                        action="完成整个业务流程",
                        expected_result="业务流程成功完成"
                    ),
                    TestStep(
                        step_number=4,
                        action="验证最终结果",
                        expected_result="结果符合业务预期"
                    )
                ],
                expected_result="完整的用户场景执行成功",
                tags=["场景测试", "端到端", scenario],
                generation_method="场景测试",
                coverage_scenarios=[scenario]
            )
            testcases.append(testcase)

        except Exception as e:
            self.logger.error(f"场景测试法生成测试用例失败: {str(e)}")

        return testcases