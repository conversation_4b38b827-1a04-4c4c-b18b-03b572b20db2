"""
需求评审Agent实现
"""

import asyncio
import uuid
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime
import json
import re

from agents.base import BaseAgent
from models.schemas import (
    RequirementModel, ReviewIssue, ReviewResult, ThinkingChain, ReflectionResult
)
from models.enums import (
    AgentType, RequirementStatus, RiskLevel, AgentStatus, IssueType, IssueSeverity
)
from core.logger import get_agent_logger
from core.traceability import get_traceability_manager


class RequirementReviewAgent(BaseAgent):
    """需求评审Agent"""
    
    def __init__(self, session_id: str):
        super().__init__(AgentType.REQUIREMENT_REVIEW, session_id)
        self.logger = get_agent_logger(f"RequirementReview_{self.agent_id}")
        self.traceability_manager = None
        
        # 评审规则配置
        self.review_criteria = {
            "完整性检查": {
                "必要字段": ["name", "description", "acceptance_criteria", "type"],
                "验收标准": ["positive", "negative", "boundary", "error"],
                "最小描述长度": 10,
                "最小场景数量": 2
            },
            "一致性检查": {
                "命名规范": r"^[A-Za-z0-9\u4e00-\u9fa5\s\-_]+$",
                "追溯ID格式": r"^R\d+-\d{8}-[a-f0-9]{4}$",
                "优先级值": ["low", "medium", "high", "critical"],
                "状态值": ["draft", "review", "approved", "rejected", "clarify"]
            },
            "质量标准": {
                "描述清晰度": ["明确", "具体", "可测试", "无歧义"],
                "验收标准完整性": ["正向场景", "负向场景", "边界条件", "异常处理"],
                "风险评估合理性": ["技术复杂度", "业务影响", "依赖关系", "时间约束"]
            },
            "业务逻辑": {
                "功能依赖": "检查功能间的依赖关系",
                "业务流程": "验证业务流程的完整性",
                "数据一致性": "确保数据定义的一致性",
                "接口规范": "检查接口定义的规范性"
            }
        }
        
        # 常见问题模式
        self.issue_patterns = {
            "模糊描述": [
                r"可能|也许|大概|应该会|估计",
                r"适当|合理|足够|较好|比较",
                r"一些|某些|相关|等等|之类"
            ],
            "缺失信息": [
                r"待定|TBD|TODO|未确定",
                r"后续|以后|将来|暂时",
                r"详见|参考|另行|单独"
            ],
            "技术术语": [
                r"高性能|高可用|高并发",
                r"用户友好|易于使用|简单",
                r"安全|稳定|可靠"
            ]
        }
    
    async def initialize(self):
        """初始化Agent"""
        await super().initialize()
        self.traceability_manager = await get_traceability_manager()
        self.logger.info("需求评审Agent初始化完成")
    
    def get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """
你是一个资深的需求评审专家，具备以下专业能力：
1. 需求完整性和一致性检查
2. 业务逻辑合理性验证
3. 技术可行性评估
4. 风险识别和评估
5. 质量改进建议

评审原则：
- 严格按照评审标准进行检查
- 识别需求中的模糊、缺失或矛盾之处
- 评估需求的技术可行性和业务价值
- 提供具体的改进建议和澄清问题
- 确保需求的可测试性和可追溯性

评审重点：
- 需求描述的清晰性和完整性
- 验收标准的可测试性
- 业务流程的逻辑性
- 非功能需求的量化指标
- 需求间的依赖关系和一致性

输出格式必须严格遵循JSON结构，包含详细的评审问题、改进建议和更新后的需求。
"""
    
    async def process(self, input_data: Any) -> Dict[str, Any]:
        """处理需求评审任务"""
        self._ensure_initialized()
        
        try:
            await self.update_status(AgentStatus.PROCESSING, "开始需求评审")
            
            # 解析输入数据
            if isinstance(input_data, dict):
                requirements_data = input_data.get("requirements", [])
                context = input_data.get("context", {})
                review_focus = input_data.get("review_focus", "all")
            else:
                requirements_data = input_data if isinstance(input_data, list) else []
                context = {}
                review_focus = "all"
            
            if not requirements_data:
                raise ValueError("需求数据不能为空")
            
            # 使用迭代处理进行需求评审
            result = await self.iterative_process(
                task_description="需求评审和质量检查",
                initial_input={
                    "requirements": requirements_data,
                    "context": context,
                    "review_focus": review_focus
                }
            )
            
            await self.update_status(AgentStatus.COMPLETED, "需求评审完成", 1.0)
            return result
            
        except Exception as e:
            await self.update_status(AgentStatus.ERROR, f"需求评审失败: {str(e)}")
            self.logger.error(f"需求评审处理失败: {str(e)}")
            raise
    
    async def process_iteration(
        self,
        task_description: str,
        current_input: Any,
        iteration: int
    ) -> Dict[str, Any]:
        """处理单次迭代"""
        try:
            requirements = current_input["requirements"]
            context = current_input.get("context", {})
            review_focus = current_input.get("review_focus", "all")
            
            # 第一步：使用思维链进行评审分析
            review_prompt = f"""
请对以下需求进行全面评审：

需求数据：
{json.dumps(requirements, ensure_ascii=False, indent=2)}

评审上下文：
{json.dumps(context, ensure_ascii=False, indent=2)}

评审重点：{review_focus}

评审任务：
1. 完整性检查：验证每个需求的必要字段和信息完整性
2. 一致性检查：检查需求间的一致性和命名规范
3. 质量评估：评估需求描述的清晰度和验收标准的可测试性
4. 风险识别：识别潜在的技术风险和业务风险
5. 改进建议：提供具体的改进建议和澄清问题
6. 依赖分析：分析需求间的依赖关系

请使用思维链方式逐步分析，最后生成结构化的评审结果。
"""
            
            # 生成思维链分析
            review_analysis, thinking_chain = await self.generate_with_thinking(
                task_description=review_prompt,
                system_prompt=self.get_system_prompt(),
                max_steps=8
            )
            
            # 第二步：执行详细的评审检查
            detailed_review = await self._perform_detailed_review(
                requirements, context, review_focus
            )
            
            # 第三步：生成评审报告
            review_report = await self._generate_review_report(
                requirements, detailed_review, review_analysis
            )
            
            # 第四步：更新需求状态
            updated_requirements = await self._update_requirements_status(
                requirements, detailed_review
            )
            
            # 第五步：更新追溯矩阵
            await self._update_traceability_matrix(updated_requirements)
            
            return {
                "review_issues": detailed_review["issues"],
                "updated_requirements": updated_requirements,
                "review_summary": detailed_review["summary"],
                "recommendations": detailed_review["recommendations"],
                "traceability_matrix": detailed_review.get("traceability_updates", []),
                "version": current_input.get("version", 1) + 1,
                "reviewed_at": datetime.now().isoformat(),
                "reviewer_agent": self.agent_id
            }
            
        except Exception as e:
            self.logger.error(f"评审迭代处理失败: {str(e)}")
            raise
    
    async def _perform_detailed_review(
        self,
        requirements: List[Dict[str, Any]],
        context: Dict[str, Any],
        review_focus: str
    ) -> Dict[str, Any]:
        """执行详细的评审检查"""
        try:
            issues = []
            summary = {
                "total_requirements": len(requirements),
                "issues_found": 0,
                "critical_issues": 0,
                "high_issues": 0,
                "medium_issues": 0,
                "low_issues": 0,
                "requirements_with_issues": 0
            }
            recommendations = []
            
            for req in requirements:
                req_issues = []
                
                # 完整性检查
                if review_focus in ["all", "completeness"]:
                    completeness_issues = await self._check_completeness(req)
                    req_issues.extend(completeness_issues)
                
                # 一致性检查
                if review_focus in ["all", "consistency"]:
                    consistency_issues = await self._check_consistency(req)
                    req_issues.extend(consistency_issues)
                
                # 质量检查
                if review_focus in ["all", "quality"]:
                    quality_issues = await self._check_quality(req)
                    req_issues.extend(quality_issues)
                
                # 业务逻辑检查
                if review_focus in ["all", "business_logic"]:
                    business_issues = await self._check_business_logic(req, requirements)
                    req_issues.extend(business_issues)
                
                # 风险评估
                if review_focus in ["all", "risk"]:
                    risk_issues = await self._assess_risks(req)
                    req_issues.extend(risk_issues)
                
                # 统计问题
                if req_issues:
                    summary["requirements_with_issues"] += 1
                    issues.extend(req_issues)
                    
                    for issue in req_issues:
                        summary["issues_found"] += 1
                        severity = issue.get("severity", "medium")
                        summary[f"{severity}_issues"] += 1
            
            # 生成整体建议
            recommendations = await self._generate_recommendations(issues, summary, context)
            
            return {
                "issues": issues,
                "summary": summary,
                "recommendations": recommendations
            }
            
        except Exception as e:
            self.logger.error(f"详细评审检查失败: {str(e)}")
            raise
    
    async def _check_completeness(self, requirement: Dict[str, Any]) -> List[Dict[str, Any]]:
        """检查需求完整性"""
        issues = []
        req_id = requirement.get("id", "未知")
        req_name = requirement.get("name", "未命名需求")
        
        # 检查必要字段
        required_fields = self.review_criteria["完整性检查"]["必要字段"]
        for field in required_fields:
            if not requirement.get(field):
                issues.append({
                    "requirement_id": req_id,
                    "requirement_name": req_name,
                    "issue_type": IssueType.MISSING_FIELD,
                    "severity": IssueSeverity.HIGH,
                    "description": f"缺少必要字段: {field}",
                    "suggestion": f"请补充 {field} 字段的内容",
                    "location": field
                })
        
        # 检查描述长度
        description = requirement.get("description", "")
        min_length = self.review_criteria["完整性检查"]["最小描述长度"]
        if len(description) < min_length:
            issues.append({
                "requirement_id": req_id,
                "requirement_name": req_name,
                "issue_type": IssueType.INSUFFICIENT_DETAIL,
                "severity": IssueSeverity.MEDIUM,
                "description": f"需求描述过于简短 (当前: {len(description)} 字符, 最少: {min_length} 字符)",
                "suggestion": "请提供更详细的需求描述，包括背景、目标和约束条件",
                "location": "description"
            })
        
        # 检查验收标准完整性
        acceptance_criteria = requirement.get("acceptance_criteria", {})
        required_criteria = self.review_criteria["完整性检查"]["验收标准"]
        for criteria_type in required_criteria:
            if not acceptance_criteria.get(criteria_type) or not acceptance_criteria[criteria_type]:
                issues.append({
                    "requirement_id": req_id,
                    "requirement_name": req_name,
                    "issue_type": IssueType.INCOMPLETE_CRITERIA,
                    "severity": IssueSeverity.MEDIUM,
                    "description": f"缺少验收标准: {criteria_type}",
                    "suggestion": f"请补充 {criteria_type} 验收标准",
                    "location": f"acceptance_criteria.{criteria_type}"
                })
        
        # 检查测试场景数量
        scenarios = requirement.get("pending_scenarios", [])
        min_scenarios = self.review_criteria["完整性检查"]["最小场景数量"]
        if len(scenarios) < min_scenarios:
            issues.append({
                "requirement_id": req_id,
                "requirement_name": req_name,
                "issue_type": IssueType.INSUFFICIENT_SCENARIOS,
                "severity": IssueSeverity.LOW,
                "description": f"测试场景数量不足 (当前: {len(scenarios)}, 建议: {min_scenarios}+)",
                "suggestion": "请补充更多测试场景，包括边界值和异常情况",
                "location": "pending_scenarios"
            })
        
        return issues
    
    async def _check_consistency(self, requirement: Dict[str, Any]) -> List[Dict[str, Any]]:
        """检查需求一致性"""
        issues = []
        req_id = requirement.get("id", "未知")
        req_name = requirement.get("name", "未命名需求")
        
        # 检查命名规范
        name = requirement.get("name", "")
        naming_pattern = self.review_criteria["一致性检查"]["命名规范"]
        if name and not re.match(naming_pattern, name):
            issues.append({
                "requirement_id": req_id,
                "requirement_name": req_name,
                "issue_type": IssueType.NAMING_VIOLATION,
                "severity": IssueSeverity.LOW,
                "description": "需求名称不符合命名规范",
                "suggestion": "请使用字母、数字、中文、空格、连字符或下划线",
                "location": "name"
            })
        
        # 检查追溯ID格式
        trace_id = requirement.get("trace_id", "")
        trace_pattern = self.review_criteria["一致性检查"]["追溯ID格式"]
        if trace_id and not re.match(trace_pattern, trace_id):
            issues.append({
                "requirement_id": req_id,
                "requirement_name": req_name,
                "issue_type": IssueType.FORMAT_ERROR,
                "severity": IssueSeverity.MEDIUM,
                "description": "追溯ID格式不正确",
                "suggestion": "追溯ID应符合格式: R{数字}-{YYYYMMDD}-{4位16进制}",
                "location": "trace_id"
            })
        
        # 检查优先级值
        priority = requirement.get("priority", "")
        valid_priorities = self.review_criteria["一致性检查"]["优先级值"]
        if priority and priority not in valid_priorities:
            issues.append({
                "requirement_id": req_id,
                "requirement_name": req_name,
                "issue_type": IssueType.INVALID_VALUE,
                "severity": IssueSeverity.LOW,
                "description": f"优先级值无效: {priority}",
                "suggestion": f"请使用有效的优先级值: {', '.join(valid_priorities)}",
                "location": "priority"
            })
        
        # 检查状态值
        status = requirement.get("status", "")
        valid_statuses = self.review_criteria["一致性检查"]["状态值"]
        if status and status not in valid_statuses:
            issues.append({
                "requirement_id": req_id,
                "requirement_name": req_name,
                "issue_type": IssueType.INVALID_VALUE,
                "severity": IssueSeverity.LOW,
                "description": f"状态值无效: {status}",
                "suggestion": f"请使用有效的状态值: {', '.join(valid_statuses)}",
                "location": "status"
            })
        
        return issues
    
    async def _check_quality(self, requirement: Dict[str, Any]) -> List[Dict[str, Any]]:
        """检查需求质量"""
        issues = []
        req_id = requirement.get("id", "未知")
        req_name = requirement.get("name", "未命名需求")
        
        # 检查描述清晰度
        description = requirement.get("description", "")
        for pattern_type, patterns in self.issue_patterns.items():
            for pattern in patterns:
                if re.search(pattern, description, re.IGNORECASE):
                    issues.append({
                        "requirement_id": req_id,
                        "requirement_name": req_name,
                        "issue_type": IssueType.AMBIGUOUS_DESCRIPTION,
                        "severity": IssueSeverity.MEDIUM,
                        "description": f"需求描述存在{pattern_type}: 发现模式 '{pattern}'",
                        "suggestion": f"请明确具体的{pattern_type}，避免使用模糊表述",
                        "location": "description"
                    })
        
        # 检查验收标准的可测试性
        acceptance_criteria = requirement.get("acceptance_criteria", {})
        for criteria_type, criteria_list in acceptance_criteria.items():
            if isinstance(criteria_list, list):
                for i, criteria in enumerate(criteria_list):
                    if isinstance(criteria, str):
                        # 检查是否包含可测试的具体条件
                        if len(criteria) < 10 or any(word in criteria.lower() for word in ["待定", "tbd", "todo"]):
                            issues.append({
                                "requirement_id": req_id,
                                "requirement_name": req_name,
                                "issue_type": IssueType.UNTESTABLE_CRITERIA,
                                "severity": IssueSeverity.MEDIUM,
                                "description": f"{criteria_type}验收标准不够具体或可测试",
                                "suggestion": "请提供具体、可测试的验收标准",
                                "location": f"acceptance_criteria.{criteria_type}[{i}]"
                            })
        
        return issues
    
    async def _check_business_logic(
        self,
        requirement: Dict[str, Any],
        all_requirements: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """检查业务逻辑"""
        issues = []
        req_id = requirement.get("id", "未知")
        req_name = requirement.get("name", "未命名需求")
        
        # 检查功能依赖关系
        req_description = requirement.get("description", "").lower()
        req_type = requirement.get("type", "")
        
        # 识别可能的依赖关系
        dependency_keywords = ["登录", "认证", "权限", "用户", "数据", "接口"]
        found_dependencies = []
        
        for keyword in dependency_keywords:
            if keyword in req_description:
                found_dependencies.append(keyword)
        
        # 检查是否存在相关的依赖需求
        if found_dependencies:
            related_requirements = []
            for other_req in all_requirements:
                if other_req.get("id") != req_id:
                    other_desc = other_req.get("description", "").lower()
                    for dep in found_dependencies:
                        if dep in other_desc:
                            related_requirements.append(other_req.get("name", "未命名"))
                            break
            
            if not related_requirements and len(found_dependencies) > 1:
                issues.append({
                    "requirement_id": req_id,
                    "requirement_name": req_name,
                    "issue_type": IssueType.MISSING_DEPENDENCY,
                    "severity": IssueSeverity.MEDIUM,
                    "description": f"可能缺少依赖需求，涉及: {', '.join(found_dependencies)}",
                    "suggestion": "请确认是否需要添加相关的依赖需求或明确依赖关系",
                    "location": "dependencies"
                })
        
        # 检查业务流程完整性
        if req_type == "functional":
            process_keywords = ["开始", "结束", "流程", "步骤", "操作"]
            has_process_info = any(keyword in req_description for keyword in process_keywords)
            
            if not has_process_info and len(req_description) > 50:
                issues.append({
                    "requirement_id": req_id,
                    "requirement_name": req_name,
                    "issue_type": IssueType.INCOMPLETE_PROCESS,
                    "severity": IssueSeverity.LOW,
                    "description": "功能需求缺少业务流程描述",
                    "suggestion": "请补充业务流程的关键步骤和操作顺序",
                    "location": "description"
                })
        
        return issues
    
    async def _assess_risks(self, requirement: Dict[str, Any]) -> List[Dict[str, Any]]:
        """评估需求风险"""
        issues = []
        req_id = requirement.get("id", "未知")
        req_name = requirement.get("name", "未命名需求")
        
        # 获取当前风险级别
        current_risk = requirement.get("risk_level", "medium")
        req_description = requirement.get("description", "").lower()
        req_type = requirement.get("type", "")
        
        # 高风险指标
        high_risk_indicators = [
            "安全", "支付", "金融", "核心", "关键", "重要",
            "数据库", "集成", "第三方", "性能", "并发"
        ]
        
        # 中风险指标
        medium_risk_indicators = [
            "用户", "界面", "报表", "查询", "管理",
            "配置", "设置", "通知", "邮件"
        ]
        
        # 计算风险评分
        risk_score = 0
        found_indicators = []
        
        for indicator in high_risk_indicators:
            if indicator in req_description:
                risk_score += 3
                found_indicators.append(indicator)
        
        for indicator in medium_risk_indicators:
            if indicator in req_description:
                risk_score += 1
                found_indicators.append(indicator)
        
        # 根据需求类型调整风险
        if req_type in ["security", "performance"]:
            risk_score += 2
        elif req_type == "functional":
            risk_score += 1
        
        # 评估风险级别是否合理
        suggested_risk = "low"
        if risk_score >= 6:
            suggested_risk = "high"
        elif risk_score >= 3:
            suggested_risk = "medium"
        
        if current_risk != suggested_risk:
            severity = IssueSeverity.MEDIUM if abs(["low", "medium", "high"].index(current_risk) - 
                                                  ["low", "medium", "high"].index(suggested_risk)) > 1 else IssueSeverity.LOW
            
            issues.append({
                "requirement_id": req_id,
                "requirement_name": req_name,
                "issue_type": IssueType.RISK_MISMATCH,
                "severity": severity,
                "description": f"风险级别可能不准确 (当前: {current_risk}, 建议: {suggested_risk})",
                "suggestion": f"基于需求内容分析，建议风险级别为: {suggested_risk}。涉及关键词: {', '.join(found_indicators)}",
                "location": "risk_level"
            })
        
        # 检查高风险需求的验收标准
        if suggested_risk == "high":
            acceptance_criteria = requirement.get("acceptance_criteria", {})
            security_criteria = acceptance_criteria.get("error", [])
            
            if not security_criteria or len(security_criteria) < 2:
                issues.append({
                    "requirement_id": req_id,
                    "requirement_name": req_name,
                    "issue_type": IssueType.INSUFFICIENT_SECURITY,
                    "severity": IssueSeverity.HIGH,
                    "description": "高风险需求缺少足够的异常处理和安全验收标准",
                    "suggestion": "请补充详细的异常处理、安全验证和错误恢复验收标准",
                    "location": "acceptance_criteria.error"
                })
        
        return issues
    
    async def _generate_recommendations(
        self,
        issues: List[Dict[str, Any]],
        summary: Dict[str, Any],
        context: Dict[str, Any]
    ) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        try:
            # 基于问题统计生成建议
            if summary["critical_issues"] > 0:
                recommendations.append(f"发现 {summary['critical_issues']} 个严重问题，建议优先解决这些问题后再进行后续开发")
            
            if summary["high_issues"] > 0:
                recommendations.append(f"发现 {summary['high_issues']} 个高优先级问题，建议在需求确认前解决")
            
            # 基于问题类型生成建议
            issue_types = {}
            for issue in issues:
                issue_type = issue.get("issue_type", "unknown")
                issue_types[issue_type] = issue_types.get(issue_type, 0) + 1
            
            if issue_types.get(IssueType.MISSING_FIELD, 0) > 0:
                recommendations.append("多个需求存在字段缺失，建议使用需求模板确保信息完整性")
            
            if issue_types.get(IssueType.AMBIGUOUS_DESCRIPTION, 0) > 0:
                recommendations.append("部分需求描述存在歧义，建议与业务方进一步澄清具体要求")
            
            if issue_types.get(IssueType.UNTESTABLE_CRITERIA, 0) > 0:
                recommendations.append("验收标准需要更加具体和可测试，建议添加量化指标和明确的判断条件")
            
            if issue_types.get(IssueType.RISK_MISMATCH, 0) > 0:
                recommendations.append("部分需求的风险评估可能不准确，建议重新评估风险级别并相应调整测试策略")
            
            # 基于整体质量生成建议
            issue_rate = summary["issues_found"] / summary["total_requirements"] if summary["total_requirements"] > 0 else 0
            
            if issue_rate > 0.8:
                recommendations.append("需求质量整体偏低，建议进行需求评审培训并建立质量检查流程")
            elif issue_rate > 0.5:
                recommendations.append("需求质量有待提升，建议加强需求分析和评审环节")
            elif issue_rate < 0.2:
                recommendations.append("需求质量良好，建议保持当前的需求管理流程")
            
            # 基于上下文生成建议
            project_type = context.get("project_type", "")
            if project_type == "security":
                recommendations.append("安全类项目建议增加安全测试场景和合规性检查")
            elif project_type == "performance":
                recommendations.append("性能类项目建议明确性能指标和测试基准")
            
            if not recommendations:
                recommendations.append("需求评审完成，整体质量符合标准")
            
        except Exception as e:
            self.logger.error(f"生成改进建议失败: {str(e)}")
            recommendations.append("无法生成改进建议，请手动检查评审结果")
        
        return recommendations
    
    async def _generate_review_report(
        self,
        requirements: List[Dict[str, Any]],
        detailed_review: Dict[str, Any],
        analysis_text: str
    ) -> Dict[str, Any]:
        """生成评审报告"""
        try:
            issues = detailed_review["issues"]
            summary = detailed_review["summary"]
            recommendations = detailed_review["recommendations"]
            
            # 按严重程度分组问题
            issues_by_severity = {
                "critical": [],
                "high": [],
                "medium": [],
                "low": []
            }
            
            for issue in issues:
                severity = issue.get("severity", "medium")
                issues_by_severity[severity].append(issue)
            
            # 按需求分组问题
            issues_by_requirement = {}
            for issue in issues:
                req_id = issue.get("requirement_id", "unknown")
                if req_id not in issues_by_requirement:
                    issues_by_requirement[req_id] = []
                issues_by_requirement[req_id].append(issue)
            
            # 生成报告
            report = {
                "review_summary": summary,
                "issues_by_severity": issues_by_severity,
                "issues_by_requirement": issues_by_requirement,
                "recommendations": recommendations,
                "analysis_details": analysis_text[:500] + "..." if len(analysis_text) > 500 else analysis_text,
                "generated_at": datetime.now().isoformat()
            }
            
            return report
            
        except Exception as e:
            self.logger.error(f"生成评审报告失败: {str(e)}")
            return {
                "review_summary": detailed_review.get("summary", {}),
                "error": f"报告生成失败: {str(e)}"
            }
    
    async def _update_requirements_status(
        self,
        requirements: List[Dict[str, Any]],
        detailed_review: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """更新需求状态"""
        try:
            updated_requirements = []
            issues = detailed_review["issues"]
            
            # 按需求ID分组问题
            issues_by_req = {}
            for issue in issues:
                req_id = issue.get("requirement_id")
                if req_id not in issues_by_req:
                    issues_by_req[req_id] = []
                issues_by_req[req_id].append(issue)
            
            for req in requirements:
                req_copy = req.copy()
                req_id = req.get("id")
                req_issues = issues_by_req.get(req_id, [])
                
                # 根据问题严重程度更新状态
                if req_issues:
                    critical_issues = [i for i in req_issues if i.get("severity") == "critical"]
                    high_issues = [i for i in req_issues if i.get("severity") == "high"]
                    
                    if critical_issues:
                        req_copy["status"] = RequirementStatus.REJECTED
                        req_copy["review_notes"] = f"发现 {len(critical_issues)} 个严重问题，需要重新分析"
                    elif high_issues:
                        req_copy["status"] = RequirementStatus.CLARIFY
                        req_copy["review_notes"] = f"发现 {len(high_issues)} 个高优先级问题，需要澄清"
                    else:
                        req_copy["status"] = RequirementStatus.REVIEW
                        req_copy["review_notes"] = f"发现 {len(req_issues)} 个问题，需要修改"
                else:
                    req_copy["status"] = RequirementStatus.APPROVED
                    req_copy["review_notes"] = "评审通过"
                
                # 添加评审信息
                req_copy["reviewed_at"] = datetime.now().isoformat()
                req_copy["reviewer"] = self.agent_id
                req_copy["review_issues"] = req_issues
                
                updated_requirements.append(req_copy)
            
            return updated_requirements
            
        except Exception as e:
            self.logger.error(f"更新需求状态失败: {str(e)}")
            return requirements
    
    async def _update_traceability_matrix(self, requirements: List[Dict[str, Any]]):
        """更新追溯矩阵"""
        try:
            for req_data in requirements:
                requirement = RequirementModel(**req_data)
                await self.traceability_manager.add_requirement(self.session_id, requirement)
            
            self.logger.info(f"已更新 {len(requirements)} 个需求到追溯矩阵")
            
        except Exception as e:
            self.logger.error(f"更新追溯矩阵失败: {str(e)}")
            # 不抛出异常，避免影响主流程
    
    async def apply_improvement(
        self,
        current_result: Any,
        improvement: str
    ) -> Any:
        """应用改进建议"""
        try:
            if isinstance(current_result, dict) and "updated_requirements" in current_result:
                requirements = current_result["updated_requirements"]
                
                # 根据改进建议调整评审结果
                if "降低严重程度" in improvement:
                    for req in requirements:
                        issues = req.get("review_issues", [])
                        for issue in issues:
                            if issue.get("severity") == "high":
                                issue["severity"] = "medium"
                            elif issue.get("severity") == "critical":
                                issue["severity"] = "high"
                
                if "增加澄清问题" in improvement:
                    for req in requirements:
                        if req.get("status") == RequirementStatus.CLARIFY:
                            clarifications = req.get("pending_clarifications", [])
                            clarifications.append("需要进一步澄清的问题")
                            req["pending_clarifications"] = clarifications
                
                # 更新版本号
                current_result["version"] = current_result.get("version", 1) + 1
                current_result["improved_at"] = datetime.now().isoformat()
            
            return current_result
            
        except Exception as e:
            self.logger.error(f"应用改进建议失败: {str(e)}")
            return current_result
    
    async def export_review_report(self, review_data: Dict[str, Any], format: str = "json") -> str:
        """导出评审报告"""
        try:
            if format.lower() == "json":
                return json.dumps(review_data, ensure_ascii=False, indent=2)
            
            elif format.lower() == "markdown":
                lines = ["# 需求评审报告\n"]
                
                # 添加摘要
                summary = review_data.get("review_summary", {})
                lines.append("## 评审摘要")
                lines.append(f"- 总需求数: {summary.get('total_requirements', 0)}")
                lines.append(f"- 发现问题数: {summary.get('issues_found', 0)}")
                lines.append(f"- 严重问题: {summary.get('critical_issues', 0)}")
                lines.append(f"- 高优先级问题: {summary.get('high_issues', 0)}")
                lines.append(f"- 中等问题: {summary.get('medium_issues', 0)}")
                lines.append(f"- 低优先级问题: {summary.get('low_issues', 0)}")
                lines.append(f"- 有问题的需求数: {summary.get('requirements_with_issues', 0)}")
                lines.append("")
                
                # 添加问题详情
                issues_by_severity = review_data.get("issues_by_severity", {})
                for severity in ["critical", "high", "medium", "low"]:
                    issues = issues_by_severity.get(severity, [])
                    if issues:
                        lines.append(f"## {severity.upper()}级别问题")
                        for i, issue in enumerate(issues, 1):
                            lines.append(f"### {i}. {issue.get('description', 'N/A')}")
                            lines.append(f"- **需求**: {issue.get('requirement_name', 'N/A')}")
                            lines.append(f"- **类型**: {issue.get('issue_type', 'N/A')}")
                            lines.append(f"- **位置**: {issue.get('location', 'N/A')}")
                            lines.append(f"- **建议**: {issue.get('suggestion', 'N/A')}")
                            lines.append("")
                
                # 添加改进建议
                recommendations = review_data.get("recommendations", [])
                if recommendations:
                    lines.append("## 改进建议")
                    for i, rec in enumerate(recommendations, 1):
                        lines.append(f"{i}. {rec}")
                    lines.append("")
                
                return "\n".join(lines)
            
            else:
                raise ValueError(f"不支持的导出格式: {format}")
                
        except Exception as e:
            self.logger.error(f"导出评审报告失败: {str(e)}")
            raise
    
    async def get_review_statistics(self, session_id: str) -> Dict[str, Any]:
        """获取评审统计信息"""
        try:
            # 从记忆中检索评审历史
            review_history = await self.retrieve_context(
                query="需求评审",
                context_type="requirement",
                limit=100
            )
            
            stats = {
                "total_reviews": len(review_history),
                "avg_issues_per_requirement": 0,
                "common_issue_types": {},
                "quality_trend": [],
                "review_efficiency": {
                    "avg_review_time": 0,
                    "throughput": 0
                }
            }
            
            if review_history:
                total_issues = 0
                total_requirements = 0
                issue_type_counts = {}
                
                for review in review_history:
                    review_data = review.get("content", {})
                    if isinstance(review_data, dict):
                        issues = review_data.get("review_issues", [])
                        total_issues += len(issues)
                        total_requirements += 1
                        
                        for issue in issues:
                            issue_type = issue.get("issue_type", "unknown")
                            issue_type_counts[issue_type] = issue_type_counts.get(issue_type, 0) + 1
                
                if total_requirements > 0:
                    stats["avg_issues_per_requirement"] = total_issues / total_requirements
                
                stats["common_issue_types"] = dict(sorted(
                    issue_type_counts.items(),
                    key=lambda x: x[1],
                    reverse=True
                )[:5])
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取评审统计信息失败: {str(e)}")
            return {"error": str(e)}
