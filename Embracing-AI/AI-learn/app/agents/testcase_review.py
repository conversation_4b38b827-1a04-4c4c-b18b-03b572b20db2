"""
测试用例评审Agent实现
"""

import asyncio
import uuid
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime
import json
import re

from agents.base import BaseAgent
from models.schemas import (
    TestCaseModel, ReviewIssue, ReviewResult, ThinkingChain, ReflectionResult
)
from models.enums import (
    AgentType, TestCaseStatus, TestCaseType, TestPriority, AgentStatus, 
    IssueType, IssueSeverity
)
from core.logger import get_agent_logger
from core.traceability import get_traceability_manager


class TestCaseReviewAgent(BaseAgent):
    """测试用例评审Agent"""
    
    def __init__(self, session_id: str):
        super().__init__(AgentType.TESTCASE_REVIEW, session_id)
        self.logger = get_agent_logger(f"TestCaseReview_{self.agent_id}")
        self.traceability_manager = None
        
        # 评审标准配置
        self.review_criteria = {
            "完整性检查": {
                "必要字段": ["name", "description", "test_steps", "expected_result"],
                "测试步骤要素": ["action", "expected_result"],
                "最小步骤数": 2,
                "最小描述长度": 15
            },
            "可执行性检查": {
                "步骤清晰度": "每个步骤应该明确具体",
                "前置条件": "应该明确测试前置条件",
                "预期结果": "应该具体可验证",
                "数据依赖": "应该明确测试数据要求"
            },
            "可维护性检查": {
                "命名规范": r"^[A-Za-z0-9\u4e00-\u9fa5\s\-_()]+$",
                "标签规范": "标签应该有意义且一致",
                "优先级合理性": "优先级应该与风险匹配",
                "分类准确性": "测试类型应该准确"
            },
            "质量标准": {
                "独立性": "测试用例应该相互独立",
                "可重复性": "测试结果应该可重复",
                "覆盖性": "应该覆盖指定的测试场景",
                "有效性": "应该能够发现缺陷"
            },
            "业务逻辑": {
                "需求追溯": "应该明确关联的需求",
                "场景合理性": "测试场景应该符合业务逻辑",
                "数据有效性": "测试数据应该真实有效",
                "流程完整性": "业务流程应该完整"
            }
        }
        
        # 常见问题模式
        self.issue_patterns = {
            "模糊步骤": [
                r"检查|验证|确认",
                r"正确|正常|合适",
                r"相关|适当|合理"
            ],
            "缺失信息": [
                r"待定|TBD|TODO",
                r"省略|忽略|跳过",
                r"详见|参考|另行"
            ],
            "不可执行": [
                r"自动|手动",
                r"可能|也许|大概",
                r"根据情况|视情况而定"
            ]
        }
        
        # 质量评分权重
        self.quality_weights = {
            "完整性": 0.25,
            "可执行性": 0.30,
            "可维护性": 0.20,
            "业务逻辑": 0.25
        }
    
    async def initialize(self):
        """初始化Agent"""
        await super().initialize()
        self.traceability_manager = await get_traceability_manager()
        self.logger.info("测试用例评审Agent初始化完成")
    
    def get_system_prompt(self) -> str:
        """获取系统提示词"""
        return """
你是一个资深的测试用例评审专家，具备以下专业能力：
1. 测试用例完整性和规范性检查
2. 可执行性和可维护性评估
3. 业务逻辑合理性验证
4. 测试覆盖度分析
5. 质量改进建议

评审原则：
- 确保测试用例的完整性和准确性
- 验证测试步骤的可执行性和清晰度
- 检查测试用例的独立性和可重复性
- 评估测试用例的业务价值和有效性
- 提供具体的改进建议和优化方案

评审重点：
- 测试步骤的详细程度和可操作性
- 预期结果的具体性和可验证性
- 测试数据的真实性和有效性
- 需求追溯关系的准确性
- 测试用例间的独立性和一致性

输出格式必须严格遵循JSON结构，包含详细的评审结果、问题分析和改进建议。
"""
    
    async def process(self, input_data: Any) -> Dict[str, Any]:
        """处理测试用例评审任务"""
        self._ensure_initialized()
        
        try:
            await self.update_status(AgentStatus.PROCESSING, "开始测试用例评审")
            
            # 解析输入数据
            if isinstance(input_data, dict):
                testcases_data = input_data.get("new_test_cases", [])
                feature_id = input_data.get("feature_id")
                remaining_scenarios = input_data.get("remaining_scenarios", [])
                context = input_data.get("context", {})
                review_config = input_data.get("review_config", {})
            else:
                testcases_data = input_data if isinstance(input_data, list) else []
                feature_id = None
                remaining_scenarios = []
                context = {}
                review_config = {}
            
            if not testcases_data:
                raise ValueError("测试用例数据不能为空")
            
            # 使用迭代处理进行测试用例评审
            result = await self.iterative_process(
                task_description="测试用例质量评审和完整性检查",
                initial_input={
                    "testcases": testcases_data,
                    "feature_id": feature_id,
                    "remaining_scenarios": remaining_scenarios,
                    "context": context,
                    "review_config": review_config
                }
            )
            
            await self.update_status(AgentStatus.COMPLETED, "测试用例评审完成", 1.0)
            return result
            
        except Exception as e:
            await self.update_status(AgentStatus.ERROR, f"测试用例评审失败: {str(e)}")
            self.logger.error(f"测试用例评审处理失败: {str(e)}")
            raise
    
    async def process_iteration(
        self,
        task_description: str,
        current_input: Any,
        iteration: int
    ) -> Dict[str, Any]:
        """处理单次迭代"""
        try:
            testcases = current_input["testcases"]
            feature_id = current_input.get("feature_id")
            remaining_scenarios = current_input.get("remaining_scenarios", [])
            context = current_input.get("context", {})
            review_config = current_input.get("review_config", {})
            
            # 第一步：使用思维链进行评审分析
            review_prompt = f"""
请对以下测试用例进行全面评审：

测试用例数据：
{json.dumps(testcases, ensure_ascii=False, indent=2)}

评审上下文：
- 功能ID: {feature_id}
- 剩余场景数: {len(remaining_scenarios)}
- 评审配置: {json.dumps(review_config, ensure_ascii=False)}

评审任务：
1. 完整性检查：验证测试用例的必要字段和信息完整性
2. 可执行性检查：评估测试步骤的清晰度和可操作性
3. 可维护性检查：检查命名规范、标签和分类的准确性
4. 质量评估：评估测试用例的独立性、可重复性和有效性
5. 业务逻辑检查：验证需求追溯和业务场景的合理性
6. 覆盖度分析：分析测试场景的覆盖情况

请使用思维链方式逐步分析，最后生成结构化的评审结果。
"""
            
            # 生成思维链分析
            review_analysis, thinking_chain = await self.generate_with_thinking(
                task_description=review_prompt,
                system_prompt=self.get_system_prompt(),
                max_steps=6
            )
            
            # 第二步：执行详细的测试用例评审
            detailed_review = await self._perform_detailed_review(
                testcases, context, review_config
            )
            
            # 第三步：更新测试用例状态
            reviewed_cases = await self._update_testcase_status(
                testcases, detailed_review
            )
            
            # 第四步：更新剩余场景
            updated_remaining_scenarios = await self._update_remaining_scenarios(
                remaining_scenarios, reviewed_cases, detailed_review
            )
            
            # 第五步：更新追溯矩阵
            await self._update_traceability_matrix(reviewed_cases)
            
            # 第六步：评估是否还有更多工作
            has_more = await self._evaluate_has_more(
                updated_remaining_scenarios, detailed_review
            )
            
            return {
                "feature_id": feature_id,
                "reviewed_cases": [tc.dict() if hasattr(tc, 'dict') else tc for tc in reviewed_cases],
                "updated_remaining_scenarios": updated_remaining_scenarios,
                "review_summary": detailed_review["summary"],
                "review_issues": detailed_review["issues"],
                "quality_metrics": detailed_review["quality_metrics"],
                "recommendations": detailed_review["recommendations"],
                "traceability_matrix": detailed_review.get("traceability_updates", []),
                "has_more": has_more,
                "version": current_input.get("version", 1) + 1,
                "reviewed_at": datetime.now().isoformat(),
                "reviewer_agent": self.agent_id
            }
            
        except Exception as e:
            self.logger.error(f"评审迭代处理失败: {str(e)}")
            raise
    
    async def _perform_detailed_review(
        self,
        testcases: List[Dict[str, Any]],
        context: Dict[str, Any],
        review_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """执行详细的测试用例评审"""
        try:
            issues = []
            quality_metrics = {
                "total_testcases": len(testcases),
                "passed_testcases": 0,
                "failed_testcases": 0,
                "average_quality_score": 0.0,
                "completeness_score": 0.0,
                "executability_score": 0.0,
                "maintainability_score": 0.0,
                "business_logic_score": 0.0
            }
            
            total_scores = {
                "completeness": 0.0,
                "executability": 0.0,
                "maintainability": 0.0,
                "business_logic": 0.0
            }
            
            for testcase in testcases:
                tc_issues = []
                tc_scores = {}
                
                # 完整性检查
                completeness_issues, completeness_score = await self._check_completeness(testcase)
                tc_issues.extend(completeness_issues)
                tc_scores["completeness"] = completeness_score
                
                # 可执行性检查
                executability_issues, executability_score = await self._check_executability(testcase)
                tc_issues.extend(executability_issues)
                tc_scores["executability"] = executability_score
                
                # 可维护性检查
                maintainability_issues, maintainability_score = await self._check_maintainability(testcase)
                tc_issues.extend(maintainability_issues)
                tc_scores["maintainability"] = maintainability_score
                
                # 业务逻辑检查
                business_issues, business_score = await self._check_business_logic(testcase, testcases)
                tc_issues.extend(business_issues)
                tc_scores["business_logic"] = business_score
                
                # 计算综合质量分数
                weighted_score = sum(
                    tc_scores[aspect] * self.quality_weights[aspect.replace("_", "")]
                    for aspect in tc_scores
                )
                
                # 累计分数
                for aspect, score in tc_scores.items():
                    total_scores[aspect] += score
                
                # 判断测试用例是否通过评审
                if weighted_score >= 0.7 and not any(issue.get("severity") == IssueSeverity.CRITICAL for issue in tc_issues):
                    quality_metrics["passed_testcases"] += 1
                else:
                    quality_metrics["failed_testcases"] += 1
                
                issues.extend(tc_issues)
            
            # 计算平均分数
            if testcases:
                for aspect in total_scores:
                    total_scores[aspect] /= len(testcases)
                
                quality_metrics["completeness_score"] = total_scores["completeness"]
                quality_metrics["executability_score"] = total_scores["executability"]
                quality_metrics["maintainability_score"] = total_scores["maintainability"]
                quality_metrics["business_logic_score"] = total_scores["business_logic"]
                
                quality_metrics["average_quality_score"] = sum(
                    total_scores[aspect] * self.quality_weights[aspect.replace("_", "")]
                    for aspect in total_scores
                )
            
            # 生成改进建议
            recommendations = await self._generate_recommendations(issues, quality_metrics, context)
            
            # 生成评审摘要
            summary = {
                "total_issues": len(issues),
                "critical_issues": sum(1 for issue in issues if issue.get("severity") == IssueSeverity.CRITICAL),
                "high_issues": sum(1 for issue in issues if issue.get("severity") == IssueSeverity.HIGH),
                "medium_issues": sum(1 for issue in issues if issue.get("severity") == IssueSeverity.MEDIUM),
                "low_issues": sum(1 for issue in issues if issue.get("severity") == IssueSeverity.LOW),
                "pass_rate": quality_metrics["passed_testcases"] / quality_metrics["total_testcases"] if quality_metrics["total_testcases"] > 0 else 0,
                "overall_quality": "优秀" if quality_metrics["average_quality_score"] >= 0.8 else 
                                 "良好" if quality_metrics["average_quality_score"] >= 0.6 else
                                 "一般" if quality_metrics["average_quality_score"] >= 0.4 else "较差"
            }
            
            return {
                "issues": issues,
                "quality_metrics": quality_metrics,
                "summary": summary,
                "recommendations": recommendations
            }
            
        except Exception as e:
            self.logger.error(f"详细评审执行失败: {str(e)}")
            raise
    
    async def _check_completeness(self, testcase: Dict[str, Any]) -> Tuple[List[Dict[str, Any]], float]:
        """检查测试用例完整性"""
        issues = []
        score = 1.0
        
        try:
            tc_id = testcase.get("id", "未知")
            tc_name = testcase.get("name", "未命名测试用例")
            
            # 检查必要字段
            required_fields = self.review_criteria["完整性检查"]["必要字段"]
            missing_fields = []
            
            for field in required_fields:
                if not testcase.get(field):
                    missing_fields.append(field)
                    score -= 0.2
            
            if missing_fields:
                issues.append({
                    "testcase_id": tc_id,
                    "testcase_name": tc_name,
                    "issue_type": IssueType.MISSING_FIELD,
                    "severity": IssueSeverity.CRITICAL,
                    "description": f"缺少必要字段: {', '.join(missing_fields)}",
                    "suggestion": f"请补充以下字段: {', '.join(missing_fields)}",
                    "location": "fields"
                })
            
            # 检查描述长度
            description = testcase.get("description", "")
            min_length = self.review_criteria["完整性检查"]["最小描述长度"]
            if len(description) < min_length:
                issues.append({
                    "testcase_id": tc_id,
                    "testcase_name": tc_name,
                    "issue_type": IssueType.INSUFFICIENT_DETAIL,
                    "severity": IssueSeverity.MEDIUM,
                    "description": f"测试用例描述过于简短 (当前: {len(description)} 字符, 最少: {min_length} 字符)",
                    "suggestion": "请提供更详细的测试用例描述，说明测试目的和预期行为",
                    "location": "description"
                })
                score -= 0.1
            
            # 检查测试步骤
            test_steps = testcase.get("test_steps", [])
            min_steps = self.review_criteria["完整性检查"]["最小步骤数"]
            
            if len(test_steps) < min_steps:
                issues.append({
                    "testcase_id": tc_id,
                    "testcase_name": tc_name,
                    "issue_type": IssueType.INSUFFICIENT_STEPS,
                    "severity": IssueSeverity.HIGH,
                    "description": f"测试步骤数量不足 (当前: {len(test_steps)}, 最少: {min_steps})",
                    "suggestion": "请补充详细的测试步骤，确保测试流程完整",
                    "location": "test_steps"
                })
                score -= 0.3
            
            # 检查测试步骤要素
            step_elements = self.review_criteria["完整性检查"]["测试步骤要素"]
            for i, step in enumerate(test_steps):
                if isinstance(step, dict):
                    for element in step_elements:
                        if not step.get(element):
                            issues.append({
                                "testcase_id": tc_id,
                                "testcase_name": tc_name,
                                "issue_type": IssueType.INCOMPLETE_STEP,
                                "severity": IssueSeverity.MEDIUM,
                                "description": f"测试步骤{i+1}缺少{element}",
                                "suggestion": f"请为测试步骤{i+1}补充{element}",
                                "location": f"test_steps[{i}].{element}"
                            })
                            score -= 0.05
            
            # 检查预期结果
            expected_result = testcase.get("expected_result", "")
            if not expected_result or len(expected_result) < 5:
                issues.append({
                    "testcase_id": tc_id,
                    "testcase_name": tc_name,
                    "issue_type": IssueType.MISSING_EXPECTED_RESULT,
                    "severity": IssueSeverity.HIGH,
                    "description": "缺少或预期结果过于简单",
                    "suggestion": "请提供具体、可验证的预期结果",
                    "location": "expected_result"
                })
                score -= 0.2
            
            return issues, max(0.0, score)
            
        except Exception as e:
            self.logger.error(f"完整性检查失败: {str(e)}")
            return issues, 0.0
    
    async def _check_executability(self, testcase: Dict[str, Any]) -> Tuple[List[Dict[str, Any]], float]:
        """检查测试用例可执行性"""
        issues = []
        score = 1.0
        
        try:
            tc_id = testcase.get("id", "未知")
            tc_name = testcase.get("name", "未命名测试用例")
            
            # 检查前置条件
            preconditions = testcase.get("preconditions", [])
            if not preconditions:
                issues.append({
                    "testcase_id": tc_id,
                    "testcase_name": tc_name,
                    "issue_type": IssueType.MISSING_PRECONDITION,
                    "severity": IssueSeverity.MEDIUM,
                    "description": "缺少前置条件",
                    "suggestion": "请明确测试执行前需要满足的条件",
                    "location": "preconditions"
                })
                score -= 0.2
            
            # 检查测试步骤的清晰度
            test_steps = testcase.get("test_steps", [])
            for i, step in enumerate(test_steps):
                if isinstance(step, dict):
                    action = step.get("action", "")
                    expected = step.get("expected_result", "")
                    
                    # 检查动作描述的模糊性
                    for pattern_type, patterns in self.issue_patterns.items():
                        for pattern in patterns:
                            if re.search(pattern, action, re.IGNORECASE):
                                issues.append({
                                    "testcase_id": tc_id,
                                    "testcase_name": tc_name,
                                    "issue_type": IssueType.AMBIGUOUS_STEP,
                                    "severity": IssueSeverity.MEDIUM,
                                    "description": f"测试步骤{i+1}存在{pattern_type}: '{pattern}'",
                                    "suggestion": f"请明确具体的操作步骤，避免使用模糊表述",
                                    "location": f"test_steps[{i}].action"
                                })
                                score -= 0.1
                    
                    # 检查预期结果的具体性
                    if len(expected) < 5:
                        issues.append({
                            "testcase_id": tc_id,
                            "testcase_name": tc_name,
                            "issue_type": IssueType.VAGUE_EXPECTED_RESULT,
                            "severity": IssueSeverity.MEDIUM,
                            "description": f"测试步骤{i+1}的预期结果过于简单",
                            "suggestion": "请提供具体、可验证的预期结果",
                            "location": f"test_steps[{i}].expected_result"
                        })
                        score -= 0.1
            
            # 检查测试数据依赖
            description = testcase.get("description", "")
            if "数据" in description and not any("数据" in str(step) for step in test_steps):
                issues.append({
                    "testcase_id": tc_id,
                    "testcase_name": tc_name,
                    "issue_type": IssueType.MISSING_TEST_DATA,
                    "severity": IssueSeverity.MEDIUM,
                    "description": "测试用例涉及数据但未明确测试数据要求",
                    "suggestion": "请在测试步骤中明确所需的测试数据",
                    "location": "test_steps"
                })
                score -= 0.15
            
            return issues, max(0.0, score)
            
        except Exception as e:
            self.logger.error(f"可执行性检查失败: {str(e)}")
            return issues, 0.0
    
    async def _check_maintainability(self, testcase: Dict[str, Any]) -> Tuple[List[Dict[str, Any]], float]:
        """检查测试用例可维护性"""
        issues = []
        score = 1.0
        
        try:
            tc_id = testcase.get("id", "未知")
            tc_name = testcase.get("name", "未命名测试用例")
            
            # 检查命名规范
            naming_pattern = self.review_criteria["可维护性检查"]["命名规范"]
            if tc_name and not re.match(naming_pattern, tc_name):
                issues.append({
                    "testcase_id": tc_id,
                    "testcase_name": tc_name,
                    "issue_type": IssueType.NAMING_VIOLATION,
                    "severity": IssueSeverity.LOW,
                    "description": "测试用例名称不符合命名规范",
                    "suggestion": "请使用清晰、有意义的测试用例名称",
                    "location": "name"
                })
                score -= 0.1
            
            # 检查标签规范
            tags = testcase.get("tags", [])
            if not tags:
                issues.append({
                    "testcase_id": tc_id,
                    "testcase_name": tc_name,
                    "issue_type": IssueType.MISSING_TAGS,
                    "severity": IssueSeverity.LOW,
                    "description": "缺少测试标签",
                    "suggestion": "请添加有意义的测试标签，便于分类和管理",
                    "location": "tags"
                })
                score -= 0.1
            else:
                # 检查标签的有意义性
                meaningless_tags = ["测试", "test", "用例", "case"]
                for tag in tags:
                    if isinstance(tag, str) and tag.lower() in meaningless_tags:
                        issues.append({
                            "testcase_id": tc_id,
                            "testcase_name": tc_name,
                            "issue_type": IssueType.MEANINGLESS_TAG,
                            "severity": IssueSeverity.LOW,
                            "description": f"标签'{tag}'缺乏具体意义",
                            "suggestion": "请使用更具体、有意义的标签",
                            "location": "tags"
                        })
                        score -= 0.05
            
            # 检查优先级合理性
            priority = testcase.get("priority", "")
            test_type = testcase.get("type", "")
            
            # 根据测试类型判断优先级是否合理
            if test_type == "security" and priority not in ["high", "critical"]:
                issues.append({
                    "testcase_id": tc_id,
                    "testcase_name": tc_name,
                    "issue_type": IssueType.PRIORITY_MISMATCH,
                    "severity": IssueSeverity.MEDIUM,
                    "description": "安全测试用例优先级可能过低",
                    "suggestion": "安全相关测试用例建议设置为高优先级",
                    "location": "priority"
                })
                score -= 0.15
            
            # 检查分类准确性
            if test_type and test_type not in ["functional", "performance", "security", "usability", "integration", "unit", "api", "ui", "boundary", "negative", "scenario"]:
                issues.append({
                    "testcase_id": tc_id,
                    "testcase_name": tc_name,
                    "issue_type": IssueType.INVALID_TYPE,
                    "severity": IssueSeverity.LOW,
                    "description": f"测试类型'{test_type}'可能不准确",
                    "suggestion": "请使用标准的测试类型分类",
                    "location": "type"
                })
                score -= 0.1
            
            return issues, max(0.0, score)
            
        except Exception as e:
            self.logger.error(f"可维护性检查失败: {str(e)}")
            return issues, 0.0
    
    async def _check_business_logic(
        self,
        testcase: Dict[str, Any],
        all_testcases: List[Dict[str, Any]]
    ) -> Tuple[List[Dict[str, Any]], float]:
        """检查业务逻辑合理性"""
        issues = []
        score = 1.0
        
        try:
            tc_id = testcase.get("id", "未知")
            tc_name = testcase.get("name", "未命名测试用例")
            
            # 检查需求追溯
            requirement_ids = testcase.get("requirement_ids", [])
            if not requirement_ids:
                issues.append({
                    "testcase_id": tc_id,
                    "testcase_name": tc_name,
                    "issue_type": IssueType.MISSING_TRACEABILITY,
                    "severity": IssueSeverity.HIGH,
                    "description": "缺少需求追溯关系",
                    "suggestion": "请明确测试用例关联的需求ID",
                    "location": "requirement_ids"
                })
                score -= 0.3
            
            # 检查场景覆盖
            coverage_scenarios = testcase.get("coverage_scenarios", [])
            if not coverage_scenarios:
                issues.append({
                    "testcase_id": tc_id,
                    "testcase_name": tc_name,
                    "issue_type": IssueType.MISSING_SCENARIO_COVERAGE,
                    "severity": IssueSeverity.MEDIUM,
                    "description": "未明确覆盖的测试场景",
                    "suggestion": "请明确测试用例覆盖的具体测试场景",
                    "location": "coverage_scenarios"
                })
                score -= 0.2
            
            # 检查测试用例独立性
            tc_description = testcase.get("description", "").lower()
            tc_steps = str(testcase.get("test_steps", [])).lower()
            
            dependency_keywords = ["依赖", "前面", "之前", "先执行", "基于"]
            for keyword in dependency_keywords:
                if keyword in tc_description or keyword in tc_steps:
                    issues.append({
                        "testcase_id": tc_id,
                        "testcase_name": tc_name,
                        "issue_type": IssueType.DEPENDENCY_ISSUE,
                        "severity": IssueSeverity.MEDIUM,
                        "description": f"测试用例可能存在依赖关系: 发现关键词'{keyword}'",
                        "suggestion": "测试用例应该相互独立，避免依赖其他测试用例的执行结果",
                        "location": "description_or_steps"
                    })
                    score -= 0.15
            
            # 检查重复性
            similar_testcases = []
            for other_tc in all_testcases:
                if other_tc.get("id") != tc_id:
                    other_name = other_tc.get("name", "").lower()
                    if tc_name.lower() in other_name or other_name in tc_name.lower():
                        similar_testcases.append(other_tc.get("name", "未命名"))
            
            if similar_testcases:
                issues.append({
                    "testcase_id": tc_id,
                    "testcase_name": tc_name,
                    "issue_type": IssueType.DUPLICATE_TESTCASE,
                    "severity": IssueSeverity.LOW,
                    "description": f"可能存在重复的测试用例: {', '.join(similar_testcases[:3])}",
                    "suggestion": "请检查是否与其他测试用例重复，考虑合并或明确区别",
                    "location": "name"
                })
                score -= 0.1
            
            return issues, max(0.0, score)
            
        except Exception as e:
            self.logger.error(f"业务逻辑检查失败: {str(e)}")
            return issues, 0.0
    
    async def _generate_recommendations(
        self,
        issues: List[Dict[str, Any]],
        quality_metrics: Dict[str, Any],
        context: Dict[str, Any]
    ) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        try:
            # 基于问题统计生成建议
            if quality_metrics.get("failed_testcases", 0) > 0:
                fail_rate = quality_metrics["failed_testcases"] / quality_metrics["total_testcases"]
                if fail_rate > 0.5:
                    recommendations.append(f"超过50%的测试用例未通过评审，建议重新审视测试用例设计方法和质量标准")
                elif fail_rate > 0.3:
                    recommendations.append(f"有{quality_metrics['failed_testcases']}个测试用例未通过评审，建议优先修复高严重性问题")
            
            # 基于质量分数生成建议
            avg_score = quality_metrics.get("average_quality_score", 0)
            if avg_score < 0.6:
                recommendations.append("测试用例整体质量偏低，建议加强测试用例设计培训和评审流程")
            elif avg_score < 0.8:
                recommendations.append("测试用例质量有待提升，建议重点关注可执行性和业务逻辑合理性")
            
            # 基于具体问题类型生成建议
            issue_types = {}
            for issue in issues:
                issue_type = issue.get("issue_type", "unknown")
                issue_types[issue_type] = issue_types.get(issue_type, 0) + 1
            
            if issue_types.get(IssueType.MISSING_FIELD, 0) > 0:
                recommendations.append("多个测试用例存在字段缺失，建议使用测试用例模板确保信息完整性")
            
            if issue_types.get(IssueType.AMBIGUOUS_STEP, 0) > 0:
                recommendations.append("部分测试步骤描述模糊，建议使用具体、可操作的动作描述")
            
            if issue_types.get(IssueType.MISSING_TRACEABILITY, 0) > 0:
                recommendations.append("缺少需求追溯关系，建议建立完整的需求-测试用例追溯矩阵")
            
            if issue_types.get(IssueType.DEPENDENCY_ISSUE, 0) > 0:
                recommendations.append("存在测试用例依赖问题，建议重新设计确保测试用例独立性")
            
            # 基于质量维度生成建议
            if quality_metrics.get("completeness_score", 0) < 0.7:
                recommendations.append("测试用例完整性不足，建议补充必要的字段和详细信息")
            
            if quality_metrics.get("executability_score", 0) < 0.7:
                recommendations.append("测试用例可执行性有待改善，建议明确测试步骤和预期结果")
            
            if quality_metrics.get("maintainability_score", 0) < 0.7:
                recommendations.append("测试用例可维护性需要提升，建议规范命名和标签管理")
            
            if quality_metrics.get("business_logic_score", 0) < 0.7:
                recommendations.append("业务逻辑合理性需要加强，建议完善需求追溯和场景覆盖")
            
            # 基于上下文生成建议
            project_type = context.get("project_type", "")
            if project_type == "security" and any("security" in str(issue) for issue in issues):
                recommendations.append("安全类项目建议增加安全测试用例的专项评审")
            
            if not recommendations:
                recommendations.append("测试用例质量良好，建议保持当前的设计和评审标准")
            
        except Exception as e:
            self.logger.error(f"生成改进建议失败: {str(e)}")
            recommendations.append("无法生成改进建议，请手动检查评审结果")
        
        return recommendations
    
    async def _update_testcase_status(
        self,
        testcases: List[Dict[str, Any]],
        review_result: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """更新测试用例状态"""
        try:
            reviewed_cases = []
            issues_by_tc = {}
            
            # 按测试用例分组问题
            for issue in review_result.get("issues", []):
                tc_id = issue.get("testcase_id", "unknown")
                if tc_id not in issues_by_tc:
                    issues_by_tc[tc_id] = []
                issues_by_tc[tc_id].append(issue)
            
            for testcase in testcases:
                tc_id = testcase.get("id", "unknown")
                tc_issues = issues_by_tc.get(tc_id, [])
                
                # 判断测试用例状态
                critical_issues = [i for i in tc_issues if i.get("severity") == IssueSeverity.CRITICAL]
                high_issues = [i for i in tc_issues if i.get("severity") == IssueSeverity.HIGH]
                
                if critical_issues:
                    new_status = TestCaseStatus.REJECTED
                elif high_issues:
                    new_status = TestCaseStatus.REVIEW
                elif tc_issues:
                    new_status = TestCaseStatus.REVIEW
                else:
                    new_status = TestCaseStatus.APPROVED
                
                # 更新测试用例
                updated_testcase = testcase.copy()
                updated_testcase["status"] = new_status
                updated_testcase["review_issues"] = tc_issues
                updated_testcase["reviewed_at"] = datetime.now().isoformat()
                updated_testcase["reviewer_agent"] = self.agent_id
                
                reviewed_cases.append(updated_testcase)
                
                self.logger.info(f"测试用例 {tc_id} 状态更新为: {new_status}")
            
            return reviewed_cases
            
        except Exception as e:
            self.logger.error(f"更新测试用例状态失败: {str(e)}")
            return testcases
    
    async def _update_remaining_scenarios(
        self,
        remaining_scenarios: List[str],
        reviewed_cases: List[Dict[str, Any]],
        review_result: Dict[str, Any]
    ) -> List[str]:
        """更新剩余场景"""
        try:
            # 获取已通过评审的测试用例覆盖的场景
            covered_scenarios = set()
            
            for testcase in reviewed_cases:
                if testcase.get("status") == TestCaseStatus.APPROVED:
                    coverage_scenarios = testcase.get("coverage_scenarios", [])
                    covered_scenarios.update(coverage_scenarios)
            
            # 从剩余场景中移除已覆盖的场景
            updated_remaining = []
            for scenario in remaining_scenarios:
                if isinstance(scenario, dict):
                    scenario_name = scenario.get("scenario", "")
                else:
                    scenario_name = str(scenario)
                
                if scenario_name not in covered_scenarios:
                    updated_remaining.append(scenario)
            
            self.logger.info(f"剩余场景更新: {len(remaining_scenarios)} -> {len(updated_remaining)}")
            return updated_remaining
            
        except Exception as e:
            self.logger.error(f"更新剩余场景失败: {str(e)}")
            return remaining_scenarios
    
    async def _update_traceability_matrix(self, reviewed_cases: List[Dict[str, Any]]):
        """更新追溯矩阵"""
        try:
            for testcase in reviewed_cases:
                if testcase.get("status") == TestCaseStatus.APPROVED:
                    # 创建TestCaseModel对象
                    tc_model = TestCaseModel(**testcase)
                    await self.traceability_manager.add_testcase(self.session_id, tc_model)
            
            self.logger.info(f"追溯矩阵更新完成: {len(reviewed_cases)} 个测试用例")
            
        except Exception as e:
            self.logger.error(f"更新追溯矩阵失败: {str(e)}")
    
    async def _evaluate_has_more(
        self,
        remaining_scenarios: List[str],
        review_result: Dict[str, Any]
    ) -> bool:
        """评估是否还有更多工作需要完成"""
        try:
            # 如果还有剩余场景，需要继续生成测试用例
            if remaining_scenarios:
                return True
            
            # 如果有被拒绝的测试用例，可能需要重新生成
            rejected_count = sum(
                1 for issue in review_result.get("issues", [])
                if issue.get("severity") == IssueSeverity.CRITICAL
            )
            
            if rejected_count > 0:
                return True
            
            # 如果整体质量分数过低，可能需要改进
            avg_score = review_result.get("quality_metrics", {}).get("average_quality_score", 1.0)
            if avg_score < 0.6:
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"评估是否有更多工作失败: {str(e)}")
            return False
    
    async def export_review_report(
        self,
        review_result: Dict[str, Any],
        format: str = "json"
    ) -> str:
        """导出评审报告"""
        try:
            if format.lower() == "json":
                return json.dumps(review_result, ensure_ascii=False, indent=2)
            
            elif format.lower() == "markdown":
                return await self._generate_markdown_report(review_result)
            
            elif format.lower() == "html":
                return await self._generate_html_report(review_result)
            
            else:
                raise ValueError(f"不支持的导出格式: {format}")
                
        except Exception as e:
            self.logger.error(f"导出评审报告失败: {str(e)}")
            raise
    
    async def _generate_markdown_report(self, review_result: Dict[str, Any]) -> str:
        """生成Markdown格式的评审报告"""
        try:
            summary = review_result.get("summary", {})
            quality_metrics = review_result.get("quality_metrics", {})
            issues = review_result.get("issues", [])
            recommendations = review_result.get("recommendations", [])
            
            md_content = f"""# 测试用例评审报告

## 评审摘要
- **总测试用例数**: {quality_metrics.get('total_testcases', 0)}
- **通过评审**: {quality_metrics.get('passed_testcases', 0)}
- **未通过评审**: {quality_metrics.get('failed_testcases', 0)}
- **通过率**: {summary.get('pass_rate', 0):.1%}
- **整体质量**: {summary.get('overall_quality', '未知')}

## 质量指标
- **平均质量分数**: {quality_metrics.get('average_quality_score', 0):.2f}
- **完整性分数**: {quality_metrics.get('completeness_score', 0):.2f}
- **可执行性分数**: {quality_metrics.get('executability_score', 0):.2f}
- **可维护性分数**: {quality_metrics.get('maintainability_score', 0):.2f}
- **业务逻辑分数**: {quality_metrics.get('business_logic_score', 0):.2f}

## 问题统计
- **严重问题**: {summary.get('critical_issues', 0)}
- **高优先级问题**: {summary.get('high_issues', 0)}
- **中优先级问题**: {summary.get('medium_issues', 0)}
- **低优先级问题**: {summary.get('low_issues', 0)}

## 详细问题列表
"""
            
            for i, issue in enumerate(issues[:10], 1):  # 只显示前10个问题
                md_content += f"""
### 问题 {i}
- **测试用例**: {issue.get('testcase_name', '未知')}
- **问题类型**: {issue.get('issue_type', '未知')}
- **严重程度**: {issue.get('severity', '未知')}
- **问题描述**: {issue.get('description', '无描述')}
- **改进建议**: {issue.get('suggestion', '无建议')}
"""
            
            if len(issues) > 10:
                md_content += f"\n*注: 还有 {len(issues) - 10} 个问题未显示*\n"
            
            md_content += "\n## 改进建议\n"
            for i, rec in enumerate(recommendations, 1):
                md_content += f"{i}. {rec}\n"
            
            md_content += f"\n---\n*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*"
            
            return md_content
            
        except Exception as e:
            self.logger.error(f"生成Markdown报告失败: {str(e)}")
            return "# 评审报告生成失败\n\n无法生成评审报告，请检查数据格式。"
    
    async def _generate_html_report(self, review_result: Dict[str, Any]) -> str:
        """生成HTML格式的评审报告"""
        try:
            summary = review_result.get("summary", {})
            quality_metrics = review_result.get("quality_metrics", {})
            
            html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试用例评审报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .metrics {{ display: flex; justify-content: space-around; margin: 20px 0; }}
        .metric {{ text-align: center; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }}
        .issues {{ margin-top: 20px; }}
        .issue {{ margin: 10px 0; padding: 10px; border-left: 4px solid #ff6b6b; background-color: #fff5f5; }}
        .recommendations {{ margin-top: 20px; background-color: #f0f8ff; padding: 15px; border-radius: 5px; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>测试用例评审报告</h1>
        <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    
    <div class="metrics">
        <div class="metric">
            <h3>总用例数</h3>
            <p>{quality_metrics.get('total_testcases', 0)}</p>
        </div>
        <div class="metric">
            <h3>通过率</h3>
            <p>{summary.get('pass_rate', 0):.1%}</p>
        </div>
        <div class="metric">
            <h3>质量分数</h3>
            <p>{quality_metrics.get('average_quality_score', 0):.2f}</p>
        </div>
        <div class="metric">
            <h3>整体质量</h3>
            <p>{summary.get('overall_quality', '未知')}</p>
        </div>
    </div>
    
    <div class="recommendations">
        <h2>改进建议</h2>
        <ul>
"""
            
            for rec in review_result.get("recommendations", []):
                html_content += f"<li>{rec}</li>"
            
            html_content += """
        </ul>
    </div>
</body>
</html>
"""
            
            return html_content
            
        except Exception as e:
            self.logger.error(f"生成HTML报告失败: {str(e)}")
            return "<html><body><h1>报告生成失败</h1></body></html>"
    
    async def get_review_statistics(self) -> Dict[str, Any]:
        """获取评审统计信息"""
        try:
            state = await self.get_state()
            
            return {
                "agent_id": self.agent_id,
                "session_id": self.session_id,
                "status": state.status,
                "total_iterations": len(state.thinking_chain),
                "total_reflections": len(state.reflection_results),
                "last_activity": state.last_activity.isoformat() if state.last_activity else None,
                "processing_time": state.processing_time,
                "memory_usage": await self._get_memory_usage()
            }
            
        except Exception as e:
            self.logger.error(f"获取评审统计信息失败: {str(e)}")
            return {}
    
    async def _get_memory_usage(self) -> Dict[str, Any]:
        """获取内存使用情况"""
        try:
            # 这里可以添加实际的内存监控逻辑
            return {
                "vector_memory_size": 0,
                "cache_size": 0,
                "total_memory": 0
            }
        except Exception as e:
            self.logger.error(f"获取内存使用情况失败: {str(e)}")
            return {}
