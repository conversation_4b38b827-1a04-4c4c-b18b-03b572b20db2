#!/usr/bin/env python3
"""
MCP Demo 启动脚本
================

这是一个便捷的启动脚本，提供友好的用户界面来运行MCP计算器演示。

使用方法:
    python run_demo.py
"""

import os
import sys
import subprocess
import asyncio


def print_banner():
    """打印欢迎横幅"""
    print("=" * 60)
    print("🧮 MCP计算器演示 - Model Context Protocol Demo")
    print("=" * 60)
    print("这是一个最小但功能完整的MCP实现示例")
    print("展示了MCP协议的核心概念和工作原理")
    print("=" * 60)


def check_requirements():
    """检查系统要求"""
    print("🔍 正在检查系统要求...")
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ 错误: 需要Python 3.7或更高版本")
        print(f"   当前版本: Python {sys.version}")
        return False
    
    print(f"✅ Python版本: {sys.version.split()[0]}")
    
    # 检查依赖包
    try:
        import mcp
        print("✅ MCP库已安装")
    except ImportError:
        print("❌ MCP库未安装")
        print("💡 请运行: pip install -r requirements.txt")
        return False
    
    return True


def show_menu():
    """显示菜单选项"""
    print("\n📋 请选择运行模式:")
    print("1. 🎯 交互式模式 (推荐新手)")
    print("2. 🎬 批量演示模式")
    print("3. 📖 查看使用说明")
    print("4. 🔧 安装依赖包")
    print("5. 🚪 退出")
    print()


def install_dependencies():
    """安装依赖包"""
    print("📦 正在安装依赖包...")
    try:
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 依赖包安装成功!")
            return True
        else:
            print("❌ 依赖包安装失败:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ 安装过程中出错: {e}")
        return False


def show_usage():
    """显示使用说明"""
    print("\n📖 MCP计算器使用说明")
    print("=" * 40)
    print()
    print("🎯 交互式模式:")
    print("   • 启动后输入工具名称 (add, subtract, multiply, divide)")
    print("   • 按提示输入计算参数")
    print("   • 输入 'help' 查看详细帮助")
    print("   • 输入 'quit' 退出程序")
    print()
    print("🎬 批量演示模式:")
    print("   • 自动执行预定义的计算任务")
    print("   • 展示所有工具的功能")
    print("   • 包含错误处理演示")
    print()
    print("🔧 可用工具:")
    print("   • add      - 加法运算")
    print("   • subtract - 减法运算") 
    print("   • multiply - 乘法运算")
    print("   • divide   - 除法运算")
    print()
    print("💡 提示:")
    print("   • 所有工具都需要两个数字参数 a 和 b")
    print("   • 除法运算会自动检查除零错误")
    print("   • 支持小数和负数")
    print()


def run_interactive_mode():
    """运行交互式模式"""
    print("🎯 启动交互式模式...")
    print("💡 提示: 程序启动后输入 'help' 查看详细帮助")
    print()
    
    try:
        # 运行客户端
        subprocess.run([sys.executable, "mcp_client.py"])
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        print(f"❌ 运行失败: {e}")


def run_demo_mode():
    """运行演示模式"""
    print("🎬 启动批量演示模式...")
    print("💡 这将自动执行一系列计算任务")
    print()
    
    try:
        # 运行客户端演示模式
        subprocess.run([sys.executable, "mcp_client.py", "demo"])
    except KeyboardInterrupt:
        print("\n👋 演示被用户中断")
    except Exception as e:
        print(f"❌ 演示失败: {e}")


def main():
    """主函数"""
    print_banner()
    
    # 检查系统要求
    if not check_requirements():
        print("\n❌ 系统要求检查失败，无法继续")
        print("💡 请解决上述问题后重新运行")
        return
    
    while True:
        show_menu()
        
        try:
            choice = input("请输入选项 (1-5): ").strip()
            
            if choice == "1":
                run_interactive_mode()
                
            elif choice == "2":
                run_demo_mode()
                
            elif choice == "3":
                show_usage()
                
            elif choice == "4":
                if install_dependencies():
                    print("🔄 请重新启动脚本以使用新安装的依赖")
                
            elif choice == "5":
                print("👋 感谢使用MCP计算器演示!")
                break
                
            else:
                print("❌ 无效选择，请输入 1-5")
                
        except KeyboardInterrupt:
            print("\n👋 程序被用户中断")
            break
        except Exception as e:
            print(f"❌ 处理选择时出错: {e}")
        
        # 添加分隔线
        print("\n" + "-" * 60)


if __name__ == "__main__":
    """
    脚本执行入口
    
    这个脚本提供了一个友好的界面来运行MCP演示，
    包括系统要求检查、依赖安装和多种运行模式。
    """
    try:
        main()
    except Exception as e:
        print(f"❌ 脚本运行失败: {e}")
        sys.exit(1)