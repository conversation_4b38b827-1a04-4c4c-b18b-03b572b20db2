#!/usr/bin/env python3
"""
MCP基础测试
==========

最小化的MCP测试，用于诊断连接问题
"""

import asyncio
import sys
import json
from typing import Any, Dict, List

# MCP相关导入
import mcp.server.stdio
import mcp.types as types
from mcp.server import NotificationOptions, Server
from mcp.server.models import InitializationOptions


class MinimalMCPServer:
    """最小化的MCP服务器"""
    
    def __init__(self):
        self.server = Server("test-server")
        self._register_handlers()
    
    def _register_handlers(self):
        """注册处理器"""
        
        @self.server.list_tools()
        async def handle_list_tools() -> List[types.Tool]:
            """返回工具列表"""
            print("服务器：收到工具列表请求")
            return [
                types.Tool(
                    name="test",
                    description="测试工具",
                    inputSchema={
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                )
            ]
        
        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[types.TextContent]:
            """处理工具调用"""
            print(f"服务器：收到工具调用 {name}")
            return [types.TextContent(type="text", text="测试成功")]
    
    async def run(self):
        """运行服务器"""
        print("🚀 启动最小化MCP服务器...")
        print("📡 等待客户端连接...")
        
        try:
            async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
                print("📡 服务器已就绪，开始监听...")
                await self.server.run(
                    read_stream,
                    write_stream,
                    InitializationOptions(
                        server_name="test-server",
                        server_version="1.0.0",
                        capabilities=self.server.get_capabilities(
                            notification_options=NotificationOptions(),
                            experimental_capabilities={},
                        ),
                    ),
                )
        except Exception as e:
            print(f"❌ 服务器错误: {e}")
            import traceback
            traceback.print_exc()


async def test_server_only():
    """仅测试服务器启动"""
    print("🧪 测试服务器启动...")
    server = MinimalMCPServer()
    await server.run()


if __name__ == "__main__":
    try:
        print("=" * 50)
        print("MCP基础测试")
        print("=" * 50)
        asyncio.run(test_server_only())
    except KeyboardInterrupt:
        print("\n👋 测试结束")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc() 