# MCP 计算器 Demo

一个完全可工作的MCP（Model Context Protocol）演示项目，实现了基本的计算器功能。

## 🚀 功能特性

- ✅ **完整的MCP协议实现**：客户端和服务器之间的标准MCP通信
- ✅ **四种基本运算**：加法、减法、乘法、除法
- ✅ **错误处理**：除零检测、参数验证、类型检查
- ✅ **交互式界面**：用户友好的命令行交互
- ✅ **批量演示模式**：自动执行预定义的计算任务
- ✅ **详细的中文注释**：完整的代码文档和解释

## 📁 项目结构

```
mcp-demo/
├── mcp_server.py          # MCP服务器实现
├── mcp_client.py          # MCP客户端实现  
├── working_demo.py        # 集成测试版本
├── test_mcp_basic.py      # 基础测试工具
├── simple_mcp_demo.py     # 简化测试版本
├── run_demo.py            # 启动脚本
└── README.md              # 项目文档
```

## 🎯 使用方法

### 1. 交互式模式

```bash
python mcp_client.py
```

**功能**：
- 提供用户友好的交互界面
- 支持实时输入和计算
- 完整的帮助系统
- 参数验证和错误提示

**示例交互**：
```
🧮 欢迎使用MCP计算器!
💭 请输入命令 (help/quit/工具名): add

🔧 正在配置工具: add
请输入 a (第一个数字) [必需]: 123
请输入 b (第二个数字) [必需]: 456
🎯 计算结果: 123.0 + 456.0 = 579.0
```

### 2. 批量演示模式

```bash
python mcp_client.py demo
```

**功能**：
- 自动执行预定义的计算任务
- 展示所有基本运算功能
- 包含错误处理演示（除零测试）

### 3. 集成测试版本

```bash
python working_demo.py
```

**功能**：
- 单文件完整演示
- 自动执行所有测试用例
- 详细的执行日志

## 🔧 可用工具

| 工具名 | 描述 | 参数 |
|--------|------|------|
| `add` | 加法运算 | a: 第一个数字, b: 第二个数字 |
| `subtract` | 减法运算 | a: 被减数, b: 减数 |
| `multiply` | 乘法运算 | a: 第一个数字, b: 第二个数字 |
| `divide` | 除法运算 | a: 被除数, b: 除数(不能为0) |

## 📋 技术实现

### MCP协议特性

- **标准通信**：使用stdin/stdout进行JSON-RPC通信
- **工具发现**：动态获取服务器提供的工具列表
- **类型安全**：完整的参数验证和类型检查
- **错误处理**：优雅的异常处理和用户反馈

### 架构设计

```
┌─────────────────┐    MCP Protocol    ┌─────────────────┐
│   MCP Client    │ ←──────────────→   │   MCP Server    │
│                 │   (stdin/stdout)   │                 │
│ - 用户交互      │                    │ - 工具实现      │
│ - 会话管理      │                    │ - 计算逻辑      │
│ - 工具调用      │                    │ - 错误处理      │
└─────────────────┘                    └─────────────────┘
```

## 🛠️ 技术修复过程

### 原始问题
- **初始化失败**：MCP会话握手卡住
- **通信错误**：TaskGroup异常和BrokenResourceError
- **协议不兼容**：初始化参数格式错误

### 解决方案
1. **简化初始化**：使用基本的`session.initialize()`调用
2. **错误输出重定向**：将服务器日志输出到`stderr`避免干扰MCP通信
3. **连接管理优化**：正确使用`stdio_client`异步上下文管理器
4. **异常处理增强**：添加详细的错误诊断和用户友好的提示

### 关键修复点

```python
# ✅ 正确的初始化方式
await session.initialize()

# ✅ 正确的日志输出
print("服务器日志", file=sys.stderr)

# ✅ 正确的连接管理
async with stdio_client(server_params) as (read, write):
    async with ClientSession(read, write) as session:
        # 业务逻辑
```

## 🎉 测试结果

### 演示模式测试
```
✅ 加法: 10 + 5 = 15
✅ 减法: 10 - 3 = 7  
✅ 乘法: 4 × 6 = 24
✅ 除法: 20 ÷ 4 = 5.0
✅ 错误处理: 除数不能为0
```

### 交互式测试
```
✅ 用户输入验证
✅ 参数类型检查
✅ 帮助系统完整
✅ 优雅退出机制
```

## 💡 学习要点

1. **MCP协议理解**：标准的客户端-服务器通信模式
2. **异步编程**：正确使用Python asyncio和上下文管理器
3. **错误处理**：从诊断到修复的完整流程
4. **用户体验**：友好的交互界面和详细的反馈信息

## 🔍 扩展建议

- 添加更多数学函数（三角函数、对数等）
- 实现计算历史记录功能
- 支持复杂表达式解析
- 添加配置文件支持
- 实现Web界面版本

---

**状态**: ✅ 完全可工作  
**最后更新**: 2024年1月  
**测试环境**: Python 3.11, MCP 1.10.1