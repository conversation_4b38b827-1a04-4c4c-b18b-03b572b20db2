#!/usr/bin/env python3
"""
简单MCP Demo - 集成版本
====================

这是一个最简单的MCP演示，集成了客户端和服务器功能。
用于测试MCP协议的基本通信。
"""

import asyncio
import sys
from typing import Any, Dict, List

# MCP相关导入
import mcp.server.stdio
import mcp.types as types
from mcp.server import NotificationOptions, Server
from mcp.server.models import InitializationOptions
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client


class SimpleMCPDemo:
    """简单的MCP计算器演示"""
    
    def __init__(self):
        self.server = Server("simple-calculator")
        self._setup_server()
    
    def _setup_server(self):
        """设置服务器处理器"""
        
        @self.server.list_tools()
        async def handle_list_tools() -> List[types.Tool]:
            """返回可用工具列表"""
            print("📋 [服务器] 客户端请求工具列表")
            return [
                types.Tool(
                    name="add",
                    description="两数相加",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "a": {"type": "number", "description": "第一个数"},
                            "b": {"type": "number", "description": "第二个数"}
                        },
                        "required": ["a", "b"]
                    }
                )
            ]
        
        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[types.TextContent]:
            """处理工具调用"""
            print(f"🔧 [服务器] 调用工具: {name}, 参数: {arguments}")
            
            if name == "add":
                a = arguments.get("a", 0)
                b = arguments.get("b", 0)
                result = a + b
                response = f"结果: {a} + {b} = {result}"
                print(f"✅ [服务器] 计算完成: {response}")
                return [types.TextContent(type="text", text=response)]
            
            return [types.TextContent(type="text", text="未知工具")]
    
    async def run_server(self):
        """运行服务器"""
        print("🚀 [服务器] 启动简单MCP服务器...")
        
        async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
            await self.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="simple-calculator",
                    server_version="1.0.0",
                    capabilities=self.server.get_capabilities(
                        notification_options=NotificationOptions(),
                        experimental_capabilities={},
                    ),
                ),
            )


async def run_client_test():
    """运行客户端测试"""
    print("\n" + "=" * 50)
    print("🎯 [客户端] 开始测试")
    print("=" * 50)
    
    # 配置服务器参数
    server_params = StdioServerParameters(
        command="python",
        args=["simple_mcp_demo.py", "server"],
        env=None
    )
    
    try:
        print("📡 [客户端] 连接到服务器...")
        async with stdio_client(server_params) as (read, write):
            print("✅ [客户端] 连接成功")
            
            async with ClientSession(read, write) as session:
                print("🔗 [客户端] 初始化会话...")
                
                # 简单初始化
                await session.initialize()
                print("✅ [客户端] 初始化完成")
                
                # 获取工具列表
                print("🔍 [客户端] 获取工具列表...")
                tools_result = await session.list_tools()
                print(f"📋 [客户端] 发现 {len(tools_result.tools)} 个工具:")
                
                for tool in tools_result.tools:
                    print(f"  • {tool.name}: {tool.description}")
                
                # 调用工具
                print("\n🔧 [客户端] 调用加法工具...")
                result = await session.call_tool("add", {"a": 5, "b": 3})
                
                if result.content:
                    for content in result.content:
                        if hasattr(content, 'text'):
                            print(f"🎯 [客户端] {content.text}")
                
                print("\n✅ [客户端] 测试完成!")
                
    except Exception as e:
        print(f"❌ [客户端] 测试失败: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == "server":
        # 运行服务器模式
        demo = SimpleMCPDemo()
        await demo.run_server()
    else:
        # 运行客户端测试
        await run_client_test()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 程序已停止")
    except Exception as e:
        print(f"❌ 程序错误: {e}")
        sys.exit(1) 