#!/usr/bin/env python3
"""
可工作的MCP Demo
==============

这是一个完整可工作的MCP演示，包含：
1. MCP服务器（通过子进程运行）
2. MCP客户端（自动测试模式）
3. 完整的计算器功能演示
"""

import asyncio
import sys
import json
from typing import Any, Dict, List

# MCP相关导入
import mcp.server.stdio
import mcp.types as types
from mcp.server import NotificationOptions, Server
from mcp.server.models import InitializationOptions
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client


class WorkingMCPServer:
    """可工作的MCP计算器服务器"""
    
    def __init__(self):
        self.server = Server("working-calculator")
        self._register_handlers()
    
    def _register_handlers(self):
        """注册MCP处理器"""
        
        @self.server.list_tools()
        async def handle_list_tools() -> List[types.Tool]:
            """返回可用工具列表"""
            print("🔧 [服务器] 客户端请求工具列表", file=sys.stderr)
            
            tools = [
                types.Tool(
                    name="add",
                    description="加法运算",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "a": {"type": "number", "description": "第一个数字"},
                            "b": {"type": "number", "description": "第二个数字"}
                        },
                        "required": ["a", "b"]
                    }
                ),
                types.Tool(
                    name="subtract",
                    description="减法运算",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "a": {"type": "number", "description": "被减数"},
                            "b": {"type": "number", "description": "减数"}
                        },
                        "required": ["a", "b"]
                    }
                ),
                types.Tool(
                    name="multiply",
                    description="乘法运算",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "a": {"type": "number", "description": "第一个数字"},
                            "b": {"type": "number", "description": "第二个数字"}
                        },
                        "required": ["a", "b"]
                    }
                ),
                types.Tool(
                    name="divide",
                    description="除法运算",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "a": {"type": "number", "description": "被除数"},
                            "b": {"type": "number", "description": "除数"}
                        },
                        "required": ["a", "b"]
                    }
                )
            ]
            
            print(f"📋 [服务器] 返回 {len(tools)} 个工具", file=sys.stderr)
            return tools
        
        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[types.TextContent]:
            """处理工具调用"""
            print(f"🔧 [服务器] 调用工具: {name}, 参数: {arguments}", file=sys.stderr)
            
            try:
                a = arguments.get("a")
                b = arguments.get("b")
                
                if a is None or b is None:
                    result = "错误: 缺少必需参数 a 或 b"
                elif name == "add":
                    result = f"结果: {a} + {b} = {a + b}"
                elif name == "subtract":
                    result = f"结果: {a} - {b} = {a - b}"
                elif name == "multiply":
                    result = f"结果: {a} × {b} = {a * b}"
                elif name == "divide":
                    if b == 0:
                        result = "错误: 除数不能为0"
                    else:
                        result = f"结果: {a} ÷ {b} = {a / b}"
                else:
                    result = f"错误: 未知工具 {name}"
                
                print(f"✅ [服务器] 计算完成: {result}", file=sys.stderr)
                return [types.TextContent(type="text", text=result)]
                
            except Exception as e:
                error_msg = f"错误: {str(e)}"
                print(f"❌ [服务器] 计算错误: {error_msg}", file=sys.stderr)
                return [types.TextContent(type="text", text=error_msg)]
    
    async def run(self):
        """运行MCP服务器"""
        print("🚀 [服务器] 启动计算器MCP服务器...", file=sys.stderr)
        
        async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
            print("📡 [服务器] 服务器就绪，等待客户端连接...", file=sys.stderr)
            await self.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="working-calculator",
                    server_version="1.0.0",
                    capabilities=self.server.get_capabilities(
                        notification_options=NotificationOptions(),
                        experimental_capabilities={},
                    ),
                ),
            )


async def run_demo():
    """运行完整的MCP演示"""
    print("🎬 MCP计算器演示开始")
    print("=" * 50)
    
    # 配置服务器参数
    server_params = StdioServerParameters(
        command="python",
        args=["working_demo.py", "server"],
        env=None
    )
    
    try:
        print("📡 [客户端] 启动服务器并建立连接...")
        
        async with stdio_client(server_params) as (read, write):
            print("✅ [客户端] 连接成功")
            
            async with ClientSession(read, write) as session:
                print("🔗 [客户端] 初始化会话...")
                
                # 初始化会话
                await session.initialize()
                print("✅ [客户端] 初始化完成")
                
                # 获取工具列表
                print("\n🔍 [客户端] 获取可用工具...")
                tools_result = await session.list_tools()
                tools = tools_result.tools
                
                print(f"📋 [客户端] 发现 {len(tools)} 个可用工具:")
                for tool in tools:
                    print(f"  • {tool.name}: {tool.description}")
                
                # 执行一系列计算演示
                print(f"\n🧮 [客户端] 开始计算演示:")
                print("-" * 30)
                
                test_cases = [
                    ("add", {"a": 10, "b": 5}, "加法: 10 + 5"),
                    ("subtract", {"a": 15, "b": 3}, "减法: 15 - 3"),
                    ("multiply", {"a": 4, "b": 7}, "乘法: 4 × 7"),
                    ("divide", {"a": 20, "b": 4}, "除法: 20 ÷ 4"),
                    ("divide", {"a": 10, "b": 3}, "除法: 10 ÷ 3 (小数)"),
                    ("divide", {"a": 10, "b": 0}, "除法: 10 ÷ 0 (错误测试)"),
                ]
                
                for i, (tool_name, arguments, description) in enumerate(test_cases, 1):
                    print(f"\n📊 测试 {i}: {description}")
                    
                    try:
                        result = await session.call_tool(tool_name, arguments)
                        
                        if result.content:
                            for content in result.content:
                                if hasattr(content, 'text'):
                                    print(f"   🎯 {content.text}")
                        else:
                            print("   ⚠️  没有返回内容")
                            
                    except Exception as e:
                        print(f"   ❌ 调用失败: {e}")
                
                print(f"\n🎉 [客户端] 演示完成！")
                print("=" * 50)
                
    except Exception as e:
        print(f"❌ [客户端] 演示失败: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == "server":
        # 服务器模式（作为子进程运行）
        server = WorkingMCPServer()
        await server.run()
    else:
        # 客户端演示模式
        await run_demo()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 演示已停止")
    except Exception as e:
        print(f"❌ 程序错误: {e}")
        sys.exit(1) 