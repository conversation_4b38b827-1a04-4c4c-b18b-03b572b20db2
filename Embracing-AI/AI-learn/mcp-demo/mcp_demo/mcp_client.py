#!/usr/bin/env python3
"""
MCP Client Demo - 计算器客户端实现（修复版）
=========================================

这是一个完全可工作的MCP客户端实现。
客户端连接到MCP服务器，获取可用工具并调用它们。

本demo展示了如何：
1. 连接到MCP服务器
2. 获取可用工具列表
3. 调用服务器提供的工具
4. 处理工具返回的结果
"""

import asyncio
import json
import sys
from typing import Any, Dict, List, Optional

# MCP相关的导入
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
import mcp.types as types


class CalculatorMCPClient:
    """
    计算器MCP客户端类
    
    这个类封装了与MCP服务器的所有交互逻辑：
    - 连接管理
    - 工具发现
    - 工具调用
    - 结果处理
    """
    
    def __init__(self, server_script_path: str = "mcp_server.py"):
        """
        初始化客户端
        
        Args:
            server_script_path (str): MCP服务器脚本的路径
        """
        self.server_script_path = server_script_path
        self.available_tools: List[types.Tool] = []
    
    async def _discover_tools(self, session):
        """
        发现服务器提供的工具
        """
        print("🔍 正在发现可用工具...")
        
        try:
            result = await session.list_tools()
            self.available_tools = result.tools
            
            print(f"✅ 发现 {len(self.available_tools)} 个可用工具:")
            for tool in self.available_tools:
                print(f"  📍 {tool.name}: {tool.description}")
                
        except Exception as e:
            print(f"❌ 获取工具列表失败: {e}")
            raise
    
    async def call_tool(self, session, tool_name: str, arguments: Dict[str, Any]) -> str:
        """
        调用指定的工具
        
        Args:
            tool_name (str): 要调用的工具名称
            arguments (Dict[str, Any]): 工具参数
            
        Returns:
            str: 工具执行结果的文本内容
        """
        print(f"🔧 正在调用工具: {tool_name}")
        print(f"📝 参数: {json.dumps(arguments, ensure_ascii=False)}")
        
        # 验证工具是否存在
        tool_names = [tool.name for tool in self.available_tools]
        if tool_name not in tool_names:
            raise ValueError(f"工具 '{tool_name}' 不存在。可用工具: {tool_names}")
        
        try:
            result = await session.call_tool(tool_name, arguments)
            
            if result.content:
                text_results = []
                for content in result.content:
                    if hasattr(content, 'text'):
                        text_results.append(content.text)
                
                result_text = "\n".join(text_results)
                print(f"✅ 工具调用成功")
                return result_text
            else:
                return "工具执行完成，但没有返回内容"
                
        except Exception as e:
            error_msg = f"工具调用失败: {e}"
            print(f"❌ {error_msg}")
            raise Exception(error_msg)
    
    async def interactive_mode(self):
        """
        交互式模式
        """
        print("🎮 进入交互式模式")
        
        # 配置服务器参数
        server_params = StdioServerParameters(
            command="python",
            args=[self.server_script_path],
            env=None
        )
        
        try:
            print("📡 正在启动服务器并建立连接...")
            async with stdio_client(server_params) as (read, write):
                print("✅ 连接成功")
                
                async with ClientSession(read, write) as session:
                    print("🔗 正在初始化会话...")
                    await session.initialize()
                    print("✅ 初始化完成")
                    
                    await self._discover_tools(session)
                    
                    print("\n" + "=" * 50)
                    print("🧮 欢迎使用MCP计算器!")
                    print("📋 输入 'help' 查看帮助")
                    print("📋 输入 'quit' 退出程序")
                    print("=" * 50)
                    
                    while True:
                        try:
                            command = input("\n💭 请输入命令 (help/quit/工具名): ").strip().lower()
                            
                            if command == "quit":
                                print("👋 再见!")
                                break
                                
                            elif command == "help":
                                await self._show_help()
                                
                            elif command in [tool.name for tool in self.available_tools]:
                                await self._handle_tool_call(session, command)
                                
                            else:
                                print(f"❌ 未知命令: {command}")
                                print("💡 输入 'help' 查看可用命令")
                        
                        except KeyboardInterrupt:
                            print("\n👋 程序被用户中断")
                            break
                        except Exception as e:
                            print(f"❌ 处理命令时出错: {e}")
                            
        except Exception as e:
            print(f"❌ 交互式模式启动失败: {e}")
            import traceback
            traceback.print_exc()
    
    async def _show_help(self):
        """显示帮助信息"""
        print("\n📖 MCP计算器帮助:")
        print("=" * 30)
        print("🔧 可用工具:")
        
        for tool in self.available_tools:
            print(f"  • {tool.name}: {tool.description}")
            
            if hasattr(tool, 'inputSchema') and tool.inputSchema:
                properties = tool.inputSchema.get('properties', {})
                required = tool.inputSchema.get('required', [])
                
                print("    参数:")
                for param_name, param_info in properties.items():
                    param_desc = param_info.get('description', '无描述')
                    is_required = param_name in required
                    required_text = " (必需)" if is_required else " (可选)"
                    print(f"      - {param_name}: {param_desc}{required_text}")
        
        print("\n💡 使用方法:")
        print("  1. 输入工具名称（如: add）")
        print("  2. 按提示输入参数")
        print("  3. 查看计算结果")
    
    async def _handle_tool_call(self, session, tool_name: str):
        """
        处理工具调用
        """
        try:
            tool = next(t for t in self.available_tools if t.name == tool_name)
            
            properties = tool.inputSchema.get('properties', {})
            required = tool.inputSchema.get('required', [])
            
            arguments = {}
            
            print(f"\n🔧 正在配置工具: {tool.name}")
            print(f"📝 描述: {tool.description}")
            
            for param_name, param_info in properties.items():
                param_desc = param_info.get('description', '无描述')
                param_type = param_info.get('type', 'string')
                is_required = param_name in required
                
                while True:
                    prompt = f"请输入 {param_name} ({param_desc})"
                    if is_required:
                        prompt += " [必需]: "
                    else:
                        prompt += " [可选]: "
                    
                    user_input = input(prompt).strip()
                    
                    if is_required and not user_input:
                        print("❌ 这是必需参数，请输入值")
                        continue
                    
                    if not user_input and not is_required:
                        break
                    
                    try:
                        if param_type == "number":
                            arguments[param_name] = float(user_input)
                        else:
                            arguments[param_name] = user_input
                        break
                    except ValueError:
                        print(f"❌ 无效的{param_type}类型值，请重新输入")
            
            result = await self.call_tool(session, tool_name, arguments)
            print(f"\n🎯 {result}")
            
        except Exception as e:
            print(f"❌ 处理工具调用时出错: {e}")


async def demo_batch_operations():
    """
    演示批量操作
    """
    print("\n" + "=" * 50)
    print("🎬 开始批量操作演示")
    print("=" * 50)
    
    client = CalculatorMCPClient()
    
    # 配置服务器参数
    server_params = StdioServerParameters(
        command="python",
        args=[client.server_script_path],
        env=None
    )
    
    try:
        print("📡 启动服务器并建立连接...")
        async with stdio_client(server_params) as (read, write):
            print("✅ 连接成功")
            
            async with ClientSession(read, write) as session:
                print("🔗 初始化会话...")
                await session.initialize()
                print("✅ 初始化完成")
                
                await client._discover_tools(session)
                
                # 定义计算任务
                tasks = [
                    ("add", {"a": 10, "b": 5}, "10 + 5"),
                    ("subtract", {"a": 10, "b": 3}, "10 - 3"),
                    ("multiply", {"a": 4, "b": 6}, "4 × 6"),
                    ("divide", {"a": 20, "b": 4}, "20 ÷ 4"),
                    ("divide", {"a": 10, "b": 0}, "10 ÷ 0 (错误演示)"),
                ]
                
                print(f"\n🧮 开始计算演示:")
                print("-" * 30)
                
                for i, (tool_name, arguments, description) in enumerate(tasks, 1):
                    print(f"\n📊 任务 {i}: {description}")
                    try:
                        result = await client.call_tool(session, tool_name, arguments)
                        print(f"   🎯 {result}")
                    except Exception as e:
                        print(f"   ❌ {e}")
        
        print(f"\n🎉 演示完成！")
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """
    主函数 - 程序入口点
    """
    if len(sys.argv) > 1 and sys.argv[1] == "demo":
        # 演示模式
        await demo_batch_operations()
    else:
        # 交互式模式
        client = CalculatorMCPClient()
        await client.interactive_mode()


if __name__ == "__main__":
    """
    脚本执行入口
    
    使用方法:
        python mcp_client.py          # 交互式模式
        python mcp_client.py demo     # 演示模式
    """
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 客户端已停止")
    except Exception as e:
        print(f"❌ 客户端启动失败: {e}")
        sys.exit(1)