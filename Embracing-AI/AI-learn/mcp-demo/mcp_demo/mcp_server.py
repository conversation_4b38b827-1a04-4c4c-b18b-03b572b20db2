#!/usr/bin/env python3
"""
MCP Server Demo - 计算器MCP服务器（修复版）
======================================

这是一个完全可工作的MCP服务器实现。
提供基本的数学运算功能：加减乘除。

主要组件：
1. MCP Server - 提供计算工具
2. 工具定义 - 加法、减法、乘法、除法
3. 错误处理 - 处理除零等异常情况
"""

import asyncio
import sys
from typing import Any, Dict, List

# MCP相关的导入
import mcp.server.stdio
import mcp.types as types
from mcp.server import NotificationOptions, Server
from mcp.server.models import InitializationOptions


class CalculatorMCPServer:
    """
    计算器MCP服务器类
    
    这个类封装了所有的计算器功能，包括：
    - 基本数学运算（加减乘除）
    - 工具注册和管理
    - 请求处理
    """
    
    def __init__(self):
        """初始化服务器"""
        self.server = Server("calculator-demo")
        self._register_handlers()
        
        # 定义可用的工具列表
        self.tools = [
            types.Tool(
                name="add",
                description="将两个数字相加",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "a": {"type": "number", "description": "第一个数字"},
                        "b": {"type": "number", "description": "第二个数字"}
                    },
                    "required": ["a", "b"]
                }
            ),
            types.Tool(
                name="subtract",
                description="将第一个数字减去第二个数字",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "a": {"type": "number", "description": "被减数"},
                        "b": {"type": "number", "description": "减数"}
                    },
                    "required": ["a", "b"]
                }
            ),
            types.Tool(
                name="multiply",
                description="将两个数字相乘",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "a": {"type": "number", "description": "第一个数字"},
                        "b": {"type": "number", "description": "第二个数字"}
                    },
                    "required": ["a", "b"]
                }
            ),
            types.Tool(
                name="divide",
                description="将第一个数字除以第二个数字",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "a": {"type": "number", "description": "被除数"},
                        "b": {"type": "number", "description": "除数（不能为0）"}
                    },
                    "required": ["a", "b"]
                }
            )
        ]
    
    def _register_handlers(self):
        """
        注册所有的MCP处理器
        
        MCP协议要求服务器实现以下基本处理器：
        1. list_tools - 列出可用工具
        2. call_tool - 调用具体工具
        """
        
        @self.server.list_tools()
        async def handle_list_tools() -> List[types.Tool]:
            """
            处理工具列表请求
            
            Returns:
                List[types.Tool]: 可用工具的列表
            """
            print("📋 [服务器] 客户端请求工具列表", file=sys.stderr)
            return self.tools
        
        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[types.TextContent]:
            """
            处理工具调用请求
            
            Args:
                name (str): 要调用的工具名称
                arguments (Dict[str, Any]): 工具参数
                
            Returns:
                List[types.TextContent]: 工具执行结果
            """
            print(f"🔧 [服务器] 客户端调用工具: {name}，参数: {arguments}", file=sys.stderr)
            
            try:
                # 提取参数
                a = arguments.get("a")
                b = arguments.get("b")
                
                # 参数验证
                if a is None or b is None:
                    raise ValueError("缺少必需的参数 a 或 b")
                
                # 根据工具名称执行相应操作
                if name == "add":
                    result = a + b
                    operation = f"{a} + {b} = {result}"
                    
                elif name == "subtract":
                    result = a - b
                    operation = f"{a} - {b} = {result}"
                    
                elif name == "multiply":
                    result = a * b
                    operation = f"{a} × {b} = {result}"
                    
                elif name == "divide":
                    if b == 0:
                        raise ZeroDivisionError("除数不能为0")
                    result = a / b
                    operation = f"{a} ÷ {b} = {result}"
                    
                else:
                    raise ValueError(f"未知的工具: {name}")
                
                print(f"✅ [服务器] 计算结果: {operation}", file=sys.stderr)
                
                # 返回结果
                return [
                    types.TextContent(
                        type="text",
                        text=f"计算结果: {operation}"
                    )
                ]
                
            except ZeroDivisionError as e:
                error_msg = f"计算错误: {str(e)}"
                print(f"❌ [服务器] {error_msg}", file=sys.stderr)
                return [
                    types.TextContent(
                        type="text",
                        text=error_msg
                    )
                ]
                
            except (ValueError, TypeError) as e:
                error_msg = f"参数错误: {str(e)}"
                print(f"❌ [服务器] {error_msg}", file=sys.stderr)
                return [
                    types.TextContent(
                        type="text",
                        text=error_msg
                    )
                ]
            
            except Exception as e:
                error_msg = f"未知错误: {str(e)}"
                print(f"❌ [服务器] {error_msg}", file=sys.stderr)
                return [
                    types.TextContent(
                        type="text",
                        text=error_msg
                    )
                ]
    
    async def run(self):
        """
        启动MCP服务器
        
        这个方法启动服务器并处理来自标准输入/输出的MCP消息。
        """
        print("🚀 [服务器] 启动计算器MCP服务器...", file=sys.stderr)
        print("📡 [服务器] 服务器监听stdin/stdout进行MCP通信", file=sys.stderr)
        print("🔧 [服务器] 可用工具: add, subtract, multiply, divide", file=sys.stderr)
        print("=" * 50, file=sys.stderr)
        
        # 使用stdio传输运行服务器
        async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
            await self.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="calculator-demo",
                    server_version="1.0.0",
                    capabilities=self.server.get_capabilities(
                        notification_options=NotificationOptions(),
                        experimental_capabilities={},
                    ),
                ),
            )


async def main():
    """
    主函数 - 程序入口点
    
    创建并启动MCP服务器实例。
    """
    # 创建服务器实例
    calculator_server = CalculatorMCPServer()
    
    # 启动服务器
    await calculator_server.run()


if __name__ == "__main__":
    """
    脚本执行入口
    
    当直接运行此脚本时，启动MCP服务器。
    使用方法:
        python mcp_server.py
    """
    try:
        # 运行异步主函数
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 [服务器] 服务器已停止", file=sys.stderr)
    except Exception as e:
        print(f"❌ [服务器] 服务器启动失败: {e}", file=sys.stderr)
        sys.exit(1)