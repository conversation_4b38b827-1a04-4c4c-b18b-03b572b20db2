#!/usr/bin/env python3
"""
Google搜索MCP Demo启动脚本
提供多种使用方式的入口
"""

import asyncio
import sys
import os

# 在模块级别导入，确保与直接运行client.py的方式一致
try:
    from client import GoogleSearchClient
except ImportError:
    GoogleSearchClient = None  # 稍后处理导入错误

def print_banner():
    """打印欢迎横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════════╗
║                    🔍 Google搜索MCP演示程序                       ║
║                                                                  ║
║  这是一个基于MCP（Model Context Protocol）的Google搜索工具      ║
║  使用Playwright自动化浏览器，获取真实的Google搜索结果            ║
╚══════════════════════════════════════════════════════════════════╝
"""
    print(banner)

def print_usage():
    """打印使用说明"""
    usage = """
🚀 使用方式:
    python run.py                   # 交互式模式
    python run.py demo              # 演示模式
    python run.py help              # 显示帮助

📝 功能说明:
    • 交互式模式: 可以持续输入搜索关键字，实时获取结果
    • 演示模式: 自动执行预设的搜索示例
    • 每次搜索返回Google搜索结果的前3条

⚙️  技术栈:
    • MCP (Model Context Protocol) - AI工具集成协议
    • Playwright - 浏览器自动化
    • Python AsyncIO - 异步编程

💡 提示:
    • 首次运行需要下载浏览器驱动，可能需要一些时间
    • 确保网络连接正常，能够访问Google
    • 搜索过程中请耐心等待，浏览器操作需要时间
"""
    print(usage)

async def check_dependencies():
    """检查依赖是否安装"""
    missing_deps = []
    
    try:
        import mcp
    except ImportError:
        missing_deps.append("mcp")
    
    try:
        import playwright
    except ImportError:
        missing_deps.append("playwright")
    
    if missing_deps:
        print("❌ 缺少以下Python依赖:")
        for dep in missing_deps:
            print(f"   • {dep}")
        print("\n📦 请运行以下命令安装依赖:")
        print("   pip install -r requirements.txt")
        print("   playwright install chromium")
        return False
    
    # 轻量级检查：只验证Playwright包是否安装，浏览器检查在运行时进行
    try:
        from playwright.async_api import async_playwright
        print("✅ Playwright包检查通过")
        print("💡 浏览器可用性将在实际运行时验证")
    except ImportError:
        print("❌ Playwright Python包未安装")
        print("\n📦 请运行以下命令安装依赖:")
        print("   pip install -r requirements.txt")
        print("   playwright install chromium")
        return False
    
    return True

async def run_interactive():
    """运行交互式模式"""
    print("🎯 启动交互式模式...")
    print("⏳ 正在初始化，请稍候...\n")
    
    if GoogleSearchClient is None:
        print("❌ 无法导入GoogleSearchClient，请检查client.py文件是否存在")
        return
    
    client = GoogleSearchClient()
    await client.interactive_mode()

async def run_demo():
    """运行演示模式"""
    print("🎯 启动演示模式...")
    print("⏳ 正在初始化，请稍候...\n")
    
    if GoogleSearchClient is None:
        print("❌ 无法导入GoogleSearchClient，请检查client.py文件是否存在")
        return
    
    client = GoogleSearchClient()
    await client.demo_mode()

async def main():
    """主函数"""
    print_banner()
    
    # 解析命令行参数
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command in ['help', '-h', '--help']:
            print_usage()
            return
        elif command == 'demo':
            mode = 'demo'
        else:
            print(f"❌ 未知命令: {command}")
            print_usage()
            return
    else:
        mode = 'interactive'
    
    # 检查依赖
    if not await check_dependencies():
        return
    
    try:
        if mode == 'demo':
            await run_demo()
        else:
            await run_interactive()
    except KeyboardInterrupt:
        print("\n\n👋 程序被用户中断，正在清理资源...")
    except ImportError as e:
        print(f"\n❌ 导入错误: {e}")
        print("💡 请确保已正确安装所有依赖")
    except Exception as e:
        print(f"\n❌ 程序执行错误: {e}")
        print("💡 请检查网络连接和依赖安装")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        sys.exit(1) 