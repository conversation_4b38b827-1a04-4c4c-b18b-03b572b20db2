#!/usr/bin/env python3
"""
Google搜索MCP服务器
使用playwright自动化浏览器，在Google上搜索关键字并返回top3结果
"""

import sys
import asyncio
import json
import random
import time
from typing import List, Dict, Any
from urllib.parse import quote_plus

# MCP相关的导入
import mcp.server.stdio
import mcp.types as types
from mcp.server import NotificationOptions, Server
from mcp.server.models import InitializationOptions
from playwright.async_api import async_playwright, <PERSON>, Browser, BrowserContext

class WebSearcher:
    """网络搜索器类，支持多种搜索引擎"""
    
    def __init__(self):
        self.playwright = None
        self.browser = None
        self.context = None
        
    async def initialize(self):
        """初始化浏览器"""
        try:
            self.playwright = await async_playwright().start()
            # 启动浏览器，使用更隐蔽的参数避免被检测
            self.browser = await self.playwright.chromium.launch(
                headless=True,
                args=[
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-blink-features=AutomationControlled',  # 隐藏自动化标志
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--no-first-run',
                    '--no-default-browser-check',
                    '--disable-extensions-file-access-check',
                    '--disable-background-networking',
                ]
            )
            # 创建浏览器上下文，更好地模拟真实用户
            self.context = await self.browser.new_context(
                viewport={'width': 1366, 'height': 768},  # 更常见的分辨率
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                locale='zh-CN',
                timezone_id='Asia/Shanghai',
                extra_http_headers={
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'DNT': '1',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1',
                }
            )
            
            # 注入脚本隐藏webdriver痕迹
            await self.context.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
                
                // 隐藏其他自动化标志
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
            """)
            return True
        except Exception as e:
            print(f"初始化浏览器失败: {e}", file=sys.stderr)
            return False
            
    async def search_google(self, query: str) -> List[Dict[str, str]]:
        """执行Google搜索并返回结果"""
        try:
            if not self.context:
                await self.initialize()
            
            page = await self.context.new_page()
            
            # 构造Google搜索URL
            search_url = f"https://www.google.com/search?q={quote_plus(query)}&hl=en"
            
            # 随机延迟，模拟人类行为
            delay = random.uniform(1, 3)
            print(f"[Google] 随机延迟 {delay:.1f}秒", file=sys.stderr)
            await asyncio.sleep(delay)
            
            # 访问搜索页面
            print(f"[Google] 访问URL: {search_url}", file=sys.stderr)
            await page.goto(search_url, timeout=30000, wait_until='domcontentloaded')
            
            # 模拟人类浏览行为
            await asyncio.sleep(random.uniform(0.5, 1.5))
            
            # 检查是否遇到了验证码或反爬虫页面
            page_content = await page.content()
            if any(keyword in page_content.lower() for keyword in ['captcha', 'unusual traffic', 'verify', 'robot']):
                print("[Google] 检测到反爬虫页面，切换到百度", file=sys.stderr)
                await page.close()
                return []
            
            # 等待搜索结果加载，尝试多种选择器
            try:
                # 等待搜索结果容器
                await page.wait_for_selector('#search', timeout=10000)
                print("[Google] 找到搜索结果容器", file=sys.stderr)
            except:
                print("[Google] 等待搜索结果容器超时", file=sys.stderr)
                await page.close()
                return []
            
            # 提取搜索结果
            results = []
            
            # 尝试多种搜索结果选择器
            selectors = [
                'h3',  # 基础h3标签
                '[data-ved] h3',  # 带data-ved属性的h3
                '.g h3',  # 经典的.g容器内的h3
                '.rc h3',  # 另一种容器内的h3
            ]
            
            search_results = None
            for selector in selectors:
                try:
                    search_results = await page.locator(selector).all()
                    if search_results:
                        print(f"[Google] 使用选择器 '{selector}' 找到 {len(search_results)} 个结果", file=sys.stderr)
                        break
                except Exception as e:
                    print(f"[Google] 选择器 '{selector}' 失败: {e}", file=sys.stderr)
                    continue
            
            if not search_results:
                print("[Google] 没有找到任何搜索结果元素", file=sys.stderr)
                await page.close()
                return []
            
            for i, result_element in enumerate(search_results[:3]):  # 只取前3个
                try:
                    # 获取标题
                    title = await result_element.text_content()
                    if not title:
                        continue
                        
                    # 获取链接 - 需要找到父级的a标签
                    parent_link = result_element.locator('xpath=ancestor::a[@href][1]')
                    href = await parent_link.get_attribute('href')
                    
                    if href and title:
                        # 清理URL（移除Google重定向）
                        if href.startswith('/url?q='):
                            href = href.split('/url?q=')[1].split('&')[0]
                        elif href.startswith('/search?'):
                            continue  # 跳过内部搜索链接
                        
                        results.append({
                            'title': title.strip(),
                            'url': href,
                            'rank': i + 1,
                            'source': 'Google'
                        })
                        
                except Exception as e:
                    print(f"[Google] 提取第{i+1}个结果时出错: {e}", file=sys.stderr)
                    continue
            
            await page.close()
            return results
            
        except Exception as e:
            print(f"[Google] 搜索过程出错: {e}", file=sys.stderr)
            return []
    
    async def search_baidu(self, query: str) -> List[Dict[str, str]]:
        """执行百度搜索并返回结果"""
        try:
            if not self.context:
                await self.initialize()
            
            page = await self.context.new_page()
            
            # 构造百度搜索URL
            search_url = f"https://www.baidu.com/s?wd={quote_plus(query)}"
            
            # 访问搜索页面
            print(f"[百度] 访问URL: {search_url}", file=sys.stderr)
            await page.goto(search_url, timeout=30000)
            
            # 等待搜索结果加载
            try:
                await page.wait_for_selector('#content_left', timeout=15000)
                print("[百度] 找到搜索结果容器", file=sys.stderr)
            except:
                print("[百度] 等待搜索结果容器超时", file=sys.stderr)
                await page.close()
                return []
            
            # 提取搜索结果
            results = []
            result_elements = await page.locator('.t a').all()
            print(f"[百度] 找到 {len(result_elements)} 个结果链接", file=sys.stderr)
            
            # 提取前3个结果
            for i, element in enumerate(result_elements[:3]):
                try:
                    title = await element.text_content()
                    url = await element.get_attribute('href')
                    if title and url:
                        results.append({
                            'rank': i + 1,
                            'title': title.strip(),
                            'url': url,
                            'source': '百度'
                        })
                except Exception as e:
                    print(f"[百度] 提取第{i+1}个结果失败: {e}", file=sys.stderr)
                    continue
            
            await page.close()
            return results
            
        except Exception as e:
            print(f"[百度] 搜索过程出错: {e}", file=sys.stderr)
            return []
    
    async def search(self, query: str, engine: str = "auto") -> List[Dict[str, str]]:
        """执行搜索，自动选择可用的搜索引擎"""
        if engine == "google":
            return await self.search_google(query)
        elif engine == "baidu":
            return await self.search_baidu(query)
        else:  # auto模式
            # 先尝试Google
            print(f"🔍 [搜索] 尝试Google搜索: {query}", file=sys.stderr)
            results = await self.search_google(query)
            
            if results:
                print(f"✅ [搜索] Google搜索成功，获得 {len(results)} 个结果", file=sys.stderr)
                return results
            
            # Google失败，尝试百度
            print(f"🔍 [搜索] Google失败，尝试百度搜索: {query}", file=sys.stderr)
            results = await self.search_baidu(query)
            
            if results:
                print(f"✅ [搜索] 百度搜索成功，获得 {len(results)} 个结果", file=sys.stderr)
                return results
            
            print(f"❌ [搜索] 所有搜索引擎都失败了", file=sys.stderr)
            return []
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
        except Exception as e:
            print(f"清理资源时出错: {e}", file=sys.stderr)

class WebSearchMCPServer:
    """网络搜索MCP服务器类"""
    
    def __init__(self):
        """初始化服务器"""
        self.server = Server("web-search-server")
        self.searcher = WebSearcher()
        self._register_handlers()
        
        # 定义可用的工具列表
        self.tools = [
            types.Tool(
                name="web_search",
                description="智能网络搜索，自动选择最佳搜索引擎（Google或百度），返回前3个搜索结果",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "要搜索的关键字或短语"
                        },
                        "engine": {
                            "type": "string",
                            "description": "指定搜索引擎：'google'、'baidu' 或 'auto'（自动选择，默认）",
                            "enum": ["google", "baidu", "auto"],
                            "default": "auto"
                        }
                    },
                    "required": ["query"]
                }
            )
        ]
    
    def _register_handlers(self):
        """注册所有的MCP处理器"""
        
        @self.server.list_tools()
        async def handle_list_tools() -> List[types.Tool]:
            """处理工具列表请求"""
            print("📋 [服务器] 客户端请求工具列表", file=sys.stderr)
            return self.tools
        
        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[types.TextContent]:
            """处理工具调用请求"""
            print(f"🔧 [服务器] 客户端调用工具: {name}，参数: {arguments}", file=sys.stderr)
            
            if name == "web_search":
                try:
                    query = arguments.get("query", "").strip()
                    engine = arguments.get("engine", "auto")
                    
                    if not query:
                        return [types.TextContent(
                            type="text",
                            text="错误：搜索关键字不能为空"
                        )]
                    
                    print(f"🔍 [服务器] 开始搜索: {query}，搜索引擎: {engine}", file=sys.stderr)
                    
                    # 执行搜索
                    results = await self.searcher.search(query, engine)
                    
                    if not results:
                        return [types.TextContent(
                            type="text",
                            text=f"抱歉，没有找到关于 '{query}' 的搜索结果。请检查网络连接或尝试其他关键字。"
                        )]
                    
                    # 格式化结果
                    search_source = results[0].get('source', '网络')
                    response_text = f"🔍 {search_source}搜索结果：'{query}'\n\n"
                    for result in results:
                        response_text += f"**{result['rank']}. {result['title']}**\n"
                        response_text += f"🔗 {result['url']}\n\n"
                    
                    response_text += f"共找到 {len(results)} 个相关结果（来源：{search_source}）"
                    
                    print(f"✅ [服务器] 搜索完成，返回 {len(results)} 个结果", file=sys.stderr)
                    
                    return [types.TextContent(
                        type="text",
                        text=response_text
                    )]
                    
                except Exception as e:
                    error_msg = f"搜索时发生错误: {str(e)}"
                    print(f"❌ [服务器] {error_msg}", file=sys.stderr)
                    return [types.TextContent(
                        type="text",
                        text=error_msg
                    )]
            else:
                return [types.TextContent(
                    type="text",
                    text=f"未知工具: {name}"
                )]
    
    async def run(self):
        """启动MCP服务器"""
        print("🚀 [服务器] 启动智能网络搜索MCP服务器...", file=sys.stderr)
        print("📡 [服务器] 服务器监听stdin/stdout进行MCP通信", file=sys.stderr)
        print("🔧 [服务器] 可用工具: web_search", file=sys.stderr)
        print("🌐 [服务器] 支持搜索引擎: Google、百度（自动切换）", file=sys.stderr)
        print("=" * 50, file=sys.stderr)
        
        try:
            # 初始化搜索器
            if not await self.searcher.initialize():
                print("❌ [服务器] 浏览器初始化失败，请运行: playwright install chromium", file=sys.stderr)
                return
            
            print("✅ [服务器] 浏览器初始化成功", file=sys.stderr)
            
            # 使用stdio传输运行服务器
            async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
                await self.server.run(
                    read_stream,
                    write_stream,
                    InitializationOptions(
                        server_name="web-search-server",
                        server_version="2.0.0",
                        capabilities=self.server.get_capabilities(
                            notification_options=NotificationOptions(),
                            experimental_capabilities={},
                        ),
                    ),
                )
        except Exception as e:
            print(f"❌ [服务器] 服务器运行错误: {e}", file=sys.stderr)
        finally:
            # 清理资源
            await self.searcher.cleanup()
            print("🧹 [服务器] 资源清理完成", file=sys.stderr)

async def main():
    """主函数 - 程序入口点"""
    # 创建服务器实例
    search_server = WebSearchMCPServer()
    
    # 启动服务器
    await search_server.run()

if __name__ == "__main__":
    asyncio.run(main()) 