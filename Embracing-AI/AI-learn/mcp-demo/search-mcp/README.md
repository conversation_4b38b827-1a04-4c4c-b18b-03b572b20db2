# 🔍 智能网络搜索MCP演示程序

基于**MCP（Model Context Protocol）**的智能网络搜索工具，使用Playwright自动化浏览器获取真实的搜索结果。

## ✨ 功能特性

- 🧠 **智能引擎选择**：自动选择最佳搜索引擎（Google、百度）
- 🚀 **实时搜索**：通过Playwright控制真实浏览器访问
- 📋 **精选结果**：返回搜索结果的前3条，包含标题、链接和来源
- 🔄 **交互模式**：支持持续搜索，实时获取结果
- 🎯 **演示模式**：预设搜索示例，快速体验功能
- 🌐 **多引擎支持**：支持Google、百度等主流搜索引擎
- 🛡️ **容错机制**：搜索引擎故障时自动切换备选方案
- 📍 **地区适配**：根据网络环境自动选择可用的搜索引擎

## 🛠️ 技术栈

- **MCP (Model Context Protocol)** - AI工具集成协议
- **Playwright** - 浏览器自动化
- **Python AsyncIO** - 异步编程

## 📦 安装指南

### 1. 克隆项目并进入目录
```bash
cd google-search-mcp
```

### 2. 安装Python依赖
```bash
pip install -r requirements.txt
```

### 3. 安装Playwright浏览器
```bash
playwright install chromium
```

## 🚀 使用方法

### 快速启动（推荐）
```bash
python run.py                   # 交互式模式
python run.py demo              # 演示模式
python run.py help              # 显示帮助
```

### 直接使用客户端
```bash
python client.py               # 交互式模式
python client.py demo          # 演示模式
```

## 📖 使用示例

### 交互式模式
```
🔍 请输入搜索关键字: Python编程教程

🔍 百度搜索结果：'Python编程教程'

**1. 学习Python出来有用吗？**
🔗 https://www.baidu.com/baidu.php?url=...

**2. python基础语法学习 从AI零基础入门,多领域实战,灵活..**
🔗 https://www.baidu.com/baidu.php?url=...

**3. 【2024】Python入门,详细基础教程,零基础入门,详细图文**
🔗 http://www.baidu.com/link?url=...

共找到 3 个相关结果（来源：百度）
```

### 智能引擎切换
程序会自动尝试以下策略：
1. **首先尝试Google搜索**（如果网络允许）
2. **自动切换到百度**（当Google不可用时）
3. **显示结果来源**，让用户了解数据源

## 🏗️ 项目结构

```
google-search-mcp/
├── server.py          # MCP服务器，支持多搜索引擎
├── client.py          # MCP客户端，提供用户界面  
├── run.py             # 启动脚本，包含依赖检查
├── setup.py           # 一键安装脚本
├── requirements.txt   # 项目依赖
├── simple_test.py     # Google搜索测试
├── baidu_test.py      # 百度搜索测试
└── README.md         # 项目文档
```

## ⚙️ 工作原理

1. **MCP服务器**启动并初始化Playwright浏览器
2. **MCP客户端**通过stdio连接到服务器
3. 用户输入搜索关键字，客户端调用`web_search`工具
4. **智能引擎选择**：
   - 首先尝试Google搜索
   - 如果Google失败，自动切换到百度搜索
5. 服务器使用无头浏览器访问搜索页面
6. 解析HTML，提取前3个搜索结果的标题和链接
7. 返回格式化的搜索结果给客户端，包含来源标识

## 🔧 常见问题解决

### 问题1：浏览器可执行文件不存在
```
BrowserType.launch: Executable doesn't exist at /Users/<USER>/chrome-mac/headless_shell
```
**解决方案**：
```bash
playwright install chromium
```

### 问题2：MCP服务器启动失败
```
Server.run() missing 3 required positional arguments
```
**解决方案**：确保使用最新版本的代码，已修复此问题。

### 问题3：Google搜索失败
**症状**：程序自动切换到百度搜索
**原因**：网络环境无法访问Google（如中国大陆）
**解决方案**：
- ✅ **无需处理**：程序会自动切换到百度搜索
- 如需强制使用Google，请配置网络代理
- 可以手动指定搜索引擎参数

### 问题4：依赖版本冲突
**解决方案**：
```bash
pip install --upgrade mcp playwright
playwright install chromium
```

## 🔐 隐私说明

- 本工具仅用于教学和演示目的
- 所有搜索操作通过Playwright在本地浏览器中执行
- 不存储或记录任何搜索历史
- 请遵守Google的使用条款和当地法律法规

## 📝 开发说明

### 指定搜索引擎
可以通过参数指定使用的搜索引擎：
```python
# 客户端代码示例
result = await session.call_tool("web_search", {
    "query": "Python编程", 
    "engine": "google"  # 可选：google、baidu、auto（默认）
})
```

### 自定义搜索结果数量
修改`server.py`中的数字：
```python
# 提取前3个结果 -> 可修改为其他数量
for i, element in enumerate(result_elements[:3]):
```

### 添加新的搜索引擎
在`WebSearcher`类中添加新的搜索方法：
```python
async def search_bing(self, query: str) -> List[Dict[str, str]]:
    # 实现Bing搜索逻辑
    pass
```

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License - 详见LICENSE文件

---

**注意**：首次运行需要下载浏览器驱动，可能需要一些时间，请耐心等待。 