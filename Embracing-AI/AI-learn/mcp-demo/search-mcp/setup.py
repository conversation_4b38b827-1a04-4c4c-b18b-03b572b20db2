#!/usr/bin/env python3
"""
Google搜索MCP项目安装脚本
自动安装所有依赖，包括Playwright浏览器
"""

import subprocess
import sys
import os

def print_banner():
    """打印安装横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════════╗
║                 🔧 Google搜索MCP安装程序                          ║
║                                                                  ║
║  正在为您安装项目依赖，包括Python包和Playwright浏览器            ║
╚══════════════════════════════════════════════════════════════════╝
"""
    print(banner)

def run_command(command, description):
    """运行命令并处理错误"""
    print(f"📦 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        print(f"✅ {description}完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description}失败:")
        print(f"   错误信息: {e.stderr}")
        return False

def main():
    """主安装流程"""
    print_banner()
    
    print("🚀 开始安装...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        sys.exit(1)
    
    print(f"✅ Python版本检查通过: {sys.version}")
    
    # 安装Python依赖
    if not run_command("pip install -r requirements.txt", "安装Python依赖"):
        print("⚠️  Python依赖安装失败，请手动运行: pip install -r requirements.txt")
        return False
    
    # 安装Playwright浏览器
    if not run_command("playwright install chromium", "安装Playwright浏览器"):
        print("⚠️  Playwright浏览器安装失败，请手动运行: playwright install chromium")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 安装完成！")
    print("\n🚀 现在您可以运行以下命令启动程序:")
    print("   python run.py              # 交互式模式")
    print("   python run.py demo         # 演示模式")
    print("   python run.py help         # 查看帮助")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n👋 安装被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 安装过程出错: {e}")
        sys.exit(1) 