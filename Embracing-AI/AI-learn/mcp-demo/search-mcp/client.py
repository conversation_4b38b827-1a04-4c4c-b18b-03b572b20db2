#!/usr/bin/env python3
"""
Google搜索MCP客户端
连接到MCP服务器，提供交互式搜索界面
"""

import asyncio
import sys
from contextlib import asynccontextmanager
from typing import Optional

from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

class GoogleSearchClient:
    """Google搜索客户端类"""
    
    def __init__(self):
        # 存储MCP客户端会话对象，初始化为None
        self.session: Optional[ClientSession] = None
        
    @asynccontextmanager
    async def connect_to_server(self):
        """连接到MCP服务器"""
        server_params = StdioServerParameters(
            command="python",
            args=["server.py"],
            env=None
        )
        
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                try:
                    # 初始化会话
                    await session.initialize()
                    
                    # 列出可用工具
                    tools_result = await session.list_tools() # session.list_tools()向服务器请求可用工具列表
                    available_tools = [tool.name for tool in tools_result.tools] # tools_result.tools工具结果对象中的工具数组
                    print(f"📋 可用工具: {', '.join(available_tools)}")
                    
                    self.session = session
                    yield session #返回会话对象给 async with 调用者，这里暂停执行，等待调用者使用完毕
                    
                except Exception as e:
                    print(f"❌ 连接服务器失败: {e}")
                    raise
                finally:
                    self.session = None
    
    async def search(self, query: str) -> str:
        """执行搜索"""
        if not self.session:
            return "❌ 未连接到服务器"
        
        try:
            print(f"🔍 正在搜索: {query}")
            print("⏳ 请稍候，正在启动浏览器并获取搜索结果...")
            
            # call_tool：调用 MCP 服务器的工具，工具名称：web_search，参数：query
            result = await self.session.call_tool("web_search", {"query": query})
            
            if result.content:
                return result.content[0].text
            else:
                return "❌ 未获取到搜索结果"
                
        except Exception as e:
            return f"❌ 搜索失败: {str(e)}"
    
    async def interactive_mode(self):
        """交互式模式"""
        print("🚀 Google搜索MCP客户端启动成功！")
        print("💡 输入搜索关键字，我将为您在Google上搜索并返回前3个结果")
        print("💡 输入 'quit' 或 'exit' 退出程序")
        print("=" * 60)
        
        async with self.connect_to_server() as session: #连接服务器：使用之前定义的上下文管理器
            while True:
                try:
                    # 获取用户输入
                    query = input("\n🔍 请输入搜索关键字: ").strip()
                    
                    if not query:
                        print("⚠️  搜索关键字不能为空，请重新输入")
                        continue
                    
                    if query.lower() in ['quit', 'exit', '退出']:
                        print("👋 再见！感谢使用Google搜索MCP服务")
                        break
                    
                    # 执行搜索
                    result = await self.search(query)
                    print("\n" + "=" * 60)
                    print(result)
                    print("=" * 60)
                    
                except KeyboardInterrupt:
                    print("\n\n👋 检测到 Ctrl+C，正在退出...")
                    break
                except EOFError:
                    print("\n\n👋 输入结束，正在退出...")
                    break
                except Exception as e:
                    print(f"\n❌ 发生错误: {e}")
    
    async def demo_mode(self):
        """演示模式"""
        print("🎯 Google搜索MCP演示模式")
        print("将自动执行几个搜索示例...")
        print("=" * 60)
        
        demo_queries = [
            "Python编程教程",
            "机器学习最新技术",
            "2024年科技趋势"
        ]
        
        async with self.connect_to_server() as session:
            for i, query in enumerate(demo_queries, 1):
                print(f"\n📋 演示 {i}/{len(demo_queries)}: 搜索 '{query}'")
                result = await self.search(query)
                print("\n" + "=" * 60)
                print(result)
                print("=" * 60)
                
                if i < len(demo_queries):
                    print("⏳ 等待3秒后继续下一个演示...")
                    await asyncio.sleep(3)

async def main():
    """主函数"""
    client = GoogleSearchClient()
    
    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == "demo":
        await client.demo_mode()
    else:
        await client.interactive_mode()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        print(f"❌ 程序执行错误: {e}") 