<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 3991.265625 1089" style="max-width: 3991.265625px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-2e763745-97bd-4f08-a914-8432675a6f7e"><style>#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .error-icon{fill:#a44141;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .error-text{fill:#ddd;stroke:#ddd;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .edge-thickness-normal{stroke-width:1px;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .edge-thickness-thick{stroke-width:3.5px;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .edge-pattern-solid{stroke-dasharray:0;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .marker.cross{stroke:lightgrey;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e p{margin:0;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .cluster-label text{fill:#F9FFFE;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .cluster-label span{color:#F9FFFE;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .cluster-label span p{background-color:transparent;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .label text,#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e span{fill:#ccc;color:#ccc;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .node rect,#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .node circle,#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .node ellipse,#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .node polygon,#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .rough-node .label text,#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .node .label text,#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .image-shape .label,#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .icon-shape .label{text-anchor:middle;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .rough-node .label,#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .node .label,#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .image-shape .label,#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .icon-shape .label{text-align:center;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .node.clickable{cursor:pointer;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .arrowheadPath{fill:lightgrey;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .cluster text{fill:#F9FFFE;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .cluster span{color:#F9FFFE;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e rect.text{fill:none;stroke-width:0;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .icon-shape,#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .icon-shape p,#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .icon-shape rect,#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .userLayer&gt;*{fill:#e1f5fe!important;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .userLayer span{fill:#e1f5fe!important;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .entryLayer&gt;*{fill:#f3e5f5!important;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .entryLayer span{fill:#f3e5f5!important;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .agentLayer&gt;*{fill:#e8f5e8!important;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .agentLayer span{fill:#e8f5e8!important;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .toolLayer&gt;*{fill:#fff3e0!important;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .toolLayer span{fill:#fff3e0!important;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .flowLayer&gt;*{fill:#fce4ec!important;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .flowLayer span{fill:#fce4ec!important;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .llmLayer&gt;*{fill:#f1f8e9!important;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .llmLayer span{fill:#f1f8e9!important;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .infraLayer&gt;*{fill:#e0f2f1!important;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .infraLayer span{fill:#e0f2f1!important;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .externalLayer&gt;*{fill:#fafafa!important;}#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e .externalLayer span{fill:#fafafa!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"><g data-look="classic" id="外部服务" class="cluster"><rect height="104" width="1112.734375" y="977" x="2158.53125" style=""></rect><g transform="translate(2682.8984375, 977)" class="cluster-label"><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>外部服务</p></span></div></foreignObject></g></g><g data-look="classic" id="基础设施层" class="cluster"><rect height="128" width="935.84375" y="646" x="3047.421875" style=""></rect><g transform="translate(3475.34375, 646)" class="cluster-label"><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>基础设施层</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph5" class="cluster"><rect height="281" width="1016.6015625" y="646" x="288.9296875" style=""></rect><g transform="translate(705.08203125, 646)" class="cluster-label"><foreignObject height="24" width="184.296875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>大语言模型层 (LLM Layer)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph4" class="cluster"><rect height="434" width="253.0859375" y="340" x="8" style=""></rect><g transform="translate(47.37109375, 340)" class="cluster-label"><foreignObject height="24" width="174.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>流程控制层 (Flow Layer)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph3" class="cluster"><rect height="281" width="1701.890625" y="646" x="1325.53125" style=""></rect><g transform="translate(2107.515625, 646)" class="cluster-label"><foreignObject height="24" width="137.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>工具层 (Tool Layer)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph2" class="cluster"><rect height="256" width="2590.28125" y="340" x="520.6171875" style=""></rect><g transform="translate(1732.75, 340)" class="cluster-label"><foreignObject height="24" width="166.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>智能体层 (Agent Layer)</p></span></div></foreignObject></g></g><g data-look="classic" id="应用入口层" class="cluster"><rect height="128" width="2790.91796875" y="162" x="37.62109375" style=""></rect><g transform="translate(1393.080078125, 162)" class="cluster-label"><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>应用入口层</p></span></div></foreignObject></g></g><g data-look="classic" id="用户交互层" class="cluster"><rect height="104" width="2822.91796875" y="8" x="43.62109375" style=""></rect><g transform="translate(1415.080078125, 8)" class="cluster-label"><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>用户交互层</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UI_MAIN_0" d="M202.621,64.762L305.131,72.635C407.641,80.508,612.66,96.254,715.17,108.294C817.68,120.333,817.68,128.667,817.68,137C817.68,145.333,817.68,153.667,1125.657,168.132C1433.634,182.598,2049.587,203.195,2357.564,213.494L2665.541,223.793"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UI_MCP_1" d="M202.621,65.695L286.637,73.413C370.654,81.13,538.686,96.565,622.702,108.449C706.719,120.333,706.719,128.667,706.719,137C706.719,145.333,706.719,153.667,999.215,168.03C1291.712,182.393,1876.705,202.786,2169.201,212.983L2461.698,223.179"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UI_FLOW_2" d="M126.861,87L124.738,91.167C122.615,95.333,118.368,103.667,116.244,112C114.121,120.333,114.121,128.667,114.121,137C114.121,145.333,114.121,153.667,116.167,161.421C118.213,169.175,122.305,176.35,124.351,179.938L126.397,183.525"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CLI_MAIN_3" d="M2776.337,87L2778.621,91.167C2780.904,95.333,2785.472,103.667,2787.755,112C2790.039,120.333,2790.039,128.667,2790.039,137C2790.039,145.333,2790.039,153.667,2786.68,161.508C2783.321,169.349,2776.604,176.698,2773.245,180.373L2769.886,184.048"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CLI_MCP_4" d="M2722.098,87L2716.011,91.167C2709.925,95.333,2697.751,103.667,2691.665,112C2685.578,120.333,2685.578,128.667,2685.578,137C2685.578,145.333,2685.578,153.667,2675.18,162.488C2664.782,171.31,2643.986,180.62,2633.588,185.275L2623.19,189.93"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CLI_FLOW_5" d="M2691.539,61.892L2382.563,70.243C2073.586,78.595,1455.633,95.297,1146.656,107.815C837.68,120.333,837.68,128.667,837.68,137C837.68,145.333,837.68,153.667,736.834,167.227C635.988,180.788,434.296,199.575,333.45,208.969L232.604,218.363"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MAIN_MANUS_6" d="M2731.539,265L2731.539,269.167C2731.539,273.333,2731.539,281.667,2731.539,290C2731.539,298.333,2731.539,306.667,2731.539,315C2731.539,323.333,2731.539,331.667,2731.539,346.5C2731.539,361.333,2731.539,382.667,2731.539,404C2731.539,425.333,2731.539,446.667,2709.023,464.231C2686.507,481.795,2641.474,495.59,2618.958,502.487L2596.442,509.385"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MCP_MANUS_7" d="M2542.617,265L2542.617,269.167C2542.617,273.333,2542.617,281.667,2542.617,290C2542.617,298.333,2542.617,306.667,2542.617,315C2542.617,323.333,2542.617,331.667,2542.617,346.5C2542.617,361.333,2542.617,382.667,2542.617,404C2542.617,425.333,2542.617,446.667,2541.514,460.864C2540.411,475.061,2538.204,482.121,2537.101,485.652L2535.998,489.182"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FLOW_FACTORY_8" d="M150.621,265L150.621,269.167C150.621,273.333,150.621,281.667,150.621,290C150.621,298.333,150.621,306.667,150.621,315C150.621,323.333,150.621,331.667,150.621,339.333C150.621,347,150.621,354,150.621,357.5L150.621,361"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FACTORY_MANUS_9" d="M178.726,443L181.729,447.167C184.731,451.333,190.737,459.667,569.052,474.161C947.368,488.655,1697.993,509.309,2073.306,519.637L2448.619,529.964"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FACTORY_DATA_10" d="M166.539,443L168.239,447.167C169.94,451.333,173.341,459.667,240.069,472.333C306.798,485,436.853,502,501.881,510.5L566.909,519"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FACTORY_BROWSER_11" d="M144.296,443L143.621,447.167C142.945,451.333,141.594,459.667,392.09,473.942C642.587,488.217,1144.932,508.434,1396.104,518.542L1647.277,528.65"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BASE_MANUS_12" d="M1924.766,416.341L1984.762,424.951C2044.758,433.561,2164.75,450.78,2252.081,466.745C2339.413,482.709,2394.084,497.418,2421.419,504.773L2448.755,512.127"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BASE_BROWSER_13" d="M1913.328,443L1921.294,447.167C1929.261,451.333,1945.193,459.667,1928.571,470.657C1911.949,481.647,1862.773,495.294,1838.185,502.118L1813.597,508.942"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BASE_DATA_14" d="M1838.766,443L1838.766,447.167C1838.766,451.333,1838.766,459.667,1659.944,473.595C1481.123,487.523,1123.48,507.046,944.659,516.808L765.838,526.57"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BASE_SWE_15" d="M1826.578,443L1825.276,447.167C1823.974,451.333,1821.37,459.667,1860.576,471.619C1899.782,483.572,1980.798,499.144,2021.306,506.929L2061.814,514.715"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BASE_REACT_16" d="M1755.281,443L1746.362,447.167C1737.443,451.333,1719.604,459.667,1736.371,470.8C1753.138,481.933,1804.51,495.866,1830.196,502.832L1855.882,509.798"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MANUS_TOOLCOL_17" d="M2592.617,541.119L2662.831,550.266C2733.044,559.413,2873.471,577.706,2943.685,591.02C3013.898,604.333,3013.898,612.667,2930.816,621C2847.734,629.333,2681.57,637.667,2551.458,650.036C2421.345,662.405,2327.283,678.81,2280.253,687.012L2233.222,695.215"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BROWSER_TOOLCOL_18" d="M1809.742,548.702L1847.139,556.585C1884.536,564.468,1959.331,580.234,1996.728,592.284C2034.125,604.333,2034.125,612.667,2061.238,621C2088.35,629.333,2142.576,637.667,2166.942,645.468C2191.308,653.27,2185.816,660.539,2183.07,664.174L2180.323,667.809"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DATA_TOOLCOL_19" d="M761.844,553.649L792.975,560.708C824.107,567.766,886.37,581.883,917.501,593.108C948.633,604.333,948.633,612.667,1046.092,621C1143.551,629.333,1338.469,637.667,1524.302,651.029C1710.135,664.392,1886.883,682.783,1975.257,691.979L2063.631,701.175"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TOOLCOL_PYTHON_20" d="M2229.281,716.8L2342.615,726.333C2455.948,735.867,2682.615,754.933,2795.948,768.633C2909.281,782.333,2909.281,790.667,2909.281,798.333C2909.281,806,2909.281,813,2909.281,816.5L2909.281,820"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TOOLCOL_BROWSER_TOOL_21" d="M2229.281,719.55L2306.095,728.625C2382.909,737.7,2536.536,755.85,2613.35,769.092C2690.164,782.333,2690.164,790.667,2690.164,798.333C2690.164,806,2690.164,813,2690.164,816.5L2690.164,820"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TOOLCOL_EDITOR_22" d="M2229.281,726.373L2268.471,734.311C2307.661,742.249,2386.042,758.124,2425.232,770.229C2464.422,782.333,2464.422,790.667,2464.422,798.333C2464.422,806,2464.422,813,2464.422,816.5L2464.422,820"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TOOLCOL_SEARCH_23" d="M2213.701,749L2220.673,753.167C2227.644,757.333,2241.588,765.667,2248.56,774C2255.531,782.333,2255.531,790.667,2255.531,798.333C2255.531,806,2255.531,813,2255.531,816.5L2255.531,820"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TOOLCOL_CHART_24" d="M2083.19,749L2076.218,753.167C2069.246,757.333,2055.303,765.667,2048.331,774C2041.359,782.333,2041.359,790.667,2041.359,798.333C2041.359,806,2041.359,813,2041.359,816.5L2041.359,820"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TOOLCOL_MCP_TOOL_25" d="M2067.609,725.349L2024.906,733.458C1982.203,741.566,1896.797,757.783,1854.094,770.058C1811.391,782.333,1811.391,790.667,1811.391,798.333C1811.391,806,1811.391,813,1811.391,816.5L1811.391,820"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TOOLCOL_HUMAN_26" d="M2067.609,719.602L1991.284,728.668C1914.958,737.735,1762.307,755.867,1685.982,769.1C1609.656,782.333,1609.656,790.667,1609.656,798.333C1609.656,806,1609.656,813,1609.656,816.5L1609.656,820"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TOOLCOL_TERMINATE_27" d="M2067.609,717.168L1960.789,726.64C1853.969,736.112,1640.328,755.056,1533.508,768.695C1426.688,782.333,1426.688,790.667,1426.688,798.333C1426.688,806,1426.688,813,1426.688,816.5L1426.688,820"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MANUS_LLM_28" d="M2580.36,571L2586.529,575.167C2592.698,579.333,2605.037,587.667,2611.206,596C2617.375,604.333,2617.375,612.667,2369.574,621C2121.773,629.333,1626.172,637.667,1345.543,649.367C1064.914,661.068,999.258,676.136,966.43,683.67L933.602,691.204"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BROWSER_LLM_29" d="M1651.273,538.656L1537.5,548.213C1423.727,557.771,1196.18,576.885,1082.406,590.609C968.633,604.333,968.633,612.667,952.549,621C936.466,629.333,904.299,637.667,887.089,645.365C869.878,653.063,867.624,660.126,866.496,663.658L865.369,667.189"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DATA_LLM_30" d="M666.359,571L666.359,575.167C666.359,579.333,666.359,587.667,666.359,596C666.359,604.333,666.359,612.667,666.359,621C666.359,629.333,666.359,637.667,683.62,647.793C700.88,657.92,735.401,669.84,752.662,675.801L769.922,681.761"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LLM_OPENAI_31" d="M929.703,723.741L977.253,732.117C1024.802,740.494,1119.901,757.247,1167.451,769.79C1215,782.333,1215,790.667,1215,800.333C1215,810,1215,821,1215,826.5L1215,832"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LLM_ANTHROPIC_32" d="M929.703,735.861L948.875,742.218C968.047,748.574,1006.391,761.287,1025.563,771.81C1044.734,782.333,1044.734,790.667,1044.734,800.333C1044.734,810,1044.734,821,1044.734,826.5L1044.734,832"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LLM_AZURE_33" d="M851.703,749L851.703,753.167C851.703,757.333,851.703,765.667,851.703,774C851.703,782.333,851.703,790.667,851.703,800.333C851.703,810,851.703,821,851.703,826.5L851.703,832"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LLM_GOOGLE_34" d="M773.703,725.963L734.582,733.969C695.461,741.975,617.219,757.988,578.098,770.16C538.977,782.333,538.977,790.667,538.977,800.333C538.977,810,538.977,821,538.977,826.5L538.977,832"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LLM_OLLAMA_35" d="M773.703,720.562L707.934,729.469C642.164,738.375,510.625,756.187,444.855,769.26C379.086,782.333,379.086,790.667,379.086,800.333C379.086,810,379.086,821,379.086,826.5L379.086,832"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MANUS_CONFIG_36" d="M2568.173,571L2573.04,575.167C2577.907,579.333,2587.641,587.667,2592.508,596C2597.375,604.333,2597.375,612.667,2812.19,621C3027.005,629.333,3456.635,637.667,3671.451,645.333C3886.266,653,3886.266,660,3886.266,663.5L3886.266,667"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MANUS_MEMORY_37" d="M2509.063,571L2507.615,575.167C2506.167,579.333,2503.271,587.667,2501.823,596C2500.375,604.333,2500.375,612.667,2702.357,621C2904.339,629.333,3308.302,637.667,3510.284,645.333C3712.266,653,3712.266,660,3712.266,663.5L3712.266,667"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MANUS_LOGGER_38" d="M2452.617,569.571L2444.41,573.975C2436.203,578.38,2419.789,587.19,2411.582,595.762C2403.375,604.333,2403.375,612.667,2592.523,621C2781.672,629.333,3159.969,637.667,3349.117,645.333C3538.266,653,3538.266,660,3538.266,663.5L3538.266,667"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MANUS_SANDBOX_39" d="M2452.617,552.718L2428.243,559.931C2403.87,567.145,2355.122,581.573,2330.749,592.953C2306.375,604.333,2306.375,612.667,2482.69,621C2659.005,629.333,3011.635,637.667,3187.951,645.333C3364.266,653,3364.266,660,3364.266,663.5L3364.266,667"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MANUS_MCP_CLIENT_40" d="M2452.617,544.82L2406.04,553.35C2359.464,561.88,2266.31,578.94,2219.733,591.637C2173.156,604.333,2173.156,612.667,2338.854,621C2504.552,629.333,2835.948,637.667,3001.646,645.333C3167.344,653,3167.344,660,3167.344,663.5L3167.344,667"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MCP_CLIENT_MCP_SERVER_41" d="M3167.344,749L3167.344,753.167C3167.344,757.333,3167.344,765.667,3167.344,774C3167.344,782.333,3167.344,790.667,3167.344,805.5C3167.344,820.333,3167.344,841.667,3167.344,863C3167.344,884.333,3167.344,905.667,3167.344,920.5C3167.344,935.333,3167.344,943.667,3167.344,952C3167.344,960.333,3167.344,968.667,3167.344,976.333C3167.344,984,3167.344,991,3167.344,994.5L3167.344,998"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BROWSER_TOOL_BROWSER_SERVICE_42" d="M2690.164,902L2690.164,906.167C2690.164,910.333,2690.164,918.667,2690.164,927C2690.164,935.333,2690.164,943.667,2690.164,952C2690.164,960.333,2690.164,968.667,2690.164,976.333C2690.164,984,2690.164,991,2690.164,994.5L2690.164,998"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SEARCH_SEARCH_ENGINE_43" d="M2255.531,902L2255.531,906.167C2255.531,910.333,2255.531,918.667,2255.531,927C2255.531,935.333,2255.531,943.667,2255.531,952C2255.531,960.333,2255.531,968.667,2255.531,976.333C2255.531,984,2255.531,991,2255.531,994.5L2255.531,998"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FLOW_BASE_PLANNING_44" d="M120.242,571L120.242,575.167C120.242,579.333,120.242,587.667,120.242,596C120.242,604.333,120.242,612.667,120.242,621C120.242,629.333,120.242,637.667,120.242,645.333C120.242,653,120.242,660,120.242,663.5L120.242,667"></path><path marker-end="url(#mermaid-2e763745-97bd-4f08-a914-8432675a6f7e_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FACTORY_FLOW_BASE_45" d="M132.109,443L130.131,447.167C128.153,451.333,124.198,459.667,122.22,467.333C120.242,475,120.242,482,120.242,485.5L120.242,489"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(140.62109375, 60)" id="flowchart-UI-516" class="node default userLayer"><rect height="54" width="124" y="-27" x="-62" style="fill:#e1f5fe !important" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>用户输入</p></span></div></foreignObject></g></g><g transform="translate(2761.5390625, 60)" id="flowchart-CLI-517" class="node default userLayer"><rect height="54" width="140" y="-27" x="-70" style="fill:#e1f5fe !important" class="basic label-container"></rect><g transform="translate(-40, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>命令行接口</p></span></div></foreignObject></g></g><g transform="translate(2731.5390625, 226)" id="flowchart-MAIN-518" class="node default entryLayer"><rect height="78" width="124" y="-39" x="-62" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-32, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>main.py<br>基础模式</p></span></div></foreignObject></g></g><g transform="translate(2542.6171875, 226)" id="flowchart-MCP-519" class="node default entryLayer"><rect height="78" width="153.84375" y="-39" x="-76.921875" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-46.921875, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="93.84375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>run_mcp.py<br>MCP工具模式</p></span></div></foreignObject></g></g><g transform="translate(150.62109375, 226)" id="flowchart-FLOW-520" class="node default entryLayer"><rect height="78" width="156" y="-39" x="-78" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-48, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>run_flow.py<br>多智能体模式</p></span></div></foreignObject></g></g><g transform="translate(1838.765625, 404)" id="flowchart-BASE-521" class="node default agentLayer"><rect height="78" width="172" y="-39" x="-86" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-56, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>BaseAgent<br>基础智能体抽象</p></span></div></foreignObject></g></g><g transform="translate(2522.6171875, 532)" id="flowchart-MANUS-522" class="node default agentLayer"><rect height="78" width="140" y="-39" x="-70" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-40, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Manus<br>通用智能体</p></span></div></foreignObject></g></g><g transform="translate(1730.5078125, 532)" id="flowchart-BROWSER-523" class="node default agentLayer"><rect height="78" width="158.46875" y="-39" x="-79.234375" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-49.234375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="98.46875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>BrowserAgent<br>浏览器智能体</p></span></div></foreignObject></g></g><g transform="translate(666.359375, 532)" id="flowchart-DATA-524" class="node default agentLayer"><rect height="78" width="190.96875" y="-39" x="-95.484375" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-65.484375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="130.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>DataAnalysisAgent<br>数据分析智能体</p></span></div></foreignObject></g></g><g transform="translate(2151.7421875, 532)" id="flowchart-SWE-525" class="node default agentLayer"><rect height="78" width="172" y="-39" x="-86" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-56, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>SWEAgent<br>软件工程智能体</p></span></div></foreignObject></g></g><g transform="translate(1937.7421875, 532)" id="flowchart-REACT-526" class="node default agentLayer"><rect height="78" width="156" y="-39" x="-78" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-48, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ReactAgent<br>反应式智能体</p></span></div></foreignObject></g></g><g transform="translate(2148.4453125, 710)" id="flowchart-TOOLCOL-527" class="node default toolLayer"><rect height="78" width="161.671875" y="-39" x="-80.8359375" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-50.8359375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="101.671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ToolCollection<br>工具集合管理</p></span></div></foreignObject></g></g><g transform="translate(2909.28125, 863)" id="flowchart-PYTHON-528" class="node default toolLayer"><rect height="78" width="166.28125" y="-39" x="-83.140625" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-53.140625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="106.28125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>PythonExecute<br>Python执行</p></span></div></foreignObject></g></g><g transform="translate(2690.1640625, 863)" id="flowchart-BROWSER_TOOL-529" class="node default toolLayer"><rect height="78" width="171.953125" y="-39" x="-85.9765625" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-55.9765625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="111.953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>BrowserUseTool<br>浏览器操作</p></span></div></foreignObject></g></g><g transform="translate(2464.421875, 863)" id="flowchart-EDITOR-530" class="node default toolLayer"><rect height="78" width="179.53125" y="-39" x="-89.765625" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-59.765625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="119.53125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>StrReplaceEditor<br>文件编辑</p></span></div></foreignObject></g></g><g transform="translate(2255.53125, 863)" id="flowchart-SEARCH-531" class="node default toolLayer"><rect height="78" width="138.25" y="-39" x="-69.125" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-39.125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="78.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>WebSearch<br>网络搜索</p></span></div></foreignObject></g></g><g transform="translate(2041.359375, 863)" id="flowchart-CHART-532" class="node default toolLayer"><rect height="78" width="190.09375" y="-39" x="-95.046875" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-65.046875, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="130.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ChartVisualization<br>图表可视化</p></span></div></foreignObject></g></g><g transform="translate(1811.390625, 863)" id="flowchart-MCP_TOOL-533" class="node default toolLayer"><rect height="78" width="169.84375" y="-39" x="-84.921875" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-54.921875, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="109.84375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>MCPClientTool<br>MCP工具客户端</p></span></div></foreignObject></g></g><g transform="translate(1609.65625, 863)" id="flowchart-HUMAN-534" class="node default toolLayer"><rect height="78" width="133.625" y="-39" x="-66.8125" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-36.8125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="73.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>AskHuman<br>人机交互</p></span></div></foreignObject></g></g><g transform="translate(1426.6875, 863)" id="flowchart-TERMINATE-535" class="node default toolLayer"><rect height="78" width="132.3125" y="-39" x="-66.15625" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-36.15625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="72.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Terminate<br>终止工具</p></span></div></foreignObject></g></g><g transform="translate(120.2421875, 532)" id="flowchart-FLOW_BASE-536" class="node default flowLayer"><rect height="78" width="126.28125" y="-39" x="-63.140625" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-33.140625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="66.28125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>BaseFlow<br>基础流程</p></span></div></foreignObject></g></g><g transform="translate(120.2421875, 710)" id="flowchart-PLANNING-537" class="node default flowLayer"><rect height="78" width="154.484375" y="-39" x="-77.2421875" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-47.2421875, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="94.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>PlanningFlow<br>规划流程</p></span></div></foreignObject></g></g><g transform="translate(150.62109375, 404)" id="flowchart-FACTORY-538" class="node default flowLayer"><rect height="78" width="147.375" y="-39" x="-73.6875" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-43.6875, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="87.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>FlowFactory<br>流程工厂</p></span></div></foreignObject></g></g><g transform="translate(851.703125, 710)" id="flowchart-LLM-539" class="node default llmLayer"><rect height="78" width="156" y="-39" x="-78" style="fill:#f1f8e9 !important" class="basic label-container"></rect><g transform="translate(-48, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>LLM<br>语言模型抽象</p></span></div></foreignObject></g></g><g transform="translate(1215, 863)" id="flowchart-OPENAI-540" class="node default llmLayer"><rect height="54" width="111.0625" y="-27" x="-55.53125" style="fill:#f1f8e9 !important" class="basic label-container"></rect><g transform="translate(-25.53125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="51.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>OpenAI</p></span></div></foreignObject></g></g><g transform="translate(1044.734375, 863)" id="flowchart-ANTHROPIC-541" class="node default llmLayer"><rect height="54" width="129.46875" y="-27" x="-64.734375" style="fill:#f1f8e9 !important" class="basic label-container"></rect><g transform="translate(-34.734375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="69.46875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Anthropic</p></span></div></foreignObject></g></g><g transform="translate(851.703125, 863)" id="flowchart-AZURE-542" class="node default llmLayer"><rect height="54" width="156.59375" y="-27" x="-78.296875" style="fill:#f1f8e9 !important" class="basic label-container"></rect><g transform="translate(-48.296875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96.59375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Azure OpenAI</p></span></div></foreignObject></g></g><g transform="translate(538.9765625, 863)" id="flowchart-GOOGLE-543" class="node default llmLayer"><rect height="54" width="109.46875" y="-27" x="-54.734375" style="fill:#f1f8e9 !important" class="basic label-container"></rect><g transform="translate(-24.734375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="49.46875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Google</p></span></div></foreignObject></g></g><g transform="translate(379.0859375, 863)" id="flowchart-OLLAMA-544" class="node default llmLayer"><rect height="54" width="110.3125" y="-27" x="-55.15625" style="fill:#f1f8e9 !important" class="basic label-container"></rect><g transform="translate(-25.15625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="50.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Ollama</p></span></div></foreignObject></g></g><g transform="translate(3886.265625, 710)" id="flowchart-CONFIG-545" class="node default infraLayer"><rect height="78" width="124" y="-39" x="-62" style="fill:#e0f2f1 !important" class="basic label-container"></rect><g transform="translate(-32, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Config<br>配置管理</p></span></div></foreignObject></g></g><g transform="translate(3712.265625, 710)" id="flowchart-MEMORY-546" class="node default infraLayer"><rect height="78" width="124" y="-39" x="-62" style="fill:#e0f2f1 !important" class="basic label-container"></rect><g transform="translate(-32, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Memory<br>记忆管理</p></span></div></foreignObject></g></g><g transform="translate(3538.265625, 710)" id="flowchart-LOGGER-547" class="node default infraLayer"><rect height="78" width="124" y="-39" x="-62" style="fill:#e0f2f1 !important" class="basic label-container"></rect><g transform="translate(-32, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Logger<br>日志系统</p></span></div></foreignObject></g></g><g transform="translate(3364.265625, 710)" id="flowchart-SANDBOX-548" class="node default infraLayer"><rect height="78" width="124" y="-39" x="-62" style="fill:#e0f2f1 !important" class="basic label-container"></rect><g transform="translate(-32, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Sandbox<br>沙箱环境</p></span></div></foreignObject></g></g><g transform="translate(3167.34375, 710)" id="flowchart-MCP_CLIENT-549" class="node default infraLayer"><rect height="78" width="169.84375" y="-39" x="-84.921875" style="fill:#e0f2f1 !important" class="basic label-container"></rect><g transform="translate(-54.921875, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="109.84375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>MCPClients<br>MCP客户端管理</p></span></div></foreignObject></g></g><g transform="translate(3167.34375, 1029)" id="flowchart-MCP_SERVER-550" class="node default externalLayer"><rect height="54" width="137.84375" y="-27" x="-68.921875" style="fill:#fafafa !important" class="basic label-container"></rect><g transform="translate(-38.921875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="77.84375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>MCP服务器</p></span></div></foreignObject></g></g><g transform="translate(2690.1640625, 1029)" id="flowchart-BROWSER_SERVICE-551" class="node default externalLayer"><rect height="54" width="140" y="-27" x="-70" style="fill:#fafafa !important" class="basic label-container"></rect><g transform="translate(-40, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>浏览器服务</p></span></div></foreignObject></g></g><g transform="translate(2255.53125, 1029)" id="flowchart-SEARCH_ENGINE-552" class="node default externalLayer"><rect height="54" width="124" y="-27" x="-62" style="fill:#fafafa !important" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>搜索引擎</p></span></div></foreignObject></g></g></g></g></g></svg>