from datasets import load_dataset,load_from_disk

#在线加载数据
# dataset = load_dataset(path="NousResearch/hermes-function-calling-v1",split="train")
# print(dataset)
#转存为CSV格式
# dataset.to_csv(path_or_buf="/Users/<USER>/yiyan/code/Embracing-AI/AI-learn/AI-learn/bert/base_bert_sentiment_classify/data/hermes-function-calling-v1.csv")
#加载csv格式数据
# dataset = load_dataset(path="csv",data_files="/Users/<USER>/yiyan/code/Embracing-AI/AI-learn/AI-learn/bert/base_bert_sentiment_classify/data/hermes-function-calling-v1.csv")
# print(dataset)
#加载缓存数据
dataset = load_from_disk("/Users/<USER>/yiyan/code/Embracing-AI/AI-learn/AI-learn/bert/base_bert_sentiment_classify/data/ChnSentiCorp")
print(dataset)
"""
DatasetDict({
    train: Dataset({
        features: ['text', 'label'],
        num_rows: 9600
    })
    validation: Dataset({
        features: ['text', 'label'],
        num_rows: 1200
    })
    test: Dataset({
        features: ['text', 'label'],
        num_rows: 1200
    })
})
"""

test_data = dataset["train"]
# for data in test_data:
#     print(data)