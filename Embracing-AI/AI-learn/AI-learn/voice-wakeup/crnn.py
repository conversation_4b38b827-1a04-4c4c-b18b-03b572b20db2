import torch.nn as nn

class WakeWordCRNN(nn.Module):
    """
    语音唤醒词检测的CRNN模型（卷积循环神经网络）
    
    模型架构：
    1. CNN部分：用于提取音频特征的局部模式和频域特征
    2. RNN部分：用于捕捉音频序列的时序依赖关系
    3. 分类头：将特征映射到二分类结果（唤醒词/非唤醒词）
    
    设计优势：
    - CNN擅长提取局部特征，适合处理音频的频谱特征
    - RNN擅长建模序列关系，适合处理音频的时序信息
    - 结合两者可以同时捕捉音频的空间和时序特征
    """

    def __init__(self, input_dim=39):
        """
        初始化CRNN模型
        
        Args:
            input_dim (int): 输入特征维度，通常为MFCC特征的维度
                           默认39维（13个MFCC系数 + 13个一阶差分 + 13个二阶差分）
        """
        super().__init__()
        
        # ==================== CNN特征提取部分 ====================
        # 用于提取音频频谱的局部模式特征
        self.cnn = nn.Sequential(
            # 第一个卷积层：提取基础特征
            nn.Conv2d(
                in_channels=1,      # 输入通道数：单通道音频
                out_channels=32,    # 输出通道数：32个特征图
                kernel_size=(3, 5), # 卷积核大小：频域3，时域5
                padding=(1, 2)      # 填充：保持特征图尺寸
            ),
            nn.BatchNorm2d(32),     # 批归一化：稳定训练，加速收敛
            nn.GELU(),              # GELU激活函数：比ReLU更平滑，梯度更稳定
            nn.MaxPool2d((2, 2)),   # 最大池化：降采样，减少计算量，增强特征鲁棒性

            # 第二个卷积层：提取更复杂的特征
            nn.Conv2d(
                in_channels=32,     # 输入通道数：上一层输出
                out_channels=64,    # 输出通道数：64个特征图
                kernel_size=(3, 5), # 卷积核大小：保持一致
                padding=(1, 2)      # 填充：保持特征图尺寸
            ),
            nn.BatchNorm2d(64),     # 批归一化
            nn.GELU(),              # GELU激活函数
            nn.MaxPool2d((2, 2)),   # 最大池化：进一步降采样

            # 自适应平均池化：动态调整输出尺寸
            nn.AdaptiveAvgPool2d((None, 25))  # 时间维度保持不变，频率维度调整为25
        )

        # ==================== RNN序列建模部分 ====================
        # 用于捕捉音频序列的时序依赖关系
        self.gru = nn.GRU(
            input_size=64 * (input_dim // 4),  # 输入特征维度
            # 计算说明：经过两次池化后，频率维度变为原来的1/4
            # 64个通道 * (39//4) = 64 * 9 = 576
            hidden_size=128,        # 隐藏层大小：128个神经元
            bidirectional=True,     # 双向GRU：同时利用过去和未来的信息
            num_layers=2,           # 层数：2层GRU堆叠，增强模型表达能力
            dropout=0.3             # Dropout：防止过拟合，提高泛化能力
        )

        # ==================== 分类头部分 ====================
        # 将RNN提取的特征映射到分类结果
        self.classifier = nn.Sequential(
            # 第一个全连接层：特征降维
            nn.Linear(256, 128),    # 256 = 128 * 2 (双向GRU)
            nn.GELU(),              # GELU激活函数
            nn.Dropout(0.4),        # Dropout：防止过拟合
            
            # 第二个全连接层：最终分类
            nn.Linear(128, 2)       # 输出2个类别：唤醒词/非唤醒词
        )

    def forward(self, x):
        """
        前向传播过程
        
        Args:
            x (torch.Tensor): 输入音频特征 [B, F, T]
                            B: 批次大小 (Batch Size)
                            F: 特征维度 (Feature Dimension)
                            T: 时间步数 (Time Steps)
        
        Returns:
            torch.Tensor: 分类结果 [B, 2]
                         B: 批次大小
                         2: 两个类别的概率分布
        """
        # ==================== 数据预处理 ====================
        # 为CNN添加通道维度：[B, F, T] -> [B, 1, F, T]
        x = x.unsqueeze(1)
        
        # ==================== CNN特征提取 ====================
        # 通过CNN提取空间特征：[B, 1, F, T] -> [B, C, F', T']
        x = self.cnn(x)  # (B, 64, F//4, T//4)
        
        # ==================== 数据维度调整 ====================
        # 获取张量维度信息
        B, C, F, T = x.size()
        
        # 调整维度以适应RNN输入要求
        # [B, C, F, T] -> [B, T, C, F] -> [B, T, C*F]
        x = x.permute(0, 3, 1, 2)  # 将时间维度移到第二位
        x = x.reshape(B, T, -1)    # 展平特征维度：C*F
        
        # ==================== RNN序列建模 ====================
        # 通过双向GRU捕捉时序信息
        x, _ = self.gru(x)  # (B, T, 256) - 256 = 128 * 2 (双向)
        
        # ==================== 特征聚合 ====================
        # 使用平均池化聚合所有时间步的信息
        # 相比于只使用最后时间步，平均池化能更好地利用全局信息
        x = x.mean(dim=1)  # (B, 256) - 沿时间维度求平均
        
        # ==================== 分类预测 ====================
        # 通过分类头得到最终预测结果
        return self.classifier(x)  # (B, 2) - 二分类结果