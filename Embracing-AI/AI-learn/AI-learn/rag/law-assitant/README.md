# 法律条文智能问答系统 (Law Assistant)

基于检索增强生成 (RAG) 技术的法律条文智能助手系统，专门为法律从业者和普通用户提供准确的法律条文检索和解释服务。

## 🎯 项目简介

本项目实现了一个完整的法律条文智能问答系统，主要功能包括：

- **智能检索**: 基于语义相似度的法律条文检索
- **准确回答**: 严格基于法律条文进行回答，避免幻觉
- **来源追溯**: 提供详细的法律依据和相关度评分
- **交互界面**: 命令行式的实时问答交互

## 🏗️ 技术架构

### 核心技术栈

| 组件 | 技术选型 | 作用 |
|------|----------|------|
| **RAG框架** | LlamaIndex | 检索增强生成应用开发 |
| **向量数据库** | ChromaDB | 高效语义相似度检索 |
| **嵌入模型** | BAAI/bge-small-zh-v1.5 | 中文语义嵌入 |
| **大语言模型** | Qwen 3.0 1.7B | 本地部署LLM (通过Ollama) |
| **数据爬取** | Playwright | 法律条文数据获取 |
| **数据存储** | JSON + ChromaDB | 双重持久化存储 |

### 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户问题      │───▶│   嵌入模型      │───▶│   向量检索      │
│   "劳动合同..." │    │   BGE-small     │    │   ChromaDB      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   智能回答      │◀───│   LLM生成       │◀───│   相关条文      │
│   基于法条回答  │    │   Qwen 3.0      │    │   Top-K结果     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📁 项目结构

```
law-assitant/
├── core/                          # 核心业务逻辑
│   └── rag_law.py                 # RAG系统主实现 (560行)
├── data/                          # 数据文件和获取脚本
│   ├── data.json                  # 法律条文数据 (67KB)
│   ├── get_labour_law.py          # 劳动法数据爬取脚本
│   └── get_labour_contract_law.py # 劳动合同法数据爬取脚本
├── chroma_db/                     # ChromaDB向量数据库
│   └── (向量数据持久化目录)
├── storage/                       # LlamaIndex存储
│   ├── docstore.json             # 文档存储
│   ├── graph_store.json          # 图存储
│   └── *.json                     # 其他索引文件
├── read_json.py                   # JSON数据查看工具
└── README.md                      # 项目文档
```

## 🚀 快速开始

### 环境要求

- Python 3.8+
- 至少 4GB RAM
- 10GB 可用磁盘空间

### 依赖安装

```bash
# 安装核心依赖
pip install llama-index
pip install llama-index-llms-ollama
pip install llama-index-embeddings-huggingface
pip install llama-index-vector-stores-chroma
pip install chromadb

# 数据爬取依赖 (可选)
pip install playwright
```

### Ollama安装和配置

```bash
# 1. 安装Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# 2. 启动Ollama服务
ollama serve

# 3. 下载Qwen模型
ollama pull qwen3:1.7b
```

### 嵌入模型下载

```bash
# 使用ModelScope下载BGE中文嵌入模型
pip install modelscope
python -c "from modelscope import snapshot_download; snapshot_download('AI-ModelScope/bge-small-zh-v1.5')"
```

### 配置路径

编辑 `core/rag_law.py` 中的配置类：

```python
class Config:
    # 根据实际路径修改
    EMBED_MODEL_PATH = "/your/path/to/bge-small-zh-v1.5"
    DATA_DIR = "/your/path/to/law-assitant/data"
    VECTOR_DB_DIR = "/your/path/to/law-assitant/chroma_db"
    PERSIST_DIR = "/your/path/to/law-assitant/storage"
```

### 运行系统

```bash
cd law-assitant
python core/rag_law.py
```

## 💡 使用示例

### 启动界面
```
🚀 启动法律条文智能问答系统
==================================================
🤖 初始化AI模型...
✅ AI模型初始化成功
🔍 加载和处理法律数据和索引构建...
✅ 向量存储初始化成功
🔍 创建智能查询引擎...
✅ 查询引擎创建成功

==================================================
🎯 法律条文智能助手已准备就绪！
```

### 问答示例
```
📝 请输入您的问题（第1个问题，输入q退出）: 劳动合同试用期最长多久？

🔍 检索到的相关法律条文：
1. 中华人民共和国劳动合同法 第十九条 (相似度: 0.85)
2. 中华人民共和国劳动法 第二十一条 (相似度: 0.78)

🤖 AI回答：
根据《中华人民共和国劳动合同法》第十九条规定：
- 劳动合同期限三个月以上不满一年的，试用期不得超过一个月
- 劳动合同期限一年以上不满三年的，试用期不得超过二个月  
- 三年以上固定期限和无固定期限的劳动合同，试用期不得超过六个月

因此，试用期最长不得超过六个月。
```

## 📊 数据说明

### 法律条文数据

当前系统包含以下法律条文：
- **中华人民共和国劳动合同法**: 98个条文
- **中华人民共和国劳动法**: 107个条文
- **总计**: 205个法律条文

### 数据格式

```json
[
    {
        "中华人民共和国劳动合同法 第一条": "为了完善劳动合同制度...",
        "中华人民共和国劳动合同法 第二条": "中华人民共和国境内的企业..."
    },
    {
        "中华人民共和国劳动法 第一条": "为了保护劳动者的合法权益...",
        "中华人民共和国劳动法 第二条": "在中华人民共和国境内的企业..."
    }
]
```

## 🔧 系统配置

### 核心参数

```python
class Config:
    # 模型配置
    EMBED_MODEL_PATH = "BAAI/bge-small-zh-v1.5"  # 嵌入模型路径
    LLM_MODEL_NAME = "qwen3:1.7b"                # LLM模型名称
    OLLAMA_BASE_URL = "http://localhost:11434"   # Ollama服务地址
    
    # 检索参数
    TOP_K = 3                                    # 检索返回条文数量
    COLLECTION_NAME = "labor_laws"               # ChromaDB集合名称
    
    # 存储路径
    DATA_DIR = "data"                            # 数据目录
    VECTOR_DB_DIR = "chroma_db"                  # 向量数据库目录
    PERSIST_DIR = "storage"                      # 持久化存储目录
```

### 提示词模板

系统使用ChatML格式的提示词模板：

```python
QA_TEMPLATE = (
    "<|im_start|>system\n"
    "你是一个专业的法律助手，请严格根据以下法律条文回答问题：\n"
    "相关法律条文：\n{context_str}\n<|im_end|>\n"
    "<|im_start|>user\n{query_str}<|im_end|>\n"
    "<|im_start|>assistant\n"
)
```

## 🛠️ 开发指南

### 添加新的法律数据

1. **创建数据获取脚本** (参考 `get_labour_law.py`):
```python
def get_new_law(url):
    # 使用Playwright爬取网页内容
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=True)
        # ... 爬取逻辑
```

2. **数据格式转换**:
```python
def extract_law_articles(data_str):
    # 正则表达式提取条文
    pattern = re.compile(r'第([一二三...]+)条.*?(?=\n第|$)', re.DOTALL)
    # ... 转换逻辑
```

3. **更新数据文件**:
   - 将新数据添加到 `data.json`
   - 删除 `chroma_db/` 和 `storage/` 目录
   - 重新运行系统进行索引构建

### 自定义嵌入模型

```python
# 替换为其他中文嵌入模型
embed_model = HuggingFaceEmbedding(
    model_name="your_model_path",
    encode_kwargs={
        "normalize_embeddings": True,
        "device": "cuda"  # 使用GPU加速
    }
)
```

### 自定义LLM

```python
# 使用其他本地LLM
llm = Ollama(
    model="your_model_name",
    base_url="http://localhost:11434",
    temperature=0.1,  # 降低温度提高确定性
    request_timeout=300.0
)
```

## 🔍 工具脚本

### 查看JSON数据
```bash
python read_json.py
```

### 重建索引
```bash
# 删除现有索引
rm -rf chroma_db/ storage/

# 重新运行系统
python core/rag_law.py
```

## 📈 性能优化

### 硬件要求
- **CPU**: 4核以上推荐
- **内存**: 8GB以上推荐  
- **存储**: SSD推荐 (提升索引加载速度)
- **GPU**: 可选 (加速嵌入计算)

### 优化建议

1. **嵌入模型优化**:
   - 使用GPU加速嵌入计算
   - 批量处理减少模型调用次数

2. **向量检索优化**:
   - 调整 `TOP_K` 参数平衡准确性和速度
   - 使用HNSW索引提升检索速度

3. **LLM推理优化**:
   - 调整温度参数控制生成随机性
   - 使用量化模型减少内存占用

## 🚨 注意事项

1. **法律条文更新**: 法律条文可能随时间变化，需要定期更新数据
2. **模型限制**: 当前系统仅适用于中文法律条文
3. **准确性声明**: AI回答仅供参考，重要法律事务请咨询专业律师
4. **数据安全**: 确保敏感法律咨询数据的安全性

## 🤝 贡献指南

欢迎贡献代码和改进建议！

1. Fork 本仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 📞 联系方式

- 作者vx: yiyan_k
- 创建时间: 2025年7月4日
- 版本: v1.0

## 🙏 致谢

- [LlamaIndex](https://github.com/run-llama/llama_index) - RAG框架
- [ChromaDB](https://github.com/chroma-core/chroma) - 向量数据库
- [BGE](https://github.com/FlagOpen/FlagEmbedding) - 中文嵌入模型
- [Qwen](https://github.com/QwenLM/Qwen) - 大语言模型
- [Ollama](https://github.com/ollama/ollama) - 本地LLM部署

---

**⚖️ 免责声明**: 本系统提供的法律信息仅供参考，不构成正式的法律建议。在处理具体法律事务时，请咨询专业律师。 