import asyncio
from dotenv import load_dotenv
load_dotenv()
from browser_use import Agent
from browser_use.llm import ChatOpenAI

async def main():
    agent = Agent(
        task="""
        1. 导航到登录页面https://glzx.yonghui.cn/login
        2. 填写手机号18359101702和密码HY1233s2b
        3. 点击登录触发滑块验证
        4. 使用图像分析获取滑块验证结构并计算精确拖拽距离（JavaScript代码分析背景图片找到缺口精确位置，使用获得的精确缺口位置执行拖拽）
        5. 执行精确拖拽操作
        6. 验证登录成功并截图确认
        """,
        llm=ChatOpenAI(
            model="gpt-4o",
            temperature=0.7,
            base_url="https://api.openai-hk.com/v1/",
            api_key="hk-j0zga71000057126822beeaf21f043dc4eb927ad7a3ea7b7",
            timeout=60,  # 增加超时时间
            max_retries=3,  # 设置重试次数
        ),
        use_vision=True,  # 确保启用视觉功能
    )
    await agent.run()

asyncio.run(main())