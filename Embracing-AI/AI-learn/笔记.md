"""
BertModel(
  (embeddings): BertEmbeddings(
    (word_embeddings): Embedding(21128, 768, padding_idx=0)
    (position_embeddings): Embedding(512, 768)
    (token_type_embeddings): Embedding(2, 768)
    (LayerNorm): LayerNorm((768,), eps=1e-12, elementwise_affine=True)
    (dropout): Dropout(p=0.1, inplace=False)
  )
  (encoder): <PERSON><PERSON><PERSON><PERSON>(
    (layer): ModuleList(
      (0-11): 12 x Bert<PERSON>ayer(
        (attention): BertAttention(
          (self): BertSelfAttention(
            (query): Linear(in_features=768, out_features=768, bias=True)
            (key): Linear(in_features=768, out_features=768, bias=True)
            (value): Linear(in_features=768, out_features=768, bias=True)
            (dropout): Dropout(p=0.1, inplace=False)
          )
          (output): BertSelfOutput(
            (dense): Linear(in_features=768, out_features=768, bias=True)
            (LayerNorm): LayerNorm((768,), eps=1e-12, elementwise_affine=True)
            (dropout): Dropout(p=0.1, inplace=False)
          )
        )
        (intermediate): BertIntermediate(
          (dense): Linear(in_features=768, out_features=3072, bias=True)
          (intermediate_act_fn): GELUActivation()
        )
        (output): BertOutput(
          (dense): Linear(in_features=3072, out_features=768, bias=True)
          (LayerNorm): LayerNorm((768,), eps=1e-12, elementwise_affine=True)
          (dropout): Dropout(p=0.1, inplace=False)
        )
      )
    )
  )
  (pooler): BertPooler(
    (dense): Linear(in_features=768, out_features=768, bias=True)
    (activation): Tanh()
  )
)


BERT（Bidirectional Encoder Representations from Transformers）模型是一种基于Transformer编码器结构的预训练语言模型。以下是对您提供的`BertModel`结构的详细分块解释：

---

### **1. BertEmbeddings（嵌入层）**
将输入文本转化为向量表示，包含三种嵌入的融合：

- **word_embeddings**（词嵌入）：
  - 参数：`Embedding(21128, 768, padding_idx=0)`
  - 功能：将21128大小的词表映射为768维向量，`padding_idx=0`表示索引0的词（填充符）不参与梯度更新。

- **position_embeddings**（位置嵌入）：
  - 参数：`Embedding(512, 768)`
  - 功能：为序列中的每个位置（最多512个位置）生成768维向量，捕捉词的位置信息。

- **token_type_embeddings**（段落类型嵌入）：
  - 参数：`Embedding(2, 768)`
  - 功能：区分句子A和句子B（如问答任务），两种类型分别映射为768维向量。

- **融合与标准化**：
  - 三种嵌入相加 → **LayerNorm**（层标准化，稳定训练） → **Dropout**（概率0.1，防止过拟合）。

---

### **2. BertEncoder（编码器）**
由12层相同的`BertLayer`堆叠而成，每层包含自注意力机制和前馈网络。

#### **(1) BertAttention（自注意力模块）**
- **BertSelfAttention**：
  - `query`、`key`、`value`：三个独立的线性层（`Linear(768, 768)`），将输入投影到768维空间。
  - **多头机制**：隐含将768维拆分为12个头（每个头64维），并行计算注意力。
  - 计算注意力分数 → Softmax归一化 → **Dropout**（0.1）。

- **BertSelfOutput**（残差连接与标准化）：
  - `dense`：线性层（768→768）整合多头输出。
  - **LayerNorm**：对`输入 + 自注意力输出`做标准化。
  - **Dropout**（0.1）。

#### **(2) BertIntermediate（中间层）**
- `dense`：线性扩展维度（768→3072），激活函数为**GELU**，增强非线性表达能力。

#### **(3) BertOutput（前馈输出层）**
- `dense`：线性降维（3072→768）。
  - **残差连接**：中间层输出与BertAttention的输出相加。
  - **LayerNorm** → **Dropout**（0.1）。

---

### **3. BertPooler（池化层）**
- `dense`：线性层（768→768），对[CLS]标记的隐藏状态进行变换。
- **Tanh激活**：将输出限制在[-1, 1]区间，常用于下游任务的句向量表示。

---

### **关键参数总结**
- **模型规模**：BERT-base（12层、768隐藏维、12注意力头）。
- **输入处理**：最大序列长度512，支持两段文本（如问答）。
- **正则化**：每层后使用LayerNorm和Dropout（0.1）。

---

### **完整流程示例**
1. **输入**：`[CLS]句子A[SEP]句子B[SEP]`。
2. **嵌入层**：词ID → 词嵌入 + 位置嵌入 + 段落嵌入 → 相加 → LayerNorm → Dropout。
3. **编码器**：12层Transformer处理，每层通过自注意力捕捉上下文，前馈网络深化特征。
4. **输出**：最后一层的[CLS]向量经池化层输出，用于分类等任务；其他token向量可用于序列标注。

此结构通过预训练（如MLM和NSP任务）学习通用语言表示，可微调适配下游任务。
"""





训练ChnSentiCorp时：
使用MyData.py
net中self.fc = torch.nn.Linear(768,2) 2分类，
run中names = ["负向情绪","正向情绪"]，    model.load_state_dict(torch.load("params/best_bert.pth",map_location=DEVICE))


训练Weibo时：
使用MyData02.py
net中self.fc = torch.nn.Linear(768,8) 8分类，
run中names = ["喜欢","厌恶","快乐","悲伤","愤怒","惊讶","恐惧","无无情绪"]




知识点：
1.微调模式

- 全量微调（预训练）
  - 对所有参数进行微调
  - 对算力和显存要求高
  - 效果最佳

- 局部微调（指令微调）
  - 只调整某些某部分参数，例如输出层，输入层或某些特殊层
  - 对算力和显存要求一般

- 增量微调
  - 通过新增参数的方式进行微调，新的知识存储在新的参数中。
  - 对显存和算力要求低
  - 效果不如全量微调

2.https://chatgpt.com/c/683194bf-54f4-8001-a107-ae19d63065ca
    x*w+b=h
    训练/微调主要就是调权重（Weights）和偏置（Bias）
    | 网络类型 | 优势          | 典型场景           |
    | ---- | ----------- | -------------- |
    | MLP  | 通用、结构简单     | 表格数据、小规模分类回归   |
    | CNN  | 局部模式提取、参数高效 | 图像、语音等网格数据     |
    | RNN  | 时序依赖建模      | 文本、时间序列、语音     |
    | GNN  | 拓扑结构建模      | 社交推荐、分子图、交通网络等 |



demo_7:
1.关于分类问题中的样本不均衡问题
    提高模型精度就两个方法：
        第一保证数据质量：
            1.处理分类问题时，数据集中每个类别的数据量必须均衡,如果不均衡就需要重采样，处理分布不均衡的问题，一般使用imbalanced-learn库  
        第二增加训练时长

2.如何解决样本不均衡问题


3.模型微调训练中超长文本训练存在的问题

4.如何更改模型配置信息案例:更改模型配置实现自定义新闻数据分类
    
    max_position_embeddings



# day11
ollama 配置文件：/etc/systemd/system/ollama.service
[Unit]
Description=Ollama Service
After=network-online.target

[Service]
ExecStart=/usr/local/bin/ollama serve
User=ollama
Group=ollama
Restart=always
RestartSec=3
Environment="PATH=/usr/local/bin:/usr/local/nvidia/bin:/usr/local/cuda/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"

[Install]
WantedBy=default.target






不同网络类型的特征标识
| 网络类型 | 关键层类型 | 常见库/模块 |
|---------|-----------|------------|
| 全连接网络 | torch.nn.Linear | torch.nn |
| CNN | torch.nn.Conv1d/2d/3d, torch.nn.MaxPool2d | torch.nn |
| RNN | torch.nn.LSTM, torch.nn.GRU, torch.nn.RNN | torch.nn |
| Transformer | torch.nn.TransformerEncoder, BertModel | transformers, torch.nn |
| GNN | GCNConv, GraphConv | torch_geometric, dgl |




## GPT2 文本生成原理

### 核心架构
GPT2是**Decoder-only Transformer**，采用**自回归生成**方式：

```python
# 关键代码：demo_8/train.py
model = AutoModelForCausalLM.from_pretrained(...)  # 因果语言模型
data['labels'] = data['input_ids'].clone()  # 标签就是输入序列本身
```

### 工作流程

1. **训练数据处理**：
   - 输入：中文诗歌文本序列
   - 标签：同一序列向右偏移一位
   - 目标：给定前N个字，预测第N+1个字

2. **生成过程**：
   ```
   输入: "春眠不觉晓"
   预测: "春" → "眠", "春眠" → "不", "春眠不" → "觉" ...
   ```

3. **损失计算**：
   ```python
   out = model(**data)  # 前向传播
   loss = out['loss']   # 自动计算交叉熵损失
   ```

### 生成机制
- **自回归**：每次只能看到当前位置之前的token
- **逐步生成**：t时刻的输出作为t+1时刻的输入
- **概率采样**：从词汇表概率分布中采样下一个token

## BERT 分类原理

### 核心架构
BERT是**Encoder-only Transformer**，采用**双向编码**：

```python
# 关键代码：demo_5/net.py
pretrained = BertModel.from_pretrained(...)  # 双向编码模型
out = pretrained(input_ids, attention_mask, token_type_ids)
```

### 工作流程

1. **数据处理**：
   ```python
   # demo_5/MyData.py
   text = self.dataset[item]['text']    # 评论文本
   label = self.dataset[item]['label']  # 情感标签(0/1)
   ```

2. **编码过程**：
   ```
   输入: "[CLS] 这个产品很好用 [SEP]"
   BERT: 双向attention，每个位置都能看到全文
   输出: 768维向量表示
   ```

3. **分类实现**：
   ```python
   # demo_5/net.py
   class Model(torch.nn.Module):
       def __init__(self):
           self.fc = torch.nn.Linear(768, 2)  # 768→2分类
       
       def forward(self, input_ids, attention_mask, token_type_ids):
           with torch.no_grad():  # 冻结BERT参数
               out = pretrained(...)
           # 取[CLS] token的表示进行分类
           out = self.fc(out.last_hidden_state[:,0])
           return out
   ```

### 分类机制
- **双向理解**：同时看到前后文信息
- **[CLS] token**：句子级别的语义表示
- **微调策略**：冻结BERT，只训练分类头

## 关键差异对比

| 特性 | GPT2 | BERT |
|------|------|------|
| **架构** | Decoder-only | Encoder-only |
| **注意力** | 单向（因果mask） | 双向（全局attention） |
| **训练目标** | 下一个token预测 | 掩码语言模型+NSP |
| **应用场景** | 文本生成 | 文本理解/分类 |
| **推理方式** | 自回归逐步生成 | 并行编码整个序列 |

## 实际应用示例

**GPT2生成诗歌**：
```
输入: "春"
生成: "春眠不觉晓，处处闻啼鸟..."
```

**BERT情感分析**：
```
输入: "这个产品质量很差"
输出: 负面情感 (label=0)
```

这两个模型体现了Transformer在生成和理解任务上的不同应用方式，GPT2专注于创造性生成，BERT专注于深度理解。




-------------------------

# Transformer
简单理解：编码器就是特征提取，解码器就是特征还原。

基于BERT和GPT2的实现，我来详细讲解Transformer的结构和理：

## Transformer 完整架构

Transformer最初是一个**Encoder-Decoder**架构，用于机器翻译任务：

```
输入序列 → [Encoder] → 编码表示 → [Decoder] → 输出序列
```

### 1. 原始Transformer结构

```
┌─────────────────────────────────────────────┐
│                 Transformer                  │
├─────────────────────┬───────────────────────┤
│      Encoder        │       Decoder         │
├─────────────────────┼───────────────────────┤
│  Multi-Head         │  Masked Multi-Head    │
│  Self-Attention     │  Self-Attention       │
│        ↓            │         ↓             │
│  Add & Norm         │  Add & Norm           │
│        ↓            │         ↓             │
│  Feed Forward       │  Multi-Head           │
│        ↓            │  Cross-Attention      │
│  Add & Norm         │         ↓             │
│        ↓            │  Add & Norm           │
│   (重复N次)         │         ↓             │
│                     │  Feed Forward         │
│                     │         ↓             │
│                     │  Add & Norm           │
│                     │    (重复N次)          │
└─────────────────────┴───────────────────────┘
```

### 2. 核心组件详解

#### 2.1 自注意力机制（Self-Attention）

```python
# 核心公式：Attention(Q,K,V) = softmax(QK^T/√d_k)V

# BERT中的使用（双向注意力）
attention_mask = data["attention_mask"]  # [1,1,1,0,0] 标记真实token
# 可以看到所有位置的信息

# GPT2中的使用（因果注意力）
# 使用下三角mask，只能看到当前位置之前的信息
```

#### 2.2 多头注意力（Multi-Head Attention）

```
Multi-Head = Concat(head_1, head_2, ..., head_h)W^O
其中 head_i = Attention(QW_i^Q, KW_i^K, VW_i^V)
```

每个头关注不同的语义信息：
- 头1：可能关注语法关系
- 头2：可能关注语义相似性
- 头3：可能关注位置关系

#### 2.3 位置编码（Positional Encoding）

Transformer没有循环结构，需要位置信息：

```python
# BERT的位置编码
position_ids = torch.arange(seq_length)
position_embeddings = self.position_embeddings(position_ids)

# 原始Transformer使用正弦位置编码
PE(pos,2i) = sin(pos/10000^(2i/d_model))
PE(pos,2i+1) = cos(pos/10000^(2i/d_model))
```

### 3. BERT：只用Encoder

BERT使用了Transformer的**Encoder部分**，实现双向理解：

```python
# demo_5/net.py 的实现
class Model(torch.nn.Module):
    def __init__(self):
        super().__init__()
        # BERT是12层Encoder堆叠
        self.fc = torch.nn.Linear(768, 2)  # 768是隐藏维度
    
    def forward(self, input_ids, attention_mask, token_type_ids):
        # BERT的三个输入
        # input_ids: token的ID [batch, seq_len]
        # attention_mask: 标记padding [batch, seq_len]
        # token_type_ids: 区分句子A/B [batch, seq_len]
        
        with torch.no_grad():
            out = pretrained(input_ids, attention_mask, token_type_ids)
        
        # 取[CLS]的表示做分类
        out = self.fc(out.last_hidden_state[:,0])
        return out
```

**BERT的特点**：
- **双向注意力**：每个位置都能看到全文
- **MLM预训练**：随机mask 15%的token进行预测
- **NSP任务**：判断两个句子是否相邻

### 4. GPT2：只用Decoder

GPT2使用了Transformer的**Decoder部分**（去掉Cross-Attention）：

```python
# demo_8/train.py 的实现
model = AutoModelForCausalLM.from_pretrained(...)  # 因果语言模型

def collate_fn(data):
    data = tokenizer.batch_encode_plus(data, ...)
    # 关键：标签就是输入向右偏移一位
    data['labels'] = data['input_ids'].clone()
    return data
```

**GPT2的特点**：
- **单向注意力**：使用因果mask，只能看到之前的token
- **自回归生成**：逐个token生成
- **统一架构**：12/24/48层Decoder堆叠

### 5. 注意力掩码对比

```python
# BERT的注意力掩码（可以看到所有位置）
attention_mask = [
    [1, 1, 1, 1],
    [1, 1, 1, 1],
    [1, 1, 1, 1],
    [1, 1, 1, 1]
]

# GPT2的因果掩码（只能看到之前的位置）
causal_mask = [
    [1, 0, 0, 0],
    [1, 1, 0, 0],
    [1, 1, 1, 0],
    [1, 1, 1, 1]
]
```

### 6. 前馈网络（Feed Forward）

两个模型都使用相同的FFN结构：

```python
FFN(x) = max(0, xW1 + b1)W2 + b2
# 通常：d_model=768, d_ff=3072
```

### 7. 层归一化和残差连接

```python
# 每个子层都有
output = LayerNorm(x + Sublayer(x))
```

## 总结对比

| 组件 | 原始Transformer | BERT | GPT2 |
|------|----------------|------|------|
| **架构** | Encoder-Decoder | Encoder only | Decoder only |
| **层数** | 6+6 | 12/24 | 12/24/48 |
| **注意力** | 双向+单向 | 双向 | 单向（因果） |
| **预训练** | 监督翻译 | MLM+NSP | 语言建模 |
| **应用** | 序列到序列 | 理解任务 | 生成任务 |

## 实际应用示例

**BERT处理流程**：
```
输入: "这个产品[MASK]好用" 
→ Tokenize: [101, 2110, 782, 103, 1962, 102]
→ 12层Encoder双向编码
→ 输出: 每个位置的768维表示
→ 预测[MASK]: "很"
```

**GPT2生成流程**：
```
输入: "今天天气"
→ Tokenize: [791, 1921, 1921, 2698]
→ 12层Decoder单向编码
→ 预测下一个: "很"
→ 继续预测: "好"
→ 最终: "今天天气很好"
```

Transformer的革命性在于完全基于注意力机制，抛弃了RNN/CNN，实现了并行计算和长距离依赖建模。BERT和GPT2分别展示了其在理解和生成任务上的强大能力。




day09少了视频中的代码demo16和demo17，
AutoDL ：买服务器的网站，可以通过vscode ssh连接，https://blog.csdn.net/qq_38198286/article/details/127886049
在终端执行命令的时候，要用nohup python -u 脚本 >log.txt 2>&1 &
或者 nohup stdbuf -oL -eL llamafactory-cli webui > nohup.out 2>&1 &

TensorBoard：可视化工具， 本地部署打开，要安装tensorboard和tensorboardX
pip install tensorboard 
pip install tensorboardX
https://blog.csdn.net/HowieXue/article/details/105412155

混合精度运算

# ollama个性化配置


# llamafactory 
## 部署
创建新的虚拟环境
python -m venv llamafactory_env
激活虚拟环境
cd /mnt/workspace/yiyan/LLaMA-Factory-main
source llamafactory_env/bin/activate
升级pip
pip install --upgrade pip
安装LLaMA-Factory及其依赖
cd /mnt/workspace/yiyan/LLaMA-Factory-main
pip install -e .
启动webui
llamafactory-cli webui

## llamafactory webui的配置截图
https://cdn.nlark.com/yuque/0/2025/png/26316200/1749118472661-b9bd68c2-50bd-4721-8974-7136bdd5b4aa.png

## 查看显存占用信息
nvitop

## 训练
## 评估
## 推理
## 合并导出

"instruction": "国际经济与贸易专业的就业前景是怎样的？", --对应问题
"input": "", --对应阅读理解的原文参考，即回答要基于原文进行回答
"output": "", --大模型回答
"history" --历史上下文， 按时间顺序，最新的一条就是instruction


模型的名字规则
Llama-3.2-1B
Llama-3.2-1B-Base
Llama-3.2-1B-Chat
Llama-3.2-1B-Instruct
1.首选Chat版本，其次Instruct版本，再下来就是Base
2.Instruct和Chat版本加入了监督微调



torch环境安装指令从官网根据版本取
https://pytorch.org/get-started/previous-versions/


# llamafactory评估指标比较少，只包含核心指标
{
  "predict_bleu-4": 97.29608461538461,  # 质量评估指标，综合评价1-4个词组合的整体匹配质量，侧重的是准确率
  "predict_model_preparation_time": 0.0036,  # 质量评估指标， 模型准备时间
  "predict_rouge-1": 100.0,  # 质量评估指标， 单个词的召回率（覆盖率）匹配100%
  "predict_rouge-2": 100.0,  # 质量评估指标， 相邻两个词的召回率（覆盖率）匹配100%
  "predict_rouge-l": 100.0,  # 质量评估指标， 整个句子的召回率（覆盖率）匹配100%
  "predict_runtime": 3.4557,  # 性能指标，模型整体评估过程所用时间
  "predict_samples_per_second": 3.762, # 性能指标，模型每秒能处理3.76个样本
  "predict_steps_per_second": 0.289  # 性能指标，模型每秒能处理0.289个步骤
}

翻译任务例子
标准答案：我今天很开心
学生答案：我很开心
BLEU说：学生写了4个字，有3个对的，给75分！✨
ROUGE说：标准答案5个字，学生只覆盖了3个，给60分。😐
摘要任务例子
原文要点：价格上涨、销量下降、用户投诉、公司亏损
学生摘要：价格上涨了，销量也下降了
BLEU说：学生写的内容都是对的，虽然简短但很准确！给80分！👍
ROUGE说：4个要点你只写了2个，信息不够全面，给50分。😕

一般来讲两个指标都要高，模型效果才会好


量化就是牺牲很小的精度，换来模型很大的性能提升
秩就是隐藏层矩阵的维度，维度越高离原始模型越接近
Qlora微调+量化8位+秩32缩放系数64 效果比 lora微调+量化8位 要好，即用qlora微调把秩升上去来替换原来的lora微调


/Qwen1.5-1.8B-Chat  中文问答效果比llama3.2效果好


# llamafactory工具使用
1.lora微调（训练+验证）
2.lora模型合并与推理测试（合并指的是微调好的hf模型和微调后的检查点数据文件做合并，checkpoiont文件又叫LoRA Adapter）
3.模型导出量化
4.Qlora微调
5.模型评估（lora和Qlora）
6.模型打包部署（Lora模型合并&转GGUF模型部署）
7.使用Qwen1.5-1.8B-Chat模型加ruozhiba的数据集来训练，



一般流程：微调完模型，合并模型，导出模型，

# ------------------------------------------------------------

# llama.cpp 转hf模型为gguf格式，通过ollama+open webui跑起来


# 安装conda
下载Miniconda到持久化目录
cd /mnt/workspace
wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh

安装到持久化目录
bash Miniconda3-latest-Linux-x86_64.sh -b -p /mnt/workspace/miniconda3

初始化conda
/mnt/workspace/miniconda3/bin/conda init

添加到PATH（写入bashrc）
echo 'export PATH="/mnt/workspace/miniconda3/bin:$PATH"' >> ~/.bashrc
source ~/.bashrc

# 安装LLaaMA-Factory环境
git clone 项目
创建LLaMA-Factory虚拟环境
/mnt/workspace/LLaMA-Factory#conda create -n llamafactory
激活LLaMA-Factory虚拟环境
/mnt/workspace/LLaMA-Factory# conda activate llamafactory
安装LLaMA-Factory项目依赖包
pip install -e .
运行项目
llamafactory-cli webui



