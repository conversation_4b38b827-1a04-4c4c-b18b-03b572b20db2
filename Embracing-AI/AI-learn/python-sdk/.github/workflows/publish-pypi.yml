name: Publishing

on:
  release:
    types: [published]

jobs:
  release-build:
    name: Build distribution
    runs-on: ubuntu-latest
    needs: [checks]
    steps:
      - uses: actions/checkout@v4

      - name: Install uv
        uses: astral-sh/setup-uv@v3
        with:
          enable-cache: true
          version: 0.7.2

      - name: Set up Python 3.12
        run: uv python install 3.12

      - name: Build
        run: uv build

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: release-dists
          path: dist/

  checks:
    uses: ./.github/workflows/shared.yml

  pypi-publish:
    name: Upload release to PyPI
    runs-on: ubuntu-latest
    environment: release
    needs:
      - release-build
    permissions:
      id-token: write # IMPORTANT: this permission is mandatory for trusted publishing

    steps:
      - name: Retrieve release distributions
        uses: actions/download-artifact@v4
        with:
          name: release-dists
          path: dist/

      - name: Publish package distributions to PyPI
        uses: pypa/gh-action-pypi-publish@release/v1

  docs-publish:
    runs-on: ubuntu-latest
    needs: ["pypi-publish"]
    permissions:
      contents: write
    steps:
      - uses: actions/checkout@v4
      - name: Configure Git Credentials
        run: |
          git config user.name github-actions[bot]
          git config user.email 41898282+github-actions[bot]@users.noreply.github.com

      - name: Install uv
        uses: astral-sh/setup-uv@v3
        with:
          enable-cache: true
          version: 0.7.2

      - run: echo "cache_id=$(date --utc '+%V')" >> $GITHUB_ENV
      - uses: actions/cache@v4
        with:
          key: mkdocs-material-${{ env.cache_id }}
          path: .cache
          restore-keys: |
            mkdocs-material-

      - run: uv sync --frozen --group docs
      - run: uv run --frozen --no-sync mkdocs gh-deploy --force
