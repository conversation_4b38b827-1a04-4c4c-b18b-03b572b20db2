name: 🚀 MCP Python SDK Feature Request
description: "Suggest a new feature for the MCP Python SDK"
labels: ["feature request"]

body:
  - type: markdown
    attributes:
      value: Thank you for contributing to the MCP Python SDK! ✊

  - type: textarea
    id: description
    attributes:
      label: Description
      description: |
        Please give as much detail as possible about the feature you would like to suggest. 🙏

        You might like to add:
        * A demo of how code might look when using the feature
        * Your use case(s) for the feature
        * Reference to other projects that have a similar feature
    validations:
      required: true

  - type: textarea
    id: references
    attributes:
      label: References
      description: |
        Please add any links or references that might help us understand your feature request better. 📚
